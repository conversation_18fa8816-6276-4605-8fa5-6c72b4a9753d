*** Settings ***
Documentation       f07p14 Patient can go to another clinic they have access to

Resource            ${EXECDIR}${/}resources${/}common_mobile.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_clinic.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_library.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_common.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource

Suite Setup         Set Libraries Order
Test Setup          Setup Native App In Browserstack
Test Teardown       Close Application
Test Timeout        ${TEST_TIMEOUT}


*** Test Cases ***
Patient can go to another clinic they have access to - Clinic switchers / name in header status
    [Tags]    nms9-ver-239-app    native-app
    Login To Native App In Browserstack    ${f07p14_patient}[email]
    Patient Is In The Right Page    Diary
    Switch To Clinic    header    any
    clinic.Navigate to Clinic
    Patient Is In The Right Page    Clinic
    Switch To Clinic    header    ${current_clinic}
    app_library.Navigate To Library
    Patient Is In The Right Page    Library
    Switch To Clinic    header    ${current_clinic}

Extension A - From More menu
    [Tags]    nms9-ver-240-app    native-app
    Login To Native App In Browserstack    ${f07p14_patient}[email]
    Patient Is In The Right Page    Diary
    Switch To Clinic    more menu    any
    clinic.Navigate to Clinic
    Patient Is In The Right Page    Clinic
    Switch To Clinic    more menu    ${current_clinic}
    app_library.Navigate To Library
    Patient Is In The Right Page    Library
    Switch To Clinic    more menu    ${current_clinic}


*** Keywords ***
Switch To Clinic
    [Arguments]    ${method}    ${starting_clinic}
    Wait Until Element Is Visible    //*[@id='${spmc_clinic_toggle_header}']
    IF    '${starting_clinic}'=='any'
        ${current_clinic}    Get Text    //*[@id='${spmc_clinic_toggle_header}']
    ELSE
        ${current_clinic}    Set Variable    ${starting_clinic}
    END
    IF    '${method}'=='header'
        Try To Click Native App Element    //*[@id='${spmc_clinic_toggle_header}']
    ELSE IF    '${method}'=='more menu'
        app_more.Try To Click More Button
        Try To Click Native App Element    ${go_to_another_clinic_button}
    END
    Wait Until Page Contains    Go to another clinic
    IF    '${current_clinic}'=='${native_app_automated_tests}[name]'
        Try To Click Native App Element    //li/label[text()="${automated_tests_clinic}[name]"]
        Wait Until Page Does Not Contain    Go to another clinic
        Wait Until Page Contains    You are now in ${automated_tests_clinic}[name]    timeout=10s
        Try To Click Native App Element    ${banner_message_element}
    ELSE IF    '${current_clinic}'=='${automated_tests_clinic}[name]'
        Try To Click Native App Element    //li/label[text()="${native_app_automated_tests}[name]"]
        Wait Until Page Does Not Contain    Go to another clinic
        Wait Until Page Contains    You are now in ${native_app_automated_tests}[name]    timeout=10s
        Try To Click Native App Element    ${banner_message_element}
    END
    Wait Until Element Is Visible    //*[@id='${spmc_clinic_toggle_header}']
    ${current_clinic}    Get Text    //*[@id='${spmc_clinic_toggle_header}']
    Set Test Variable    ${current_clinic}
    Sleep    1s
