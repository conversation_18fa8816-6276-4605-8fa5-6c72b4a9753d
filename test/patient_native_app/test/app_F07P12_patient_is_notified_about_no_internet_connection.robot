*** Settings ***
Documentation       F07P12 Patient is notified about no internet connection

Resource            ${EXECDIR}${/}resources${/}common_mobile.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_clinic.resource
Library             RequestsLibrary

Suite Setup         Set Libraries Order
Test Setup          Setup Native App In Browserstack
Test Teardown       Close Application
Test Timeout        ${TEST_TIMEOUT}

Force Tags          native-app    usecase-f07p12-app


*** Variables ***
${no_internet_connection_header_label}      No Internet connection
${no_internet_connection_p1}                //*[contains(text(),'There seems to be an issue with your network connection.')]
${no_internet_connection_p2}                //*[contains(text(),'Please make sure that you are connected to the Internet.')]
${url1}                                     https://api-cloud.browserstack.com/app-automate/sessions/
${url2}                                     /update_network.json


*** Test Cases ***
Main - Pat<PERSON> is notified about no internet connection
    [Documentation]    In case of failure, check browserstack run video as well to make sure it is an actual failure.
    [Tags]    nms9-ver-22-app
    ${patient_email}    Set Variable If
    ...    '${PLATFORM_NAME}'=='android'
    ...    ${f07p12_patient_android}[email]
    ...    ${f07p12_patient_ios}[email]
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Ask about symptoms
    Set Device Offline
    Wait Until Page Contains    ${no_internet_connection_header_label}
    Page Should Contain Element    ${no_internet_connection_p1}
    Page Should Contain Element    ${no_internet_connection_p2}


*** Keywords ***
Set Device Offline
    [Documentation]    Use "no-netword" value for networkProfile to simulate no network connect which is compatable for both Android and iOS (ver > 11)
    ${session_id}    Get Appium SessionId
    ${header}    Create Dictionary    Content-Type=application/json
    VAR    ${body}    {"networkProfile":"no-network"}
    ${auth}    Evaluate    ('${SERVICE_ACCOUNT_USERNAME}', '${SERVICE_ACCOUNT_PASSWORD}')
    ${url}    Catenate    ${url1}    ${session_id}    ${url2}
    ${url}    Replace String    ${url}    ${SPACE}    ${EMPTY}
    ${response}    PUT    ${url}    auth=${auth}    headers=${header}    data=${body}    expected_status=200
