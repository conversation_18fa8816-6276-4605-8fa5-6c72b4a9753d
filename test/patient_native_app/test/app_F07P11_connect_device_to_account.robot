*** Settings ***
Documentation       F07P11 Patient can connect a device to patient's account - Main success scenario (app)
...                 Preconditions:
...                 1. Patient is logging into native application
...                 2. Patient sets PIN code or allows notifications
...
...                 For removing connected device see Extension A in app_F02P01 Patient can update personal profile

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}account_preferences.resource
Resource            ${EXECDIR}${/}resources${/}common_mobile.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_account_preferences.resource

Suite Setup         Set Libraries Order
Test Setup          Setup Native App In Browserstack
Test Teardown       Close Application
Test Timeout        ${TEST_TIMEOUT}

Force Tags          native-app    usecase-f07p11-app


*** Test Cases ***
Patient Account Is Connected To Device
    [Documentation]    Also includes:    F02P01 Extension A - Remove a connected device
    [Tags]    nms9-ver-360-app    nms9-ver-165-app
    ${patient_email}    Set Variable If
    ...    '${PLATFORM_NAME}'=='android'
    ...    ${f02p01_f07p11_android}[email]
    ...    ${f02p01_f07p11_ios}[email]
    Login To Native App In Browserstack    ${patient_email}
    Verify Connected Device From Account Preferences
    Remove Connected Device From Account Preferences
    Wait Until Page Does Not Contain    ${patient_email}
