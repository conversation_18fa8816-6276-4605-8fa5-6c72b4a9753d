*** Settings ***
Documentation       F07U01 Patient can login
...                 Preconditions:
...                 - Patient account has been created
...                 - Pat<PERSON> has already setup a password
...
...                 F07P10 Pat<PERSON> can set PIN code for native application
...                 - Patient logs in with email and password.
...                 - Patient sets four-digit PIN code.
...                 - Patient verifies PIN code.
...
...                 F07P09 Patient can allow native application notifications
...                 - Patient allows app notifications
...                 - In Android, patient selects Next
...                 - In iOS, patient selects Next.
...                 - A dialog is displayed and patient selects Allow
...
...                 F07P15 Patient native application is updated automatically
...                 Preconditions:
...                 - User has installed native application
...

Resource            ${EXECDIR}${/}resources${/}common_mobile.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Application
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u01-app    usecase-f07p10-app    usecase-f07p09-app


*** Test Cases ***
F07U01 User can login: Extension A — Expired password
    [Tags]    nms9-ver-242-app    native-app
    [Setup]    Set Application On Environment
    Launch Noona
    Try To Click Element    ${landing_page_login_button_app}
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    FOR    ${index}    IN RANGE    0    6
        # input correct email, incorrect password
        Input Login Credentials With Error Message
        ...    ${expired_password_patient_email}[email]
        ...    !passmenot?
        Sleep    5s    # Sleep is needed as per NOONA-18026
        Clear Patient Login Details    ${expired_password_patient_email}[email]
    END
    # input correct email, correct password
    Input Login Credentials With Error Message
    ...    ${expired_password_patient_email}[email]
    ...    ${DEFAULT_PASSWORD}
    # TODO: unlock patient in clinic

F07U01 User can login: Extension D - Patient unlocks native application with PIN code
    [Documentation]    Device's PIN code should be enabled in AVD via mobile settings
    ...    Test case is now obsolete due to NOONA-19516: Biometric authentication
    [Tags]    manual    obsolete    nms9-ver-356-app
    [Setup]    Setup Native App In Browserstack
    Login To Native App In Browserstack    ${f07p10_a_patient}[email]
    Put App In Backgroud    60

F07P10 Extension A - Patient can reset the PIN (app)
    [Documentation]    If patient selects Have you forgotten your PIN code?, patient has to log into native application.
    ...    Test case is now obsolete due to NOONA-19516: Biometric authentication
    [Tags]    manual    obsolete    nms9-ver-357-app
    [Setup]    Setup Native App In Browserstack
    Login To Native App In Browserstack    ${f07p10_a_patient}[email]
    Put App In Backgroud    60
    Patient selects "Have you forgotten your PIN?"
    Re-login To Native App In Browserstack    ${f07p10_a_patient}[email]

F07U01 User can login - Extension I – The device is not secure (app)
    [Tags]    nms9-ver-380-app    native-app
    [Setup]    Setup Native App In Browserstack    passcode=false
    Wait Until Page Contains    ${landing_page_connect_to_clinic_text}
    Wait Until Page Contains
    ...    To protect your privacy, you need to secure your device by activating screen lock in the settings. Then, remove and install the Noona application again.
    IF    '${PLATFORM_NAME}'=='android'
        Page Should Not Contain Element    xpath=${oidc_password_input_app_android}
        Page Should Not Contain Element    xpath=${oidc_username_input_android}
    ELSE IF    '${PLATFORM_NAME}'=='ios'
        Page Should Not Contain Element    xpath=${oidc_password_input_app}
        Page Should Not Contain Element    xpath=${oidc_username_input_app}
    END
