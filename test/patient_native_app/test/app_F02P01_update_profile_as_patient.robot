*** Settings ***
Documentation       F02P01 Patient can update personal profile
...
...                 Extension A - Remove a connected device (app)
...                     # TODO: Remove a connected device (web)
...                 Not yet testable through automation:
...                 Extension B - Set backup channel if native application notification is not delivered (web / app)

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}account_preferences.resource
Resource            ${EXECDIR}${/}resources${/}common_mobile.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_account_preferences.resource

Suite Setup         Set Libraries Order
Test Setup          Setup Native App In Browserstack
Test Teardown       Close Application
Test Timeout        ${TEST_TIMEOUT}

Force Tags          native-app    usecase-f02p01-app


*** Test Cases ***
Extension B - Set backup channel if native application notification is not delivered (web / app)
    [Tags]    manual    native-app-todo
    Precondition: <PERSON><PERSON> has logged in with a native application
    Select My profile from the menu
    Select notification channel if native application notification is not delivered
    Email and SMS
    Email
    SMS
    Click Save
