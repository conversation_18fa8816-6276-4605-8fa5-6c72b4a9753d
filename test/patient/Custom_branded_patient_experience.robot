*** Settings ***
Documentation       Custom branded patient experience - Patient side
...
...                 Preconditions:
...                 - Access Clinic as clinic admin
...                 - Navigate to Clinic Settings (under clinic link in main navigation)
...                 - Scroll to “Clinic experience for patients”
...                 - Enable “Custom branding”
...
...                 Test Data:
...                 - Clinic for custom branding tests: TA clinic Automated_tests
...                 - Scenario 1: Verify Patient Custom Email Links
...                 - Scenario 2: Verify Patient Web App Custom Logo Only
...                 - Sceanrio 3: Verify Patient Web App Color Only
...                 - Scenario 4: Verify Patient Web App Custom Service Name Only
...                 - Scenario 5: Verify Patient Web App Custom Color, Logo, & Service Name
...                 - Scenario 6: Verify Patient Web App when Custom Branding is Disabled

Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}co-branding_patient.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          patient-web    manual


*** Variables ***
${svg_logo}                     valid-logo.svg
${jpg_photo}                    file_example_JPG_100kB.jpg
${patient_email}                <EMAIL>
${patient_email_cbath}          <EMAIL>
${patient_email_cbatt}          <EMAIL>
${patient_email_cbatp}          <EMAIL>
${patient_email_cbatcc}         <EMAIL>
${co-branding_nurse_email}      <EMAIL>
${default_service_names}        Noona's
${default_service_name}         Noona
${custom_service_names}         Test Custom Clinic's
${custom_service_name}          Test Custom Clinic


*** Test Cases ***
# TODO: Verify Patient Custom Email Links

Verify Patient Web App Custom Logo Only
    [Documentation]    NOONA-14049: Co-branding: Default logo is not displayed in email invitation if custom logo is not set
    Clinic Admin Defines A Logo In Clinic Settings
    Custom Branding Is Visible To Patient    ${patient_email_cbatt}    default_color    custom_logo    default_noona
    Continue Patient Login
    Verify Keep Me Login Next Button Color
    Verify Color In Navigation Bar

Verify Patient Web App Color Only
    # TODO: Add more scenarios for color comparison
    Clinic Admin Defines A Brand Color In Clinic Settings
    Custom Branding Is Visible To Patient    ${patient_email_cbath}    custom_color    default_logo    default_noona
    Continue Patient Login
    Verify Keep Me Login Next Button Color
    Verify Color In Navigation Bar

Verify Patient Web App Custom Service Name Only
    # TODO: Add verification of custom service name
    Clinic Admin Defines Custom Service Name
    Custom Branding Is Visible To Patient    ${patient_email}    default_color    default_logo    custom_clinic_name
    Continue Patient Login
    Verify Keep Me Login Next Button Color
    Verify Color In Navigation Bar

Verify Patient Web App Custom Color, Logo, & Service Name
    # TODO: Add verification when custom branding is enabled
    Clinic Admin Defines Custom Branding
    Custom Branding Is Visible To Patient
    ...    ${patient_email_cbatcc}
    ...    custom_color
    ...    custom_logo
    ...    custom_clinic_name
    Continue Patient Login
    Verify Keep Me Login Next Button Color
    Verify Color In Navigation Bar

Verify Patient Web App when Custom Branding is Disabled
    # TODO: Add verification when custom branding is disabled
    Clinic Admin Toggles Off Custom Branding
    Custom Branding Is Visible To Patient    ${patient_email}    default_color    default_logo    default_noona
    Continue Patient Login
    Verify Keep Me Login Next Button Color
    Verify Color In Navigation Bar

Verify Custom Service Name Is Displayed Upon Logout
    Clinic Admin Defines Custom Service Name
    Custom Branding Is Visible To Patient
    ...    ${patient_email_cbatp}
    ...    default_color
    ...    default_logo
    ...    custom_clinic_name
    Continue Patient Login
    Verify Keep Me Login Next Button Color
    Verify Color In Navigation Bar
    Logout As Patient
    Verify Patient Log In Custom Clinic Name

Verify Default Service Name Is Displayed Upon Logout
    Clinic Admin Toggles Off Custom Branding
    Custom Branding Is Visible To Patient    ${patient_email}    default_color    default_logo    default_noona
    Continue Patient Login
    Verify Keep Me Login Next Button Color
    Verify Color In Navigation Bar
    Logout As Patient
    Verify Patient Log In Default Clinic Name


*** Keywords ***
Clinic Admin Edits Custom Branding In Basic Settings
    [Arguments]    ${clinic_name}
    Define Clinic Shortname And Custom Url    ${clinic_name}
    Login As Nurse    clinic=${clinic_name}    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Basic Settings Tab
    Noona Administrator Edits Clinic Experience For Patients

Clinic Admin Defines A Logo In Clinic Settings
    Clinic Admin Edits Custom Branding In Basic Settings    ${custom_branding_tampere}[name]
    Clear Custom Color
    Upload Logo With SVG Format    ${svg_logo}
    Upload Logo With Non-svg Format    ${jpg_photo}
    Upload Logo With SVG Format    ${svg_logo}
    Select Custom Service Name    no
    Save Basic Settings
    Close Browser

Clinic Admin Defines A Brand Color In Clinic Settings
    Clinic Admin Edits Custom Branding In Basic Settings    ${custom_branding_helsinki}[name]
    Clear Custom Color
    Admin Can Preview Derived Color Before Saving    40a82d
    Clear Uploaded Logo
    Select Custom Service Name    yes
    Select Custom Service Name    no
    Save Basic Settings
    Close Browser

Clinic Admin Defines Custom Service Name
    Clinic Admin Edits Custom Branding In Basic Settings    ${custom_branding_porvoo}[name]
    Toggle Off Custom Branding
    Save Basic Settings
    Noona Administrator Edits Clinic Experience For Patients
    Clear Custom Color
    Clear Uploaded Logo
    Select Custom Service Name    yes
    Input Clinic Specific Name    ${custom_service_names}    ${custom_service_name}
    Save Basic Settings
    Close Browser

Clinic Admin Defines Custom Branding
    Clinic Admin Edits Custom Branding In Basic Settings    TA clinic Automated_tests
    Admin Can Preview Derived Color Before Saving    40a82d
    Upload Logo With SVG Format    ${svg_logo}
    Select Custom Service Name    yes
    Input Clinic Specific Name    ${custom_service_names}    ${custom_service_name}
    Save Basic Settings
    Close Browser

Clinic Admin Toggles Off Custom Branding
    Clinic Admin Edits Custom Branding In Basic Settings    ${custom_branding_1}[name]
    Toggle Off Custom Branding
    Save Basic Settings
    Close Browser
