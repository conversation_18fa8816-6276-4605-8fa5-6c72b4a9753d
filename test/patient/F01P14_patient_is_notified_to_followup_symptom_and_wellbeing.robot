*** Settings ***
Documentation       F01P14 Patient is notified to follow-up symptom and wellbeing information
...                 Preconditions:
...                 - Patient account needs to be activated.
...                 - Patients' main module is not surgery, recovery module or radiotherapy module.
...                 - Patient has AEQs scheduled to be sent in the future for a module which is not surgery, recovery module or radiotherapy module.
...                 Clinic: Time Constraint Testing
...                 - Patient who has clinic preference "As often as possible". A symptom questonnaire has to be scheduled for smart symptom patient in the future. The questionnaire sending date is
...                 the date of expected smart symptom notification (date of last reported symptom + 3 days) + 3 days. Hence, sending date is current date + 7 days to satisfy the logic condition.
...                 - Patient who has clinic preferenc "Less frequently". A symptom questonnaire has to be scheduled for smart symptom patient in the future. The questionnaire sending date is
...                 the date of expected smart symptom notification (date of last reported symptom + 6 days) + 6 days. Hence, sending date is current date + 13 days to satisfy the logic condition.

Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p14    patient-web    time-constraint    manual


*** Variables ***
${smart_symptom_modal}                                  //nh-smart-symptom-inquiry-selection-form
${smart_symptom_inquiry_form_title}                     ${smart_symptom_modal}//h1[@class="modal-title"]
${smart_symptom_inquiry_form_title_text}                Update your symptoms
${smart_symptom_inquiry_form_question}                  ${smart_symptom_modal}//div[@class="question"]
${smart_symptom_inquiry_form_none_of_above_button}      ${smart_symptom_modal}//*[contains(text(), "None of the above")]
${smart_symptom_inquiry_form_next_button}               ${smart_symptom_modal}//*[contains(text(), "Next")]
${smart_symptom_generic_question_form}                  //nh-smart-symptom-inquiry-generic-question-form//div//div[contains(text(),"Have you had any symptoms")]
${smart_symptom_generic_question_form_no_button}        //*[@id="smart-symptom-inquiry-generic-question-form-primary-1"]
${smart_symptom_generic_question_form_yes_button}       //*[@id="smart-symptom-inquiry-generic-question-form-secondary-0"]
${show_more_symptoms_button}                            //*[@id="more-symptom"]
${smart_symptom_frequency_long}                         //label[@for="smart-symptom-frequency-long"]
${smart_symptom_frequency_short}                        //label[@for="smart-symptom-frequency-short"]


*** Test Cases ***
Main success scenario (web / app)
    [Tags]    native-app-todo    -time-constraint
    Precondition: Patient has selected As often as needed to Noona can ask about my wellbeing.
    Patient is notified to use her diary by sending an SMS / email / native app notification.
    Patient receives a notification at the 3rd day after the last symptom entry has been made
    Patient has no reported symptoms in past 2 days
    Reported symptom is not of type SYMPTOM_GENERAL_CONDITION
    Patient has no NO_SYMPTOM patient value in past 3 days
    Patient has not received any smart-symptom notification in past 2 days (This is just a safe check for migration)
    Patient receives a link to Noona smart-symptom-follow-up re-direct page
    Patient receives a notification at the 7th day if
    Patient has not reported any symptoms in past 6 days
    Patient has not reported any patient value in past 6 days
    Patient has not received any smart-symptom notification in past 6 days
    Patient receives a link to Noona smart-symptom-follow-up re-direct page
    In both cases the following must apply or the notification is not sent:
    Patient needs to be in enabled status (active, locked)
    Patient main module is not in breast cancer recovery, gynecologic recovery or breast surgery
    Scheduled AEQ sending date cannot be within +/- 2 days of the wellness inquiry
    Front end (smart-symptom-follow-up re-direct page):
    Patient has reported a symptom in past three days
    Symptom is not of type SYMPTOM_GENERAL_CONDITION
    Direct link in notification will take patient straight to diary symptom menu
    Menu is filtered based on the symptoms recorded since -30 days
    Display also the "symptom free option"
    Patient has not reported Symptoms past three days, or has reported a SYMPTOM_GENERAL_CONDITION symptom
    Direct link in notification will take patient straight to diary "Add entry" dialog menu.

Extension A - Patient can set less frequent symptom and wellbeing follow-up (web / app)
    [Tags]    native-app-todo    -time-constraint
    Patient navigates to profile
    Patient selects Less frequently to Noona can ask about my wellbeing
    Now, patient receives a notification at the 6th day after the last symptom entry has been made and on 14th day when no entry was made
    Other conditions are tied to these numbers (6 and 14). More detailed description below:
    1.1 2 days => 5 days
    1.3 3 days => 6 days
    2.1 6 days => 13 days
    2.2 6 days => 13 days
    2.3 6 days => 13 days
    +/- 2 days => +/- 5 days

Main scenario - Update More Frequent Smart Symptom Patient Diary - Has Reported A Symptom
    [Documentation]    Run this test every Mondays & Fridays to add a symptom to Smart Symptom patient's diary so that the previous test case can be run the next iteration.
    ...    This script matches the use case Step 1.2
    [Tags]     nms9-ver-11-1    nms9-ver-11
    Set Up Smart Symptom Patient Data    has_symptom
    Login As Patient    ${patient_email}
    Click Add Menu Button
    Add Symptom Entry
    Add Difficulty Eating Symptom To Diary
    Send Symptom Questionnaire To Smart Symptom Patient    6 days

Main scenario - Frequent Smart Symptom Patient Diary - No Symptom Entry In Last 3 Days
    [Documentation]    The test patient has these following conditions:
    ...    Tx Chemotherapy 18 symptoms
    ...    Frequent smart symptom set in account preference as default,
    ...    No symptoms in diary
    ...    A symptom questionnaire is scheduled in 7th day since last reported symptom (in this case since account activation date)
    ...    This script matches the use case Step 1.1
    ...    Patient's created but adding no symptom. Patient has been scheduled symptom questionnaire 13 days from the account creation day. Smart symptom dialog is visible on the 7th day since last login.
    [Tags]     nms9-ver-11-2    nms9-ver-11
    Set Up Smart Symptom Patient Data    no_symptom_yet_last_3d
    Login As Patient    ${patient_email}
    Send Symptom Questionnaire To Smart Symptom Patient    13 days

Extension A - Patient can set less frequent symptom and wellbeing follow-up - Has Reported A Symptom In Last 6 days
    [Documentation]   Patient has reported a symptom, patient has set "Less frequently" for smart symptom tracking in "Clinic Preferences"
    [Tags]    nms9-ver-12-1    nms9-ver-12
    Set Up Smart Symptom Patient Data    has_symptom    less_frequent
    Login As Patient    ${patient_email}
    Go To Clinic Preferences
    Patient Selects Smart Symptom Frequency    long
    Click Add Menu Button
    Add Symptom Entry
    Add Difficulty Eating Symptom To Diary
    Send Symptom Questionnaire To Smart Symptom Patient    12 days

Extension A - Patient can set less frequent symptom and wellbeing follow-up - No Symptom Entry In Last 13 Days
    [Documentation]    Patient's created but haven't added any symptom (No symptom), patient has set "Less frequently" for smart symptom tracking in "Clinic Preferences".Patient has been scheduled symptom questionnaire on the 14th day from the account creation day.
    [Tags]    nms9-ver-12-2    nms9-ver-12
    Set Up Smart Symptom Patient Data    no_symptom_yet    less_frequent
    Login As Patient    ${patient_email}
    Go To Clinic Preferences
    Patient Selects Smart Symptom Frequency    long
    Send Symptom Questionnaire To Smart Symptom Patient    28 days


*** Keywords ***
Set Up Smart Symptom Patient Data
    [Documentation]    ${patient_has_reported_symptom} takes either "has_symptom" or "no_symptom_yet" to differetiate 2 groups of patients. ${smart_symptom_setting} takes "frequently" as default and "less_frequent"
    [Arguments]    ${patient_has_reported_symptom}    ${smart_symptom_setting}=frequently
    ${current_timestamp}    Get Current Date
    ...    result_format=%d%m%Y
    Set Test Variable
    ...    ${patient_identifier}
    ...    ${current_timestamp}_${patient_has_reported_symptom}_${smart_symptom_setting}
    Set Test Variable    @{mailosaur_keys}    ${time_constraint_keys}[0]    ${time_constraint_keys}[1]
    Add An Activated Patient Under Time Constraint Clinic
    ...    ${patient_identifier}
    ...    ${TIME_CONSTRAINT_CLINIC_SUB_ID_F01P12}
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}

Send Symptom Questionnaire To Smart Symptom Patient
    [Arguments]    ${in_number_of_days}
    ${todays_date}    Get Current Date    result_format=%Y-%m-%d
    ${questionnaire_sending_date}    Add Time To Date    ${todays_date}    ${in_number_of_days}
    ${questionnaire_review_date}    Add Time To Date    ${questionnaire_sending_date}    2 days
    Set Test Variable    ${questionnaire_sending_date}
    Set Test Variable    ${questionnaire_review_date}
    Get Patient Main Treatment Module Id    ${patient_id}
    Send Symptom Questionnaire Via API
    ...    ${patient_id}
    ...    ${questionnaire_sending_date}
    ...    ${questionnaire_review_date}
    ...    baseline
    Sleep    90s

Patient Selects Smart Symptom Frequency
    [Arguments]    ${frequency}
    IF    '${frequency}' == 'long'
        Click Element    ${smart_symptom_frequency_long}
    ELSE IF    '${frequency}' == 'short'
        Click Element    ${smart_symptom_frequency_short}
    END
    Save Clinic Preferences
