*** Settings ***
Documentation       F10P02 Patient can view a questionnaire answer

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}appointment.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f10p02    patient-web


*** Test Cases ***
Extension A — Viewing questionnaire answer in detail (web / app)
    [Tags]    nms9-ver-269    native-web
    [Setup]    Setup App Environment
    Create Patient And Send Symptom Questionnaire
    Answer Symptom Questionnaire
    Go To Diary
    Wait Until Element Is Visible    ${latest_questionnaire_in_diary}
    Text Should Not Be In The Page    ${appointment_reason_text}
    Select Latest Questionnaire In Diary
    Questionnaire Header Is Displayed In Modal    ${BASELINE_QUESTIONNAIRE}
    Text Should Be In The Page    ${current_date}
    Set Test Variable    ${login_token}    ${AUTOMATED_TESTS_EHR_TOKEN}
    [Teardown]    Run Keywords    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    AND    Close All App Instances


*** Keywords ***
Create Patient And Send Symptom Questionnaire
    Generate Random Patient Data
    Create An Activated Patient Via API
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Sleep    30s    # wait for questionnaire to be sent

Answer Symptom Questionnaire
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire Button
    Select Yes For Symptoms    ${OTHER_SYMPTOM}
    Complete Other Symptom Questionnaire With Severe Symptom
    Click Emergency Symptom Ok Button
    Select Latest Clinic Message
    Wait Until Page Contains    ${severe_symptom_inbox_text}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Set Test Variable    ${current_date}
    Close Clinic Message
