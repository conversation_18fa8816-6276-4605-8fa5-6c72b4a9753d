*** Settings ***
Documentation       F01P06 Patient can follow-up symptom changes during a follow-up period requested by nurse
...                 Preconditions:
...                 - Patient has contacted clinic because of a symptom
...                 - Nurse has requested a follow-up period from patient
...                 - Contact clinic feature must be enabled in clinic settings.

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p06    patient-web


*** Test Cases ***
Main success scenario (web) - Symptoms have ended
    [Documentation]    NOONA-18234 Text field is missing when Follow-up instruction is selected
    [Tags]    nms9-ver-123    native-web
    [Setup]    Add An Activated Patient Under Default Clinic    f01p06
    Setup App Environment
    Patient Can Follow-up Symptom Changes During a Follow-up Period    Symptoms Have Ended
    Setup App Environment
    Patient Can Follow-up Symptom Changes During a Follow-up Period    Contact Clinic
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Patient Can Follow-up Symptom Changes During a Follow-up Period
    [Arguments]    ${option}
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Ask About Other Symptom
    Close All App Instances
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        Open Patient Cases Tab
        Select Symptom And Contact Patient With    Follow-up instruction    message_template=none
        Sleep    2s
        Close Browser
    ELSE
        Generate Clinic Token
        ...    ${automated_tests_clinic}[default_user]
        ...    ${DEFAULT_PASSWORD}
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
        Get First Patient Case via API    ${patient_id}
        Send Message Connected To First Case Via API    ${first_case_id}    12
        Open Follow Up To First Case Via API    ${first_case_id}    2
    END
    Setup App Environment
    Login As Patient    ${patient_email}
    Follow Up Component Is Displayed In The Timeline
    Go To Follow Up
    IF    '${option}'=='Symptoms Have Ended'
        Select Symptoms Have Ended
    ELSE IF    '${option}'=='Contact Clinic'
        Select Contact Clinic    modify
    END
    Generic: Wait Until Element Is Not Visible    ${noona-loader}
    Go To Diary
    Follow Up Component Is Not Displayed In The Timeline
    Close All App Instances
