*** Settings ***
Documentation       F01P01S09 Patient can edit symptom

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}modules.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}/resources/test_data/API_test_data.resource
Resource            ${EXECDIR}/resources/native_app/app_symptom_form.resource

Suite Setup         Set Libraries Order
Test Setup          Setup App Environment
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p01s09    patient-web    native-web


*** Variables ***
${next_button}              button-next
${save_button}              //*[@id="submit-button-top"]
${edit_save_button}         //*[contains(text(), "Save")]
${first_other_symptom}      (//div[contains(@class, "timeline-symptom-content")])[1]
${move_to_diary_button}     move-to-diary
${description_value}        //div[contains(text(), "Description")]/../div[contains(@class, "symptom-attribute-value")]


*** Test Cases ***
Main - Patient Can Edit Symptom - Mild To Moderate
    [Tags]    nms9-ver-108    native-web
    Add An Activated Patient Under Default Clinic    default
    Login As Patient    ${patient_email}
    Add A Mild Symptom As Patient
    Edit Symptom To Be Moderate
    Wait Until Page Contains    Changes saved
    Verify If Symptom Is Edited Correctly
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Add A Mild Symptom As Patient
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    Set Test Variable    ${first_description}    This is a mild symptom ${now}
    Patient Adds Symptom From Diary    ${OTHER_SYMPTOM}
    Input Symptom Description    ${first_description}
    Select Symptomatic Day    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Mild
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Sleep    1
    Click Symptom Form Next Button
    Wait Until Element Is Visible    xpath=(${symptom_form_summary_area})[last()]
    Save Symptom Report
    Sleep    1
    Try To Click Element    ${modal_view_your_diary}

Edit Symptom To Be Moderate
    Select Latest Symptom Diary Entry    ${OTHER_SYMPTOM}
    Wait Until Page Contains    ${first_description}
    Try To Click Element    xpath=//*[@id='${edit_symptom_button}']//button
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    Set Test Variable    ${new_description}    Symptom is now moderate ${now}
    Input Symptom Description    ${new_description}
    Click Symptom Form Next Button
    Save Symptom Report

Verify If Symptom Is Edited Correctly
    Select Latest Symptom Diary Entry    ${OTHER_SYMPTOM}
    Wait Until Page Contains    ${new_description}
    Try To Click Element    ${close_button}
    Wait Until Page Does Not Contain Element    ${close_button}
