*** Settings ***
Documentation       F01P02 Patient can view reported symptoms
...
...                 Preconditions:
...                 - Patient has reported symptoms
...
...                 Test:
...                 - Checks that patient can view reported symptom via diary timeline

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p02    accessibility-wcag-a    patient-web


*** Test Cases ***
Main - Patient can view reported symptoms
    [Documentation]    Also includes F10P02 Patient can view a questionnaire answer - Main success scenario
    [Tags]    nms9-ver-116    nms9-ver-268    native-web
    [Setup]    Set Application On Environment
    # setup
    Add An Activated Patient Under Default Clinic
    ...    f01p02-1
    ...    module=${CHEMO_18_SYMPTOMS}
    # symptom diary
    Login As Patient    ${patient_email}
    Click Add Menu Button
    Add Symptom Entry
    Add Difficulty Eating Symptom To Diary
    # nms9-ver-268
    Select Latest Symptom Diary Entry    ${DIFFICULTY_EATING}
    Symptom Diary Modal Is Displayed    ${DIFFICULTY_EATING}    symptom_diary
    Close Symptom Diary Modal
    # ask about symptom
    Complete Nausea or Vommitting Symptom Form
    Select Latest Symptom Diary Entry    ${NAUSEA_OR_VOMITING}
    Symptom Diary Modal Is Displayed    ${NAUSEA_OR_VOMITING}    symptom_report
    Close Symptom Diary Modal
    Close All App Instances
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    # symptom questionnaire
    Add An Activated Patient Under Default Clinic
    ...    f01p02-2
    ...    module=${CHEMO_18_SYMPTOMS}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Set Application On Environment
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Sleep    1
    Select Latest Clinic Message
    Select Answer Questionnaire
    Complete Stomach and bowel symptoms Symptom Form
    Select Latest Symptom Diary Entry    ${STOMACH_AND_BOWEL_SYMPTOMS}
    Symptom Diary Modal Is Displayed    ${STOMACH_AND_BOWEL_SYMPTOMS}    symptom_report
    Close Symptom Diary Modal
    Close All App Instances
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Complete Nausea or Vommitting Symptom Form
    Ask About Symptoms    ${NAUSEA_OR_VOMITING}
    Select Answer To Question    Have you had...?    Nausea
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question
    ...    How would you rate the intensity of your nausea?
    ...    Mild: no effect on my eating habits
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Click Next Button
    Send Symptom Questionnaire To Clinic
    Go To Patient Homepage
