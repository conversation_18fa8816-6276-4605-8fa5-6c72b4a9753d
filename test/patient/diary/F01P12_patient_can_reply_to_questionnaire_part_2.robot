*** Settings ***
Documentation       F01P12 Patient can reply to treatment symptom questionnaire
...                 Test:
...                 - Adds a new scheduled questionnaire for a random patient
...                 - Patient completes and checks the summary (text and number inputs) of questionnaire
...                 - Saves and checks the status of questionnaire
...                 - Checks saved summary (text and number inputs)
...                 - TODO: Add more checks to summary

Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}timeline.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}appointment.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p12    patient-web


*** Variables ***
${email}                                                <EMAIL>
${password}                                             fdmpgcggxqnnlmhx    # Zy!n57Bmu?.(*L7
${new_account_email}                                    <EMAIL>
${new_account_password}                                 tjktfqtvjuksolxt    # VFN77qRAv'9ah,$
${multiple_symptoms_dialog_instructions}
...                                                     Your symptom description indicates that you might require immediate attention from a medical professional.
...                                                     Please refer to further instructions in your inbox.
${questionnaire_specific_instructions}
...                                                     Please select if you have experienced any of the following treatment related symptoms since
${status_check_yes_radio_button}                        //*[@id="statusCheckForm-any-sympton-true"]/../label
${status_check_no_radio_button}                         //*[@id="statusCheckForm-any-sympton-false"]/../label
${status_check_medication_checkbox}                     //*[@id="option0"]/../label
${status_check_medication_field}                        //*[@id='medication-question']
${status_check_appointment_checkbox}                    //*[@id="option1"]/../label
${status_check_appointment_field}                       //*[@id='appointment-question']
${status_check_insurance_checkbox}                      //*[@id="option2"]/../label
${status_check_insurance_field}                         //*[@id='insure-or-financ-question']
${status_check_other_checkbox}                          //*[@id="other"]/../label
${status_check_other_input}                             //*[@id="otherTopics"]
${status_check_contact_phone_radio_button}              //*[@id="statusCheckForm-contact-methods-phoneCall"]/../label
${status_check_contact_noona_message_radio_button}
...                                                     //*[@id="statusCheckForm-contact-methods-noonaMessage"]/../label
${status_check_contact_yes}                             //*[@id='statusCheckForm-any-contact-true']/../label
${status_check_contact_time_input}                      //*[@id="contact-time"]
${status_check_inquiry_summary}                         //*[@id="inquiry-summary"]
${patient_message_about_symptom}                        //div[@role='button'][last()]
${activate_your_noona_account_button}                   (//div[@class='activation-btn-container'])[1]
${activate_account_next_button}                         activate-next-button
${activation_complete_next_button}                      activation-complete-next-button
${other_symptom_yes}                                    //input[@id="other-symptom-yes-selection"]
${status_check_1st_ques}                                //*[contains(text(),"How are you feeling today?")]


*** Test Cases ***
Extension E - Questionnaire type specific instructions (web / app) - Treatment Visit
    [Tags]    nms9-ver-131-1    nms9-ver-131    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f10p12-131-1    module=${BONE_RADIOTHERAPY}
    Send Symptom Questionnaire Via Api To Patient    ${treatment_visit_quest}
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Wait Until Page Contains    ${questionnaire_specific_instructions}
    # TODO: Verif if date in the instructions is correct. Should be when the last symtom is reported
    Enter Symptom Information
    Input Additional Questions For Next Visit    Additional question for next treatment visit
    Send Symptom Questionnaire To Clinic
    Click View Your Diary
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        Navigate To Patient Cases Tab
        Select First Open Case From List
        Wait Until Page Contains    Additional question for next treatment visit
        Close Browser
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E - Questionnaire type specific instructions (web / app) - Clinic Appointment
    [Tags]    nms9-ver-131-2    nms9-ver-131    native-web
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f10p12-131-2    module=${BONE_RADIOTHERAPY}
    Send Symptom Questionnaire Via Api To Patient    ${clinic_appointment_quest}
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Wait Until Page Contains    ${questionnaire_specific_instructions}
    # TODO: Verif if date in the instructions is correct. Should be when the last symtom is reported
    Enter Symptom Information
    Input Additional Questions For Next Visit    Additional question for next clinic appointment
    Send Symptom Questionnaire To Clinic
    Click View Your Diary
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        Navigate To Patient Cases Tab
        Select First Open Case From List
        Wait Until Page Contains    Additional question for next clinic appointment
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension F - Next of kin has entered symptom information - Answering a questionnaire - part 1 (web / app)
    [Documentation]    Verify next of kin information texts in (AEQ/Qol): sent questionnaire on Diary, under the summary section before sending from Patient UI.
    [Tags]    nms9-ver-132-1    nms9-ver-132    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f01p12-extf-1    module=${BONE_RADIOTHERAPY}
    Patient's Next Of Kin Answers Questionnaires    aeq
    Information Entered By Caregiver Text Is Displayed On Patient App
    Patient's Next Of Kin Answers Questionnaires    qol
    Information Entered By Caregiver Text Is Displayed On Patient App
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension F - Next of kin has entered symptom information - Answering a questionnaire - part 2 (web)
    [Documentation]    Verify next of kin information texts in (AEQ/Qol): patient report case/ symptom in Patient case view and Timeline (Clinic UI)
    [Tags]    nms9-ver-132-2    nms9-ver-132
    Add An Activated Patient Under Default Clinic    f01p12-extf-2    module=${BONE_RADIOTHERAPY}
    Set Test Variable    ${patient_email}
    Patient's Next Of Kin Answers Questionnaires    qol
    Information Entered By Caregiver Is Displayed In Clinic Side    ${QUALITY_OF_LIFE_QUESTIONNAIRE_CLINIC_LABEL}
    Information Entered By Caregiver Is Displayed In Clinic Timeline    ${QUALITY_OF_LIFE_QUESTIONNAIRE_CLINIC_LABEL}
    Patient's Next Of Kin Answers Questionnaires    aeq
    Information Entered By Caregiver Is Displayed In Clinic Side    ${BASELINE_QUESTIONNAIRE}
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension G - Reporting symptoms from diary to AEQ (web / app)
    [Tags]    nms9-ver-133    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f01p12-extg    module=${BONE_RADIOTHERAPY}
    Report Symptom From Diary
    Close All App Instances
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Setup App Environment
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Wait Until Page Contains
    ...    The symptoms you have marked in your diary are preselected. Please select any additional symptoms you may have experienced.
    Symptom Is Selected    ${other_symptom_yes}
    Element Should Be Disabled    ${other_symptom_yes}
    Try To Click Element    ${aeq_questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    # TODO: If patient has multiple same severity level entries, then the latest symptom is displayed
    Wait Until Page Contains
    ...    Here is a summary of the entries you have made for this symptom in your diary. Please check that this information is up-to-date or enter a new symptom, if necessary.
    Wait Until Page Contains Element    ${previous_diary_symptom_summary}
    Evaluate Previous Symptom    previous_symptom=up_to_date
    Click Next Button
    Wait Until Page Contains Element    ${questionnaires_summary_content}
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    Thank you for your answers!
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension H - Status check questionnaire type (web / app) - No Symptoms
    [Tags]    nms9-ver-134-1    nms9-ver-134    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f01p12-exth    module=${BONE_RADIOTHERAPY}
    Send Symptom Questionnaire Via Api To Patient    ${status_check_quest}
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire
    Answer Status Check Questionnaire    with_symptoms=no
    Check Status Check Summary
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    The questionnaire has been sent to the clinic
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension H - Status check questionnaire type (web / app) - With Symptoms
    [Tags]    nms9-ver-134-2    nms9-ver-134    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f01p12-exth    module=${BONE_RADIOTHERAPY}
    Send Symptom Questionnaire Via Api To Patient    ${status_check_quest}
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire
    Answer Status Check Questionnaire    with_symptoms=yes
    Check Status Check Summary
    Check Status Check Summary With Other Symptom
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    The questionnaire has been sent to the clinic
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Patient's Next Of Kin Answers Questionnaires
    [Arguments]    ${questionnaire_type}
    Set Test Variable    ${questionnaire_type}
    IF    '${questionnaire_type}'=='aeq'
        Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    ELSE
        Send QOL 15D Questionnaire Via API To Patient For Today
    END
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire
    IF    '${questionnaire_type}'=='aeq'
        Enter Symptom Informatiom By Caregiver
    ELSE IF    '${questionnaire_type}'=='qol'
        Enter QOL Information By Caregiver
    END

Information Entered By Caregiver Text Is Displayed On Patient App
    Click View Your Diary
    Information Entered By Caregiver Is Displayed In Diary    ${questionnaire_type}

Information Entered By Caregiver Is Displayed In Diary
    [Arguments]    ${questionnaire_type}
    Select Latest Questionnaire In Diary
    Wait Until Page Contains    Information entered by a caregiver
    Scroll Element Into View    ${close_button}
    Click Element    ${close_button}
    IF    '${questionnaire_type}'=='aeq'
        Information Entered By Caregiver Is Displayed In Symptom Diary
    END
    IF    'native' in '${ENVIRONMENT}'
        Close All App Instances
        Setup App Environment
    ELSE
        Close Browser
    END

Information Entered By Caregiver Is Displayed In Symptom Diary
    Select Latest Symptom Diary Entry    Weight
    Wait Until Page Contains    Information entered by a caregiver
    Click Element    ${close_button}
    Select Latest Symptom Diary Entry    Changes in general state of...
    Wait Until Page Contains    Information entered by a caregiver
    Click Element    ${close_button}
    Select Latest Symptom Diary Entry    Fatigue and weakness
    Wait Until Page Contains    Information entered by a caregiver

Information Entered By Caregiver Is Displayed In Clinic Side
    [Documentation]    The information regarding AEQ/ QoL/ Symptom reported by a caregiver will be displayed in
    ...    1. Patient case view
    ...    2. Timeline's view, under each expanded symptom or patient report
    [Arguments]    ${case_type}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Wait Until Noona Loader Is Not Visible
    Navigate To Patient Cases Tab
    Select A Case From List By Name    ${case_type}
    Try To Click Banner Message
    Wait Until Page Contains    Information entered by a caregiver

Information Entered By Caregiver Is Displayed In Clinic Timeline
    [Arguments]    ${symptom_or_questionnaire}
    Navigate To Timeline Tab
    Wait Until Noona Loader Is Not Visible
    #TODO: Check that each reported symptom via Baseline questionnaire which has next of kin information is displayed on Timeline's summary card
    IF    '${questionnaire_type}'=='qol'
        Expand A Symptom Or Questionnaire From Timeline Summary Section By Name    ${symptom_or_questionnaire}
        Scroll Element Into View    ${timeline_summmary_section_qol_card}
        Wait Until Element Is Visible    ${timeline_summmary_section_qol_card}
        Scroll Element Into View    ${timeline_summmary_section_next_of_kin_info_text}
        Wait Until Element Is Visible    ${timeline_summmary_section_next_of_kin_info_text}
    END
    Close Browser

Enter Symptom Information
    Select Yes For First Symptom
    Try To Click Element    ${aeq_questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    Evaluate Previous Symptom    previous_symptom=new_entry
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your fatigue or weakness?
    ...    Mild: is relieved by resting
    Select Answer To Question    Do you feel you need to rest more often than normally?    No
    Try To Click Element    ${aeq_questionnaire_next_button}

Enter Symptom Informatiom By Caregiver
    Enter Symptom Information
    Information Entered By Caregiver Checkbox Is Visible
    Tick Info Entered By Caregiver Checkbox    ${questionnaire_type}
    Send Symptom Questionnaire To Clinic

Enter QOL Information By Caregiver
    Complete QOL Questionnaire
    Click Next Button
    Information Entered By Caregiver Checkbox Is Visible
    Tick Info Entered By Caregiver Checkbox
    questionnaires.Save Questionnaire    Quality of life (15D)

Answer Symptom Questionnaire With Symptomatic Days As Patient
    [Arguments]    ${email}    ${multiple}=no
    Login As Patient    ${email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    IF    '${multiple}'=='yes'
        Select Yes For Symptoms    ${FEVER}    ${OTHER_SYMPTOM}
    ELSE
        Select Yes For Symptoms    ${OTHER_SYMPTOM}
    END
    Changes In Gen State Of Health Is Displayed
    IF    '${multiple}'=='yes'    Answer Additional Symptom
    ${status}    Run Keyword And Return Status
    ...    Element Should Be Visible
    ...    (${create_new_symptom_entry_option})[last()]
    IF    ${status}
        Try To Click Element    (${create_new_symptom_entry_option})[last()]
    END
    questionnaires.Check And Write To Text Area
    Select Specific Answer To Question    How would you rate the severity of your symptom?    Severe
    ...    questionnaire=tnonc distress
    Select Specific Answer To Question    Have you used any medication to alleviate your symptoms?    No
    ...    questionnaire=tnonc distress
    Select Mark Symptomatic Days Based On Symptom    ${OTHER_SYMPTOM}
    Mark Symptomatic Days For Symptom Questionnaires    ${OTHER_SYMPTOM}
    Click Element    (${yesterday})[2]
    Click Element    (${aeq_questionnaire_next_button})[last()]
    Send Symptom Questionnaire To Clinic

Answer Additional Symptom
    ${status}    Run Keyword And Return Status    Element Should Be Visible    ${create_new_symptom_entry_option}
    IF    ${status}    Evaluate Previous Symptom    previous_symptom=new_entry
    Select Specific Answer To Question    How high was your temperature?
    ...    Over 104.0 °F / 40 °C for more than a day    questionnaire=tnonc distress
    Select Specific Answer To Question    Have you used any fever-reducing medication?    No
    ...    questionnaire=tnonc distress
    Select Answer To Question    When did you have this symptom?    Mark symptomatic days
    Mark Symptomatic Days For Symptom Questionnaires    Fever
    Click Element    (${yesterday})[2]
    Click Element    (${aeq_questionnaire_next_button})[last()]

Answer Status Check Questionnaire
    [Arguments]    ${with_symptoms}
    Wait Until Page Contains    How are you feeling today?
    Scroll Element Into View    ${status_check_1st_ques}
    IF    'native' in '${ENVIRONMENT}'
        IF  '${PLATFORM_NAME}'=='android'
            Set Test Variable    ${rating}    7
            Rate With Vertical Slider    ${rating}
        ELSE    #this is a temporary fix and can be improved soon
            Set Test Variable    ${ios_target_element}    //XCUIElementTypeStaticText[@label='How are you feeling today?']
            Set Test Variable    ${rating}    10
            Swipe Slider For Native    ${rating}
        END
    ELSE
        Set Test Variable    ${rating}    7
        Rate With Vertical Slider    ${rating}
    END
    Text Should Be In The Page    Are you having any symptoms?
    Text Should Be In The Page    Do you have any other issues you want to discuss?
    Text Should Be In The Page    Medication questions and/or refill requests
    Text Should Be In The Page    Appointment questions
    Text Should Be In The Page    Insurance/financial questions
    Text Should Be In The Page    Other, please specify.
    IF    '${with_symptoms}'=='yes'
        Try To Click Element    ${status_check_yes_radio_button}
    ELSE
        Try To Click Element    ${status_check_no_radio_button}
    END
    Try To Click Element    ${status_check_medication_checkbox}
    Wait Until Page Contains    Enter your medication question or medication refill request below
    Wait Until Page Contains Element    ${status_check_medication_field}
    Input Text    ${status_check_medication_field}    Medication refill text input
    Try To Click Element    ${status_check_appointment_checkbox}
    Wait Until Page Contains    Enter your appointment question below
    Wait Until Page Contains Element    ${status_check_appointment_field}
    Input Text    ${status_check_appointment_field}    Appointment question text input
    Try To Click Element    ${status_check_insurance_checkbox}
    Wait Until Page Contains    Enter your insurance/financial question below
    Wait Until Page Contains Element    ${status_check_insurance_field}
    Input Text    ${status_check_insurance_field}    insurance/financial question text input
    Try To Click Element    ${status_check_other_checkbox}
    Wait Until Page Contains Element    ${status_check_other_input}
    ${other_text}    Generate Random String    20
    Set Test Variable    ${other_text}
    Try To Input Text    ${status_check_other_input}    ${other_text}
    Wait Until Page Contains    Do you want us to contact you regarding today's questionnaire?
    Try To Click Element    ${status_check_contact_yes}
    Text Should Be In The Page    Phone Call
    Text Should Be In The Page    Noona message
    Try To Click Element    ${status_check_contact_phone_radio_button}
    Wait Until Page Contains    What time would you like us to call you?
    ${contact_time}    Generate Random String    20
    Set Test Variable    ${contact_time}
    Try To Input Text    ${status_check_contact_time_input}    ${contact_time}
    Try To Click Element    ${aeq_questionnaire_next_button}
    IF    '${with_symptoms}'=='yes'
        Add Other Symptom
        Click Element    xpath=(${aeq_questionnaire_next_button})[last()]
    END

Check Status Check Summary
    Wait Until Page Contains    How are you feeling today?
    Generic: Element Should Contain    ${status_check_inquiry_summary}    ${rating} / 10
    Generic: Element Should Contain    ${status_check_inquiry_summary}    Are you having any symptoms?
    Generic: Element Should Contain    ${status_check_inquiry_summary}    No
    Generic: Element Should Contain
    ...    ${status_check_inquiry_summary}
    ...    Do you have any other issues you want to discuss?
    Generic: Element Should Contain    ${status_check_inquiry_summary}    Medication questions and/or refill requests
    Generic: Element Should Contain    ${status_check_inquiry_summary}    Appointment questions
    Generic: Element Should Contain    ${status_check_inquiry_summary}    Insurance/financial questions
    Generic: Element Should Contain    ${status_check_inquiry_summary}    ${other_text}
    Generic: Element Should Contain    ${status_check_inquiry_summary}    How do you want us to contact you?
    Generic: Element Should Contain    ${status_check_inquiry_summary}    Phone Call
    Generic: Element Should Contain    ${status_check_inquiry_summary}    What time would you like us to call you?
    Generic: Element Should Contain    ${status_check_inquiry_summary}    ${other_text}
    Generic: Element Should Contain    ${status_check_inquiry_summary}    ${contact_time}

Check Status Check Summary With Other Symptom
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Element Contains    xpath=(${questionnaires_summary_content})[last()]    ${random_desc}
        Generic: Element Should Contain    xpath=(${questionnaires_summary_content})[last()]    Severity
        Generic: Element Should Contain    xpath=(${questionnaires_summary_content})[last()]    Mild
        Generic: Element Should Contain    xpath=(${questionnaires_summary_content})[last()]    ${random_desc}
        Generic: Element Should Contain    xpath=(${questionnaires_summary_content})[last()]    Other symptom
    END

Add Other Symptom
    Select Yes For Symptoms    Other symptom
    Click Element    xpath=(${aeq_questionnaire_next_button})[last()]
    ${random_desc}    Generate Random String    160    [LOWER][UPPER][LETTERS][NUMBERS]
    Set Test Variable    ${random_desc}
    Input Text    ${other_symptom_description_field}    ${random_desc}
    Select Answer To Question    How would you rate the severity of your symptom?    Mild

Report Symptom From Diary
    Login As Patient    ${patient_email}
    Patient Adds Symptom From Diary    ${OTHER_SYMPTOM}
    Complete Other Symptom Form    Mild
    Sleep    1
    Click Next Button
    Send Symptom To Clinic
    Wait Until Page Contains    Symptom entry saved in your diary
