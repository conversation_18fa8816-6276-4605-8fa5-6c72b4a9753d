*** Settings ***
Documentation       F01P12 Patient can reply to treatment symptom questionnaire
...                 Test:
...                 - Adds a new scheduled questionnaire for a random patient
...                 - Patient completes and checks the summary (text and number inputs) of questionnaire
...                 - Saves and checks the status of questionnaire
...                 - Checks saved summary (text and number inputs)
...                 - TODO: Add more checks to summary

Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}compare_questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p12    patient-web


*** Variables ***
${multiple_symptoms_dialog_instructions}
...                                                     Your symptom description indicates that you might require immediate attention from a medical professional.
...                                                     Please refer to further instructions in your inbox.
${status_check_yes_radio_button}                        //*[@id="statusCheckForm-any-sympton-true"]/../label
${status_check_no_radio_button}                         //*[@id="statusCheckForm-any-sympton-false"]/../label
${status_check_medication_checkbox}                     //*[@id="option0"]/../label
${status_check_appointment_checkbox}                    //*[@id="option1"]/../label
${status_check_insurance_checkbox}                      //*[@id="option2"]/../label
${status_check_other_checkbox}                          //*[@id="other"]/../label
${status_check_other_input}                             otherTopics
${status_check_contact_phone_radio_button}              //*[@id="statusCheckForm-contact-methods-phoneCall"]/../label
${status_check_contact_time_input}                      contact-time
${status_check_inquiry_summary}                         //*[@id="inquiry-summary"]
${patient_message_about_symptom}                        //div[@role='button'][last()]
${severe_symptom_message}                               You described a severe symptom. Is the symptom as severe or more severe right now?
${emergency_symptom_message}                            The symptom you just described indicates that you might require immediate attention from a medical professional.
${seek_help_message}                                    If you do not believe that you can manage until the following working day or if you feel otherwise weaker, seek help at your local emergency clinic.
${patient_reported_symptom_msg}                         The patient reported the symptom as part of the symptom questionnaire.


*** Test Cases ***
Main - Patient Can Reply To Symptom Questionnaire
    [Documentation]    Also includes [nms9-ver-107] F01P01C02 Patient can report wellbeing information - Extension B
    ...    - Wellbeing information is asked in every symptom report (web / app)
    [Tags]    nms9-ver-107    nms9-ver-128    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f10n02    module=${BONE_RADIOTHERAPY}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire
    Select Yes For First Symptom
    Click Element    ${aeq_questionnaire_next_button}
    # nms9-ver-107
    Changes In Gen State Of Health Is Displayed
    Enter Symptom Information
    Symptom Questionnaire Summary Contains Text    ${CHANGES_IN_GENERAL_STATE_OF_HEALTH}
    Symptom Questionnaire Summary Contains Text
    ...    I feel well, my performance is as good as it was before my illness or start of treatment.
    Send Symptom Questionnaire To Clinic
    Questionnaire Has Been Sent Modal Is Displayed
    Click View Your Diary
    Diary Latest Entry Section Contain Symptom    Changes in general state of...
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - Emergency or semi-emergency symptom (web / app)
    [Tags]    nms9-ver-129-1    nms9-ver-129    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic
    ...    f01p12-exta1
    ...    module=${BONE_RADIOTHERAPY}
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01P12}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Answer Symptom Questionnaire With Symptomatic Days As Patient
    ...    ${patient_email}
    Wait Until Page Contains    ${OTHER_SYMPTOM}
    Wait Until Page Contains    ${severe_symptom_message}    timeout=6s
    # TODO: If multiple symptoms are emergency or semi-emergency level, dialog is displayed for each symptom
    Try To Click Element    ${same_severity_button}
    Emergency Priority Symptom Is Displayed
    # TODO: If patient has multiple emergency / semi-emergency symptoms, confirmation dialog for each symptom is displayed first and if patient answers yes to one or multiple dialogs, in the end the most severe level rule alert dialog is displayed
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Location Contains    ${PATIENT_URL}${PATIENT_PATH}#/clinic
    END
    Try To Click Element    ${clinic_messages}\[1]
    Wait Until Page Contains    ${emergency_symptom_message}
    Text Should Be In The Page    ${seek_help_message}
    Text Should Be In The Page    ${OTHER_SYMPTOM}
    Try To Click Element    ${patient_message_about_symptom}
    Text Should Be In The Page    ${patient_reported_symptom_msg}
    Close Browser
    IF    'native' not in '${ENVIRONMENT}'
        Verify If Patient With Symptom Is In Nurse's Work Queue
        ...    ${first_name}${SPACE}${family_name}
        ...    f01p12 care team
        Select Patient Card    ${first_name}${SPACE}${family_name}
        Wait Until Page Contains    ${emergency_symptom_message}
        Text Should Be In The Page    ${seek_help_message}
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - Select No To Emergency Symptom
    [Tags]    nms9-ver-129-2    nms9-ver-129    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic
    ...    f01p12-exta2
    ...    module=${BONE_RADIOTHERAPY}
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01P12}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Answer Symptom Questionnaire With Symptomatic Days As Patient
    ...    ${patient_email}
    Wait Until Page Contains    ${OTHER_SYMPTOM}
    Wait Until Page Contains    ${severe_symptom_message}
    # TODO: If multiple symptoms are emergency or semi-emergency level, dialog is displayed for each symptom
    Try To Click Element    ${severity_decreased_button}
    Click View Your Diary
    # TODO: If patient has multiple emergency / semi-emergency symptoms, confirmation dialog for each symptom is displayed first and if patient answers yes to one or multiple dialogs, in the end the most severe level rule alert dialog is displayed
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Location Contains    ${PATIENT_PATH}#/diary-timeline
        Close Browser
        Verify If Patient With Symptom Is In Nurse's Work Queue
        ...    ${first_name}${SPACE}${family_name}
        ...    f01p12 care team
        ...    displayed=no
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - Select Yes To Multiple Symptoms
    [Tags]    nms9-ver-129-3    nms9-ver-129    native-web    pendo
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic
    ...    f01p12-exta3
    ...    module=${IMMUNO_ONCO_PHARMACOTHERAPY}
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01P12}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Answer Symptom Questionnaire With Symptomatic Days As Patient
    ...    ${patient_email}
    ...    multiple=yes
    Wait Until Page Contains    ${severe_symptom_message}
    Text Should Be In The Page    ${FEVER}
    Try To Click Element    ${same_severity_button}
    Wait Until Page Contains    ${OTHER_SYMPTOM}
    Text Should Be In The Page    ${severe_symptom_message}
    Try To Click Element    ${same_severity_button}
    Sleep    1
    Wait Until Page Contains    ${multiple_symptoms_dialog_instructions}
    Click Emergency Symptom Ok Button
    IF    'native' not in '${ENVIRONMENT}'
        Wait Until Location Contains    ${PATIENT_PATH}#/clinic
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension C - Symptom report request expiration (web / app)
    [Documentation]    Symptom questionnaire is set to expire 2 days after sending.
    [Tags]    manual    native-app-todo    nms9-ver-9    time-constraint
    # use date to identify when patient is created
    ${patient_identifier}    Get Current Date
    ...    result_format=%d-%m-%Y
    Set Test Variable    @{mailosaur_keys}    ${time_constraint_keys}[0]    ${time_constraint_keys}[1]
    Add An Activated Patient Under Time Constraint Clinic
    ...    ${patient_identifier}-extc
    ...    ${TIME_CONSTRAINT_CLINIC_SUB_ID_F01P12}
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Nurse    ${time_constraint_testing}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Add Questionnaire To Schedule    ${BASELINE_QUESTIONNAIRE}
    Patient Received Invitation To Answer AEQ    ${patient_email}    ${time_constraint_testing}[name]
    # TODO: testing instructions:
    # 1. Log in as nurse (<EMAIL>)
    # 2. Go to List of patients
    # 3. Select f01p12 care team and
    # 4. Select a patient with expired questionnaire. Take note of patient's email.
    # 5. Log in as patient > Continue with usecase steps
    # 6. Go to mailosaur with server: Single Patient Time Constraints Email >    Continue with usecase steps

Extension D - Patient navigates to an AEQ through a direct link (web)
    [Tags]    nms9-ver-130-1    nms9-ver-130    pendo    patient-email-link
    ${new_email}    Create New Patient With Mailosaur Email
    Login As Nurse    ${spmc_consent_a_clinic}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Add Questionnaire To Schedule    Baseline questionnaire
    @{message_data}    Patient Received Invitation To Answer AEQ    ${patient_email}    ${spmc_consent_a_clinic}[name]
    Sleep    2s    # wait for some seconds after email is sent
    Set Test Variable    ${link}    ${message_data}[0]
    Close Browser
    Open URL In Chrome    ${link}
    Accept All Cookies If Visible
    Answer Symptom Form From Clicking Link
    Wait Until Page Contains    The questionnaire has been sent to the clinic
    Try To Click Element    ${questionnaire_complete_login_button}
    # Location Should Be    ${PATIENT_LOGIN_URL}    #only until v8, uncomment after
    Close Browser
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Login As Nurse    ${spmc_consent_a_clinic}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Add Questionnaire To Schedule    Baseline questionnaire
    @{message_data}    Patient Received Invitation To Answer AEQ    ${patient_email}    ${spmc_consent_a_clinic}[name]
    Set Test Variable    ${link}    ${message_data}[0]
    Close Browser
    Open URL In Chrome    ${link}
    Wait Until Element Is Enabled    ${landing_page_login_button}
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${spmc_consent_a_clinic}[manager_email]
    ...    ${SPMC_CONSENT_A_CLINIC_ID}
    ...    ${SPMC_CONSENT_A_EHR_TOKEN}

Extension D - Patient navigates to an AEQ through a direct link (web) - unactivated account
    [Tags]    nms9-ver-130-2    nms9-ver-130    patient-email-link
    ${new_email}    Invite An Unactivated New Patient With Mailosaur Email
    Login As Nurse    ${spmc_consent_a_clinic}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Send User Account    edit_contact=no
    Try To Click Banner Message
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Add Questionnaire To Schedule    Baseline questionnaire
    @{message_data}    Patient Received Invitation To Answer AEQ    ${patient_email}    ${spmc_consent_a_clinic}[name]
    Sleep    2s    # wait for some seconds after email is sent
    Set Test Variable    ${link}    ${message_data}[0]
    Activate Noona Account With AEQ    ${link}    ${patient_email}
    Select Yes For Symptoms    Other symptom
    Changes In Gen State Of Health Is Displayed
    Select Answer To Question    When did you have this symptom?    Today
    questionnaires.Check And Select Radio Buttons
    questionnaires.Check And Write To Text Area
    questionnaires.Check And Write To Number Field
    questionnaires.Check And Tick Checkboxes
    Click Element    ${aeq_questionnaire_next_button}
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    Thank you for your answers!
    [Teardown]    Close Browser

Extension D - Patient navigates to an AEQ through a direct link (app)
    [Tags]    manual
    Direct links are enabled in clinic settings and patient is logged in the native app and the patient has set PIN code
    Patient receives an AEQ request from clinic as a native notification
    Patient opens notification
    Patient enters PIN
    Patient is directed to AEQ form
    Patient fills in the form and send it
    Confirmation page about sending AEQ is displayed
    Patient is directed to Diary


*** Keywords ***
Information Entered By Caregiver Is Displayed In Diary
    [Arguments]    ${questionnaire_type}
    Select Latest Questionnaire In Diary
    Wait Until Page Contains    Information entered by a caregiver
    Scroll Element Into View    ${close_button}
    Click Element    ${close_button}
    IF    '${questionnaire_type}'=='aeq'
        Information Entered By Caregiver Is Displayed In Symptom Diary
    END
    Close Browser

Information Entered By Caregiver Is Displayed In Symptom Diary
    Select Latest Symptom Diary Entry    Weight
    Wait Until Page Contains    Information entered by a caregiver
    Click Element    ${close_button}
    Select Latest Symptom Diary Entry    Changes in general state of...
    Wait Until Page Contains    Information entered by a caregiver
    Click Element    ${close_button}
    Select Latest Symptom Diary Entry    Fatigue and weakness
    Wait Until Page Contains    Information entered by a caregiver

Information Entered By Caregiver Is Displayed In Clinic Side
    [Arguments]    ${patient_ssn}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Patient Cases Tab
    Select First Open Case From List
    Wait Until Page Contains    Information entered by a caregiver

Information Entered By Caregiver Displays Correctly
    [Arguments]    ${questionnaire_type}    ${ssn}    ${patient_email}
    Search Patient By Identity Code    ${ssn}
    ${questionnaire}    Set Variable If    '${questionnaire_type}'=='aeq'    Baseline questionnaire
    ...    Quality of life (15D)
    Add Questionnaire To Schedule    ${questionnaire}
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire
    IF    '${questionnaire_type}'=='aeq'
        Enter Symptom Informatiom By Caregiver
    ELSE IF    '${questionnaire_type}'=='qol'
        Enter QOL Information By Caregiver
    END
    Click View Your Diary
    Information Entered By Caregiver Is Displayed In Diary    ${questionnaire_type}
    Information Entered By Caregiver Is Displayed In Clinic Side    ${ssn}

Enter Symptom Information
    Evaluate Previous Symptom    previous_symptom=new_entry
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your fatigue or weakness?
    ...    Mild: is relieved by resting
    Select Answer To Question    Do you feel you need to rest more often than normally?    No
    Click Element    ${aeq_questionnaire_next_button}

Enter Symptom Informatiom By Caregiver
    Select Yes For First Symptom
    Click Element    ${aeq_questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    Enter Symptom Information
    Information Entered By Caregiver Checkbox Is Visible
    Tick Info Entered By Caregiver Checkbox
    Send Symptom Questionnaire To Clinic

Enter QOL Information By Caregiver
    Complete QOL Questionnaire
    Click Next Button
    Information Entered By Caregiver Checkbox Is Visible
    Tick Info Entered By Caregiver Checkbox
    questionnaires.Save Questionnaire    Quality of life (15D)

Answer Symptom Questionnaire With Symptomatic Days As Patient
    [Arguments]    ${email}    ${multiple}=no
    Login As Patient    ${email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    IF    '${multiple}'=='yes'
        Select Yes For Symptoms    Fever    Other symptom
    ELSE
        Select Yes For Symptoms    Other symptom
    END
    Changes In Gen State Of Health Is Displayed
    IF    '${multiple}'=='yes'    Answer Additional Symptom
    ${status}    Run Keyword And Return Status
    ...    Element Should Be Visible
    ...    xpath=(${create_new_symptom_entry_option})[last()]
    IF    ${status}
        Try To Click Element    xpath=(${create_new_symptom_entry_option})[last()]
    END
    questionnaires.Check And Write To Text Area
    Select Answer To Question    How would you rate the severity of your symptom?    Severe
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Select Mark Symptomatic Days Based On Symptom    Other symptom
    Mark Symptomatic Days For Symptom Questionnaires    Other symptom
    Try To Click Element    xpath=(${yesterday})[2]
    Try To Click Element    xpath=(${aeq_questionnaire_next_button})[last()]
    Send Symptom Questionnaire To Clinic

Answer Additional Symptom
    ${status}    Run Keyword And Return Status    Element Should Be Visible    xpath=${create_new_symptom_entry_option}
    IF    ${status}    Evaluate Previous Symptom    previous_symptom=new_entry
    Select Answer To Question    How high was your temperature?
    ...    Over 104.0 °F / 40 °C for more than a day
    Select Answer To Question    Have you used any fever-reducing medication?    No
    Select Answer To Question    When did you have this symptom?    Mark symptomatic days
    Mark Symptomatic Days For Symptom Questionnaires    Fever
    Try To Click Element    xpath=(${yesterday})[2]
    Try To Click Element    xpath=(${aeq_questionnaire_next_button})[last()]

Answer Status Check Questionnaire Without Symptoms
    Wait Until Page Contains    How are you feeling today?
    Page Should Contain    Are you having any symptoms?
    Page Should Contain    Do you have any other issues you want to discuss?
    Page Should Contain    Medication questions and/or refill requests
    Page Should Contain    Appointment questions
    Page Should Contain    Insurance/financial questions
    Page Should Contain    Other, please specify.
    Rate With Vertical Slider    7
    Try To Click Element    ${status_check_no_radio_button}
    Try To Click Element    ${status_check_medication_checkbox}
    Try To Click Element    ${status_check_appointment_checkbox}
    Try To Click Element    ${status_check_insurance_checkbox}
    Try To Click Element    ${status_check_other_checkbox}
    Wait Until Page Contains Element    ${status_check_other_input}
    ${other_text}    Generate Random String    20
    Set Test Variable    ${other_text}
    Try To Input Text    ${status_check_other_input}    ${other_text}
    Wait Until Page Contains    How do you want us to contact you?
    Page Should Contain    Phone Call
    Page Should Contain    Noona message
    Try To Click Element    ${status_check_contact_phone_radio_button}
    Wait Until Page Contains    What time would you like us to call you?
    ${contact_time}    Generate Random String    20
    Set Test Variable    ${contact_time}
    Try To Input Text    ${status_check_contact_time_input}    ${contact_time}
    Try To Click Element    ${aeq_questionnaire_next_button}

Answer Status Check Questionnaire With Other Symptom
    Wait Until Page Contains    How are you feeling today?
    Page Should Contain    Are you having any symptoms?
    Page Should Contain    Do you have any other issues you want to discuss?
    Page Should Contain    Medication questions and/or refill requests
    Page Should Contain    Appointment questions
    Page Should Contain    Insurance/financial questions
    Page Should Contain    Other, please specify.
    Rate With Vertical Slider    7
    Try To Click Element    ${status_check_yes_radio_button}
    Try To Click Element    ${status_check_medication_checkbox}
    Try To Click Element    ${status_check_appointment_checkbox}
    Try To Click Element    ${status_check_insurance_checkbox}
    Try To Click Element    ${status_check_other_checkbox}
    Wait Until Page Contains Element    ${status_check_other_input}
    ${other_text}    Generate Random String    20
    Set Test Variable    ${other_text}
    Try To Input Text    ${status_check_other_input}    ${other_text}
    Wait Until Page Contains    How do you want us to contact you?
    Page Should Contain    Phone Call
    Page Should Contain    Noona message
    Try To Click Element    ${status_check_contact_phone_radio_button}
    Wait Until Page Contains    What time would you like us to call you?
    ${contact_time}    Generate Random String    20
    Set Test Variable    ${contact_time}
    Try To Input Text    ${status_check_contact_time_input}    ${contact_time}
    Try To Click Element    ${aeq_questionnaire_next_button}
    Add Other Symptom
    Click Element    (${aeq_questionnaire_next_button})[last()]

Check Status Check Summary
    Wait Until Page Contains    How are you feeling today?
    Element Should Contain    ${status_check_inquiry_summary}    7 / 10
    Element Should Contain    ${status_check_inquiry_summary}    Are you having any symptoms?
    Element Should Contain    ${status_check_inquiry_summary}    No
    Element Should Contain    ${status_check_inquiry_summary}    Do you have any other issues you want to discuss?
    Element Should Contain    ${status_check_inquiry_summary}    Medication questions and/or refill requests
    Element Should Contain    ${status_check_inquiry_summary}    Appointment questions
    Element Should Contain    ${status_check_inquiry_summary}    Insurance/financial questions
    Element Should Contain    ${status_check_inquiry_summary}    ${other_text}
    Element Should Contain    ${status_check_inquiry_summary}    How do you want us to contact you?
    Element Should Contain    ${status_check_inquiry_summary}    Phone Call
    Element Should Contain    ${status_check_inquiry_summary}    What time would you like us to call you?
    Element Should Contain    ${status_check_inquiry_summary}    ${other_text}    ${contact_time}

Check Status Check Summary With Other Symptom
    Wait Until Element Contains    (${questionnaires_summary_content})[last()]    ${random_desc}
    Element Should Contain    (${questionnaires_summary_content})[last()]    Severity
    Element Should Contain    (${questionnaires_summary_content})[last()]    Mild
    Element Should Contain    (${questionnaires_summary_content})[last()]    ${random_desc}
    Element Should Contain    (${questionnaires_summary_content})[last()]    Other symptom

Add Other Symptom
    Select Yes For Last Symptom
    Click Element    (${aeq_questionnaire_next_button})[last()]
    Evaluate Previous Symptom    previous_symptom=new_entry
    Click Element    ${aeq_questionnaire_next_button}
    ${random_desc}    Generate Random String    160    [LOWER][UPPER][LETTERS][NUMBERS]
    Set Test Variable    ${random_desc}
    Input Text    ${other_symptom_description_field}    ${random_desc}
    Try To Click Element    ${other_symptom_grading_mild}

Report Symptom From Diary
    Login As Patient    <EMAIL>
    Patient Adds Symptom From Diary    Other symptom
    Complete Other Symptom Questionnaire
    Click Next Button
    Send Symptom To Clinic

Update Patient Email And SSN
    Try To Input Text    ${identity_code_field}    ${patient_dict}[social security number]
    Try To Input Text    ${email_field}    ${patient_dict}[email]
    Try To Click Element    save-patient
    Wait Until Page Contains    Patient updated
    Set Test Variable    ${patient_dict}
    Try To Click Element    ${back_to_button}

Create New Patient With Mailosaur Email
    [Arguments]    ${activate_patient}=yes
    Set Test Variable    @{mailosaur_keys}    nysd6yzv    PxNab77NmnHVleuf
    ${now}    Get Current Date    result_format=-%H%M%S
    Generate Random Patient Data    mailosaur=${mailosaur_keys}[0]    name=f01p12${now}
    Generate Clinic Token
    ...    ${spmc_consent_a_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${SPMC_CONSENT_A_CLINIC_ID}
    Add Patient To New Clinic Via API    ${SPMC_CONSENT_A_SUB_ID_CARETEAM_1}
    IF    '${activate_patient}'=='yes'    Send Change User Password Request
    Log    ${patient_email}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

Verify That Text On Landing Page Is Correct
    Wait Until Element Is Enabled    ${landing_page_login_button}
    Wait Until Page Contains    Connect to your clinic using Noona
    Try To Click Element    ${landing_page_login_button}
    Wait Until Page Contains    Please log in to include your diary entries in the questionnaire.

Invite An Unactivated New Patient With Mailosaur Email
    Set Test Variable    @{mailosaur_keys}    nysd6yzv    PxNab77NmnHVleuf
    ${now}    Get Current Date    result_format=-%H%M%S
    Generate Random Patient Data    nysd6yzv
    Invite Patient Via API
    ...    ${SPMC_CONSENT_A_EHR_TOKEN}
    ...    ${spmc_consent_a_clinic}[tunit]
    Log    ${patient_email}
