*** Settings ***
Documentation       F07P16 Cookie Setting Use Case

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}cookies.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07p16    patient-web


*** Test Cases ***
Main Success Scenario (web) - Select Necessary Or All Cookies
    [Tags]    nms9-ver-460-1    nms9-ver-460
    Open Url    ${PATIENT_URL}    en
    Check That Cookie Settings Are Displayed
    Select Necessary Cookies
    Verify Necessary Cookies Selection In Browser Session
    Verify Necessary Cookies Selection In Cookie Policy From Landing Page
    Close Browser
    Open Url    ${PATIENT_URL}    en
    Select All Cookies
    Verify All Cookies Selection In Browser Session
    Verify All Cookies Selection In Cookie Policy From Landing Page
    [Teardown]    Close Browser

Main Success Scenario (web) - Change Cookies After Login
    [Tags]    nms9-ver-460-2    nms9-ver-460
    Login As Patient    ${f07p16_cookie_setting}[email]
    Select Cookie Policy From More Menu
    Select Necessary Cookies
    Verify Necessary Cookies Selection In Browser Session
    Verify Necessary Cookies Selection In Cookie Policy From More Menu
    Select All Cookies
    Verify All Cookies Selection In Browser Session
    Verify All Cookies Selection In Cookie Policy From More Menu
    [Teardown]    Close Browser

Main Success Scenario (web) - Verify Cookie Policy Translations From Cookie Setting Modal
    [Tags]    nms9-ver-460-3    nms9-ver-460
    Set Locale List
    FOR    ${locale}    IN    @{locales_list}
        Open Url    ${PATIENT_URL}    ${locale}
        Verify Cookie Settings Modal Content    ${locale}
        Verify Cookie Policy Content From Cookie Settings Modal    ${locale}
        Close Browser
        Sleep    1s
    END

Main Success Scenario (web) - Verify Cookie Policy Translations From Landing Page
    [Tags]    nms9-ver-460-4    nms9-ver-460
    Set Locale List
    FOR    ${locale}    IN    @{locales_list}
        Open Url    ${PATIENT_URL}    ${locale}
        Verify Cookie Policy Content From Landing Page    ${locale}
        Close Browser
    END

Main Success Scenario (web) - Verify Cookie Policy Translations From More Menu
    [Tags]    nms9-ver-460-5    nms9-ver-460
    Set Language Dictionary
    Login As Patient    ${f07p16_cookie_setting}[email]
    FOR    ${key}    IN    @{language_dict.keys()}
        Log    ${language_dict['${key}']}
        Switch Language In Clinic Preferences For Cookie Selection    ${language_dict['${key}']}
        Try To Click More Button
        Try To Click Element    ${cookie_policy_button_more_menu}
        Verify Cookie Policy Content From More Menu For Web    ${key}
        Close Cookie Policy Modal
    END
    [Teardown]    Close All App Instances

Extension A - Cookie Policy (app) - Verify Translations For Cookie Policy From More Menu
    [Tags]    nms9-ver-461    native-app
    [Setup]    Setup App Environment
    Set Language Dictionary
    Login As Patient    ${f07p16_cookie_setting}[email]
    FOR    ${key}    IN    @{language_dict.keys()}
        Log    ${language_dict['${key}']}
        Switch Language In Clinic Preferences For Cookie Selection    ${language_dict['${key}']}
        Try To Click More Button
        Try To Click Element    ${cookie_policy_button_more_menu}
        Verify Cookie Policy Content From More Menu For Native    ${key}
        Close Cookie Policy Modal
    END
    [Teardown]    Close All App Instances
