*** Settings ***
Documentation       F07P06 Patient can accept terms of use

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}single_patient_multi_clinic.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07p06    patient-web


*** Variables ***
${close_modal_button}       //*[@id="close-modal"]/button


*** Test Cases ***
Patient can accept terms of use
    [Documentation]    NOONA-22641: Fix to handle only the clicking of next button by adding re-ticking of checkbox after clicking Next.
    ...    Please see ticket's comments for details.
    [Tags]    nms9-ver-228    native-web
    [Setup]    Setup App Environment
    Create New Patient And Login For The First TIme
    Check Terms Of Use
    Approve Terms And Click Next
    Approve Consents And Click Next
    Wait Until Page Contains    Diary
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${spmc_consent_a_clinic}[manager_email]
    ...    ${SPMC_CONSENT_A_CLINIC_ID}
    ...    ${SPMC_CONSENT_A_EHR_TOKEN}

Extension A - Terms of use is updated
    [Tags]    nms9-ver-229    native-app-todo
    Login As Patient    ${f07p06_exta}[email]
    Approve Terms And Consent Only If Visible
    Close Browser
    Modify Terms Of Use And Save    ${spmc_consent_a_clinic}[name]
    Close Browser
    Login As Patient    ${f07p06_exta}[email]
    New Terms Of Use Are Shown To Returning Patient
    Approve Terms And Click Next
    Wait Until Page Contains    Diary
    Close Browser
    Returning Patient Can Login Without Terms Of Use    ${f07p06_exta}[email]
    [Teardown]    Close All App Instances

Extension B - Terms of use is updated while patient is logged in
    [Tags]    nms9-ver-230    native-app-todo
    Login As Patient    ${f07p06_extb}[email]
    Approve Terms And Consent Only If Visible
    Get Patient Browser ID
    Modify Terms Of Use And Save    ${spmc_consent_a_clinic}[name]
    Sleep    1
    Switch To Patient Browser And Load Page
    New Terms Of Use Are Shown To Returning Patient
    Approve Terms And Click Next
    Wait Until Page Contains    Diary
    Returning Patient Can Login Without Terms Of Use    ${f07p06_extb}[email]
    [Teardown]    Close All App Instances

Extension C - Latest Terms of use of the clinic are not accepted while Patient is switching to the clinic
    [Documentation]    NOONA-22641: Fix to handle only the clicking of next button by adding re-ticking of checkbox after clicking Next.
    ...    Please see ticket's comments for details. Note that only Android is updated. Note that only Android is updated.
    [Tags]    nms9-ver-20    native-web
    [Setup]    Setup App Environment
    Generate Random Patient Data    f07p06-c
    Create Activated/Activated Patient On Multi-Clinic Via API
    ...    ${SPMC_CONSENT_A_EHR_TOKEN}
    ...    ${spmc_consent_a_clinic}[tunit]
    ...    ${spmc_consent_a_clinic}[manager_email]
    ...    ${spmc_consent_b_clinic}[manager_email]
    ...    ${SPMC_CONSENT_A_CLINIC_ID}
    ...    ${SPMC_CONSENT_B_CLINIC_ID}
    ...    ${SPMC_CONSENT_B_SUB_ID_CARETEAM_1}
    Login As Patient    ${patient_email}
    Approve Terms And Click Next
    Approve Consents And Click Next
    Wait Until Page Contains    Diary
    Sleep    1
    Switch Clinic From Clinic Header    ${spmc_consent_b_clinic}[name]
    Approve Terms And Click Next    add_tick_checkbox=no
    Approve Consents And Click Next
    Wait Until Page Contains    Diary
    [Teardown]    Close All App Instances


*** Keywords ***
Create New Patient And Login For The First TIme
    Generate Random Patient Data    name=f07p06
    Create An Activated Patient Via API
    ...    ${spmc_consent_a_clinic}[manager_email]
    ...    ${SPMC_CONSENT_A_CLINIC_ID}
    ...    ${SPMC_CONSENT_A_SUB_ID_CARETEAM_1}
    Login As Patient    ${patient_email}

Check Terms Of Use
    Wait Until Page Contains Element    ${patient_consent}
    Try To Click Element    ${terms_of_use_button}
    Try To Click Element    ${close_modal_button}
    Wait Until Page Contains Element    ${privacy_notice_button}
    Try To Click Element    ${privacy_notice_button}
    Wait Until Page Contains    ${privacy_text_header}
    Scroll Element Into View    ${close_modal_button}
    Try To Click Element    ${close_modal_button}

Returning Patient Can Login Without Terms Of Use
    [Arguments]    ${patient_email}
    Login As Patient    email=${patient_email}
    Wait Until Page Contains    Diary

New Terms Of Use Are Shown To Returning Patient
    Wait Until Page Contains Element    ${terms_of_use_button}
    Wait Until Element Is Visible    ${terms_of_use_button}
    Try To Click Element    ${terms_of_use_button}
    Wait Until Page Contains    ${terms}
    Try To Click Element    ${close_modal_button}
    Sleep    1    #needed to make sure the modal is fully closed before opening a new one
    Wait Until Element Is Visible    ${privacy_notice_button}
    Try To Click Element    ${privacy_notice_button}
    Wait Until Page Contains    ${privacy_text_header}
    Try To Click Element    ${close_modal_button}

Get Patient Browser ID
    ${patient_browser_id}    Get Browser Ids
    ${patient_browser_id}    Get From List    ${patient_browser_id}    0
    Set Test Variable    ${patient_browser_id}

Switch To Patient Browser And Load Page
    Switch Browser    ${patient_browser_id}
    Try To Click Element    ${library_button}
