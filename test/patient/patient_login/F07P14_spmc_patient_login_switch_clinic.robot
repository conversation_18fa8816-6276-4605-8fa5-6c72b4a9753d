*** Settings ***
Documentation       F07P14 Patient can go to another clinic they have access to
...                 - Main success scenario: from clinic name in the header
...                 - Extension A - From More menu
...
...                 Precondition:
...                 1. Create single patient from different clinics (clinic A & clinic B)
...
...                 Steps:
...
...                 1. Changed patient password by the clinic user
...
...                 2. <PERSON><PERSON> as Patient
...
...                 3. Switch Clinics
...                 - a. from MORE menu : Go to another clinic
...                 - b. from clinic name in the header : Go to another clinic

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}single_patient_multi_clinic.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}/resources/patient/patient_screens.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          spmc    spmc-switch-clinics    patient-web    usecase-f07p14


*** Test Cases ***
Patient can go to another clinic they have access to - Clinic switchers / name in header status
    [Documentation]    nms9-ver-239-app is the equivalent of this tc on native
    ...    TODO: consolidate both existing tcs into hybrid on this tag
    [Tags]    nms9-ver-239
    Login As Patient And Verify Clinic Header Is Not Displayed    ${f07p14_patient_1_active}[email]
    Login As Patient And Verify Clinic Header Is Not Displayed    ${f07p14_patient_delegate}[email]    delegate=yes
    Login As Patient And Verify Clinic Header Is Displayed    ${f07p14_active_active}[email]
    Login As Patient And Verify Clinic Header Is Not Displayed    ${f07p14_active_disabled}[email]
    Login As Patient And Verify Clinic Header Is Not Displayed    ${f07p14_active_locked}[email]
    Login As Patient And Verify Clinic Header Is Not Displayed    ${f07p14_active_proxy}[email]
    Login As Patient And Verify Clinic Header Is Not Displayed    ${f07p14_active_candidate}[email]
    Login As Patient And Verify Clinic Header Is Displayed    ${f07p14_active_invited}[email]
    Login As Nurse    clinic=${spmc_consent_b_clinic}[name]    user_type=${USER_TYPE}[noona_admin]
    Modify Consents And Save
    Close Browser
    Login As Patient And Verify Clinic Header Is Displayed
    ...    ${f07p14_active_unapproved}[email]
    ...    clinic_2=${spmc_consent_b_clinic}[name]
    ...    approve_consent_clinic_2=yes
    Login As Nurse    clinic=${spmc_consent_a_clinic}[name]    user_type=${USER_TYPE}[noona_admin]
    Modify Consents And Save
    Close Browser
    Login As Nurse    clinic=${spmc_consent_b_clinic}[name]    user_type=${USER_TYPE}[noona_admin]
    Modify Consents And Save
    Close Browser
    Login As Patient And Verify Clinic Header Is Displayed
    ...    ${f07p14_unapproved_unapproved}[email]
    ...    clinic_1=${spmc_consent_a_clinic}[name]
    ...    clinic_2=${spmc_consent_b_clinic}[name]
    ...    approve_consent_clinic_1=yes
    ...    approve_consent_clinic_2=yes
    Login As Patient And Verify Clinic Header Is Not Displayed    ${f07p14_active_declined}[email]

Login As Patient And Switch Clinics From Clinic Name Header
    [Documentation]    NOONA-19439 SPMC: Noona Does Not Take Patient To Last Visited Clinic Upon Patient Login
    ...    nms9-ver-239-app and nms9-ver-240-app covers this tc for native
    ...    TODO: consolidate both existing tcs into hybrid on this tag
    [Tags]    nms9-ver-238
    Create Single Patient On Multi-Clinic And Update Password
    Login As Patient    ${patient_email}
    Approve Terms And Click Next
    Approve Consents And Click Next
    # patient is logged in to clinic A and switching to clinic B
    Switch Clinic From Clinic Header    ${spmc_consent_b_clinic}[name]
    Approve Terms And Click Next
    Approve Consents And Click Next
    Verify Visited Clinic    ${spmc_consent_b_clinic}[name]
    # logout and verify last clinic visited
    Logout As Patient
    Login To Last Clinic    ${patient_email}
    # patient is logged in to clinic B
    Verify Visited Clinic    ${spmc_consent_b_clinic}[name]
    Switch Clinic From Clinic Header    ${spmc_consent_a_clinic}[name]

Login As Patient And Switch Clinics From More Menu
    [Documentation]    nms9-ver-240-app is the equivalent of this tc on native
    ...    TODO: consolidate both existing tcs into hybrid on this tag
    [Tags]    nms9-ver-240
    Create Single Patient On Multi-Clinic And Update Password
    Login As Patient    ${patient_email}
    Approve Terms And Click Next
    Approve Consents And Click Next
    # patient is logged in to clinic A and switching to clinic B
    ${clinic_id}    Set Variable If
    ...    'test' in '${ENVIRONMENT}'
    ...    ${spmc_consent_b_clinic}[id_test]
    ...    ${spmc_consent_b_clinic}[id_staging]
    Go To Another Clinic From More Menu    ${spmc_consent_b_clinic}[name]    ${clinic_id}
    Wait Until Page Does Not Contain    You are now in ${spmc_consent_b_clinic}[name]
    # logout and verify last clinic visited
    Logout As Patient
    Login To Last Clinic    ${patient_email}
    # patient is logged in to clinic B
    Verify Visited Clinic    ${spmc_consent_b_clinic}[name]


*** Keywords ***
Create Single Patient On Multi-Clinic And Update Password
    Set Test Variable    @{mailosaur_keys}    wulwnmw4    Y031pC6RHoMe8ABS0sTz5Zf9BlrPNWzs
    Create An Activated/Activated Patient On Multi-Clinic Via API With Mailosaur Email
    ...    ${SPMC_CONSENT_A_EHR_TOKEN}
    ...    ${spmc_consent_a_clinic}[tunit]
    ...    ${spmc_consent_a_clinic}[manager_email]
    ...    ${spmc_consent_b_clinic}[manager_email]
    ...    ${SPMC_CONSENT_A_CLINIC_ID}
    ...    ${SPMC_CONSENT_B_CLINIC_ID}
    ...    ${SPMC_CONSENT_B_SUB_ID_CARETEAM_1}

Login To Last Clinic
    [Arguments]    ${email}
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${email}

Login As Patient And Verify Clinic Header Is Displayed
    [Arguments]    ${patient_email}    ${clinic_1}=${spmc_clinic_a}[name]    ${clinic_2}=${spmc_clinic_b}[name]
    ...    ${approve_consent_clinic_1}=no    ${approve_consent_clinic_2}=no
    Login And Approve Terms    ${patient_email}
    IF    '${approve_consent_clinic_1}'=='yes'
        Approve Consents And Click Next
        Wait Until Page Contains    ${clinic_1}
    END
    Verify Visited Clinic    ${clinic_1}
    Switch Clinic From Clinic Header    ${clinic_2}
    Wait Until Page Contains    You are now in ${clinic_2}
    IF    '${approve_consent_clinic_2}'=='yes'
        Approve Consents And Click Next
        Wait Until Page Contains    ${clinic_2}
    END
    Switch Clinic From Clinic Header    ${clinic_1}
    Wait Until Page Contains    You are now in ${clinic_1}
    Logout As Patient
    Close Browser

Login As Patient And Verify Clinic Header Is Not Displayed
    [Arguments]    ${patient_email}    ${delegate}=no
    Login As Patient    ${patient_email}
    Clinic Header Is Not Visible    delegate=${delegate}
    Close Browser

Login And Approve Terms
    [Documentation]    Approving terms in this stage is not included in the usecase.
    ...    This is only to make sure terms is approved before the consent as some tcs also update terms in other scenarios.
    [Arguments]    ${patient_email}
    Login As Patient    ${patient_email}
    ${status}    Run Keyword And Return Status
    ...    Wait Until Page Contains
    ...    Terms of use and privacy statement
    ...    timeout=5s
    IF    ${status}    Approve Terms And Click Next
