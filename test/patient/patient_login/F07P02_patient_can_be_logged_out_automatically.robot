*** Settings ***
Documentation       F07P02 Patient can be logged out automatically
...                 Preconditions:
...                 - When the patient logged into Noona, he answered "No" to the question "Remember me"? (This question is only
...                 asked if the clinic setting "Patient Remember Password Life Time" is set to greater than 0.)
...                 - The clinic has password authentication
...                 - The patient is still logged in to Noona.
...                 - The patient has not used Noona for five minutes less than the period of time defined in the Clinic settings,
...                 "Basic settings", field "Patient Session Life Time".
...                 - App: Patient has not set a PIN code for the application.

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources/mailosaur.resource
Resource            ${EXECDIR}${/}resources/patient/patient_screens.resource
Resource            ${EXECDIR}${/}resources/patient/more/account_preferences.resource
Resource            ${EXECDIR}${/}resources/patient/patient_screens.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07p02    patient-web


*** Variables ***
${auto_logout}          Automatic logout
${login}                //*[@id="email-and-password-next-button"]
${diary}                //*[@id="navigation-diary-link"]
${logout}               //*[@id="autologout-logout"]
${continue_using}       //*[@id="autologout-close"]
${patient_email}        <EMAIL>


*** Test Cases ***
Patient can be logged out automatically (web / app)
    [Tags]    nms9-ver-223    native-app-todo    manual
    Five minutes before the end of the patient session lifetime, the Automatic logout notification dialog does appear
    The patient does not cancel the logout within the five minutes the dialog is displayed
    The patient is logged out and directed to the login page

Extension A - Patient can cancel auto log out and continue using Noona (web / app)
    [Tags]    nms9-ver-224    native-app-todo    manual
    Five minutes before the end of the patient session lifetime, the Automatic logout notification dialog does appear
    The patient clicks the "Continue using Noona" button in the dialog
    The automatic logout is canceled and the dialog is closed
    The page the patient is on remains the same it was when the automatic logout dialog was shown

Extension B - Patient can log out immediately by clicking on Logout button (web / app)
    [Tags]    nms9-ver-225    native-app-todo    manual
    Five minutes before the end of the patient session lifetime, the Automatic logout notification dialog does appear
    The patient clicks the "Logout" button in the dialog
    The patient is logged out and directed to the login page

Extension C - Auto logout dialogue does not appear if clinic has password authentication and Yes is selected for "Remember me" (web / app)
    [Tags]    nms9-ver-226    native-app-todo    manual
    User Selects Yes to the question "Remember me"
    Five minutes before the end of the patient session lifetime, the automatic logout notification dialog does not appear

Extension D.1 - Two factor authentication - Clinic 2fa Enabled (web / app)
    [Tags]    nms9-ver-227    patient-2fa    sms
    [Setup]    Extension D.1 - Test setup
    Activate Patient's Account With 2fa    ${activation_link}
    Enable Password Only Login Method
    Login As Patient With 2FA    ${patient_email}
    Patient Is In The Right Page    Diary
    Enable Password And Verification Code Login Method
    Login As Patient With 2FA    ${patient_email}
    Patient Is In The Right Page    Diary
    Logout As Patient
    Close Browser
    # cannot delete patient due to 2fa, needs to login as patient with 2fa via api first

Extension D.2 - Two factor authentication - Clinic 2fa Disabled (web / app)
    [Tags]    nms9-ver-405    patient-2fa    sms
    Login As Patient With 2FA    ${f07p02_2fa_patient}[email]
    Patient Selects Log Out From Menu
    Patient Is Logged Out
    User Can Login Again    ${f07p02_2fa_patient}[email]
    Wait Until Page Contains Element    ${clinic-icon}


*** Keywords ***
Five minutes before the end of the patient session lifetime, the Automatic logout notification dialog does appear
    Login As Patient    ${patient_email}    No
    Wait Until Page Contains    ${auto_logout}    timeout=70

The patient does not cancel the logout within the five minutes the dialog is displayed
    Sleep    300s

The patient is logged out and directed to the login page
    Wait Until Page Does Not Contain    ${auto_logout}
    Wait Until Page Contains Element    ${login}

The patient clicks the "Continue using Noona" button in the dialog
    Wait Until Page Contains    ${auto_logout}
    Click Element    ${continue_using}

The automatic logout is canceled and the dialog is closed
    Wait Until Page Does Not Contain    ${auto_logout}

The page the patient is on remains the same it was when the automatic logout dialog was shown
    Wait Until Page Does Not Contain    ${auto_logout}
    Wait Until Page Contains Element    ${diary}

The patient clicks the "Logout" button in the dialog
    Click Element    ${logout}
    Wait Until Page Does Not Contain    ${auto_logout}

User Selects Yes to the question "Remember me"
    Login As Patient    ${patient_email}    Yes

Five minutes before the end of the patient session lifetime, the automatic logout notification dialog does not appear
    Sleep    30s
    Wait Until Page Does Not Contain    ${auto_logout}

User Is Logged Out
    Wait Until Page Contains Element    ${email_textbox}

User Can Login Again
    [Arguments]    ${email}
    Delete All SMS From Mailosaur
    Login To Noona    patient    ${email}    open_browser=False
    Input Patient Verification Code

Activate Patient's Account With 2fa
    [Arguments]    ${link}
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Delete All SMS From Mailosaur
    Activate Noona Account As Patient    ${link}    ${patient_email}    with_2fa=yes

Extension D.1 - Test setup
    Set Test Variable    @{mailosaur_keys}    ${f07u02_f07p02_keys}[0]    ${f07u02_f07p02_keys}[1]
    Delete All SMS From Mailosaur
    Add A Patient Under 2FA Enabled Clinic To Activate
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${2fa_enabled_clinic}[name]
    Set Test Variable    ${activation_link}    ${message_data}[0]
    Set Test Variable    ${activation_email_id}    ${message_data}[1]
    Delete A Message In Server    ${mailosaur_keys}[1]    ${activation_email_id}
