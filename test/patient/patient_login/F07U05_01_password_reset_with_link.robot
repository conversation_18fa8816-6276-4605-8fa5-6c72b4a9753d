*** Settings ***
Documentation       F07U05_01 User can reset password with a received link

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}general_information.resource
Resource            ${EXECDIR}${/}resources/patient/more/more.resource
Resource            ${EXECDIR}${/}resources/patient/more/account_preferences.resource
Resource            ${EXECDIR}${/}resources/clinic_settings/clinic_settings.resource
Resource            ${EXECDIR}${/}resources/clinic_settings/basic_settings.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u05-01    patient-web


*** Test Cases ***
Main Success Scenario - User can reset password with a received link - Nurse has sent new login link
    [Tags]    nms9-ver-549-2    nms9-ver-549
    [Setup]    F07U05_01 Main Scenario Setup
    Send New Login Link To Patient Via API
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ${link}    Patient Received New Login Link Email    ${patient_email}    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    Open URL In Chrome    ${link}
    Accept All Cookies If Visible
    Create New Password To Activate Account    password=${new_pw}    #new_pw set inside the setup
    Login As Patient From OIDC Login Page    ${patient_email}    password=${new_pw}
    Patient Is In The Right Page    Diary
    Close Browser
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    patient_password=${new_pw}

Extension A - Patient is asked for 2 factor authentication when they have requested for a new logging link themselves - Single Clinic
    [Tags]    nms9-ver-550    sms
    F07U05_01 Extensions Setup
    Enable Password And Verification Code Login Method
    Configure Setting For Patient To Use 2fa When Requesting Link    No
    Delete All Messages In Server    ${f07u05_1_email_keys}[0]    ${f07u05_1_email_keys}[1]
    Send Password Reset Request Via API    ${patient_email}    ${AUTOMATED_TESTS_EHR_TOKEN}
    @{message_data}    Patient Received An Email About Reset Password
    ...    ${patient_email}
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    Patient Clicks Reset Password Link    ${message_data}[1]
    Patient Enters The Authentication Code From SMS
    Create New Password To Activate Account    password=${new_pw}
    Set Test Variable    ${DEFAULT_PASSWORD}    ${new_pw}
    Login As Patient With 2FA    ${patient_email}
    Patient Is In The Right Page    Diary
    Enable Password Only Login Method    with_password_2fa=yes
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${test_clinic_setting_5}[user_email]
    ...    ${TEST_CLINIC_SETTING_5_CLINIC_ID}
    ...    ${TEST_CLINIC_SETTING_5_EHR_TOKEN}
    ...    patient_password=${new_pw}

Extension B.1 - Patient has multiple clinics associated with them with one custom branded clinic (web / app)
    [Tags]    nms9-ver-551     patient-2fa
    Set Test Variable    @{mailosaur_keys}    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Delete All Messages In Server    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Request Password Reset And Check Email
    ...    ${f07uo5_extd_patient}[email]
    ...    ${custom_branding_enabled_clinic}[custom_clinic_name]
    Delete All SMS From Mailosaur
    Go To    ${link}
    Page Has Correct Logo    winter-city-publicdomain.svg
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    ${new_pw}    Set Variable    Pw-${now}
    Create New Password To Activate Account    password=${new_pw}    check_next_button_color=rgba(81, 106, 150, 1)
    Verify Custom Branding In Landing Page
    Login As Patient From OIDC Login Page    ${f07uo5_extd_patient}[email]    password=${new_pw}
    Patient Is In The Right Page    Diary
    Verify Custom Branding In Diary
    [Teardown]    Close All App Instances

Extension B.2 - Patient has multiple clinics associated with them (web / app) with 2FA
    [Tags]    nms9-ver-552     patient-2fa      sms
    Set Test Variable    @{mailosaur_keys}    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Delete All Messages In Server    ${f07u05_email_keys}[0]    ${f07u05_email_keys}[1]
    Request Password Reset And Check Email
    ...    ${f07uo5_extd_patient}[email]
    ...    ${custom_branding_enabled_clinic}[custom_clinic_name]
    Delete All SMS From Mailosaur
    Go To    ${link}
    Page Has Correct Logo    winter-city-publicdomain.svg
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    ${new_pw}    Set Variable    Pw-${now}
    Create New Password To Activate Account    password=${new_pw}    check_next_button_color=rgba(81, 106, 150, 1)
    Verify Custom Branding In Landing Page
    Login As Patient From OIDC Login Page    ${f07uo5_extd_patient}[email]    password=${new_pw}
    Patient Is In The Right Page    Diary
    Verify Custom Branding In Diary
    [Teardown]    Close All App Instances

#Extension C - Patient can reset password from a link the clinic has sent them (web/app) with 2FA

#Extension D - Delegate user can reset password with a link sent by the patient

*** Keywords ***
F07U05_01 Main Scenario Setup
    Set Email And Delete Previous Messages    ${f07u05_1_email_keys}[0]    ${f07u05_1_email_keys}[1]
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    Set Test Variable    ${new_pw}    F07u05_1-${now}
    Generate Random Patient Data    mailosaur=${f07u05_1_email_keys}[0]
    Patient Is Invited To Use Noona
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${automated_tests_clinic}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Activate Noona Account As Patient    ${message_data}[0]    ${patient_email}    terms_enabled=false
    Patient Received Successful Account Activation Email    ${patient_email}    ${automated_tests_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Sleep    60s    #needed wait for the activation to register completely

F07U05_01 Extensions Setup
    Set Email And Delete Previous Messages    ${f07u05_1_email_keys}[0]    ${f07u05_1_email_keys}[1]
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    Set Test Variable    ${new_pw}    F07u05_1-${now}
    Generate Random Patient Data    mailosaur=${f07u05_1_email_keys}[0]    phone_number=${mailosaur_number}
    Patient Is Invited To Use Noona
    ...    ${test_clinic_setting_5}[user_email]
    ...    ${TEST_CLINIC_SETTING_5_CLINIC_ID}
    ...    ${TEST_CLINIC_SETTING_5_SUB_ID_CARE_TEAM_1}
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${test_clinic_setting_5}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Activate Noona Account As Patient    ${message_data}[0]    ${patient_email}    terms_enabled=false
    IF    '${ENVIRONMENT}'=='test'
        Patient Received Successful Account Activation Email    ${patient_email}    ${test_clinic_setting_5}[name]
    ELSE
        Patient Received Welcome To Custom Clinic Email    ${patient_email}    ${test_clinic_setting_5}[name]
    END
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

Configure Setting For Patient To Use 2fa When Requesting Link
    [Arguments]    ${option}
    Login As Nurse    clinic=${test_clinic_setting_5}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings", Basic settings tab is selected by default
    IF    '${option}'=='yes'    #to make sure that save button is enabled
        Select Clinic Settings Radio Button    ${patients_use_2fa_when_logging_in_via_link}    No
        Select Clinic Settings Radio Button    ${patients_use_2fa_when_logging_in_via_link}    Yes
    ELSE
        Select Clinic Settings Radio Button    ${patients_use_2fa_when_logging_in_via_link}    Yes
        Select Clinic Settings Radio Button    ${patients_use_2fa_when_logging_in_via_link}    No
    END
    Save Settings
    Close Browser

Patient Clicks Reset Password Link
    [Arguments]    ${link}
    Delete All SMS From Mailosaur
    Open URL In Chrome    ${link}
    Accept All Cookies If Visible

Patient Enters The Authentication Code From SMS
    ${sms_code}    User Received SMS
    Wait Until Element Is Visible    ${password_input_container_oidc_false}
    Input 2fa Code During Activation    ${sms_code}

Request Password Reset And Check Email
    [Arguments]    ${email}    ${clinic}=Noona
    Open Patient Page
    Request Password Reset    ${email}
    @{message_data}    Patient Received An Email About Reset Password
    ...    ${email}
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    ...    clinic=${clinic}
    Set Test Variable    ${link}    ${message_data}[1]

Verify Custom Branding In Landing Page
    Wait Until Page Contains    Log in to ${custom_branding_enabled_clinic}[custom_clinic_name]
    Page Has Correct Logo    winter-city-publicdomain.svg
    Verify Css Property Color    ${landing_page_login_ds_button}    rgb(81, 106, 150)    background
    Verify Css Property Color    ${dont_have_account_link}    rgba(81, 106, 150, 1)    color
    Verify Css Property Color    ${privacy_statement_link}    rgba(81, 106, 150, 1)    color
    Verify Css Property Color    ${about_link_login_page}    rgba(81, 106, 150, 1)    color

Verify Custom Branding In Diary
    Verify Css Property Color    ${clinic_name_header_id}    rgb(81, 106, 150)    background-image
    Verify Css Property Color    ${diary_icon}    rgba(81, 106, 150, 1)    color
    Verify Css Property Color    ${update_symptoms_button_ds_button}    rgb(81, 106, 150)    background
    Verify Css Property Color    ${welcome_to_noona_ok_ds_button}    rgb(81, 106, 150)    background
    Verify Css Property Color    ${add_menu_button}    rgba(81, 106, 150, 1)    color