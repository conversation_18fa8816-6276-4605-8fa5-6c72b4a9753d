*** Settings ***
Documentation       F07U01 Patient can login

Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}list_of_patients_tab.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}account_preferences.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u01-patient    accessibility-wcag-a    patient-web    login    patient-login


*** Variables ***
${invalid_code}                         777666
${patient_2FA_status_incorrect_text}    //div[contains(text(),'Incorrect verification code.')]
${library_heading}                      //*[@id='header-page-title'][contains(text(),'Library')]
${closed_logo}                          Noona is closed


*** Test Cases ***
Main success scenario - Patient Can Login With 2FA
    [Tags]    nms9-ver-241-1    nms9-ver-241    patient-2fa    native-web    sms
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Login As Patient With 2FA    ${f07u01_patient_sms1}[email]
        Wait Until Page Contains Element    ${clinic-icon}
    ELSE
        Login To Native App With 2FA In Browserstack
        ...    email=${f07u02app_patient_extD}[email]
        ...    autho=yes
    END
    [Teardown]    Close All App Instances

Main success scenario - OIDC = True - Opening A Direct URL
    [Tags]    nms9-ver-241-2    nms9-ver-241
    User Can Login From The OIDC Login Page By Opening A Direct URL
    [Teardown]    Close All Browsers

Main success scenario - OIDC = True - Usecases Links
    [Tags]    nms9-ver-241-3    nms9-ver-241
    [Setup]    User Can Log In: OIDC = True - Test Setup
    User Can Login From The OIDC Login Page Via An Activation Invitation
    User Can Login From OIDC Login Page By Completing A Syptom Questionnaire
    User Can Login From The OIDC Login After Completing Password Reset
    [Teardown]    Run Keywords    User Can Log In: OIDC = True - Test Teardown    AND    Close Browser

Extension A.1.i — Expired password/login link Patient (web / app)
    [Tags]    nms9-ver-486    time-constraint    manual
    Repeat Keyword    3 times    Create Inactivated Patient Under Time Constraints Clinic
    # TODO: testing instructions nms9-ver-486:
    # Use first link for the expired password (patient hasn't set their password within two weeks since receiving the login link)
    # Patient clicks on the email link - check that expired page is displayed and verify email is sent with new instructions to login
    # Use the link in these instructions and activate account
    # Use second link for the expired password (patient hasn't set their password within two weeks since receiving the login link)
    # Patient clicks on the email link - check that expired page is displayed and verify email is sent with new instructions to login
    # From the Noona login page, go via problems logging in flow and activate account from these instructions
    # Use third link for the expired password (patient hasn't set their password within two weeks since receiving the login link)
    # Patient clicks on the email link - check that expired page is displayed and verify email is sent with new instructions to login
    # Login as clinic user to send them a new login link
    # When a new link is received the patient can follow the link to update/reset their password and log into Noona

Extension A.1.ii — Expired password/login link Delegate user(web / app)
    [Documentation]    NOONA-24121: Delegate: accessing link to expired invitation does not display the expired page
    [Tags]    nms9-ver-485    time-constraint    manual
    Repeat Keyword    2 times    Create Inactive Delegate Patient Under Time Constraints Clinic
    # TODO: testing instructions nms9-ver-485:
    # The delegate’s password has expired (delegate user hasn't set their password within two weeks since receiving the account activation link)
    # Delegate user clicks on the account activation link
    # Delegate user is directed to a page telling them that the login link has expired
    # To get a new login link the delegate user has to be removed and added again to the account by the patient
    # When a new link is received the delegate user can follow the link to update/reset their password and log into Noona

Extension A.2.i — Locked password Patient (web / app) - unlocked by nurse
    [Tags]    nms9-ver-242
    [Setup]    Set Email And Delete Previous Messages    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Add An Activated Patient Under Default Clinic    f07u01_a2i    mailosaur=${mailosaur_keys}[0]
    Patient Can Login
    Patient Selects Log Out From Menu
    Patient Is Logged Out
    Check If Email Notification About Permanently Locked Account Is Sent
    Close Browser
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Choose General Information Tab
    Status Is Locked And Unlock The Patient
    Close Browser
    Patient Can Login
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A.2.i — Locked password Patient (web / app) - Reset step
    [Tags]    nms9-ver-488    native-app-todo
    [Setup]    Set Email And Delete Previous Messages    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Add An Activated Patient Under Default Clinic    f07u01_a2i    mailosaur=${mailosaur_keys}[0]
    Patient Can Login
    Patient Selects Log Out From Menu
    Patient Is Logged Out
    Delete All Messages In Server    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Check If Email Notification About Permanently Locked Account Is Sent
    User Can Login From The OIDC Login After Completing Password Reset
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A.2.ii — Locked password Patient - automatic unlocking (web / app)- unlocked by nurse
    [Tags]    nms9-ver-487-1    nms9-ver-487    native-app-todo
    [Setup]    Set Email And Delete Previous Messages    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Add An Activated Patient Under Appointment Clinic    f07u01_a2ii    mailosaur=${mailosaur_keys}[0]
    Patient Can Login
    Patient Selects Log Out From Menu
    Patient Is Logged Out
    Close Browser
    Check If Email Notification About Temporary Locked Account Is Sent
    Login As Nurse    ${appointment_clinic}[user_email]    clinic=${appointment_clinic}[name]
    Search Patient By Identity Code    ${patient_ssn}
    Choose General Information Tab
    Status Is Locked And Unlock The Patient
    Close Browser
    Patient Can Login
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${appointment_clinic}[manager_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_USER_TOKEN}

Extension A.2.ii — Locked password Patient - automatic unlocking (web / app) - password reset requested
    [Tags]    nms9-ver-487-2    nms9-ver-487    native-app-todo
    [Setup]    Set Email And Delete Previous Messages    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Add An Activated Patient Under Appointment Clinic    f07u01_a2ii    mailosaur=${mailosaur_keys}[0]
    Patient Can Login
    Patient Selects Log Out From Menu
    Patient Is Logged Out
    Close Browser
    Check If Email Notification About Temporary Locked Account Is Sent
    User Can Login From The OIDC Login After Completing Password Reset
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${appointment_clinic}[manager_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_USER_TOKEN}

Extension A.2.iii — Locked password Delegate user(web / app)
    [Tags]    nms9-ver-489    native-app-todo
    Set Test Variable    @{mailosaur_keys}    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Add An Activated Patient Under Default Clinic    usecase=f07u01_a2iii    mailosaur=${mailosaur_keys}[0]
    Add Active Delegate User For Patient Under Default Clinic    ${patient_email}    @{f07u01_email_keys}
    Verify Delegate User Can Login    ${delegate_email}
    Logout As Delegate User
    Delete All Messages In Server    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Check That Delegate User Cannot Login After 6 Wrong Password Attempts    ${delegate_email}
    Close Browser
    Email Notification Is Not Received    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]    ${delegate_email}
    Email Notification Is Not Received    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]    ${patient_email}
    Login As Patient    ${patient_email}
    Go To Clinic Preferences
    Reset Delegate User Password    ${delegate_email}
    Delegate User Opens Password Reset Link Via Email    ${delegate_email}
    Delegate Has A Newly Reset Password And Is Able To Login    ${delegate_email}
    [Teardown]    Extension A.2.iii - Test Teardown

Extension C - Patient logs into native application (app)
    [Documentation]    Testing done includes:
    ...    - nms9-VER-352 F07P09 Patient can allow native application notifications
    ...    - nms9-VER-358 F07P15 Patient native application is updated automatically
    [Tags]    nms9-ver-354-app    nms9-ver-352-app    nms9-ver-358-app    native-app
    [Setup]    Setup Native App In Browserstack
    Login To Native App In Browserstack    ${f07p10_c_patient}[email]

Extension D.1 - Patient unlocks native application with Device PIN code/Passcode (app)
    [Documentation]    Device's PIN code should be enabled in AVD via mobile settings
    [Tags]    nms9-ver-356    manual
    # TODO: To be automated
#    Precondition:
#    Patient has installed native application and is logged in.
#    Patient returns to native application after more than 30 seconds of inactivity.
#    Patient enters the device PIN code/Passcode.
#    If patient selects Have you forgotten your PIN code?, patient has to log into native application. See extension C.
#    Noona specific PIN code is no longer used after release 8.4. Biometric authentication or device PIN code will be used instead.
#    Application is unlocked.
#    Patient enters Noona to the page where they where when closed the app.
#    Depending on the device options, patient presses Cancel in the log in flow
#    if cancel is pressed 3 times in a row, patient is directed to the landing page
#    Patient presses log in and is requested to re-enter the password
#    Patient presses log in
#    Patient acknowledges native application notifications
#    Patient enters noona diary

Extension D.2 - Patient is asked for permission to use Biometric authentication when they unlock the native application for the 1st time after logging to the app (app)
    [Tags]    nms9-ver-411    manual
    # TODO: To be automated
#    Patient has installed native application and is logged in.
#    Patient returns to native application after more than 30 seconds of inactivity or after swiping the application up
#    Device supports biometric authentication
#    Patient resumes the application
#    If patient is using an iOS device
#    Patient is asked to allow the use of Biometrics
#    Patient says YES
#    Patient is identified with what the device supports (FaceID or fingerprint)
#    Patient says NO
#    Patient is identified with Device PIN code/Passcode
#    If patient is using an Android device
#    Patient is asked to authenticate with the biometrics that are available on the device or to use a PIN
#    Application is unlocked.
#    Patient enters Noona to the page where they where when closed the app if they had not swiped the app up, if they swiped up they will be directed to Noona diary.

Extension D.3 - Patient unlocks native application with Biometric authentication (app)
    [Tags]    nms9-ver-412    manual
    # TODO: To be automated
#    Patient has installed native application and is logged in.
#    Patient returns to native application after more than 30 seconds of inactivity.
#    Device supports biometric authentication, patient is identified with what the device supports (FaceID or fingerprint)
#    Application is unlocked.
#    Patient enters Noona to the page where they where when closed the app.

Extension D.4 - Patient unlocks native application with device PIN code/Passcode or Biometrics after swiping the app up (app)
    [Tags]    nms9-ver-413    manual
    # TODO: To be automated
#    Precondition:
#    Patient has installed native application and is logged in.
#    Patient has swiped the application up
#    Patient returns to native application
#    Patients device does not support biometrics or patient has not taken it into use.
#    Patient enters the device PIN code/Passcode.
#    Patients device supports biometrics and is taken into use.
#    Patient uses Biometrics to unlock the device, based on what the device supports (face or fingerprint).
#    Application is unlocked.
#    Patient enters Noona diary.

Extension E - Several failed two-factor authentication attempts - Patient Is Logged In
    [Tags]    nms9-ver-245-1    nms9-ver-245    native-web
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f07u01-exte
    Login As Patient    ${patient_email}
    Go To Account Preferences
    Select Login Method    password and verification code
    Input Password To Update Account Preferences
    Wait Until Element Is Visible    ${password_input_container_oidc_false}
    Input Invalid Code 3 Times
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Element Is Visible    ${landing_page_login_button_app}
    ELSE
        Wait Until Element Is Visible    ${landing_page_login_button}
    END
    [Teardown]    Run Keywords    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    AND    Close Browser

Extension E - Several failed two-factor authentication attempts - Patient Is Logged Out
    [Tags]    nms9-ver-245-2    nms9-ver-245
    Open Page At    ${PATIENT_LOGIN_URL}    ${f07u01_patient_sms3}[email]
    FOR    ${i}    IN RANGE    4
        Type Verification Code When OIDC Is Enabled    ${invalid_code}
        Wait Until Page Contains    Incorrect verification code. Try again
    END
    [Teardown]    Close Browser

Extension F - User resends verification code - Patient
    [Tags]    nms9-ver-244    patient-2fa    sms
    Patient Logins And Resends Verification Code    email=${f07u01_patient_sms2}[email]
    Wait Until Page Contains Element    ${clinic-icon}
    [Teardown]    Close Browser

Extension H - Patient is not allowed to log in (web/app) - 1
    [Tags]    nms9-ver-24-1    nms9-ver-24    native-web
    [Setup]    Setup App Environment
    Check That Invited Patient Cannot Login
    Check That Proxy Patient Cannot Login
    Checked That Declined Patient Cannot Login
    Check That Locked Patient Cannot Login
    Check That Candidate Patient Cannot Login
    Check That Activated Patient Can Login
    [Teardown]    Close All App Instances

Extension H - Patient is not allowed to log in (web/app) - 2
    [Tags]    nms9-ver-24-2    nms9-ver-24    native-web
    [Setup]    Setup App Environment
    Check That Active/Active SPMC Patient Can Login
    Check That Active/Invited SPMC Patient Can Login
    Check That Active/Locked SPMC Patient Can Login
    Check That Active/Proxy SPMC Patient Can Login
    Check That Activated/Activated With One Consent Not Approved SPMC Patient Can Login
    [Teardown]    Close All App Instances

Extension H - Patient is not allowed to log in (web/app) - 3
    [Tags]    nms9-ver-24-3    nms9-ver-24    native-web
    [Setup]    Setup App Environment
    Check That Invited/Invited SPMC Patient Cannot Login
    Check That Locked/Locked SPMC Patient Cannot Login
    Check That Proxy/Proxy SPMC Patient Cannot Login
    Check That Candidate/Candidate SPMC Patient Cannot Login
    Check That Declined/Declined SPMC Patient Cannot Login
    Check That Activated/Activated And All Consents Not Approved SPMC Patient Can Login
    [Teardown]    Close All App Instances

Extension H - Patient is not allowed to log in (web/app) - 4
    [Tags]    nms9-ver-24-4    nms9-ver-24    native-web
    [Setup]    Setup App Environment
    Check That Active Patient In Disabled Clinic Cannot Login
    Check That Active SPMC Patient In Disabled Clinics Cannot Login
    Check That Active/Disabled Clinic SPMC Patient Can Login
    [Teardown]    Close All App Instances

Extension H - Patient is not allowed to log in (web/app) - 5
    [Tags]    nms9-ver-24-5    nms9-ver-24    native-web
    [Setup]    Setup App Environment
    Check That Active/Candidate SPMC Patient Can Login
    Check That Activated/Declined SPMC Patient Can Login
    Check That Delegate User Can Login
    [Teardown]    Close All App Instances

Extension J - The user cancels login (app, OIDC enabled)
    [Tags]    nms9-ver-402    manual
    # TODO: To be automated
#    Patient enters the login screen in the native app
#    Patient decides to cancel the login by pressing Done (iOS) or X (Android) in the login screen
#    User is returned to the landing page and “Failed login” banner is displayed.

Extension K - The user is not identified with biometrics when re-entering the app
    [Tags]    nms9-ver-403    manual
    # TODO: To be automated
#    Patient is logged in to Noona and has closed the app
#    Patient opens application
#    Biometrics does not identify the user
#    On an iOS device patient is prompted to try biometrics again or Cancel
#    Patient presses Try biometrics again
#    Biometrics does not identify the user
#    Patient is prompted to Enter Passcode or Cancel
#    Patient presses Enter Passcode
#    Patient enters device PIN/Passcode
#    Patient enters Noona to the page where they where when closed the app
#    Patient presses Cancel
#    Patient presses Cancel
#    if cancel is pressed 3 times in a row, patient is directed to landing page
#    Patient presses log in and is requested to re-enter the email and the password
#    Patient presses log in
#    Patient acknowledges native application notifications
#    Patient enters noona diary
#    On an Android device patient can either try biometrics again or use the device PIN

Extension L.1 - Patient tries to log in to a deactivated clinic
    [Tags]    nms9-ver-424    native-web
    [Setup]    Setup App Environment
    Check That Active Patient In Disabled Clinic Cannot Login
    Check That Delegate User In Inactive Clinic Cannot Login
    [Teardown]    Close All App Instances


*** Keywords ***
Extension A.2.iii - Test Teardown
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    Close Browser
    Delete All Messages In Server    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]

Patient Can Login
    Login As Patient    ${patient_email}
    Wait Until Page Contains    Diary

Status Is Locked And Unlock The Patient
    Wait Until Page Contains    Username locked
    Unlock Patient
    Wait Until Page Contains    Username active

Open Page At
    [Arguments]    ${url}    ${email}
    Open Page    ${url}
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${email}

Patient Logins And Resends Verification Code
    [Arguments]    ${email}=${CLINIC_PATIENT}[email]    ${remember_login}=Yes
    Delete All SMS From Mailosaur
    Login To Noona    patient    ${email}
    Input Patient Verification Code
    # Need to resend verification code if 1st one failed due to one server sharing 2FA in mailosaur
    ${2fa_incorrect_status}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${patient_2FA_status_incorrect_text}
    IF    ${2fa_incorrect_status}
        Run Keyword    Resend Patient Verification Code
    END

Resend Patient Verification Code
    Delete All SMS From Mailosaur
    Try To Click Element    ${patient_send_new_verification_code_link}
    Wait Until Page Contains    A new verification code has been sent
    Input Patient Verification Code

Check That Invited Patient Cannot Login
    Add A Not Activated Patient Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}'=='android'    Disable Privacy Screen
        Try To Click Native App Element    ${landing_page_login_button_app}
        Input Login Credentials And Login With OIDC    ${patient_email}    ${DEFAULT_PASSWORD}
    ELSE
        Login To Noona    patient    ${patient_email}
    END
    Check Incorrect Password Error And Prepare For Next Login

Check That Activated Patient Can Login
    Add An Activated Patient Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    IF    'native' in '${ENVIRONMENT}'
        IF    'android' in '${PLATFORM_NAME}'
            Page Should Not Contain Element    //*[contains(text(),'${incorrect_username_password}')]
        ELSE
            Page Should Not Contain Element    ${incorrect_username_password_native}
        END
    ELSE
        Page Should Not Contain Element    ${incorrect_username_password}
    END
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications
    Wait Until Element Is Visible    ${diary_heading}

Check That Proxy Patient Cannot Login
    Add Proxy Patient Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[default_user]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Checked That Declined Patient Cannot Login
    Add A Declined Patient Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[default_user]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Check That Locked Patient Cannot Login
    Add A Locked Patient Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Check That Delegate User Can Login
    IF    'native' not in '${ENVIRONMENT}'
        Input Login Credentials And Login    ${f07u01_patient}[delegate]    ${DEFAULT_PASSWORD}
    ELSE
        Input Login Credentials And Login    ${f07u01_app_delegate}[email]    ${DEFAULT_PASSWORD}
    END
    Wait Until Noona Loader Is Not Visible
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    Wait Until Element Is Visible    ${library_heading}    timeout=10s

Check That Delegate User In Inactive Clinic Cannot Login
    IF    'native' in '${ENVIRONMENT}'
        Input Login Credentials And Login    ${f07u01_app_disabled_clinic}[delegate]    ${DEFAULT_PASSWORD}
    ELSE
        Input Login Credentials And Login    ${f07u01_disabled_clinic}[delegate]    ${DEFAULT_PASSWORD}
    END
    IF    'native' in '${ENVIRONMENT}'
        Check Error Message For Patient In Disabled Clinic(s) And Prepare For Next Login On Native App
        ...    ${f07u01_app_disabled_clinic}[delegate]
    ELSE
        Check Error Message For Patient In Disabled Clinic(s) And Prepare For Next Login On Web
    END

Check That Candidate Patient Cannot Login
    Add Candidate Patient Via API    ${AUTOMATED_TESTS_EHR_TOKEN}    ${automated_tests_clinic}[tunit]
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Login As Patient In Disabled Clinic
    [Arguments]    ${patient_email}
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${patient_email}

Check That Active Patient In Disabled Clinic Cannot Login
    IF    'native' in '${ENVIRONMENT}'
        Login As Patient Without Setting Up PIN    ${f07u01_app_disabled_clinic}[email]    ${DEFAULT_PASSWORD}
    ELSE
        Login As Patient Without Setting Up PIN    ${f07u01_disabled_clinic}[email]    ${DEFAULT_PASSWORD}
    END
    IF    'native' in '${ENVIRONMENT}'
        Check Error Message For Patient In Disabled Clinic(s) And Prepare For Next Login On Native App
        ...    ${f07u01_app_disabled_clinic}[email]
    ELSE
        Check Error Message For Patient In Disabled Clinic(s) And Prepare For Next Login On Web
    END

Check That Active SPMC Patient In Disabled Clinics Cannot Login
    IF    'native' in '${ENVIRONMENT}'
        Input Login Credentials And Login    ${f07u01_app_disabled_two_clinics}[email]    ${DEFAULT_PASSWORD}
    ELSE
        Input Login Credentials And Login    ${f07u01_disabled_two_clinics}[email]    ${DEFAULT_PASSWORD}
    END
    IF    'native' in '${ENVIRONMENT}'
        Check Error Message For Patient In Disabled Clinic(s) And Prepare For Next Login On Native App
        ...    ${f07u01_app_disabled_two_clinics}[email]
    ELSE
        Check Error Message For Patient In Disabled Clinic(s) And Prepare For Next Login On Web
    END

Check That Active/Active SPMC Patient Can Login
    Create Activated/Activated Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Login As Patient Without Setting Up PIN    ${patient_email}    ${DEFAULT_PASSWORD}
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications
    Wait Until Element Is Visible    ${diary_heading}
    Prepare Next Login

Check That Active/Invited SPMC Patient Can Login
    Create Activated/Invited Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications Upon Re-login
    Wait Until Element Is Visible    ${diary_heading}
    Prepare Next Login

Check That Invited/Invited SPMC Patient Cannot Login
    Create Invited/Invited Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Login As Patient Without Setting Up PIN    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Check That Active/Locked SPMC Patient Can Login
    Create Activated/Locked Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications Upon Re-login
    Wait Until Element Is Visible    ${diary_heading}
    Prepare Next Login

Check That Locked/Locked SPMC Patient Cannot Login
    Create Locked/Locked Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Check That Active/Proxy SPMC Patient Can Login
    Create Activated/Proxy Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications Upon Re-login
    Wait Until Element Is Visible    ${diary_heading}    timeout=10s
    Prepare Next Login

Check That Proxy/Proxy SPMC Patient Cannot Login
    Create Proxy/Proxy Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Check That Active/Candidate SPMC Patient Can Login
    Create Activated/Candidate Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${appointment_clinic}[tunit1]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    Login As Patient Without Setting Up PIN    ${patient_email}    ${DEFAULT_PASSWORD}
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications
    Wait Until Element Is Visible    ${diary_heading}    timeout=10s
    Prepare Next Login

Check That Candidate/Candidate SPMC Patient Cannot Login
    Create Candidate/Candidate Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${appointment_clinic}[tunit1]
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Check That Activated/Declined SPMC Patient Can Login
    Create Activated/Declined Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications Upon Re-login
    Wait Until Element Is Visible    ${diary_heading}    timeout=10s
    Prepare Next Login

Check That Declined/Declined SPMC Patient Cannot Login
    Create Declined/Declined Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Check Incorrect Password Error And Prepare For Next Login

Check That Activated/Activated With One Consent Not Approved SPMC Patient Can Login
    Create Activated/Activated Patient On Multi-Clinic Via API
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${spmc_consent_a_clinic}[manager_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${SPMC_CONSENT_A_CLINIC_ID}
    ...    ${SPMC_CONSENT_A_SUB_ID_CARETEAM_1}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications Upon Re-login
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    Wait Until Element Is Visible    ${diary_heading}

Check That Activated/Activated And All Consents Not Approved SPMC Patient Can Login
    Create Activated/Activated Patient On Multi-Clinic Via API
    ...    ${SPMC_CONSENT_A_EHR_TOKEN}
    ...    ${spmc_consent_a_clinic}[tunit]
    ...    ${spmc_consent_a_clinic}[manager_email]
    ...    ${spmc_consent_b_clinic}[manager_email]
    ...    ${SPMC_CONSENT_A_CLINIC_ID}
    ...    ${SPMC_CONSENT_B_CLINIC_ID}
    ...    ${SPMC_CONSENT_B_SUB_ID_CARETEAM_1}
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Text Should Not Be In The Page    ${incorrect_username_password}
    Text Should Not Be In The Page    ${incorrect_username_password_native}
    Text Should Not Be In The Page    ${incorrect_username_password_false}
    IF    'native' in '${ENVIRONMENT}'    Allow Notifications
    Wait Until Element Is Visible    ${patient_consent}

Check That Active/Disabled Clinic SPMC Patient Can Login
    IF    'native' not in '${ENVIRONMENT}'
        Input Login Credentials And Login    ${f07u01_active_disabled_clinic}[email]    ${DEFAULT_PASSWORD}
    ELSE
        Input Login Credentials And Login With OIDC
        ...    ${f07u01_app_active_disabled_clinic}[email]
        ...    ${DEFAULT_PASSWORD}
    END
    IF    'native' in '${ENVIRONMENT}'
        IF    'android' in '${PLATFORM_NAME}'
            Page Should Not Contain Element    //*[contains(text(),'${incorrect_username_password}')]
        ELSE
            Page Should Not Contain Element    ${incorrect_username_password_native}
        END
        Allow Notifications
    ELSE
        Page Should Not Contain Element    ${incorrect_username_password}
    END
    Wait Until Element Is Visible    ${diary_heading}

Patient Gets Invitation Link And Open It On Browser To Activate Account
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${automated_tests_clinic}[name]
    Set Test Variable    ${invitation_link}    ${message_data}[0]
    Set Test Variable    ${received_invitation_email_id}    ${message_data}[1]
    Activate Noona Account Without Logging In    ${invitation_link}

Patient Gets A Symptom Questionnaire Via Email And Opens The Link To Questionnaire
    @{message_data}    Patient Received Invitation To Answer AEQ    ${patient_email}    ${automated_tests_clinic}[name]
    Set Test Variable    ${link_to_questionnaire}    ${message_data}[0]
    Set Test Variable    ${received_email_to_questionnaire_id}    ${message_data}[1]
    Open URL In Chrome    ${link_to_questionnaire}
    Accept All Cookies If Visible

Patient Opens Password Reset Link Via Email
    @{message_data}    Patient Received An Email About Reset Password    ${patient_email}    yes
    Set Test Variable    ${link_to_reset_password}    ${message_data}[1]
    Set Test Variable    ${received_email_reset_password_id}    ${message_data}[0]
    Reset Password From Link    ${link_to_reset_password}

User Can Log In: OIDC = True - Test Teardown
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

User Can Log In: OIDC = True - Test Setup
    Set Test Variable    @{mailosaur_keys}    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Invite New Patient Via API As Clinic User
    ...    ${f07u01_email_keys}[0]
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}

User Can Login From The OIDC Login Page By Opening A Direct URL
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Wait Until Location Contains    ${PATIENT_URL}/patient/#/sign-in    timeout=5s
    Close Browser
    Open URL In Chrome    ${PATIENT_URL}/patient/#/sign-in
    Wait Until Page Contains    ${landing_page_connect_to_clinic_text}    timeout=5s
    Close Browser
    Open URL In Chrome    ${PATIENT_URL}/patient/#/diary-timeline
    Wait Until Location Contains    ${PATIENT_URL}/patient/#/sign-in    timeout=5s
    Close Browser

User Can Login From The OIDC Login Page Via An Activation Invitation
    Patient Gets Invitation Link And Open It On Browser To Activate Account
    Try To Click Element    ${activation_complete_next_button}
    Wait Until Location Contains    ${PATIENT_URL}/patient/#/sign-in    timeout=5s
    Login As Patient From Direct Link    ${patient_email}
    Patient Is In The Right Page    Diary
    Close Browser
    # use delete all messages in this keyword to make sure the "Wecome to Noona"-email is not selected when the next step starts
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

User Can Login From OIDC Login Page By Completing A Syptom Questionnaire
    Send Symptom Questionnaire Via Api To Patient    baseline
    Patient Gets A Symptom Questionnaire Via Email And Opens The Link To Questionnaire
    Answer Symptom Form From Clicking Link
    Try To Click Element    ${questionnaire_complete_login_button}
    Wait Until Location Contains    ${PATIENT_URL}/patient/#/sign-in    timeout=5s
    Close Browser
    Delete A Message In Server    ${mailosaur_keys}[1]    ${received_email_to_questionnaire_id}

User Can Login From The OIDC Login After Completing Password Reset
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Accept All Cookies If Visible
    Try To Click Element    ${landing_page_login_button}
    Request Password Reset    ${patient_email}
    Close Browser
    Patient Opens Password Reset Link Via Email
    Wait Until Location Contains    ${PATIENT_URL}/patient/#/sign-in    timeout=5s
    Close Browser
    Login As Patient    ${patient_email}
    Close Browser
    Delete A Message In Server    ${mailosaur_keys}[1]    ${received_email_reset_password_id}

Check Error Message For Patient In Disabled Clinic(s) And Prepare For Next Login On Web
    Wait Until Noona Loader Is Not Visible
    Wait Until Page Contains    ${closed_logo}
    Wait Until Noona Loader Is Not Visible
    Text Should Be In The Page    ${disabled_clinic_login_message_1}
    Text Should Be In The Page    ${disabled_clinic_login_message_2}
    Try To Click Element    ${disabled_clinic_back_to_login_button}
    Sleep    1s    # needed as the loader step is sometimes checked too soon
    Wait Until Noona Loader Is Not Visible
    Wait Until Element Is Visible    ${landing_page_login_button}    timeout=10s
    Wait Until Page Contains    ${landing_page_connect_to_clinic_text}
    Try To Click Element    ${landing_page_login_button}

Check Error Message For Patient In Disabled Clinic(s) And Prepare For Next Login On Native App
    [Arguments]    ${patient_email}
    Wait Until Noona Loader Is Not Visible
    IF    'android' in '${PLATFORM_NAME}'
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        Wait Until Page Contains    ${closed_logo}
    ELSE
        Wait Until Page Contains Element    ${disabled_clinic_back_to_login_button}    timeout=10s
        Text Should Be In The Page    ${closed_logo}
    END
    Wait Until Noona Loader Is Not Visible
    Text Should Be In The Page    ${disabled_clinic_login_message_1}
    Text Should Be In The Page    ${disabled_clinic_login_message_2}
    IF    'android' in '${PLATFORM_NAME}'
        ${current_context}    Get Current Context
        IF    'WEBVIEW_com' not in '${current_context}'    Switch To Noona App Web Context
    END
    Try To Click Element    ${disabled_clinic_back_to_login_button}
    Sleep    1s    # needed as the loader step is sometimes checked too soon
    Wait Until Noona Loader Is Not Visible
    Wait Until Element Is Visible    ${landing_page_login_button_app}    timeout=10s
    Wait Until Page Contains    ${landing_page_connect_to_clinic_text}
    @{contexts}    Get Contexts
    IF    'android' in '${PLATFORM_NAME}'    Switch To Noona App Web Context
    Try To Click Native App Element    ${landing_page_login_button_app}
    Clear Patient Login Details    ${patient_email}

Check If Email Notification About Permanently Locked Account Is Sent
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Type Wrong Password For 6 Times    ${patient_email}
    Patient Is Locked    ${patient_email}
    Patient Is Notified About Account Being Locked    ${patient_email}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Close Browser

Check If Email Notification About Temporary Locked Account Is Sent
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Delete All Messages In Server    ${f07u01_email_keys}[0]    ${f07u01_email_keys}[1]
    Type Wrong Password For 6 Times    ${patient_email}
    Patient Is Locked    ${patient_email}
    Patient Is Notified About Account Temporarily Locked    ${patient_email}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Close Browser

Create Inactivated Patient Under Time Constraints Clinic
    ${patient_identifier}    Get Current Date
    ...    result_format=%d-%m-%Y    # use date to identify when patient is created
    Set Test Variable    @{mailosaur_keys}    ${time_constraint_keys}[0]    ${time_constraint_keys}[1]
    Login As Nurse    ${time_constraint_testing}[manager_email]
    Set New Patient Mailosaur Email Server    ${mailosaur_keys}[0]
    Set To Dictionary    ${patient_dict}    first name=${patient_identifier}    care team=f07u01 care team
    Create New Patient With Robot

Create Inactive Delegate Patient Under Time Constraints Clinic
    Set Test Variable    @{mailosaur_keys}    ${time_constraint_keys}[0]    ${time_constraint_keys}[1]
    Add An Activated Patient Under Time Constraint Clinic
    ...    f07u01_a1ii
    ...    ${TIME_CONSTRAINT_CLINIC_SUB_ID_F01P12}
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Patient    ${patient_email}
    Go To Clinic Preferences
    Delegate Users Section Is Displayed In My Profile
    ${delegate_email}    Set Variable    ${family_name}.delegate@${time_constraint_keys}[0].mailosaur.net
    Set Test Variable    ${delegate_email}
    Add Delegate User    f07u01_a1ii_delegate    ${family_name}    ${delegate_email}
    Patient Received An Email About A New Message
    ...    ${delegate_email}
    ...    ${time_constraint_testing}[name] invites you to use Noona
    ...    ${first_name}${SPACE}${family_name} wants to share their medical records with you.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/s/

Add Active Delegate User For Patient Under Default Clinic
    [Arguments]    ${patient_email}    @{mailosaur_keys}
    Login As Patient    ${patient_email}
    Go To Clinic Preferences
    Delegate Users Section Is Displayed In My Profile
    ${delegate_email}    Set Variable    ${family_name}.delegate@${mailosaur_keys}[0].mailosaur.net
    Add Delegate User
    ...    ${first_name} delegate
    ...    ${family_name} delegate
    ...    ${delegate_email}
    Set Test Variable    ${delegate_email}
    @{message_data}    Patient Received An Email About A New Message
    ...    ${delegate_email}
    ...    ${automated_tests_clinic}[name] invites you to use Noona
    ...    ${first_name}${SPACE}${family_name} wants to share their medical records with you.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    Set Test Variable    ${link}    ${message_data}[2]
    ${link}    Convert To String    ${link}
    Activate Delegate User Account    ${link}    ${delegate_email}
    Close Browser

Check That Delegate User Cannot Login After 6 Wrong Password Attempts
    [Arguments]    ${delegate_email}
    Type Wrong Password For 6 Times    ${delegate_email}
    Input Login Credentials And Login    ${delegate_email}    ${DEFAULT_PASSWORD}
    Wait Until Noona Loader Is Not Visible
    Text Should Be In The Page    ${incorrect_username_password_web}

Input Invalid Code 3 Times
    FOR    ${i}    IN RANGE    3
        Wait Until Element Is Visible    xpath=(${2fa_input_account_preference})[1]
        Try To Click Element    xpath=(${2fa_input_account_preference})[1]
        IF    'native' in '${ENVIRONMENT}'
            ${contexts}    Get Contexts
            Switch To Context    NATIVE_APP
            IF    '${PLATFORM_NAME}'=='android'
                Input Text    xpath=${patient_sms_code_input_android}    ${invalid_code}
            ELSE
                Input Text    xpath=${patient_sms_code_input_ios}    ${invalid_code}
            END
            ${contexts}    Get Contexts
            ${status}    Run Keyword And Return Status    List Should Contain Value  ${contexts}    ${NOONA_WEBVIEW}
            IF    ${status}
                Switch To Context    ${NOONA_WEBVIEW}
            ELSE
                Switch To Context    ${contexts}[1]
            END
        ELSE
            Press keys    (${2fa_input_account_preference})[1]    ${invalid_code}
        END
    END