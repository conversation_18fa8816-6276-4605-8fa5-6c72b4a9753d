*** Settings ***
Documentation       F07U07 User is redirected to page he attempted to view before login
...
...                 Preconditions:
...                 - The user has a user account, and the user's user account role has permission to access page X,
...                 which is not the login page
...                 - The user is not logged in
...
...                 Main Scenario:
...                 - The user tries to open page X in a browser
...                 - <PERSON><PERSON> redirects the user to the login page
...                 - The user logs in
...                 - Immediately after login, <PERSON><PERSON> opens page X
...                 Added Test Scenario:
...                 - User Logs out
...                 - User tries again to open page x in the same browser
...                 - Error message should display

Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}
Test Template       User Is Redirected Upon Access Of Page X Before Login

Force Tags          usecase-f07u07    patient-web    nms9-ver-254


*** Test Cases ***
Open Url A
    [Documentation]    Only testable when oidc vault is disabled (classic login) OR when OIDC_LOGIN=true
    ${PATIENT_URL}${PATIENT_PATH}#/diary-timeline
Open Url B
    [Documentation]    Only testable when oidc vault is disabled (classic login) OR when OIDC_LOGIN=true
    ${PATIENT_URL}${PATIENT_PATH}#/library
Open Url C
    [Documentation]    Only testable when oidc vault is disabled (classic login) OR when OIDC_LOGIN=true
    ${PATIENT_URL}${PATIENT_PATH}#/clinic
Open Url D - 1
    [Documentation]    Only testable when oidc vault is disabled (classic login) OR when OIDC_LOGIN=true
    ${PATIENT_URL}${PATIENT_PATH}#/account-preferences
Open Url D - 2
    [Documentation]    Only testable when oidc vault is disabled (classic login) OR when OIDC_LOGIN=true
    ${PATIENT_URL}${PATIENT_PATH}#/clinic-preferences
Open Url E
    [Documentation]    Only testable when oidc vault is disabled (classic login) OR when OIDC_LOGIN=true
    ${PATIENT_URL}${PATIENT_PATH}#/report-symptom
Open Url F
    [Documentation]    Only testable when oidc vault is disabled (classic login) OR when OIDC_LOGIN=true
    ${PATIENT_URL}${PATIENT_PATH}#/clinic/open-question
