*** Settings ***
Documentation       F07U06_1 Patient can set password on login (Clinic OIDC)

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u06-1    patient-web    patient-email-link

*** Test Cases ***
Main success scenario
    [Tags]    nms9-ver-519    clinic-oidc-on
    # 1st precondition
    Log In To Clinic With SSO    ${SSO_CLINIC_MANAGER}
    Set Test Variable    @{mailosaur_keys}    ${f07u06_email_keys}[0]    ${f07u06_email_keys}[1]
    Set New Patient Mailosaur Email Server    ${mailosaur_keys}[0]
    Create New Patient With Robot
    Close Browser
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_dict}[email]    ${automated_tests_clinic_oidc_on}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Set Test Variable    ${link}    ${message_data}[0]
    Activate Noona Account As Patient    ${link}    ${patient_dict}[email]    terms_enabled=false
    Patient Is In The Right Page    Diary
    Close Browser
    Login As Patient    ${patient_dict}[email]
    Patient Is In The Right Page    Diary
    Close Browser
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    # 2nd precondition
    Log In To Clinic With SSO    ${SSO_CLINIC_MANAGER}
    Search Patient By Identity Code    ${patient_dict}[social security number]
    Choose General Information Tab
    Click Send New Login Link
    Wait Until Page Contains    New login link sent to patient
    Try To Click Banner Message
    Close Browser
    ${link}    Patient Received New Login Link Email    ${patient_dict}[email]    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    ${new_password}    Set Variable    NewPassw0rd@123
    Reset Password From Link    ${link}    password=${new_password}
    Close Browser
    False Login       patient     ${patient_dict}[email]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${patient_dict}[email]    password=${new_password}
    Patient Is In The Right Page    Diary

