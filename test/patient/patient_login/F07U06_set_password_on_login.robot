*** Settings ***
Documentation       F07U06 User can set password on login
...                 Preconditions:
...                 A clinic user with the authority to do so has done one of the following:
...                 - A clinic user has created a new patient account, which causes a login link being sent to the patient via e-mail
...                 - The patient is logging in for the first time
...                 - A clinic user has sent a patient a new login link, which invalidates the patient's existing password
...                 - A clinic user has created a new clinic user account (for someone else), which causes a temporary password being sent to the latter via e-mail
...                 - A clinic user has sent a random password to another clinic user
...                 - The user who is the target of the action has not yet attempted to log in, and has not clicked the login link

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u06    patient-web    patient-email-link


*** Test Cases ***
Main success scenario - User Can Set Password On Login (Original)
    [Documentation]    This scenario verifies that patient can change password via a new login link sent by a clinic user.
    [Tags]    nms9-ver-253
    [Setup]    Set Test Variable    @{mailosaur_keys}    ${f07u06_email_keys}[0]    ${f07u06_email_keys}[1]
    Login As Nurse
    Send New Login Link To Patient    ${f07u06_patient}[ssn]
    Patient Gets Reset Password Link
    Reset Password From Link    ${reset_password_link}
    Patient Can Login After Resetting Password    ${f07u06_patient}[email]
    [Teardown]    Run Keywords    Delete A Message In Server    ${mailosaur_keys}[1]    ${received_email_id}
    ...    AND    Close All App Instances


*** Keywords ***
Send New Login Link To Patient
    [Arguments]    ${patient_mrn}
    Search Patient By Identity Code    ${patient_mrn}
    Choose General Information Tab
    Click Send New Login Link

Patient Gets Reset Password Link
    @{message_data}    Patient Received An Email About Reset Password
    ...    ${f07u06_patient}[email]
    ...    ${PATIENT_URL_IN_MESSAGE}/s/
    ...    clinic=Noona
    ...    request_from=clinic user
    Set Test Variable    ${reset_password_link}    ${message_data}[1]
    Set Test Variable    ${received_email_id}    ${message_data}[0]

Patient Can Login After Resetting Password
    [Arguments]    ${patient_email}
    ${current_url}    Get Location
    IF    '${current_url}' == '${PATIENT_URL}/patient/#/sign-in'
        Login As Patient    ${patient_email}
        Patient Is In Dairy Page After Logging In
    END

Patient Is In Dairy Page After Logging In
    Wait Until Location Contains    ${PATIENT_URL}/patient/#/diary-timeline    5s
