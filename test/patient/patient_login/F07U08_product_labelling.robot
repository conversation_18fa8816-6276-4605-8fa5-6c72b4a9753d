*** Settings ***
Documentation       F07U08 User can see product labelling
...                 Noona-12021 About box: Show full app technical version information, by default hidden

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}native-web_privacy_about.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_clinic.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}common.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          noona-12021    about-box    usecase-f07u08    patient-web


*** Variables ***
${instructions_link}                    //*[@data-testid='more-link-instructions']
${about_box_icons}                      //*[@id="about-icons"]
${about_box_qrcode}                     //*[@id="udi"]
${about_box_more_info}                  //*[contains(text(),'More information')]
${about_box_close_button}               //*[@id="close-modal"]/button
${about_modal_different_languages}      //*[@data-testid="about"]
${copymark_element}                     //div[contains(@class,'copymark')]
${copyright_varian_element}             //div[contains(text(),'© 2025 Varian Medical Systems, Inc.')]
${copyright_info_clinic}                //*[@id='copyright-info']
${about_noona_mobile_service_text}      About Noona Mobile Service
${about_box_eu_rep_info}                //*[@id="eu-representative"]
${about_box_heading}                    //*[@class="about-heading"]
${about_box_version_info}               //*[@id="version-info"]
${about_box_copyright_info}             //*[@id="copyright-info"]
${about_box_manufactured_date_info}     //*[@id="manufactured-info"]
${about_box_manufacturer_info}          //*[@id="manufacturer-info"]
${about_box_barcode_loc}                //*[contains(@class, "about__barcode")]
${about_screen_more_info}               //*[contains(@class, "extra-information mb-l")]


*** Test Cases ***
Main Success Scenario - User Can See Product Labelling (Login Page)
    [Documentation]    This testcase has two parts (nms9-ver-368-app was merged with this, and thus removed).
    ...    tc checks patient portal Product Labelling, Privacy Statement, and About Box contents without patient login
    [Tags]    nms9-ver-255    native-web
    [Setup]    Setup App Environment
    Open Noona Patient Page
    Product Labelling Is Displayed
    Open Privacy Notice From Front Page
    Verify That Content Displayed Is Correct
    Close Privacy Page And Return To Front Page
    Click Noona Patient About Box And Verify Content
    Close All App Instances

Extension A - Patient Sees Product Labelling In Instructions
    [Tags]    nms9-ver-256    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f07u08_labelling}[email]
    Navigate To Instructions
    Product Labelling Is Displayed
    [Teardown]    Close All App Instances

Extension B - Patient Sees Information About Noona Mobile Service (About box)
    [Tags]    nms9-ver-257-1    nms9-ver-257   native-web
    [Setup]    Setup App Environment
    # patient is not logged in
    Open Noona Patient About Box
    Verify About Box Info Login Page
    # patient is logged in
    IF    'native' not in '${ENVIRONMENT}'
        Login As Patient    ${f07u08_labelling}[email]
    ELSE
        Setup App Environment
        IF    '${PLATFORM_NAME}' == 'ios'
            Login As Patient    ${f07u08_labelling}[email]
        ELSE
            Login As Patient    ${f07u08_labelling}[email]
        END
    END
    User Navigates To About Screen
    Compare About Screen Details Vs Login About Box
    [Teardown]    Close All App Instances

Capture Screenshots From Login Page With Different Languages
    [Documentation]    Captures screenshots from login pages every language. Notice: There's two
    ...    images/language. Todo: scrolling down
    [Tags]    nms9-ver-257-2    nms9-ver-257
    Capture Screenshot From About Box
    Capture Screenshot Of Login Page With Different Languages
    [Teardown]    Close All Browsers

Extension C - Clinic User Sees Information About Noona Mobile Service
    [Tags]    nms9-ver-258
    Login As Nurse
    Wait Until Element Is Enabled    ${help_link}
    Wait Until Page Does Not Contain Element    ${loader}
    Try To Click Element    ${help_link}
    Try To Click Element    ${help_about_link}
    Wait Until Page Contains    About Noona Mobile Service
    Verify Clinic About Page When Logged In
    [Teardown]    Close All Browsers


*** Keywords ***
Product Labelling Is Displayed
    ${content}    Get File    ${EXECDIR}${/}resources${/}patient${/}labelling.txt
    @{content_lines}    Split To Lines    ${content}
    FOR    ${line}    IN    @{content_lines}
        Wait Until Page Contains    ${line}
    END

Navigate To Instructions
    Try To Click More Button
    Wait Until Page Contains Element    xpath=${instructions_link}
    Try To Click Element    xpath=${instructions_link}

Open Page
    [Arguments]    ${url}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${url}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE
        Open Browser    ${url}    ${BROWSER}
    END

Open Noona Patient About Box
    Open Noona Patient Page
    IF    'native' in '${ENVIRONMENT}'
        Click Compliance Button
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        Wait Until Element Is Visible    ${app_about_link}    timeout=10s
        Try To Click Native App Element    ${app_about_link}
        Switch To Context    ${contexts}[1]
    ELSE
        Try To Click Element    ${about_modal_link}
    END
    Wait Until Keyword Succeeds    10s    1s    Wait Until Page Contains    Noona Mobile Service
    Wait Until Element Is Visible    ${about_modal}

Capture Screenshot From About Box
    Open Page    ${PATIENT_LOGIN_URL}
    Accept All Cookies If Visible
    Wait Until Keyword Succeeds    10s    1s    Wait Until Page Contains    Noona mobile service
    Wait Until Page Contains Element    ${about_modal_different_languages}
    Try To Click Element    ${about_modal_different_languages}
    Wait Until Element Is Visible    ${about_modal}    timeout=20s
    Capture Screenshot    #needed as test evidence for about box
    Scroll Element Into View    ${about_box_close_button}
    Capture Screenshot    #needed as test evidence for about box
    Try To Click Element    ${about_box_close_button}

Capture Screenshot Of Login Page With Different Languages
    @{languages}    Create List
    ...    en_GB
    ...    fi_FI
    ...    sv_FI
    ...    no_NO
    ...    de_DE
    ...    fr_FR
    ...    es_ES
    ...    it_IT
    ...    pt_PT
    ...    nl_NL
    ...    tr_TR
    ...    pl_PL
    FOR    ${language_element}    IN    @{languages}
        Try To Click Element    ${language_dropdown}
        Try To Click Element    //div[contains(@value, "${language_element}")]
        Wait Until Page Contains Element    ${about_modal_different_languages}
        Wait Until Element Is Enabled    ${language_dropdown}
        Scroll Element Into View    ${language_dropdown}
        Wait Until Element Is Enabled    ${copyright_varian_element}
        Scroll Element Into View    ${copyright_varian_element}
        # needs to capture copyright texts in different languages
        Capture Screenshot
    END

Verify About Box Info Login Page
    Wait Until Element Is Visible    ${about_box_heading}
    ${about_box_header}    Get Text    ${about_box_heading}
    Set Test Variable    ${about_box_header}
    ${about_box_full_version}    Get Text    ${about_box_version_info}
    Set Test Variable    ${about_box_full_version}
    ${about_box_copyright}    Get Text    ${about_box_copyright_info}
    Set Test Variable    ${about_box_copyright}
    ${about_box_manufactured_date}    Get Text    ${about_box_manufactured_date_info}
    Set Test Variable    ${about_box_manufactured_date}
    ${about_box_manufacturer}    Get Text    ${about_box_manufacturer_info}
    Set Test Variable    ${about_box_manufacturer}
    ${about_box_eu_representative}    Get Text    ${about_box_eu_rep_info}
    Set Test Variable    ${about_box_eu_representative}
    ${about_box_barcode}    Get Text    ${about_box_barcode_loc}
    Set Test Variable    ${about_box_barcode}
    Wait Until Keyword Succeeds    10s    1s    Scroll Element Into View    ${about_box_close_button}
    Wait Until Page Contains Element    ${about_box_icons}
    Capture Screenshot    #needed as test evidence for product labelling
    Wait Until Page Contains Element    ${about_box_qrcode}
    Wait Until Element Is Visible    ${about_box_more_info}
    Try To Click Element    ${about_box_more_info}
    Scroll Element Into View    ${about_box_more_info}
    ${about_box_more_infos}    Get Text    ${about_screen_more_info}
    ${about_box_more_infos}    Fetch From Right    ${about_box_more_infos}    VN
    ${about_box_more_info_version_no}    Get Substring    ${about_box_more_infos}    0    17
    Set Test Variable    ${about_box_more_info_version_no}
    ${about_box_more_info_serial_no}    Get Substring    ${about_box_more_infos}    18    59
    Set Test Variable    ${about_box_more_info_serial_no}
    ${about_box_more_info_build_no}    Get Substring    ${about_box_more_infos}    60    71
    Set Test Variable    ${about_box_more_info_build_no}
    IF    'native' in '${ENVIRONMENT}'
        ${about_box_more_infos}    Get Text    ${about_screen_more_info}
        ${about_box_more_infos}    Fetch From Right    ${about_box_more_infos}    App
        ${about_box_app_version}    Get Substring    ${about_box_more_infos}    0    17
        Set Test Variable    ${about_box_app_version}
        ${about_box_JS_version}    Get Substring    ${about_box_more_infos}    18    36
        Set Test Variable    ${about_box_JS_version}
        Capture Screenshot    #needed as test evidence for product labelling
        Try To Click Element    ${about_box_close_button}
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        Try To Click Native App Element    ${native_app_compliance_close_button}
        Switch To Context    ${contexts}[1]
        Close All App Instances
    ELSE
        Capture Screenshot    #needed as test evidence for product labelling
        Close Browser
    END

Compare About Screen Details Vs Login About Box
    ${about_screen_header}    Get Text    ${about_box_heading}
    Set Test Variable    ${about_screen_header}
    Should Be Equal    ${about_screen_header}    ${about_box_header}
    ${about_screen_full_version}    Get Text    ${about_box_version_info}
    Set Test Variable    ${about_screen_full_version}
    Should Be Equal    ${about_screen_full_version}    ${about_box_full_version}
    ${about_screen_copyright}    Get Text    ${about_box_copyright_info}
    Set Test Variable    ${about_screen_copyright}
    Should Be Equal    ${about_screen_copyright}    ${about_box_copyright}
    ${about_screen_manufactured_date}    Get Text    ${about_box_manufactured_date_info}
    Set Test Variable    ${about_screen_manufactured_date}
    Should Be Equal    ${about_screen_manufactured_date}    ${about_box_manufactured_date}
    ${about_screen_manufacturer}    Get Text    ${about_box_manufacturer_info}
    Set Test Variable    ${about_screen_manufacturer}
    Should Be Equal    ${about_screen_manufacturer}    ${about_box_manufacturer}
    ${about_screen_eu_representative}    Get Text    ${about_box_eu_rep_info}
    Set Test Variable    ${about_screen_eu_representative}
    Should Be Equal    ${about_screen_eu_representative}    ${about_box_eu_representative}
    ${about_screen_barcode}    Get Text    ${about_box_barcode_loc}
    Set Test Variable    ${about_screen_barcode}
    Should Be Equal    ${about_screen_barcode}    ${about_box_barcode}
    Capture Screenshot    #needed as test evidence for product labelling
    Wait Until Keyword Succeeds    10s    1s    Scroll Element Into View    ${about_box_close_button}
    Wait Until Page Contains Element    ${about_box_icons}
    Wait Until Page Contains Element    ${about_box_qrcode}
    Wait Until Element Is Visible    ${about_box_more_info}
    Try To Click Element    ${about_box_more_info}
    ${about_screen_more_infos}    Get Text    ${about_screen_more_info}
    ${about_screen_more_infos}    Fetch From Right    ${about_screen_more_infos}    VN
    ${about_screen_more_info_version_no}    Get Substring    ${about_screen_more_infos}    0    17
    Set Test Variable    ${about_screen_more_info_version_no}
    Should Be Equal    ${about_screen_more_info_version_no}    ${about_box_more_info_version_no}
    ${about_screen_more_info_serial_no}    Get Substring    ${about_screen_more_infos}    18    59
    Set Test Variable    ${about_screen_more_info_serial_no}
    Should Be Equal    ${about_screen_more_info_serial_no}    ${about_box_more_info_serial_no}
    ${about_screen_more_info_build_no}    Get Substring    ${about_screen_more_infos}    60    71
    Set Test Variable    ${about_screen_more_info_build_no}    ${about_box_more_info_build_no}
    IF    'native' in '${ENVIRONMENT}'
        ${about_screen_more_infos}    Get Text    ${about_screen_more_info}
        ${about_screen_more_infos}    Fetch From Right    ${about_screen_more_infos}    App
        ${about_screen_app_version}    Get Substring    ${about_screen_more_infos}    0    17
        Set Test Variable    ${about_screen_app_version}
        Should Be Equal    ${about_screen_app_version}    ${about_box_app_version}
        ${about_screen_JS_version}    Get Substring    ${about_screen_more_infos}    18    36
        Set Test Variable    ${about_screen_JS_version}
        Should Be Equal    ${about_screen_JS_version}    ${about_box_JS_version}
    END
    Capture Screenshot    #needed as test evidence for product labelling

Verify Clinic About Page When Logged In
    Wait Until Page Contains    ${about_noona_mobile_service_text}
    Wait Until Element Contains    ${copyright_info_clinic}    Copyright:
    Wait Until Element Contains
    ...    ${copyright_info_clinic}
    ...    © 2018-2025 Varian Medical Systems, Inc. All rights reserved.
    Wait Until Element Contains    ${about_box_manufacturer_info}    Varian Medical Systems, Inc.
    Wait Until Element Contains    ${about_box_manufacturer_info}    3100 Hansen Way
    Wait Until Element Contains    ${about_box_manufacturer_info}    Palo Alto, CA 94304
    Wait Until Element Contains    ${about_box_manufacturer_info}    USA
    Wait Until Element Contains    ${about_box_eu_rep_info}    Varian Medical Systems Nederland B.V.
    Wait Until Element Contains    ${about_box_eu_rep_info}    Kokermolen 2
    Wait Until Element Contains    ${about_box_eu_rep_info}    3994 DH Houten
    Wait Until Element Contains    ${about_box_eu_rep_info}    The Netherlands

Open Noona Patient Page
    IF    'native' in '${ENVIRONMENT}'
        Launch Noona
    ELSE
        ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
        IF    ${remote_url_exists}
            Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
        ELSE
            Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
        END
    END
    Wait Until Page Contains    Noona mobile service
    Accept All Cookies If Visible

Click Noona Patient About Box And Verify Content
    IF    'native' in '${ENVIRONMENT}'
        ${contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        Try To Click Native App Element    ${app_about_link}
        Wait Until Element Is Visible    ${privacy_about_close_button}
        Switch To Context    ${contexts}[1]
    ELSE
        Try To Click Element    ${about_modal_link}
        Wait Until Element Is Enabled    ${about_modal}
        Wait Until Element Is Enabled    ${close_modal}
    END
    Verify About Text Is Displayed
