*** Settings ***
Documentation       F07U10 User can see in-app notifications, instructions and feedback surveys - Main success scenario
...
...                 Pendo Feature NOONA-11427 [Onboarding] General guide
...
...                 Preconditions:
...
...                 1. Patient is created by a Clinic User.
...
...                 2. Patient’s account is activated (either by <PERSON><PERSON> or by Clinic User)
...
...                 3. <PERSON><PERSON> has never visited the Diary page before.
...
...                 Main success scenario:
...
...                 1. <PERSON><PERSON> accesses the Diary page of Noona (on desktop Web, mobile Web or iOS/Android native app)
...
...                 2. Step 1 is displayed
...
...                 a. The features described in Step 1 depend on the features enabled in the clinic of the Patient.
...
...                 b. At this step, <PERSON><PERSON> can snooze the guide by clicking Next time.
...
...                 i. Then, the guide will be displayed again after 2 hours if <PERSON><PERSON> goes through Main success scenario again
...
...                 c. At any step, <PERSON><PERSON> can dismiss the guide by clicking the Close icon.
...
...                 3. Patient clicks Let’s go.
...
...                 4. Step 2 is displayed. (Clinic tab guide)
...
...                 5. Patient clicks Add symptom
...
...                 6. Step 3 is displayed next to the + button of Noona.
...
...                 7. <PERSON><PERSON> clicks the + button.
...
...                 8. Sub navigation opens and Step 4 is displayed next to the Symptom button.
...
...                 9. Patient clicks the Symptom button.
...
...                 10. Add symptom to diary page opens and guide is dismissed.
...

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources/patient/pendo_guides.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u10    pendo    patient-web


*** Test Cases ***
Login As New Patient And Navigate Pendo Guides
    [Tags]    nms9-ver-34
    Generate Random Patient Data
    Add An Activated Patient Under Pendo Clinic
    ...    f07u10
    ...    module=${BONE_RADIOTHERAPY}
    Login To Noona    patient    ${patient_email}
    Try To Click Element    ${accept_terms_next_button}
    Sleep    1
    Try To Click Element    ${approve_elements_checkbox}
    Sleep    1
    Try To Click Element    ${accept_terms_next_button}
    Sleep    1
    Try To Click Element    ${approve_elements_checkbox}    # consent to process data
    Sleep    1
    Try To Click Element    ${accept_terms_next_button}
    Try To Click Element    ${approve_elements_checkbox}
    Try To Click Element    ${accept_terms_next_button}
    Check Pendo Guides From Diary
    Go To Clinic
    Check Pendo Guides From Clinic
    # TODO: Update next steps based on updated pendo requirements

# TODO: Create New Pendo Patient And Activate Via Email
