*** Settings ***
Documentation       F02P02 Patient can delete user account
...                 F02CM01 Clinic manager can approve patient account deletion

Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}single_patient_multi_clinic.resource
Resource            ${EXECDIR}${/}resources/patient/diary.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}general_information.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02p02    nms9-ver-136    patient-web    usecase-f02cm01


*** Variables ***
${account_deletion_email_subject}       Your user account has been removed
${account_deletion_email_body}          Your user account and all your information have now been removed from Noona
${first_clinic_name}                    TA clinic Automated_tests
${second_clinic_name}                   TA clinic Appointment_Clinic
${switch_confirmation_text}             You are now in TA clinic Appointment_Clinic
${tunit}                                666
${care_team}                            Appointment Care Team
${careteam_input}                       //*[@id="responsible-unit"]//input


*** Test Cases ***
Patient can disconnect from a clinic - Single Clinic
    [Documentation]    Patient deletion performance is still under assessment. For test clinics used in this test suite, timeout is set to maximum 120s.
    [Tags]    nms9-ver-136-1    nms9-ver-166    native-web
    [Setup]    Setup App Environment
    Create New Patient To Delete
    Login As Patient    ${patient_email}
    Wait Until Element Is Visible    ${diary_main_header}
    Disconnect From Clinic
    False Login    patient    ${patient_email}
    # nms9-ver-166
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse    ${patient_education_clinic}[manager_email]
        Delete Patient Account    ${patient_email}
        Patient Received An Email About A New Message
        ...    ${patient_email}
        ...    ${account_deletion_email_subject}
        ...    ${account_deletion_email_body}
    ELSE
        Remove Patient As Test Teardown
        ...    ${patient_email}
        ...    ${patient_education_clinic}[manager_email]
        ...    ${PATIENT_EDUCATION_CLINIC_ID}
        ...    ${PATIENT_EDUCATION_EHR_TOKEN}
    END

Patient can disconnect from a clinic - Multi Clinic
    [Documentation]    Patient deletion performance is still under assessment. For test clinics used in this test suite, timeout is set to maximum 120s.
    [Tags]    nms9-ver-136-2    nms9-ver-166    native-web
    [Setup]    Patient can disconnect from a clinic - Multi Clinic - Test Setup
    Login As Patient    ${patient_email}
    Verify Visited Clinic    ${first_clinic_name}
    # Remove invitation email from server before script can pick email regarding account deletion
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Remove SPMC Patient From One Clinic Via API    # nms9-ver-166 starts within this keyword - pls see kw definition below
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    ${account_deletion_email_subject}
    ...    ${account_deletion_email_body}
    Close All App Instances
    Setup App Environment
    Verify Patient Has No Multiclinic
    [Teardown]    Patient can disconnect from a clinic - Multi Clinic - Test Teardown

Patient Can Disconnect From Clinic After Answering Questionnaire
    [Documentation]    Patient deletion performance is still under assessment. For test clinics used in this test suite, timeout is set to maximum 120s.
    [Tags]    nms9-ver-136-3    defect    native-web
    [Setup]    Setup App Environment
    Create New Patient To Delete
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse    ${patient_education_clinic}[manager_email]
        Search Patient By Identity Code    ${patient_ssn}
        Navigate To Questionnaires Tab
        Add Questionnaire To Schedule    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
        Close Browser
    ELSE
        Send QOL 15D Questionnaire Via API To Patient For Today
    END
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire Button
    questionnaires.Complete QOL Questionnaire
    questionnaires.Save Questionnaire    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    Delete All Messages In Server
    ...    ${mailosaur_keys}[0]
    ...    ${mailosaur_keys}[1]
    Disconnect From Clinic
    False Login    patient    ${patient_email}
    IF    'native' not in '${ENVIRONMENT}'
        Close Browser
        Login As Nurse    ${patient_education_clinic}[manager_email]
        Delete Patient Account    ${patient_email}
        Patient Received An Email About A New Message
        ...    ${patient_email}
        ...    ${account_deletion_email_subject}
        ...    ${account_deletion_email_body}
        ...    check_link=no
    ELSE
        Remove Patient As Test Teardown
        ...    ${patient_email}
        ...    ${patient_education_clinic}[manager_email]
        ...    ${PATIENT_EDUCATION_CLINIC_ID}
        ...    ${PATIENT_EDUCATION_EHR_TOKEN}
    END

Patient Created Via EHR Can Be Deleted Successfully
    [Documentation]    Test case was added due to a defect NOONA-24388
    ...                Defect was deleting patients created via EHR was not successful
    [Tags]    nms9-ver-136-4     defect
    Send Candidate Request
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${tunit}
    ${random_string}    Generate Random String    8    [LOWER]
    Set Test Variable
    ...    ${patient_mailosaur_email}
    ...    ${random_string}@${f02p02_email_keys}[0].mailosaur.net
    Login As Nurse    ${appointment_clinic}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Wait Until Page Contains    Patient created by the EHR
    Try To Click Element    ${send_user_account_button}
    Click Send User Account - Fill Information And Click Invite Patient
    Change Password       password=${DEFAULT_PASSWORD}
    Login As Patient      ${patient_mailosaur_email}
    Wait Until Element Is Visible    ${diary_main_header}
    Set Test Variable
    ...    @{mailosaur_keys}
    ...    ${f02p02_email_keys}[0]
    ...    ${f02p02_email_keys}[1]
    Delete All Messages In Server
    ...    ${mailosaur_keys}[0]
    ...    ${mailosaur_keys}[1]
    Disconnect From Clinic
    False Login       patient     ${patient_mailosaur_email}
    Login As Nurse    ${appointment_clinic}[manager_email]
    Delete Patient Account     ${patient_mailosaur_email}
    Patient Received An Email About A New Message
    ...    ${patient_mailosaur_email}
    ...    ${account_deletion_email_subject}
    ...    ${account_deletion_email_body}



*** Keywords ***
Create New Patient To Delete
    Set Email Values
    Add An Activated Patient Under Patient Education Clinic
    ...    ${new_email}
    ...    module=${CHEMO_18_SYMPTOMS}
    ...    mailosaur=${mailosaur_keys}[0]

Set Email Values
    Set Email And Delete Previous Messages    ${f02p02_email_keys}[0]    ${f02p02_email_keys}[1]
    ${now}    Get Current Date    result_format=-%H%M%S
    Set Test Variable    ${new_email}    f02p02${now}

Verify Patient Has No Multiclinic
    Login As Patient    ${patient_email}
    Wait Until Page Contains    Diary
    Wait Until Page Contains    Welcome to Noona
    Page Should Not Contain Element    ${clinic_name_header_button}
    Try To Click More Button
    Text Should Not Be In The Page    ${second_clinic_name}
    Text Should Be In The Page    ${first_clinic_name}

Create A New SPMC Patient
    Set Email And Delete Previous Messages    ${f02p02_email_keys}[0]    ${f02p02_email_keys}[1]
    Create An Activated/Activated Patient On Multi-Clinic Via API With Mailosaur Email
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    ${automated_tests_clinic}[tunit]
    ...    ${automated_tests_clinic}[default_user]
    ...    ${appointment_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_CLINIC_SUB_ID_CARETEAM_2}

Remove SPMC Patient From One Clinic Via API
    IF    'native' not in '${ENVIRONMENT}'
        Switch Clinic From Clinic Header    ${second_clinic_name}
        Wait Until Page Does Not Contain    ${switch_confirmation_text}
        Disconnect From Clinic
        # nms9-ver-166
        Login As Nurse    ${appointment_clinic}[manager_email]
        Delete Patient Account    ${patient_email}
    ELSE
        Login As Patient Via API    ${patient_email}    ${APPOINTMENT_CLINIC_ID}
        Send Delete Patient Request
        Set Test Variable    ${login_token}    ${APPOINTMENT_USER_TOKEN}
        Login As Nurse Via API    ${appointment_clinic}[manager_email]    ${APPOINTMENT_CLINIC_ID}
        # add Run Keyword And Ignore Error to continue execution despite of 504 (NOONA-22713), deletion email will still be checked
        Run Keyword And Ignore Error    Remove Patient Via API    ${patient_id}    ${patient_user_id}
    END

Remove SPMC Patient From Clinic Via API - Temporary Keyword
    [Documentation]    Use this keyword in test case nms9-ver-136-2 until NOONA-22713 is resolved
    Login As Patient Via API    ${patient_email}    ${APPOINTMENT_CLINIC_ID}
    Send Delete Patient Request
    Set Test Variable    ${login_token}    ${APPOINTMENT_USER_TOKEN}
    Login As Nurse Via API    ${appointment_clinic}[manager_email]    ${APPOINTMENT_CLINIC_ID}
    Remove Patient Via API    ${patient_id}    ${patient_user_id}

Patient can disconnect from a clinic - Multi Clinic - Test Setup
    Setup App Environment
    Create A New SPMC Patient

Patient can disconnect from a clinic - Multi Clinic - Test Teardown
    Close All App Instances
    Set Email And Delete Previous Messages    ${f02p02_email_keys}[0]    ${f02p02_email_keys}[1]

Click Send User Account - Fill Information And Click Invite Patient
    Wait Until Page Contains Element    ${send_invitation_icd_dropdown}
    Clear Element Text    ${email_field}
    Try To Input Text     ${email_field}     ${patient_mailosaur_email}
    Set Focus To Element    ${send_invitation_icd_dropdown}
    Try To Click Element    ${send_invitation_icd_dropdown}
    Scroll Element Into View    ${send_user_account_icd_first_option}
    Try To Click Element    ${send_user_account_icd_first_option}
    Try To Click Element    ${send_invite_button}
    Page should not contain      Email is already in use
    Wait Until Page Contains     Invitation sent
    Try To Click Banner Message
