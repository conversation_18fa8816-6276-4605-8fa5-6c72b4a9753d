*** Settings ***
Documentation       F07P13 Patient can display privacy notice and register data file

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}native-web_privacy_about.resource
Resource            ${EXECDIR}${/}resources/patient/patient_screens.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07p13    patient-web


*** Test Cases ***
Main - Patient Can View Privacy Notice
    [Tags]    nms9-ver-236    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f07p13_patient}[email]
    Select Privacy Notice From More Menu
    Verify That Content Displayed Is Correct
    Close Privacy Page And Return To Main Menu

Main - Patient Can View Privacy Statement - EMEA
    [Tags]    nms9-ver-455    native-web    emea
    [Setup]    Setup App Environment
    Login As Patient    ${f07p13_patient}[email]
    Select Privacy Notice From More Menu
    Verify That Europe Privacy Statement Content Is Correct
    Close Privacy Page And Return To Main Menu

Extension A - Non-logged In User Can Display Privacy Notice
    [Tags]    nms9-ver-237
    Open Page    ${PATIENT_LOGIN_URL}
    ${languages}    Create List
    ...    English
    ...    Deutsch
    ...    Español
    ...    Suomi
    ...    Français
    ...    Italiano
    ...    Nederlands
    ...    Norsk
    ...    Português
    ...    Svenska
    ...    Türkçe
    ...    Polski
    FOR    ${language}    IN    @{languages}
        Prepare Page For Checking Privacy
        Check Privacy Per Language    ${language}
    END

Extension A - Non-logged In User Can Display Privacy Notice - EMEA
    [Tags]    nms9-ver-457    emea
    Open Page    ${PATIENT_LOGIN_URL}
    Accept All Cookies If Visible
    Check Privacy Per Language    English    emea
    Check Privacy Per Language    Deutsch    emea
    Check Privacy Per Language    Español    emea
    Check Privacy Per Language    Suomi    emea
    Check Privacy Per Language    Français    emea
    Check Privacy Per Language    Italiano    emea
    Check Privacy Per Language    Nederlands    emea
    Check Privacy Per Language    Norsk    emea
    Check Privacy Per Language    Português    emea
    Check Privacy Per Language    Svenska    emea
    Check Privacy Per Language    Türkçe    emea
    Check Privacy Per Language    Polski    emea


*** Keywords ***
Check Privacy Per Language
    [Arguments]    ${language}    ${region}=${EMPTY}
    IF    '${region}' == 'emea'
        ${content_language}    Set Variable If    '${language}'=='English'    Europe_Noona_PrivacyStatement_EN_GB
        ...    '${language}'=='Deutsch'    Europe_Noona_PrivacyStatement_DE
        ...    '${language}'=='Español'    Europe_Noona_PrivacyStatement_ES
        ...    '${language}'=='Suomi'    Europe_Noona_PrivacyStatement_FI
        ...    '${language}'=='Français'    Europe_Noona_PrivacyStatement_FR
        ...    '${language}'=='Italiano'    Europe_Noona_PrivacyStatement_IT
        ...    '${language}'=='Nederlands'    Europe_Noona_PrivacyStatement_NL
        ...    '${language}'=='Norsk'    Europe_Noona_PrivacyStatement_NO
        ...    '${language}'=='Português'    Europe_Noona_PrivacyStatement_PT
        ...    '${language}'=='Svenska'    Europe_Noona_PrivacyStatement_SV
        ...    '${language}'=='Türkçe'    Europe_Noona_PrivacyStatement_TR
        ...    '${language}'=='Polski'    Europe_Noona_PrivacyStatement_PL
    ELSE
        ${content_language}    Set Variable If    '${language}'=='English'    PrivacyPolicy_en_GB
        ...    '${language}'=='Deutsch'    PrivacyPolicy_en_GB_DE
        ...    '${language}'=='Español'    PrivacyPolicy_en_GB_ES
        ...    '${language}'=='Suomi'    PrivacyPolicy_en_GB_FI
        ...    '${language}'=='Français'    PrivacyPolicy_en_GB_FR
        ...    '${language}'=='Italiano'    PrivacyPolicy_en_GB_IT
        ...    '${language}'=='Nederlands'    PrivacyPolicy_en_GB_NL
        ...    '${language}'=='Norsk'    PrivacyPolicy_en_GB_NO
        ...    '${language}'=='Português'    PrivacyPolicy_en_GB_PT
        ...    '${language}'=='Svenska'    PrivacyPolicy_en_GB_SV
        ...    '${language}'=='Türkçe'    PrivacyPolicy_en_GB_TR
        ...    '${language}'=='Polski'    PrivacyPolicy_en_GB_PL
    END
    Set Test Variable    ${LANGUAGE_LABEL}    ${language}
    Select Language    ${language_value}
    Open Privacy Notice From Front Page
    Verify Privacy Statement Content    ${content_language}
    Close Privacy Page And Return To Front Page

Prepare Page For Checking Privacy
    Wait Until Noona Loader Is Not Visible
    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${landing_page_login_button}
