*** Settings ***
Documentation       F02P05-1 Pat<PERSON> can manage delegate user accounts
...                 Preconditions:
...                 - Medical records set to be visible in the clinic settings
...                 - Patient has registered an account
...                 - The patient is logged in
...                 - For Reset and Delete test cases, a delegate user should be
...                 existing already. Thus, Add A Delegate User test case has passed.
...
...                 F02P05-2 Delegate user can activate account
...
...                 Preconditions:
...                 - Patient has added a delegate user with a valid email address
...                 - Delegate user has received the activation email

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}common.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02p05-1    usecase-f02p05-2    patient-web


*** Test Cases ***
Extension A - Adding a delegate user
    [Documentation]    Also includes:
    ...    nms9-ver-167    Main Success Scenario- Delegate User Section Is Visible
    ...    nms9-ver-170    Extension C - Deleting a delegate user
    ...    nms9-ver-171    F02P05-2 - Delegate user can activate account - Main success scenario
    ...    Note: NOONA-18986 Page is not loading when accessing links sent to delegate user for setting password / password reset
    [Tags]    nms9-ver-167    nms9-ver-168    nms9-ver-170    nms9-ver-171    patient-email-link    native-web
    [Setup]    Set Application On Environment
    Set Test Variable    @{mailosaur_keys}    q5kckg0r    1MoQE7LJPtQStIg5
    Add An Activated Patient Under Default Clinic    f02p05    mailosaur=${mailosaur_keys}[0]
    Login As Patient And Go To Profile    ${patient_email}
    # nms9-ver-167
    Delegate Users Section Is Displayed In My Profile
    # nms9-ver-168
    ${delegate_email}    Set Variable    ${family_name}.delegate@${mailosaur_keys}[0].mailosaur.net
    Add Delegate User
    ...    ${first_name} delegate
    ...    ${family_name} delegate
    ...    ${delegate_email}
    Go To Diary
    Prepare Next Login
    @{message_data}    Patient Received An Email About A New Message
    ...    ${delegate_email}
    ...    ${automated_tests_clinic}[name] invites you to use Noona
    ...    ${first_name}${SPACE}${family_name} wants to share their medical records with you.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    Set Test Variable    ${link}    ${message_data}[2]
    ${link}    Convert To String    ${link}
    # nms9-ver-171 -    not included in native app run
    IF    'native' not in '${ENVIRONMENT}'
        Activate Delegate User Account    ${link}    ${delegate_email}
        Verify Delegate User Can Login    ${delegate_email}
        Logout As Delegate User
        Wait Until Element Is Visible    ${landing_page_login_button}
        Try To Click Element    ${landing_page_login_button}
    END
    # nms9-ver-170
    Re-login And Go To Profile    ${patient_email}
    Delete Delegate User    ${delegate_email}
    Delegate User Is Deleted Successfully    ${delegate_email}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Reset the password of a delegate user
    [Tags]    nms9-ver-169    patient-email-link    native-web
    [Setup]    Set Application On Environment
    Set Email And Delete Previous Messages    q5kckg0r    1MoQE7LJPtQStIg5
    Login As Patient And Go To Profile    ${f02p05_patient_extb}[email]
    Reset Delegate User Password    ${f02p05_patient_extb}[delegate]
    Logout As Patient
    @{message_data}    Patient Received An Email About Reset Password
    ...    ${f02p05_patient_extb}[delegate]
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/s/
    Set Test Variable    ${link}    ${message_data}[1]
    IF    'native' not in '${ENVIRONMENT}'
        ${link}    Convert To String    ${link}
        Delegate Updates Password From Reset Password Link    ${link}    ${f02p05_patient_extb}[delegate]
        Verify Delegate User Can Login    ${f02p05_patient_extb}[delegate]
    END
    [Teardown]    Close All App Instances

F02P05-2 Extension A - Expired password/account activation link Delegate user (web / app)
    [Tags]    nms9-ver-548    patient-email-link    time-constraint    manual
    Set Test Variable    @{mailosaur_keys}    ${time_constraint_keys}[0]    ${time_constraint_keys}[1]
    Add An Activated Patient Under Time Constraint Clinic
    ...    f02p05-2exta
    ...    ${TIME_CONSTRAINT_CLINIC_SUB_ID_F02P05_2}
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Patient And Go To Profile    ${patient_email}
    ${delegate_email}    Set Variable    ${family_name}.delegate@${mailosaur_keys}[0].mailosaur.net
    Add Delegate User
    ...    ${first_name} delegate
    ...    ${family_name} delegate
    ...    ${delegate_email}
    #TODO: After 2 weeks, follow the use case steps
    #1. The delegate’s password has expired (delegate user hasn't set their password within two weeks since receiving the account activation link)
    #2. Delegate User clicks on the Learn more button containing the link to go activate the account (or copies the url to browser)
    #3. Noona landing page is displayed
    #Note: To get a new login link to activate the account the delegate user has to be removed and added again to the account by the patient.


*** Keywords ***
Login As Patient And Go To Profile
    [Arguments]    ${patient_email}
    Login As Patient    ${patient_email}
    Go To Clinic Preferences
