*** Settings ***
Documentation       F05P01 Patient has to be able to send anonymous feedback to support
...                 Preconditions:
...                 - The patient is logged in.
...                 NOTE: This was updated to manual to exclude from the day run as this feature is already removed from requirements

Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}feedback.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f05p01    patient-web    manual


*** Test Cases ***
Patient can send anonymous feedback to support
    [Tags]    nms9-ver-215    manual
    [Setup]    Setup App Environment
    Login As Patient    ${f07p13_patient}[email]    none
    Patient navigates to Feedback
    The patient selects their level of satisfaction
    #    Optionally, the patient enters a feedback message
    The patient selects if Noona (the company) can contact the patient concerning the feedback
    The patient clicks the "Save" button of the feedback dialog
    Noona displays a notification toaster message

Extension A - Patient is asked to give feedback after contacting clinic (web / app)
    [Tags]    nms9-ver-216    manual
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    usecase-f05p01exta    module=${CHEMO_18_SYMPTOMS}
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Ask About Symptom Option
    Complete Other Symptom Form    Mild
    Click Next Button
    Send Symptom Questionnaire To Clinic
    Go To Patient Homepage
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        Select Symptom And Contact Patient With    Question
        Login As Patient    ${patient_email}
    ELSE
        Sleep    1s    # needed for case to reach clinic side
        Get First Patient Case via API    ${patient_id}
        Send Message Connected To First Case Via API    ${first_case_id}    18
    END
    Navigate To Clinic
    Select Latest Clinic Message
    Close Clinic's Message Dialog
    Patient Feedback Dialog Is Displayed
    Patient Answers Feedback    happy    f05p01 ext A test    ok_to_contact=yes
    Wait Until Page Contains    Thank you for your feedback.
    IF    'native' not in '${ENVIRONMENT}'
        Sleep    1s
        Location Should Contain    /patient/#/clinic
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Patient is asked to give feedback after answering an AEQ and adding 4 symptom entries to the diary (web / app)
    [Tags]    nms9-ver-217    manual
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Set New Patient Data With Random Data
        Set To Dictionary    ${patient_dict}    care team=f05p01 care team    module=Chemotherapy 18 symptoms
        Create New Patient With Robot
        Change Password    password=${DEFAULT_PASSWORD}
        Navigate To Questionnaires Tab
        Add Questionnaire To Schedule    Baseline questionnaire
        Close Browser
        Login As Patient    ${patient_dict}[email]
    ELSE
        Add An Activated Patient Under Default Clinic    usecase-f05p01    module=${CHEMO_18_SYMPTOMS}
        Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
        Login As Patient    ${patient_email}
    END
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Answer Baseline Questionnaire With Severe Other Symptom And No Distress
    Emergency Priority Symptom Is Displayed
    Add Difficulty Eating Symptom To Diary
    Patient Feedback Dialog Is Displayed
    Patient Answers Feedback    content    test patient feedback nms9-VER-217
    IF    'native' not in '${ENVIRONMENT}'
        Location Should Contain    ${PATIENT_PATH}#/diary-timeline
    ELSE
        Remove Patient As Test Teardown
        ...    ${patient_email}
        ...    ${automated_tests_clinic}[default_manager]
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
        ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    END

Extension C - Patient is asked to give feedback after answering two AEQs (web / app)
    [Tags]    nms9-ver-218    manual
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic
    ...    f05p01-extc
    ...    module=${CHEMO_18_SYMPTOMS}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Answer Symptom Questionnaire As Patient
    Send Symptom Questionnaire Via Api To Patient    ${treatment_visit_quest}
    Set Application On Environment
    Mark Symptom As Up To Date
    Click View Your Diary
    Patient Feedback Dialog Is Displayed
    Patient Answers Feedback    happy    test patient feedback
    IF    'native' not in '${ENVIRONMENT}'
        Location Should Contain    ${PATIENT_PATH}#/diary-timeline
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension D - Patient is asked to give feedback after answering adding 5 symptom entries to the diary (web / app)
    [Tags]    nms9-ver-219    manual
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic
    ...    f05p01-extd
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Patient    ${patient_email}
    Add Difficulty Eating Symptom To Diary
    Add Nausea Or Vomiting Symptom To Diary
    Add Respiratory Symptoms To Diary
    Add Changes In Mood Or Emotions Symptoms To Diary
    Add Hair Changes Symptoms To Diary
    Patient Feedback Dialog Is Displayed
    Patient Answers Feedback    unhappy    f05p01 ext D test    ok_to_contact=yes
    IF    'native' not in '${ENVIRONMENT}'
        Location Should Contain    ${PATIENT_PATH}#/diary-timeline
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Answer Symptom Questionnaire As Patient
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Select Yes For Symptoms    Other symptom
    Try To Click Element    ${aeq_questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    Evaluate Previous Symptom    previous_symptom=new_entry
    questionnaires.Check And Write To Text Area
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Mild
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Try To Click Element    ${aeq_questionnaire_next_button}
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    Thank you for your answers!
    Close All App Instances

Mark Symptom As Up To Date
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Select Yes For Symptoms    Other symptom
    Changes In Gen State Of Health Is Displayed
    Evaluate Previous Symptom    previous_symptom=up_to_date
    Send Symptom Questionnaire To Clinic

Answer Baseline Questionnaire With Severe Other Symptom And No Distress
    [Documentation]    this keyword is specific to this test case
    Select Yes For Symptoms    Other symptom
    Try To Click Element    ${aeq_questionnaire_next_button}
    Try To Click Element    ${symptom_questionnaire_radio_1}
    Try To Click Element    ${aeq_questionnaire_next_button}
    Gen State Of Health Distress Question Is Displayed
    Rate With Vertical Slider
    Try To Click Element    ${aeq_questionnaire_next_button}
    Try To Click Element    ${aeq_questionnaire_next_button}
    Evaluate Previous Symptom    previous_symptom=new_entry
    questionnaires.Check And Write To Text Area
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Severe
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Try To Click Element    ${aeq_questionnaire_next_button}
    Send Symptom Questionnaire To Clinic
