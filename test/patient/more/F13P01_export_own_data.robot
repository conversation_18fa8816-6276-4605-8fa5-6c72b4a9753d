*** Settings ***
Documentation       F13P01 Patient can export own data
...                 Preconditions:
...                 - User is logged in the web service using Chrome
...                 - Feature is not available in the native application

Resource            ${EXECDIR}${/}resources${/}shared_login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}file_exports.resource
Library             ${EXECDIR}${/}resources${/}libraries${/}verify_downloads.py
Library             OperatingSystem

Suite Setup         Run Keywords    Set Libraries Order
...                     AND    Prepare Chrome for Downloads
Suite Teardown      Close All Local Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f13p01    chrome    patient-web


*** Variables ***
${burger-menu}                  navigation-more-link
${my-profile}                   //*[@data-testid='more-link-profile']
${pdf-export-button}            //button/div/div/div/div[contains(text(), 'PDF')]
${csv-export-button}            //button/div/div/div/div[contains(text(), 'CSV')]
${dialog-confirm-button}        ok-confirm
${progress-bar}                 css:div.progress
${download-wait}                2.5 min
${progress-cancel-button}       //*[@id="progress-modal"]//div[2]/ds-button/button/div/div
${symptom-string}               Symptom
${confirm_modal}                confirm-modal


*** Test Cases ***
Main success scenario - Patient can export own data
    [Tags]     nms9-ver-284
    # Patient can export own data as PDF
    Login To Noona    patient    ${f13p01_patient}[email]    open_browser=False
    Go To Clinic Preferences
    Select Export diary to a PDF file to download diary content in PDF file
    Confirmation Dialog Is Displayed And User Selects Confirm
    Wait Until Page Contains    PDF report downloaded    40s
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${toast_message}
    IF    ${status}    Try To Click Banner Message
    Select Export diary to a PDF file to download diary content in PDF file
    Confirmation Dialog Is Displayed And User Selects Confirm
    User Is Able To Cancel The PDF Generation By Selecting Cancel
    IF    '${remote_url_exists}'=='False'
        PDF File With Patient's Diary Content Is Downloaded To User's Computer
    END
    # Patient can export own data as CSV
    Select Export Symptoms And Messages To CSV Files To Download Symptom And Message Content As CSV Files
    Confirmation Dialog Is Displayed And User Selects Confirm
    IF    '${remote_url_exists}'=='False'
        Zip File Containing Patient's Symptoms And Messages As Separate CSV Files Is Downloaded To User's Computer
    END


*** Keywords ***
Select Export diary to a PDF file to download diary content in PDF file
    Try To Click Element    ${pdf-export-button}

Confirmation Dialog Is Displayed And User Selects Confirm
    Wait Until Element Is Visible    ${confirm_modal}
    Try To Click Element    ${dialog-confirm-button}

User Is Able To Cancel The PDF Generation By Selecting Cancel
    Wait Until Element Is Enabled    ${progress-bar}
    Wait Until Element Is Enabled    ${progress-cancel-button}
    Click Element    ${progress-cancel-button}
    Wait Until Element Is Not Visible    ${confirm_modal}

PDF File With Patient's Diary Content Is Downloaded To User's Computer
    Try To Click Element    ${pdf-export-button}
    Wait Until Element Is Visible    ${confirm_modal}
    Try To Click Element    ${dialog-confirm-button}
    Wait Until Keyword Succeeds    ${download-wait}    3    Directory Should Exist    ${downloads-dir}
    Wait Until Keyword Succeeds    ${download-wait}    3    File Should Exist    ${downloads-dir}${/}*.pdf
    Diary Download Should Contain PDF Signature    ${downloads-dir}

Select Export Symptoms And Messages To CSV Files To Download Symptom And Message Content As CSV Files
    Run Keyword And Ignore Error    Try To Click Element    ${progress-cancel-button}
    Go To Clinic Preferences
    Try To Click Element    ${csv-export-button}

Zip File Containing Patient's Symptoms And Messages As Separate CSV Files Is Downloaded To User's Computer
    Scroll Element Into View    css:div.page-header
    Wait Until Keyword Succeeds    ${download-wait}    3    Directory Should Exist    ${downloads-dir}
    Wait Until Keyword Succeeds    ${download-wait}    3    File Should Exist    ${downloads-dir}${/}*.zip
    Symptoms Download Should Contain String    ${downloads-dir}    ${symptom-string}
