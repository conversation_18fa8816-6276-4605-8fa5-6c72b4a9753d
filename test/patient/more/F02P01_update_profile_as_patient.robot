*** Settings ***
Documentation       F02P01 Patient can update personal profile
...                 Main Success Scenario: Patient updates personal profile
...                 Not yet testable through automation:
...                 Extension A - Remove a connected device (web / app)
...                 Extension B - Set backup channel if native application notification is not delivered (web / app)

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}account_preferences.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02p01    patient-web


*** Variables ***
${original_phone_number}        +********
${new_phone_number}             +*********
${preferences_saved_banner}     Preferences saved
${preferences_saved_suomi}      Asetukset tallennettu
${time_zone}                    Europe/Helsinki
${remote_time_zone}             Africa/Abidjan

*** Test Cases ***
Main success scenario - Update Account Preferences
    [Tags]    nms9-ver-165-1    nms9-ver-165    native-web
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f02p01-1
    Login As Patient    ${patient_email}
    Go To Account Preferences
    Update Email Address    ${first_name}.${family_name}-<EMAIL>
    Email address is automatically cast to lower case
    Update Phone Number    ${new_phone_number}
    Save Preferences And Input Password If Needed
    Change Patient's Language    Suomi
    Verify Profile Is Updated
    Reset Back To Original Settings
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown    ${patient_email}    ${automated_tests_clinic}[default_manager]    ${AUTOMATED_TESTS_CLINIC_ID}    ${AUTOMATED_TESTS_EHR_TOKEN}

Main success scenario - Update Patient Language Via Profile
    [Documentation]    Extension of main scenario
    [Tags]    nms9-ver-165-2    nms9-ver-165
    Add An Activated Patient Under Default Clinic    f02p01-2
    Login As Patient    ${patient_email}
    Go To Account Preferences
    Wait Until Keyword Succeeds    3x    1s    Get Original Field Values Account Preferences
    Go To Clinic Preferences
    Wait Until Keyword Succeeds    3x    1s    Get Original Field Values Clinic Preferences
    Wait Until Keyword Succeeds    3x    1s    Select Patient Language    Suomi
    Click Account Preferences Save Button
    Wait Until Page Contains    Asetukset tallennettu
    Try To Click Banner Message
    Wait Until Keyword Succeeds    3x    1s    Get Current Values Clinic Preferences
    Verify Clinic Preferences Language Is Updated
    Go To Account Preferences
    Wait Until Keyword Succeeds    3x    1s    Get Current Values Account Preferences
    Compare Account Preferences Values
    Close Browser
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Choose General Information Tab
    Verify Language Is Updated In General Info Tab
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown    ${patient_email}    ${automated_tests_clinic}[default_manager]    ${AUTOMATED_TESTS_CLINIC_ID}    ${AUTOMATED_TESTS_EHR_TOKEN}

Main success scenario - Patient Can Change Their Password From Account Preferences Page
    [Documentation]    a new patient is used on each tc run to avoid having a long list of 'connected device'
    ...                on account preferences page as a result of repeated execution with a static patient
    [Tags]     nms9-ver-165-3      nms9-ver-165     native-web
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic     f02p01-pwd
    Login As Patient     ${patient_email}
    Go To Account Preferences
    Select Change Password
    Patient Is Asked To Enter Old And New Password
    Patient Presses Save
    Change Password Modal Is Closed And Patient Is Back To Account Settings Page
    Close All App Instances
    Patient Can Login Successfully With The Updated Password
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    patient_password=${NEW_PASSWORD}

Main success scenario - Patient Can See Last Login Time
    [Tags]    nms9-ver-165-4    nms9-ver-165
    Login As Patient    ${f02p01_last_login_patient}[email]
    Patient Sees Last Login Time
    Logout As Patient
    Close All App Instances
    Login As Patient    ${f02p01_last_login_patient}[email]
    Go To Account Preferences
    Verify Last Login Time For Patient
    Close All App Instances

Extension B - Set backup channel if native application notification is not delivered - Email And SMS
    [Tags]    nms9-ver-13-1    nms9-ver-13    patient-2fa    sms
    Set Email And Delete Previous Messages    txjnyfms    SL8M3zDK6o8UWOsk3PgNtOot3jJkXCmg
    Add An Activated Patient Under Appointment Clinic    f02p01-b1    mailosaur=${mailosaur_keys}[0]
    Login And Update Phone Number    ${mailosaur_number}
    Logout As Patient
    Close Browser
    Delete All SMS From Mailosaur
    Delete All Messages In Server    txjnyfms    SL8M3zDK6o8UWOsk3PgNtOot3jJkXCmg    # email
    Send Contact Patient Request    11
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${appointment_clinic}[name]
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    Patient Received SMS About A New Message    ${appointment_clinic}[name]
    [Teardown]    Login And Update Phone Number    ${original_phone_number}    # so it won't send too many messages to mailosaur number

Extension B - Set backup channel if native application notification is not delivered - Email
    [Tags]    nms9-ver-13-2    nms9-ver-13    sms
    Set Email And Delete Previous Messages    txjnyfms    SL8M3zDK6o8UWOsk3PgNtOot3jJkXCmg
    Add An Activated Patient Under Appointment Clinic    f02p01-b2    mailosaur=${mailosaur_keys}[0]
    Login And Update Phone Number    ${mailosaur_number}
    Login And Update Notification Channel    Email
    Logout As Patient
    Close Browser
    Delete All SMS From Mailosaur
    Delete All Messages In Server    txjnyfms    SL8M3zDK6o8UWOsk3PgNtOot3jJkXCmg    # email
    Send Contact Patient Request    11
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${appointment_clinic}[name]
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    ${status}    Run Keyword And Return Status
    ...    Patient Received SMS About A New Message
    ...    ${appointment_clinic}[name]
    Should Be Equal    ${status}    ${FALSE}
    [Teardown]    Login And Update Phone Number    ${original_phone_number}    # so it won't send too many messages to mailosaur number

Extension B - Set backup channel if native application notification is not delivered - SMS
    [Tags]    nms9-ver-13-3    nms9-ver-13    patient-2fa    sms
    Set Email And Delete Previous Messages    txjnyfms    SL8M3zDK6o8UWOsk3PgNtOot3jJkXCmg    # email
    Delete All SMS From Mailosaur
    Add An Activated Patient Under Appointment Clinic    f02p01-b3    mailosaur=${mailosaur_keys}[0]
    Login And Update Phone Number    ${mailosaur_number}
    Login And Update Notification Channel    SMS
    Logout As Patient
    Close Browser
    Delete All SMS From Mailosaur
    Send Contact Patient Request    11
    Patient Received SMS About A New Message    ${appointment_clinic}[name]
    ${status}    Run Keyword And Return Status    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${appointment_clinic}[name]
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    Should Be Equal    ${status}    ${FALSE}
    [Teardown]    Login And Update Phone Number    ${original_phone_number}    # so it won't send too many messages to mailosaur number


*** Keywords ***
Verify Profile Is Updated
    Correct Language Is Selected
    Go To Account Preferences
    Updated Email Is Correct
    Updated Contact Number Is Correct

Reset Back To Original Settings
    Change Patient's Language    English
    Go To Account Preferences
    Wait Until Element Is Visible    ${account_preferences_email_field}
    ${email_value}    Get Email Value
    ${number_value}    Get Phone Number Value
    ${email_updated}    Run Keyword And Return Status    Evaluate    '${email_value}'!='${PREVIOUS_EMAIL}'
    ${number_updated}    Run Keyword And Return Status    Evaluate    '${number_value}'!='${PREVIOUS_NUMBER}'
    IF    ${email_updated}    Update Email Address    ${PREVIOUS_EMAIL}
    IF    ${number_updated}    Update Phone Number    ${PREVIOUS_NUMBER}
    IF    ${email_updated} or ${number_updated}
        Click Clinic Preferences Save Button
        Input Password To Update Clinic Preferences
        Wait Until Page Contains    ${preferences_saved_banner}    timeout=6s
        Try To Click Banner Message
    END

Change Patient's Language
    [Arguments]    ${language}
    Go To Clinic Preferences
    Select Patient Language    ${language}
    Sleep    1
    Correct Language Is Selected
    Click Clinic Preferences Save Button
    IF    '${language}'=='English'
        Wait Until Page Contains    ${preferences_saved_banner}
    ELSE IF    '${language}'=='Suomi'
        Wait Until Page Contains    ${preferences_saved_suomi}
    ELSE
        Wait Until Element Is Visible    ${banner_toast_message}
    END
    Try To Click Banner Message

Login And Update Phone Number
    [Arguments]    ${number}
    Login As Patient    ${patient_email}
    Go To Account Preferences
    Update Phone Number    ${number}
    Save Preferences And Input Password If Needed

Login And Update Notification Channel
    [Arguments]    ${channel}
    Login As Patient    ${patient_email}
    Go To Account Preferences
    Select Notification Channel    ${channel}
    Save Preferences And Input Password If Needed    password=no

Verify Clinic Preferences Language Is Updated
    Correct Language Is Selected

Select Change Password
    Scroll Element Into View              ${save_profile_button}
    Try To Click Element                  xpath=${change_password_link}
    Wait Until Page Contains Element      ${pwd_change_modal_cancel}

Patient Is Asked To Enter Old And New Password
    ${random_string_password}    Generate Random String    8
    Set Test Variable      ${NEW_PASSWORD}    ${random_string_password}-1
    Try To Input Text      ${old_password_field}     ${DEFAULT_PASSWORD}
    Try To Input Text      ${new_password_field}     ${NEW_PASSWORD}
    Try To Input Text      ${new_pwd_again_field}    ${NEW_PASSWORD}

Patient Presses Save
    Element Should Be Enabled      ${pwd_change_modal_save}
    Try To Click Element           ${pwd_change_modal_save}

Change Password Modal Is Closed And Patient Is Back To Account Settings Page
    Wait Until Page Contains           Password changed
    Wait Until Element Is Visible      ${account_preferences_header}

Patient Can Login Successfully With The Updated Password
    [Documentation]    login with new password check is done with api to reduce tc run time
    Set Application On Environment
    Login As Patient Via API
    ...    ${patient_email}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    patient_password=${NEW_PASSWORD}

Patient Sees Last Login Time
    ${time}     Get Current Date
    ${current_date_time}      Convert Date      ${time}     result_format=%d.%m.%Y %H:%M
    Set Test Variable         ${current_date_time}
    Go To Account Preferences

Verify Last Login Time For Patient
    ${displayed_last_login}      Get Text      xpath=${last_login_element}
    ${remote_url_exists}     Run Keyword And Return Status      Variable Should Exist      ${REMOTE_URL}
    IF    ${remote_url_exists}
        ${expected}    Set Variable    Last Login: ${current_date_time} (Time Zone: ${remote_time_zone})
    ELSE
        ${expected}    Set Variable    Last Login: ${current_date_time} (Time Zone: ${time_zone})
    END
    ${status}     Run Keyword And Return Status    Should Be Equal    ${displayed_last_login}    ${expected}
    Run Keyword If    not ${status}    Capture Page Screenshot
    Run Keyword If    not ${status}    Fail    The actual Last login date and time (${displayed_last_login}) does not match expected (${expected})