*** Settings ***
Documentation       F07P01 Patient can display use instructions
...                 Preconditions:
...                 - Patient has logged in
...
...                 Manual content (web / app)
...                 | =Section= | =Web= | =App= | =Standalone user= | =Display criteria= |
...                 | 1. Symptom questionnaires | x | x | - | - |
...                 | 2. Contact clinic | x | x | x (specific version) | Display if "Patient can ask about symptoms" is enabled in clinic settings. |
...                 | 3. Ask about non-clinical topic | x | x | - | Display if "The patient can ask about other issues" is enabled in clinic settings. |
...                 | 4. Medical records | x | x | - | Display if "Should medical records be visible for the patient?" is enabled in the clinic settings. |
...                 | 5. Education documents    | x | x | - | Display if "Clinic can send Patient Education messages to patients" is enabled in the clinic settings. |
...                 | 6. Laboratory reports | x | x | - | Display if "Should laboratory results be visible for the patient?" is enabled in the clinic settings. |
...                 | 7. Diary | x | x | x | - |
...                 | 8. Profile | x | x | x | - |
...                 | 9. Feedback | x | x | x | - |
...                 | 10. Native application | x | x | x | Display if "App promotion" is enabled in environment settings. |
...                 Legal information doesn't have own index number. Test case only verifies text contents, images and logos verification are not in testing scope.

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}instructions.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}account_preferences.resource

Suite Setup         Set Libraries Order
Test Setup          Setup App Environment
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07p01    patient-web    nms9-ver-222


*** Test Cases ***
Patient Can Display Instructions And Close It With Close Button - English
    [Documentation]    Verify all available instructions & legal information in ENG, both on web and native app
    [Tags]    nms9-ver-222-1    native-web
    Login As Patient And Verify Instructions Content    ${f07p01_patient}[email]    English
    [Teardown]    Close All App Instances

Patient Can Display Instructions And Close It With Close Button - Spanish
    [Documentation]    Verify all available instructions & legal information in Spanish, both on web and native app
    [Tags]    nms9-ver-222-2    native-web
    Login As Patient And Verify Instructions Content    ${f07p01_spanish}[email]    Spanish
    [Teardown]    Close All App Instances

Patient Can Display Instructions And Close It With X
    [Tags]    nms9-ver-222-3    native-web
    Login As Patient    ${f07p01_patient}[email]
    Try To Click More Button
    Select Instructions Menu
    Patient Closes Instructions With    X
    [Teardown]    Close All App Instances

Patient Can Display Legal Information And Close It With Close Button
    [Documentation]    Verify Legal Information translation in 10 languages. ENG & ES versions are checked under ver-222-1 & ver-222-2
    [Tags]    nms9-ver-222-4    native-web
    Login As Patient    ${f07p01_patient}[email]
    Switch Language And Verify Legal Information Content In Norsk
    Switch Language And Verify Legal Information Content In Suomi
    Switch Language And Verify Legal Information Content In Türkçe
    Switch Language And Verify Legal Information Content In Svenska
    Switch Language And Verify Legal Information Content In Deutsch
    Switch Language And Verify Legal Information Content In Français
    Switch Language And Verify Legal Information Content In Português
    Switch Language And Verify Legal Information Content In Nederlands
    Switch Language And Verify Legal Information Content In Italiano
    Switch Language And Verify Legal Information Content In Polski
    [Teardown]    Close All App Instances


*** Keywords ***
Login As Patient And Verify Instructions Content
    [Arguments]    ${patient_email}    ${language}
    Login As Patient    ${patient_email}
    IF    "${language}"=="Spanish"
        Go To Clinic Preferences
        Wait Until Element Is Visible    ${section_title}
        ${header}    Get Text    ${section_title}
        IF    '${header}'!='${clinic_preferences_header_spanish}'
            Select Patient Language    Espanol
            Click Clinic Preferences Save Button
            Try To Click Banner Message
        END
    END
    Try To Click More Button
    Select Instructions Menu
    Patient Selects Topics
    ${file_path}    Set Variable If    "${language}"=="English"    instructions
    ...    "${language}"=="Spanish"    instructions_spanish
    Verify Instructions Content    ${file_path}
    Patient Closes Instructions With    Close
    # TODO: refactor this keyword to accomodate other 9 languages (FI, SV, TR, PT, FR, NL, IT, DE, NO)
