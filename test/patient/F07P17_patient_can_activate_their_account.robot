*** Settings ***
Documentation       F07P17 Patient can activate their account

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources/common.resource
Resource            ${EXECDIR}${/}resources/patient/patient_screens.resource
Resource            ${EXECDIR}${/}resources/patient/clinic.resource
Resource            ${EXECDIR}${/}resources/patient/diary.resource
Resource            ${EXECDIR}${/}resources/mailosaur.resource
Resource            ${EXECDIR}${/}resources/nurse/compare_questionnaires.resource
Resource            ${EXECDIR}${/}resources/nurse/general_information.resource
Resource            ${EXECDIR}${/}resources/patient/questionnaires.resource
Resource            ${EXECDIR}${/}resources/patient/pendo_guides.resource
Resource            ${EXECDIR}${/}resources/patient/patient_api_fhir.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          patient-web    usecase-f07p17


*** Test Cases ***
Main success scenario - Patient can activate their account - Email Invitation
    [Tags]    nms9-ver-509-1    nms9-ver-509
    ${new_email}    Create New Patient To Activate    module=${BONE_RADIOTHERAPY}
    @{message_data}    Patient Received Invitation To Use Noona    ${new_email}    ${pendo_test_clinic}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Set Test Variable    ${link}    ${message_data}[0]
    Activate Noona Account As Patient    ${link}    ${new_email}
    Wait Until Location Contains    patient/#/diary-timeline
    Wait Until Page Contains    Noona connects you to your care team
    Close Pendo Guide
    Patient Is Asked Wellbeing In Diary
    Patient Received Successful Account Activation Email    ${new_email}    ${pendo_test_clinic}[name]
    Set Test Variable    ${login_token}    ${PENDO_TEST_CLINIC_TOKEN}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Main success scenario - Patient can activate their account - Candidate - Fill In With New Details
    [Tags]    nms9-ver-509-2    nms9-ver-509
    Set Test Variable    @{mailosaur_keys}    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Generate Random Patient Data    mailosaur=${mailosaur_keys}[0]    name=f07p17-main2
    ${body_json}    Invite Patient As Candidate With Specific Patient Details
    ...    ${PENDO_TEST_CLINIC_TOKEN}
    ...    unitTest
    Invite Candidate To Use Noona    fill_in_with_new_details=yes
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Set Test Variable    ${link}    ${message_data}[0]
    Activate Noona Account As Patient    ${link}    ${patient_email}
    Wait Until Location Contains    patient/#/diary-timeline
    Wait Until Page Contains    Noona connects you to your care team
    Close Pendo Guide
    Patient Is Asked Wellbeing In Diary
    Patient Received Successful Account Activation Email    ${patient_email}    ${pendo_test_clinic}[name]
    Set Test Variable    ${login_token}    ${PENDO_TEST_CLINIC_TOKEN}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Main success scenario - Patient can activate their account - Candidate - Use Existing Details
    [Documentation]    Use the details from candidate Invite
    [Tags]    nms9-ver-509-3    nms9-ver-509
    Set Test Variable    @{mailosaur_keys}    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Generate Random Patient Data    mailosaur=${mailosaur_keys}[0]    name=f07p17-main2
    ${body_json}    Invite Patient As Candidate With Specific Patient Details
    ...    ${PENDO_TEST_CLINIC_TOKEN}
    ...    unitTest
    Invite Candidate To Use Noona    fill_in_with_new_details=no
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Set Test Variable    ${link}    ${message_data}[0]
    Activate Noona Account As Patient    ${link}    ${patient_email}
    Wait Until Location Contains    patient/#/diary-timeline
    Wait Until Page Contains    Noona connects you to your care team
    Close Pendo Guide
    Patient Is Asked Wellbeing In Diary
    Patient Received Successful Account Activation Email    ${patient_email}    ${pendo_test_clinic}[name]
    Set Test Variable    ${login_token}    ${PENDO_TEST_CLINIC_TOKEN}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Main success scenario - Patient can activate their account - Proxy From EMR
    [Tags]    nms9-ver-509-4    nms9-ver-509
    Set Test Variable    @{mailosaur_keys}    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Generate Random Patient Data    mailosaur=${mailosaur_keys}[0]    name=f07p17-main3
    ${body_json}    Invite Patient As Proxy From EMR
    ...    ${PENDO_TEST_CLINIC_TOKEN}
    ...    unitTest
    Login As Nurse    ${pendo_test_clinic}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Choose General Information Tab
    Patient Status Is Correct    Proxy
    Send User Account
    Try To Click Banner Message
    Close Browser
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Set Test Variable    ${link}    ${message_data}[0]
    Activate Noona Account As Patient    ${link}    ${patient_email}
    Wait Until Location Contains    patient/#/diary-timeline
    Wait Until Page Contains    Noona connects you to your care team
    Close Pendo Guide
    Patient Is Asked Wellbeing In Diary
    Patient Received Successful Account Activation Email    ${patient_email}    ${pendo_test_clinic}[name]
    Set Test Variable    ${login_token}    ${PENDO_TEST_CLINIC_TOKEN}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension A.1 — Patient logs in for the first time (account activation) - App
    [Tags]    manual    nms9-ver-410
#    The patient clicks the login link in:
#    Invitation email/SMS
#    Email/SMS notification about Symptom Report
#    Email/SMS notification about QoL
#    Email/SMS notification about new message
#    Email/SMS notification about new results (Lab notification will be implemented with NOONA-20266)
#    Patient reads clinic specific invitation page.
#    Patient enters code received via SMS
#    Unless setting about 2FA is disabled in the clinic settings.
#    Patient starts account activation.
#    Patient enters patient's email address.
#    Unless nurse has set email address.
#    Patient creates password.
#    Now patient's account is activated and patient gets email about it.
#    Patient opens Noona application.
#    Patient goes to Noona landing page.
#    Patient Presses log in.
#    Patient fills in email and password.
#    Patient Presses log in.
#    Patient accepts the terms (see F07P06 Patient can accept terms of use and F07P07 Patient can give consent for personal information processing and for utilising de-identified data )
#    Patient acknowledges native application notifications. See details here F07P09 Patient can allow native application notifications.
#    Patient enters one of the following
#    Diary page, if the clicked link was in invitation email/SMS .
#    Questionnaire, if the clicked link was in notification about Symptom Report OR QoL. When patient has sent the questionnaire and closed the dialog, patient goes through steps 10 a, 11 and 12.
#    Noona shows the introduction slides.
#    Noona asks wellbeing information.
#    Patients account is now connected to the device. See details here F07P11 Patient can connect a device to patient's account.

Extension B.1 — Patient follows a QoL/Symptom link sent by the clinic before activating their account (account activation) (web) - AEQ
    [Tags]    nms9-ver-511-1    nms9-ver-511
    ${new_email}    Create New Patient To Activate    module=${CHEMO_18_SYMPTOMS}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    @{message_data}    Patient Received Invitation To Answer AEQ    ${new_email}    ${pendo_test_clinic}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Set Test Variable    ${link}    ${message_data}[0]
    Activate Noona Account With AEQ    ${link}    ${new_email}    pendo=yes
    Complete Stomach and bowel symptoms Symptom Form    pendo=yes
    Wait Until Location Contains    patient/#/diary-timeline
    Patient Received Successful Account Activation Email    ${new_email}    ${pendo_test_clinic}[name]
    [Teardown]    Close Browser

Extension B.1 — Patient follows a QoL/Symptom link sent by the clinic before activating their account (account activation) (web) - QOL
    [Tags]    nms9-ver-511-2    nms9-ver-511
    ${new_email}    Create New Patient To Activate    module=${BONE_RADIOTHERAPY}
    Send QOL 15D Questionnaire Via API To Patient For Today
    ${link}    Patient Received Invitation To Answer QoL    ${new_email}    ${pendo_test_clinic}[name]
    Open URL In Chrome    ${PATIENT_LOGIN_URL}
    Activate Noona Account With QoL    ${link}    ${new_email}
    Answer QOL Questions And Click Next
    Try To Click Element    ${save_questionnaire_button}
    Click View Your Diary
    Wait Until Page Contains    Noona connects you to your care team
    Close Pendo Guide
    Patient Received Successful Account Activation Email    ${new_email}    ${pendo_test_clinic}[name]
    [Teardown]    Close Browser

Extension B.2 — Patient follows a QoL/Symptom link sent by the clinic before activating their account (account activation) - App
    [Tags]    manual
    # TODO: No JAMA case yet. Please update nms number when jama case is available
#    The user is a patient who is logging in for the first time (has not yet activated account). Patient has downloaded Noona app.
#    The patient clicks the login link in:
#    Email/SMS notification about Symptom Report
#    Email/SMS notification about QoL
#    Email/SMS notification about new results
#    Follow the main success scenario steps 3 - 8
#    Patient opens Noona application.
#    Patient goes to Noona landing page.
#    Patient Presses log in.
#    Patient fills in email and password.
#    Patient Presses log in.
#    Patient accepts the terms    (see F07P06 Patient can accept terms of use and F07P07 Patient can give consent for personal information processing and for utilising de-identified data )
#    Patient acknowledges native application notifications. (See F07P09 Patient can allow native application notifications).
#    Patient enters one of the following
#    Diary page, if the clicked link was in invitation email/SMS .
#    Noona shows the introduction slides.
#    Noona asks wellbeing information.
#    Patients account is now connected to the device. See details here F07P11 Patient can connect a device to patient's account.

Extension C.1 — Patient logs in for the first time (clinic activation) to another clinic (web) - Invitation Email
    [Tags]    nms9-ver-513-1    nms9-ver-513
    [Setup]    Create And Activate Patient In SPMC A Clinic
    ${link}    Add Patient In Pendo Clinic
    Open URL In Chrome    ${link}
    Accept All Cookies If Visible
    Click Activate Your Account
    Wait Until Page Contains    ${account_activation_success_message_texts}
    Try To Click Element    ${activation_complete_next_button}
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${patient_email}
    Approve Terms And Click Next
    Approve Consents And Click Next
    Wait Until Element Is Visible    ${pendo_welcome_to_questionnaire_close_button}    timeout=5s
    Close Pendo Guide
    Patient Is In The Right Page    Diary
    [Teardown]    Extension C.1 Teardown

Extension C.1 — Patient logs in for the first time (clinic activation) to another clinic (web) - Symptom Questionnaire
    [Tags]    nms9-ver-513-2    nms9-ver-513
    [Setup]    Create And Activate Patient In SPMC A Clinic
    Add Patient In Pendo Clinic
    Delete All Messages In Server    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Sleep    30s    # wait for questionnaire to be sent
    @{message_data}    Patient Received Invitation To Answer AEQ    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Open URL In Chrome    ${message_data}[0]
    Accept All Cookies If Visible
    Click Activate Your Account
    Try To Click Element    ${activate_your_noona_account_next_button}
    Create New Password To Activate Account    # this step will stay until whole login flow is refactore, see NOONA-22647
    Wait Until Page Contains    ${account_activation_success_message_texts}
    Try To Click Element    ${activation_complete_next_button}
    Accept All Cookies If Visible
    Patient Received Successful Account Activation Email    ${patient_email}    ${pendo_test_clinic}[name]
    Login As Patient From OIDC Login Page    ${patient_email}
    Approve Terms And Click Next
    Approve Consents And Click Next
    Wait Until Page Contains    Welcome to your questionnaire!
    Close Pendo Guide
    Answer Symptom Form From Clicking Link
    Wait Until Page Contains    Was it easy to answer the questionnaire?
    [Teardown]    Extension C.1 Teardown

Extension C.1 — Patient logs in for the first time (clinic activation) to another clinic (web) - QOL Questionnaire
    [Tags]    nms9-ver-513-3    nms9-ver-513
    [Setup]    Create And Activate Patient In SPMC A Clinic
    Add Patient In Pendo Clinic
    Delete All Messages In Server    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Send QOL Questionnaire Via API To Patient For Today    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    Sleep    30s    # wait for questionnaire to be sent
    ${link}    Patient Received Invitation To Answer QoL    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Open URL In Chrome    ${link}
    Accept All Cookies If Visible
    Click Activate Your Account
    Try To Click Element    ${activate_your_noona_account_next_button}
    Create New Password To Activate Account    # this step will stay until whole login flow is refactore, see NOONA-22647
    Wait Until Page Contains    ${account_activation_success_message_texts}
    Try To Click Element    ${activation_complete_next_button}
    Accept All Cookies If Visible
    Patient Received Successful Account Activation Email    ${patient_email}    ${pendo_test_clinic}[name]
    Login As Patient From OIDC Login Page    ${patient_email}
    Approve Terms And Click Next
    Approve Consents And Click Next
    Complete QOL Questionnaire
    [Teardown]    Extension C.1 Teardown

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By Nurse - Email Invitation
    [Tags]    nms9-ver-514-1    nms9-ver-514
    Set Email And Delete Previous Messages    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Add An Activated Patient Under Pendo Clinic    f07p17-extd    mailosaur=${mailosaur_keys}[0]
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[0]    diary
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By Nurse - SQ
    [Tags]    nms9-ver-514-2    nms9-ver-514
    Set Email And Delete Previous Messages    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Add An Activated Patient Under Pendo Clinic    f07p17-extd    mailosaur=${mailosaur_keys}[0]
    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    @{message_data}    Patient Received Invitation To Answer AEQ    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[0]    symptom questionnaire
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By Nurse - QOL
    [Tags]    nms9-ver-514-3    nms9-ver-514
    Set Email And Delete Previous Messages    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Add An Activated Patient Under Pendo Clinic    f07p17-extd    mailosaur=${mailosaur_keys}[0]
    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Send QOL Questionnaire Via API To Patient For Today    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    ${link}    Patient Received Invitation To Answer QoL    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${link}    qol
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By Nurse - Scheduled Message
    [Tags]    nms9-ver-514-4    nms9-ver-514
    Set Email And Delete Previous Messages    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Add An Activated Patient Under Pendo Clinic    f07p17-extd    mailosaur=${mailosaur_keys}[0]
    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Send Scheduled Message To Patient Via Api    Sample title-f07p17    Sample content-f07p17
    @{message_data}    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${pendo_test_clinic}[name]
    ...    Your care team at ${pendo_test_clinic}[name] sent you a message. Please login to read it.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[2]    clinic
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By Nurse - Lab result
    [Tags]    nms9-ver-514-5    nms9-ver-514
    Set Email And Delete Previous Messages    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Add An Activated Patient Under Pendo Clinic    f07p17-extd    mailosaur=${mailosaur_keys}[0]
    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Send Default Lab Reports To Patient    ${PENDO_TEST_CLINIC_TOKEN}    ${patient_id}
    @{message_data}    Patient Received An Email About New Lab Results
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[name]
    ...    check_link=${PATIENT_URL_IN_MESSAGE}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[1]    lab results
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Candidate) - Email Invitation
    [Tags]    nms9-ver-514-6    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    candidate
    Patient Can Login Without Activation    ${invitation_link}    diary
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Candidate) - SQ
    [Tags]    nms9-ver-514-7    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    candidate
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    @{message_data}    Patient Received Invitation To Answer AEQ    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[0]    symptom questionnaire
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Candidate) - QOL
    [Tags]    nms9-ver-514-8    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    candidate
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Send QOL Questionnaire Via API To Patient For Today    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    ${link}    Patient Received Invitation To Answer QoL    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${link}    qol
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Candidate) - Scheduled Message
    [Tags]    nms9-ver-514-9    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    candidate
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Send Scheduled Message To Patient Via Api    Sample title-f07p17    Sample content-f07p17
    @{message_data}    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${pendo_test_clinic}[name]
    ...    Your care team at ${pendo_test_clinic}[name] sent you a message. Please login to read it.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[2]    clinic
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Candidate) - Lab result
    [Tags]    nms9-ver-514-10    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    candidate
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Send Default Lab Reports To Patient    ${PENDO_TEST_CLINIC_TOKEN}    ${patient_id}
    @{message_data}    Patient Received An Email About New Lab Results
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[name]
    ...    check_link=${PATIENT_URL_IN_MESSAGE}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[1]    lab results
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Proxy) - Email Invitation
    [Tags]    nms9-ver-514-11    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    proxy
    Patient Can Login Without Activation    ${invitation_link}    diary
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Proxy) - SQ
    [Tags]    nms9-ver-514-12    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    proxy
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    @{message_data}    Patient Received Invitation To Answer AEQ    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[0]    symptom questionnaire
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Proxy) - QOL
    [Tags]    nms9-ver-514-13    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    proxy
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Send QOL Questionnaire Via API To Patient For Today    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    ${link}    Patient Received Invitation To Answer QoL    ${patient_email}    ${pendo_test_clinic}[name]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${link}    qol
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Proxy) - Scheduled Message
    [Tags]    nms9-ver-514-14    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    proxy
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Send Scheduled Message To Patient Via Api    Sample title-f07p17    Sample content-f07p17
    @{message_data}    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${pendo_test_clinic}[name]
    ...    Your care team at ${pendo_test_clinic}[name] sent you a message. Please login to read it.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[2]    clinic
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}

Extension D — Patient follows a link sent by the clinic, Nurse has set password for patient (account activation) - Created By EMR (Proxy) - Lab result
    [Tags]    nms9-ver-514-15    nms9-ver-514
    Add Patient Via EMR And Invite To Use Noona    proxy
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Send Default Lab Reports To Patient    ${PENDO_TEST_CLINIC_TOKEN}    ${patient_id}
    @{message_data}    Patient Received An Email About New Lab Results
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[name]
    ...    check_link=${PATIENT_URL_IN_MESSAGE}
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Patient Can Login Without Activation    ${message_data}[1]    lab results
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}


*** Keywords ***
Create New Patient To Activate
    [Arguments]    ${module}=${ABDOMINAL_RADIOTHERAPY}
    Set Test Variable    @{mailosaur_keys}    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Generate Random Patient Data    mailosaur=${mailosaur_keys}[0]
    ${now}    Get Current Date    result_format=%-d%H%M%S
    Set Test Variable    ${patient_email}    f07u01${now}@cfdc2bcz.mailosaur.net
    Generate Clinic Token
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${PENDO_TEST_CLINIC_ID}
    Add Patient To New Clinic Via API    ${PENDO_TEST_SUB_ID_CARETEAM_F07U01}    module=${module}
    RETURN    ${patient_email}

Extension C.1 Teardown
    Generate Clinic Token    ${spmc_clinic_a}[manager_email]    ${DEFAULT_PASSWORD}    ${SPMC_CLINIC_A_CLINIC_ID}
    Set Test Variable    ${patient_id}    ${patient_id1}
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${spmc_clinic_a}[manager_email]
    ...    ${SPMC_CLINIC_A_CLINIC_ID}
    ...    ${SPMC_CLINIC_A_EHR_TOKEN}
    Sleep    3s
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Set Test Variable    ${patient_id}    ${patient_id2}
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[manager_email]
    ...    ${PENDO_TEST_CLINIC_ID}
    ...    ${PENDO_TEST_CLINIC_TOKEN}
    Close Browser

Create And Activate Patient In SPMC A Clinic
    [Documentation]    Agreed with PdM that it is okay that some patients receive different welcome emails hence the condition for email verification below
    Set Email And Delete Previous Messages    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Generate Random Patient Data    mailosaur=${f07p17_email_keys}[0]
    Generate Clinic Token    ${spmc_clinic_a}[manager_email]    ${DEFAULT_PASSWORD}    ${SPMC_CLINIC_A_CLINIC_ID}
    Add Patient To New Clinic Via API    ${SPMC_CLINIC_A_SUB_ID_CARE_TEAM_1}
    Set Test Variable    ${patient_id1}    ${patient_id}
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${spmc_clinic_a}[name]
    Open URL In Chrome    ${message_data}[0]
    Accept All Cookies If Visible
    Activate Noona Account As Patient    ${message_data}[0]    ${patient_email}    terms_enabled=false
    Patient Is In The Right Page    Diary
    Close Browser
    IF    'test' in '${ENVIRONMENT}'
        Patient Received Successful Account Activation Email    ${patient_email}    ${spmc_clinic_a}[name]
    ELSE
        Patient Received Welcome To Custom Clinic Email    ${patient_email}    ${spmc_clinic_a}[name]
    END
    Delete All Messages In Server    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]

Add Patient In Pendo Clinic
    Generate Clinic Token    ${pendo_test_clinic}[manager_email]    ${DEFAULT_PASSWORD}    ${PENDO_TEST_CLINIC_ID}
    Add Patient To New Clinic Via API    ${PENDO_TEST_SUB_ID_CARETEAM_1}
    Set Test Variable    ${patient_id2}    ${patient_id}
    @{message_data}    Patient Received Invitation To Use Noona
    ...    ${patient_email}
    ...    ${pendo_test_clinic}[name]
    ...    spmc=yes
    Delete All Messages In Server    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    RETURN    ${message_data}[0]

Patient Is Advised To Keep Logged In On A Personal Device
    Accept All Cookies If Visible
    Patient Is In The Patient's Landing Page
    Wait Until Element Is Visible    ${landing_page_login_button}
    Try To Click Element    ${landing_page_login_button}
    Wait Until Element Is Visible    ${keep_me_logged_in_checkbox}
    Try To Click Element    ${keep_me_logged_in_label_element}
    Checkbox Should Be Selected    ${keep_me_logged_in_checkbox}
    ${text}    Get Text    ${login_page_privacy_instruction}
    Should Be Equal    ${text}    ${keep_me_logged_in_instruction}

Patient Can Login Without Activation
    [Arguments]    ${link}    ${landing_page}
    Open URL In Chrome    ${link}
    IF    '${landing_page}'=='symptom questionnaire'
        Accept All Cookies If Visible
        Check Pendo Guides From Symptom Questionnaire Form
        Answer Symptom Form From Clicking Link
        Wait Until Page Contains    ${thank_you_for_your_answers_text}
        Page Should Contain    ${questionnaire_from_link_sent_instructions_1}
        Page Should Contain    ${questionnaire_from_link_sent_instructions_2}
        Try To Click Element    ${questionnaire_complete_login_button}
    ELSE IF    '${landing_page}'=='qol'
        Accept All Cookies If Visible
        Answer QOL Questions And Click Next
        Try To Click Element    ${save_questionnaire_button}
        Wait Until Page Contains Element    ${questionnaire_submitted_page_container}
        Wait Until Page Contains Element    ${questionnaire_was_sent_to_clinic_text}
        Wait Until Page Contains Element    ${login_to_continue_using_noona_text}
        Try To Click Element    ${questionnaire_was_sent_login_button}
    END
    Patient Is Advised To Keep Logged In On A Personal Device
    Input Login Credentials And Login    ${patient_email}    ${DEFAULT_PASSWORD}
    Approve Terms And Click Next
    Approve Consents And Click Next
    IF    '${landing_page}'=='clinic'
        Check Pendo Guides From Clinic
        New Message Indicator Is Correct    1
    ELSE IF    '${landing_page}'=='lab results'
        Wait Until Location Contains    ${LAB_RESULTS_PATH}
        Wait Until Element Is Visible    ${lab_results_page_heading}
        Wait Until Element Is Visible    ${lab_report_title}
    ELSE IF    '${landing_page}'=='diary'
        Check Pendo Guides From Diary
        Patient Is Asked Wellbeing In Diary
    END
    Email Notification Is Not Received    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]    ${patient_email}
    Close Browser

Add Patient Via EMR And Invite To Use Noona
    [Arguments]    ${import_mode}
    Set Email And Delete Previous Messages    ${f07p17_email_keys}[0]    ${f07p17_email_keys}[1]
    Generate Random Patient Data    mailosaur=${mailosaur_keys}[0]    name=f07p17-extd
    IF    '${import_mode}'=='candidate'
        ${body_json}    Invite Patient As Candidate With Specific Patient Details
        ...    ${PENDO_TEST_CLINIC_TOKEN}
        ...    ${pendo_test_clinic}[tunit]
    ELSE IF    '${import_mode}'=='proxy'
        ${body_json}    Invite Patient As Proxy From EMR
        ...    ${PENDO_TEST_CLINIC_TOKEN}
        ...    ${pendo_test_clinic}[tunit]
    END
    Login As Nurse    ${pendo_test_clinic}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    IF    '${import_mode}'=='candidate'
        Wait Until Element Is Visible    ${send_user_account_button}
        Wait Until Element Is Enabled    ${send_user_account_button}
        Try To Click Element    ${send_user_account_button}
        Fill In ICD, Module And Care Team With Default Values
        Try To Click Element    ${send_invite_button}
    ELSE IF    '${import_mode}'=='proxy'
        Choose General Information Tab
        Patient Status Is Correct    Proxy
        Send User Account
    END
    Try To Click Banner Message
    ${patient_id}    Get Location
    ${patient_id}    Fetch From Right    ${patient_id}    -patient/
    ${patient_id}    Fetch From Left    ${patient_id}    /edit
    Set Test Variable    ${patient_id}
    Set Test Variable    ${login_token}    ${PENDO_TEST_CLINIC_TOKEN}
    Change Password    ${DEFAULT_PASSWORD}
    @{message_data}    Patient Received Invitation To Use Noona    ${patient_email}    ${pendo_test_clinic}[name]
    Set Test Variable     ${invitation_link}    ${message_data}[0]
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Close Browser

Invite Candidate To Use Noona
    [Arguments]    ${fill_in_with_new_details}
    Login As Nurse    ${pendo_test_clinic}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Patient Status Is Correct    Patient created by the EHR
    Choose General Information Tab
    Try To Click Element    ${send_user_account_button}
    IF    '${fill_in_with_new_details}'=='yes'
        Fill In ICD, Module And Care Team With Default Values
    END
    Try To Click Element    ${send_invite_button}
    Try To Click Banner Message
    Close Browser
