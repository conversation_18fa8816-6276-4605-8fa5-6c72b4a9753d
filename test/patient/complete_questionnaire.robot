*** Settings ***
Documentation       [F01N12] Nurse can schedule a questionnaire or message to be sent to a patient
...                 Test:
...                 - Adds a new scheduled questionnaire for a random patient
...                 - Patient completes and checks the summary (text and number inputs) of questionnaire
...                 - Saves and checks the status of questionnaire
...                 - Checks saved summary (text and number inputs)

Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource

Suite Setup         Set Libraries Order
Test Setup          Login To Nurse
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n12-patient    questionnaires-test


*** Test Cases ***
Create And Complete IPQ Questionnaire
    Get Random Patient From The List
    Create Schedule    ${patient_ssn}    IPQ
    Login Patient    ${patient_email}
    Complete Questionnaire    IPQ
    Save Questionnaire    IPQ
    Check Saved Questionnaire    IPQ

Create And Complete HADS Questionnaire
    Get Random Patient From The List
    Create Schedule    ${patient_ssn}    HADS
    Login Patient    ${patient_email}
    Complete Questionnaire    HADS
    Save Questionnaire    HADS
    Check Saved Questionnaire    HADS

Create And Complete MAAS Questionnaire
    Get Random Patient From The List
    Create Schedule    ${patient_ssn}    MAAS
    Login Patient    ${patient_email}
    Complete Questionnaire    MAAS
    Save Questionnaire    MAAS
    Check Saved Questionnaire    MAAS

Create And Complete Sociodemographic information M3 Questionnaire
    Get Random Patient From The List
    Create Schedule    ${patient_ssn}    Sociodemographic information M3
    Login Patient    ${patient_email}
    Complete Questionnaire    Sociodemographic information M3
    Save Questionnaire    Sociodemographic information M3
    Check Saved Questionnaire    Sociodemographic information M3
