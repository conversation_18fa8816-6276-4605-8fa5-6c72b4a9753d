*** Settings ***
Documentation       F01P15 Pre-treatment and Portal-only usage of Noona
...                 Precondition: Clinic has disabled the following:
...                 Patient can ask about symptoms, Patient can capture a symptom using diary

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource

Suite Setup         Set Libraries Order
Test Setup          Setup App Environment
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p15    patient-web


*** Test Cases ***
Main Success Scenario
    [Tags]    nms9-ver-428    native-web
    Add An Activated Patient Under Pre Treatment Clinic    usecase=f01p15
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Wait Until Element Is Visible    ${ask_about_other_issues_icon}
    Page Should Not Contain Element    ${ask_about_symptom_button}
    Text Should Be In The Page    Your clinic
    Text Should Be In The Page    ${pre_treatment_clinic}[name]
    Click Add Menu Button
    Wait Until Element Is Visible    ${event_button}
    Wait Until Element Is Visible    ${diary_note_button}
    Page Should Not Contain Element    ${symptom_entry}
    [Teardown]    Run Keywords    Remove Patient As Test Teardown    ${patient_email}    ${pre_treatment_clinic}[manager_email]    ${PRE_TREATMENT_CLINIC_ID}    ${PRE_TREATMENT_EHR_TOKEN}    AND    Close All App Instances

Extension A - Symptom reporting is not yet available for the patient
    [Tags]    nms9-ver-429    native-web
    Add An Activated Patient Under Default Clinic    f01p15-exta    module=${PRE_POST_TREATMENT}
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Ask About Symptom Option
    Wait Until Page Contains    ${unactivated_symptom_reporting_message}
    Element Should Be Visible    ${unactivated_symptom_reporting_image}
    Try To Click Element    ${unactivated_symptom_reporting_close_button}
    Click Add Menu Button
    Add Symptom Entry
    Wait Until Page Contains    ${unactivated_symptom_reporting_message}
    Element Should Be Visible    ${unactivated_symptom_reporting_image}
    Try To Click Element    ${unactivated_symptom_reporting_close_button}
    Patient Is In The Right Page    Clinic
    [Teardown]    Run Keywords    Remove Patient As Test Teardown    ${patient_email}    ${automated_tests_clinic}[default_manager]    ${AUTOMATED_TESTS_CLINIC_ID}    ${AUTOMATED_TESTS_EHR_TOKEN}    AND    Close All App Instances
