*** Settings ***
Documentation       F03P02 Patient can exchange messages with a nurse in a discussion the nurse initiated
...                 Preconditions:
...                 - Contact clinic feature must be enabled in clinic settings.
...                 - Nurse has contacted the patient.

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource

Test Setup          Run Keywords    Set Libraries Order    AND    Setup App Environment
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03p02    patient-web


*** Test Cases ***
Main success scenario - Patient Can Exchange Messages With A Nurse
    [Tags]    nms9-ver-196    native-web    patient-email-link
    Create New Patient With Mailosaur Email To Contact
    Send Contact Patient Request    10    # '10' in response body corresponds to 'Invitation for clinical tests'
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${automated_tests_clinic}[name]
    ...    Your care team at ${automated_tests_clinic}[name] sent you a message
    ...    check_link=${PATIENT_URL_IN_MESSAGE}${PATIENT_PATH}
    Login As Patient    ${patient_email}
    Navigate to Clinic
    Select Latest Clinic Message
    Reply To Clinic Message    Answer to the invite for usecase-f03p02.    new_message=yes
    Select Latest Clinic Message
    Wait Until Page Contains    Answer to the invite for usecase-f03p02.
    [Teardown]    F03P02 - Main Success Scenario - Test Teardown
#    below contact types are tested in other test cases
#    If the contact type is instructions, select "No" to the question "Was the information provided sufficient?", write a message and click Send
#    If the contact type is Question, write a message and click Send


*** Keywords ***
F03P02 - Main Success Scenario - Test Teardown
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    Delete All Messages In Server    ${f03p02_email_keys}[0]    ${f03p02_email_keys}[1]
    Close All App Instances

Create New Patient With Mailosaur Email To Contact
    Generate Random Patient Data    mailosaur=${f03p02_email_keys}[0]
    Generate Clinic Token
    ...    ${automated_tests_clinic}[default_user]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Add Patient To New Clinic Via API    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    Send Change User Password Request
    Set Test Variable    ${patient_email}
    Log    ${patient_email}
    Set Email And Delete Previous Messages    ${f03p02_email_keys}[0]    ${f03p02_email_keys}[1]
