*** Settings ***
Documentation       F01P08 Patient can send open question to clinic
...                 Preconditions:
...                 - Patient is logged in.
...                 - Contact clinic feature must be enabled in clinic settings.

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}lab_reports.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}integration_settings.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinical_reports.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}lab_reports.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}accessibility${/}missing_accessible_names_checker.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p08    patient-web


*** Variables ***
${attachment_download_wait}     2.5 min


*** Test Cases ***
Patient can send open question to clinic
    [Tags]    nms9-ver-126    native-web    pendo
    [Setup]    Setup App Environment
    Login As Patient    ${f01p08_patient}[email]
    Navigate to Clinic
    Select Ask about other issues
    Select topic
    Enter question
    Send question to clinic
    Select Latest Clinic Message
    Wait Until Page Contains    Treatment plan
    Verify Clinic Message Is Correct    ${auto_test_question_content}
    [Teardown]    Close All App Instances

Ext A - Contact clinic about a lab result
    [Tags]    nms9-ver-127    native-web
    [Setup]    Setup For Ext A
    Create New Patient To Receive Lab Reports    f01p08-exta
    Set Report ID And Effective DateTime
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}
    Login As Patient    ${patient_email}
    Go To Library
    Click Laboratory Reports Intro
    Contact Clinic Modal Can Be Closed
    ${current_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y
    Set Test Variable    ${current_date}
    Contact Clinic About Lab Report    CMP    Question about lab${SPACE}${current_date}
    Wait Until Page Contains    Your question was sent to the clinic
    Try To Click Element    ${lab_report_contact_clinic_close_button}
    Navigate To Clinic
    Select Latest Clinic Message
    Patient Message About Lab Is Displayed
    Close Browser
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse    ${patient_education_clinic}[user_email]
        Remove All Care Team Filter
        Select Care Team Filter In Work Queue    ${patient_education_clinic}[care_team_1]
        Select Case Type Filter In Work Queue    ${case_type_filters}[results_or_labs]
        Click List View Button
        Case Type Is Displayed in List View    ${patient_ssn}    ${case_type_filters}[results_or_labs]
        Click Card View Button
        Select Patient Card    ${first_name}${SPACE}${family_name}
        Select Case From Patient Cases Tab    ${case_type_filters}[results_or_labs]
        Patient Message About Lab Is Displayed
    END
    [Teardown]    Close All App Instances

Extension B – Patient can contact clinic about a Clinical Report
    [Tags]    nms9-ver-382    native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Create Patient And Send Clinical Reports
        Login As Patient    ${patient_email}
    ELSE
        Create Patient And Send Clinical Reports For Native
        Login As Patient    ${patient_email}
    END
    Go To Library
    Go To Clinical Reports As Patient
    Select Clinical Report From List    ${radiology_report}[name]
    Check Content Of The Clinical Report Contact Clinic Modal
    Close Clinical Report Contact Clinic Modal
    ${current_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y
    Set Test Variable    ${current_date}
    Contact Clinic About Clinical Report    Question about radiology report${SPACE}${current_date}
    Wait Until Page Contains    Your question was sent to the clinic
    Try To Click Element    ${clinical_report_confirmation_close_button}
    Wait Until Element Is Visible    ${clinical_report_contact_clinic_button}
    Try To Click Element    ${close_clinical_reports_modal_button}
    Navigate To Clinic
    Select Latest Clinic Message
    Wait Until Element Is Visible    ${clinical_report_patient_message}
    Patient Message About Clinical Reports Is Displayed
    Close Browser
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse    ${automated_tests_clinic}[default_user]
        Search Patient By Identity Code    ${patient_ssn}
        Select Case From Patient Cases Tab    Results or labs
        Patient Message About Clinical Reports Is Displayed
    END
    [Teardown]    Close All App Instances

Extension C – Patient can attach a document to a message sent to the clinic (web) - layout and validations
    [Tags]    nms9-ver-447-1    nms9-ver-447
    Add An Activated Patient Under Default Clinic    f01p08_extc_1
    Login As Patient    ${patient_email}
    Navigate to Clinic
    Select Ask about other issues
    Select topic
    Enter question
    Check Attachment Elements
    Check Allowed File Types
    Check File Icons
    Check Error Message Displayed When More Than 10 Files Are Selected
    Check Error Message When File Over The Limit Is Added
    Check Error Message For Invalid File Type
    Check Possibility To Cancel Message
    [Teardown]    Run Keywords    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    AND    Close All App Instances

Extension C – Patient can attach a document to a message sent to the clinic (web) - send message to clinic, check sent message attachment and case in clinic
    [Tags]    nms9-ver-447-2    nms9-ver-447
    Add An Activated Patient Under Default Clinic    f01p08_extc_2
    Login As Patient    ${patient_email}
    Compose A Message With Attachments
    Send question to clinic
    Go To Clinic
    Verify Attachment Icon For The Message    Treatment plan    displayed
    Select Latest Clinic Message
    Verify Clinic Message Title Is Correct    Treatment plan
    Verify Added Attachments On Patient Side
    Open A Patient Attachment Previewer From A Sent Message    ${pdf_file_name}
    Open A Patient Attachment Previewer From A Sent Message    ${jpeg_file_name}
    Open A Patient Attachment Previewer From A Sent Message    ${jpg_file_name}
    Open A Patient Attachment Previewer From A Sent Message    ${png_file_name}
    Login As Nurse    ${automated_tests_clinic}[default_user]
    Search Patient By Identity Code    ${patient_ssn}
    Select Case From Patient Cases Tab    Treatment plan
    Acknowledge Attachment Disclaimer
    Verify Clinic Message With Attachments
    [Teardown]    Run Keywords    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    ...    AND    Close All App Instances

Extension C – Patient can attach a document to a message sent to the clinic (web) - check downloadable attachments from a sent message
    [Tags]    nms9-ver-447-3    nms9-ver-447    manual
    [Setup]    Prepare Chrome for Downloads
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${f01p08_patient}[email]
    Compose A Message With Attachments
    Send question to clinic
    Go To Clinic
    Select Latest Clinic Message
    # TODO: Find solution to get these steps work on Gitlab.
    IF    '${remote_url_exists}'=='False'
        Verify Patient Attachment Is Downloaded To Patient's Computer    ${doc_file_name}
        Verify Patient Attachment Is Downloaded To Patient's Computer    ${docx_file_name}
        Verify Patient Attachment Is Downloaded To Patient's Computer    ${xls_file_name}
        Verify Patient Attachment Is Downloaded To Patient's Computer    ${xlsx_file_name}
        Verify Patient Attachment Is Downloaded To Patient's Computer    ${heic_file_name}
    END
    [Teardown]    Close All App Instances

Verify Missing Accessible Names Under Ask About Other Issues Subpage
    [Tags]    manual    missing-ac-names
    [Setup]    Setup App Environment
    Login As Patient    ${f01p08_patient}[email]
    Navigate to Clinic
    Select Ask about other issues
    Select topic
    Check Missing Accessible Names On Page

*** Keywords ***
Patient Message About Lab Is Displayed
    Wait Until Element Is Visible    //*[contains(string(),'CMP')]
    IF    'native' in '${ENVIRONMENT}'
        Page Should Contain Text    Question about lab${SPACE}${current_date}
        Page Should Contain Text    ${current_date}
        Page Should Contain Text    Results or labs
    ELSE
        Page Should Contain    Question about lab${SPACE}${current_date}
        Page Should Contain    ${current_date}
        Page Should Contain    Results or labs
    END

Create Patient And Send Lab Reports
    Create An Activated Patient Via API
    ...    ${patient_education_clinic}[user_email]
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    ...    ${PATIENT_EDUCATION_SUB_ID_F01P08}
    Set Report ID And Effective DateTime
    Set Test Variable    ${integration_user_token}    ${PATIENT_EDUCATION_EHR_TOKEN}
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}

Create Patient And Send Clinical Reports
    Add An Activated Patient Under Default Clinic    f01p08_extb
    Set Header
    Send Clinical Reports To Patient    ${patient_mrn}    ${radiology_report}[loin]    ${radiology_report}[name]

Create Patient And Send Clinical Reports For Native
    Add An Activated Patient Under Native App Clinic    f01p08_extb
    Set Header    ${NATIVE_APP_EHR_USER_TOKEN}
    Send Clinical Reports To Patient    ${patient_mrn}    ${radiology_report}[loin]    ${radiology_report}[name]

Setup For Ext A
    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Close Open Cases Per Care Team
        ...    ${patient_education_clinic}[care_team_1]
        ...    nurse_email=${patient_education_clinic}[user_email]
        Close All Browsers
    END
    Generate Random Patient Data

Compose A Message With Attachments
    Navigate to Clinic
    Select Ask about other issues
    Select topic
    Enter question
    Try To Click Element    ${attach_file_button}
    Upload All Allowed File Types

Open A Patient Attachment Previewer From A Sent Message
    [Arguments]    ${attachment_title}
    Select An Attachment From List Of Sent Patient Attachments    ${attachment_title}
    IF    'pdf' in '${attachment_title}'
        Wait Until Element Is Visible    ${attachment_pdf_preview_modal}
    ELSE IF    'png' in '${attachment_title}' or 'PNG' in '${attachment_title}' or 'jpeg' in '${attachment_title}' or 'jpg' in '${attachment_title}'
        Wait Until Element Is Visible    ${attachment_image_preview_modal}
    END
    Wait Until Element Is Visible    ${attachment_preview_modal_primary_close_button}
    Wait Until Element Is Visible    ${attachment_preview_modal_x_button}
    Click Element    ${attachment_preview_modal_primary_close_button}

Verify Patient Attachment Is Downloaded To Patient's Computer
    [Documentation]    The keyword checks that a file is downloadable and saved to the a project's directory
    [Arguments]    ${attachment_title}
    Select An Attachment From List Of Sent Patient Attachments    ${attachment_title}
    Wait Until Keyword Succeeds    ${attachment_download_wait}    3    Directory Should Exist    ${downloads-dir}
    Wait Until Keyword Succeeds
    ...    ${attachment_download_wait}
    ...    3
    ...    File Should Exist
    ...    ${downloads-dir}${/}${attachment_title}

Select An Attachment From List Of Sent Patient Attachments
    [Arguments]    ${attachment_title}
    Wait Until Element Is Visible    ${message_attachments_list}
    Try To Click Element
    ...    ${message_attachment_item}//*[text()=" ${attachment_title} "]

Check Attachment Elements
    Wait Until Element Is Visible    ${paperclip_icon}
    Wait Until Element Is Visible    ${attach_file_button}
    Wait Until Element Is Visible    ${attach_file_info}

Check Allowed File Types
    Try To Click Element    ${attach_file_button}
    Upload All Allowed File Types

Upload And Verify Successful File Upload
    [Arguments]    ${file_name}
    ${file_path}    Catenate    SEPARATOR=    ${attachment_location}${file_name}
    Choose File    ${choose_file}    ${file_path}
    Page Should Contain    ${file_name}
    Verify Send Button Status    enabled

Upload All Allowed File Types
    Upload And Verify Successful File Upload    ${doc_file_name}
    Upload And Verify Successful File Upload    ${jpg_file_name}
    Upload And Verify Successful File Upload    ${docx_file_name}
    Upload And Verify Successful File Upload    ${jpeg_file_name}
    Upload And Verify Successful File Upload    ${pdf_file_name}
    Upload And Verify Successful File Upload    ${xls_file_name}
    Upload And Verify Successful File Upload    ${xlsx_file_name}
    Upload And Verify Successful File Upload    ${png_file_name}
    Upload And Verify Successful File Upload    ${heic_file_name}
    Scroll Element Into View    ${submit-question-button}

Remove All Added Files
    ${visible_lines}    Get Element Count    ${remove_file_button}
    Repeat Keyword    ${visible_lines}    Try To Click Element    ${remove_file_button}

Check File Icons
    ${doc_icon_class}    Get Element Attribute
    ...    //div[contains(text(), "${doc_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${doc_icon_class}    ${document_icon}
    ${docx_icon_class}    Get Element Attribute
    ...    //div[contains(text(),"${docx_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${docx_icon_class}    ${document_icon}
    ${xls_icon_class}    Get Element Attribute
    ...    //div[contains(text(),"${xls_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${xls_icon_class}    ${excel_icon}
    ${xlsx_icon_class}    Get Element Attribute
    ...    //div[contains(text(),"${xlsx_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${xlsx_icon_class}    ${excel_icon}
    ${pdf_icon_class}    Get Element Attribute
    ...    //div[contains(text(),"${pdf_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${pdf_icon_class}    ${pdf_icon}
    ${jpg_icon_class}    Get Element Attribute
    ...    //div[contains(text(),"${jpg_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${jpg_icon_class}    ${image_icon}
    ${jpeg_icon_class}    Get Element Attribute
    ...    //div[contains(text(),"${jpeg_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${jpeg_icon_class}    ${image_icon}
    ${heic_icon_class}    Get Element Attribute
    ...    //div[contains(text(),"${heic_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${heic_icon_class}    ${image_icon}
    ${png_icon_class}    Get Element Attribute
    ...    //div[contains(text(),"${png_file_name}")]/../div[@class="icon"]/ds-icon
    ...    class
    Should contain    ${png_icon_class}    ${image_icon}

Verify Send Button Status
    [Arguments]    ${status}
    Scroll Element Into View    ${submit-question-button}
    ${class_name}    Get Element Attribute    ${submit-question-button}/..    class
    IF    '${status}'=='disabled'
        Should Contain    ${class_name}    disabled
    ELSE
        Should Not Contain    ${class_name}    disabled
    END

Check Error Message When File Over The Limit Is Added
    Choose File    ${choose_file}    ${EXECDIR}${/}data${/}file_storage_service_files${/}FSS_over_limit_10.5MB.doc
    Wait Until Element Is Visible    ${error_file_over_size_limit}
    Scroll Element Into View    ${submit-question-button}
    Verify Send Button Status    disabled

Remove Files Over The Limit
    ${visible_lines}    Get Element Count    ${remove_file_button}
    ${files_over_limit}    Evaluate    ${visible_lines} - 10
    Repeat Keyword    ${files_over_limit}    Try To Click Element    ${remove_file_button}
    Wait Until Element Is Not Visible    ${error_attach_files_limit}
    Verify Send Button Status    enabled

Remove Files With Error
    ${error_status}    Run Keyword And Return Status    Element Should Be Visible    ${error_message_cancel_button}
    IF    ${error_status} == True
        ${errors}    Get Element Count    ${error_message_cancel_button}
        Repeat Keyword    ${errors}    Try To Click Element    ${error_message_cancel_button}
    END

Check Error Message Displayed When More Than 10 Files Are Selected
    Choose File    ${choose_file}    ${EXECDIR}${/}data${/}file_storage_service_files${/}FSS_doc_extension.doc
    Choose File    ${choose_file}    ${EXECDIR}${/}data${/}file_storage_service_files${/}FSS_docx_extension.docx
    Choose File    ${choose_file}    ${EXECDIR}${/}data${/}file_storage_service_files${/}file_example_JPG_1MB.jpg
    Wait Until Element Is Visible    ${error_attach_files_limit}
    Element Text Should Be    ${error_attach_files_limit}    ${error_attach_files_limit_text}
    Verify Send Button Status    disabled
    Remove Files With Error
    Remove Files Over The Limit
    Scroll Element Into View    ${submit-question-button}
    Wait Until Element Is Not Visible    ${error_attach_files_limit}
    Verify Send Button Status    enabled
    Remove All Added Files

Check Error Message For Invalid File Type
    Choose File    ${choose_file}    ${EXECDIR}${/}data${/}upload_photos${/}clinic_logo${/}valid-logo.svg
    Wait Until Element Is Visible    ${error_invalid_file_type}
    Verify Send Button Status    disabled
    Remove Files With Error

Check Possibility To Cancel Message
    Try To Click Element    ${cancel_send_message_button}
    Wait Until Element Is Visible    ${clinic_contact_your_care_team_header}
