*** Settings ***
Documentation       F01P01S17 Patient can report symptom and get automated self care instruction
...                 Preconditions:
...                 - User logged in as patient
...                 - Contact clinic feature must be enabled in clinic settings.
...                 - The Noona Mobile Service release being used is for clinical investigation.

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p01s17    patient-web


*** Test Cases ***
Patient Can Report A Mild Symptom With Self Care Instruction
    [Tags]    nms9-ver-109    native-web
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f01p01s17-1    module=${BONE_RADIOTHERAPY}
    Login As Patient    ${patient_email}
    Ask About Symptoms    ${OTHER_SYMPTOM}
    Report Mild Symptom
    Send Symptom To Clinic
    Symptom Entry Sent To Clinic
    Navigate To Clinic
    Verify Inbox For Mild Or Moderate Symptom Message
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - Symptom saved to diary triggers an automation rule
    [Tags]    nms9-ver-110    native-web
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f01p01s17-2    module=${CHEMO_11}
    Login As Patient    ${patient_email}
    Click Add Menu Button
    Add Symptom Entry
    Add A Symptom    ${OTHER_SYMPTOM}
    Report Moderate Symptom
    Send Symptom To Clinic
    Symptom Entry Saved In Diary
    Go To Clinic
    Patient Is In The Right Page    Clinic
    No Automated Selfcare Message For Mild Or Moderate Symptom
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Report Mild Symptom
    Try To Input Text    ${other_symptom_description_field}    Test comments mild
    Select Symptomatic Day    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Mild
    Select Answer To Question
    ...    Have you used any medication to alleviate your symptoms?
    ...    No
    Sleep    1
    Click Next Button

Report Moderate Symptom
    Try To Input Text    ${other_symptom_description_field}    Test comments moderate
    Select Symptomatic Day    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Moderate
    Select Answer To Question
    ...    Have you used any medication to alleviate your symptoms?
    ...    No
    Sleep    1
    Click Next Button
