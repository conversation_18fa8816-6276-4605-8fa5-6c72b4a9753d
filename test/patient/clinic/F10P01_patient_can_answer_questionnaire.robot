*** Settings ***
Documentation       F10P01 Patient can answer a questionnaire

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}appointment.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}modals.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f10p01    patient-web


*** Variables ***
${qol_full_text}                        Quality of life questionnaire
${email_text}
...                                     You have received multiple questionnaires from your care team at ${automated_tests_clinic}[name].
...                                     You can complete the questionnaires by selecting the individual links below.
...                                     You can also fill in the questionnaires from you diary or inbox by logging in.
${login_link_from_email}                ${PATIENT_URL}${PATIENT_PATH}#/diary-timeline
${other_symptom_description_field}      xpath=(//*[@id="description"])[last()]


*** Test Cases ***
Main - Patient Can Answer Questionnaire - QOL
    [Tags]    nms9-ver-265-1    nms9-ver-265    pendo
    Answer QOL Questionnaire As Patient
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Patient Cases Tab
    First Case Type Is Correct    QUALITY OF LIFE QUESTIONNAIRE (15D©)
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main - Patient Can Answer Questionnaire - Symptom Questionnaire
    [Tags]    nms9-ver-265-2    native-web
    [Setup]    Setup App Environment
    Add An Activated Patient Under Appointment Clinic    f10p01-2
    Get Appointment Date    0 days
    Send Appointments Via FHIR API    777    testsq    ${patient_id}
    Sleep    90s    # wait until the questionnaire gets sent
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire Button
    Select Yes For Symptoms    ${OTHER_SYMPTOM}
    Complete Other Symptom Questionnaire With Severe Symptom
    Wait Until Element Is Visible    ${automated_answer_modal}
    Wait Until Page Contains    ${symptom_is_sent_to_clinic_text}
    Wait Until Page Contains    ${automated_answer_modal_warning_text}
    Wait Until Page Contains    ${emergency_symptom_instructions_text_2-1}
    Wait Until Page Contains    ${emergency_symptom_instructions_text_2-2}
    Click Emergency Symptom Ok Button
    Select Latest Clinic Message
    Wait Until Page Contains
    ...    The symptom you just described indicates that you might require immediate attention from a medical professional.
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${appointment_clinic}[manager_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_USER_TOKEN}

Extension A - User can send a message related to questionnaire answer (web / app)
    [Tags]    nms9-ver-266    native-web
    [Setup]    Setup App Environment
    Answer QOL Questionnaire As Patient
    Select Latest Clinic Message
    Reply To Clinic Message    Reply to QOL    new_message=yes
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Page Contains Element    ${main_navigation_bar}
    ELSE
        Wait Until Element Is Not Visible    ${reply_to_message_textarea}
        Sleep    1
        Login As Nurse
        Go To Patient Reports
        Remove Selected Primary Providers
        Remove All Care Team Filter
        Remove Questionnaire Filter In Work Queue
        Select Care Team Filter In Work Queue    f10p01 care team
        Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Patient can receive notification of multiple questionnaires in email and answer via direct link (web)
    [Tags]    nms9-ver-267    native-app-todo    pendo    patient-email-link
    [Setup]    Set And Delete Email
    Login As Nurse And Select Patient    ${f10p01_patient_b}[ssn]
    @{questionnaires}    Create List    ${QUALITY_OF_LIFE_QUESTIONNAIRE}    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    Add Multiple Questionnaires And Save    @{questionnaires}
    @{message_data}    Patient Received An Email About A New Message    ${f10p01_patient_b}[email]
    ...    New Questionnaires from ${automated_tests_clinic}[name]
    ...    ${email_text}
    ...    check_link=${PATIENT_URL_IN_MESSAGE}
    Set Test Variable    ${link}    ${message_data}[2]
    Go To    ${link}
    Accept All Cookies If Visible
    Complete QOL Questionnaire
    questionnaires.Save Questionnaire    ${questionnaire}    via_email_link=yes
    [Teardown]    Close All App Instances

Extension C - Patient can open questionnaire from Diary - QOL
    [Tags]    nms9-ver-35-1    nms9-ver-35    native-web
    [Setup]    Setup App Environment
    ${today}    Get Current Date    result_format=%d.%m.%Y
    Add An Activated Patient Under Default Clinic    f10p01-qol
    Send QOL 15D Questionnaire Via API To Patient For Today
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire Button
    questionnaires.Complete QOL Questionnaire
    questionnaires.Save Questionnaire    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    questionnaires.Check Saved Questionnaire    ${qol_full_text}
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        Navigate To Patient Cases Tab
        Latest Case Has Correct Type And Date    QUALITY OF LIFE QUESTIONNAIRE (15D©)    ${today}
    END
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension C - Patient can open questionnaire from Diary - Symptom Questionnaire
    [Tags]    nms9-ver-35-2    nms9-ver-35    native-web
    [Setup]    Setup App Environment
    ${today}    Get Current Date    result_format=%d.%m.%Y
    Add An Activated Patient Under Default Clinic    f10p01-sq
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Login As Patient    ${patient_email}
    Sleep    1s
    Report Other Symptom From Questionnaire In Diary
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        Navigate To Patient Cases Tab
        Latest Case Has Correct Type And Date    ${BASELINE_QUESTIONNAIRE}    ${today}
    END
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E - QoL questionnaires have required fields indicated
    [Tags]    nms9-ver-414-1    nms9-ver-414    native-web
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f10p01-e
    Send Appointment To Patient With QOL
    Login As Patient    ${patient_email}
    Select Latest Questionnaire When It Appears In Diary
    All QOL Questions Have Required Fields Indicated
    Check Required Field Indicator Per Question
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E - Symptom questionnaires have required fields indicated - Baseline
    [Tags]    nms9-ver-414-2    nms9-ver-414    native-web
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f10p01-e
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Login As Patient    ${patient_email}
    Sleep    2s
    Select Latest Questionnaire In Diary
    Select Yes For Symptoms    ${OTHER_SYMPTOM}
    Click Next Button
    Question Is Mandatory Error Is Displayed    1
    Select Answer To Question
    ...    How would you rate your general state of health?
    ...    I cannot move without help, I have to lie down or sit all the time. I cannot take care of myself.
    Repeat Keyword    4    Click Next Button
    Question Is Mandatory Error Is Displayed    4
    Sleep    2s
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E - Symptom questionnaires have required fields indicated - Status Check
    [Tags]    nms9-ver-414-3    nms9-ver-414
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f10p01-e3
    Send Symptom Questionnaire Via Api To Patient    ${status_check_quest}
    Login As Patient    ${patient_email}
    Select Latest Questionnaire In Diary
    Click Next Button
    Question Is Mandatory Error Is Displayed    2
    Rate With Vertical Slider
    Question Is Mandatory Error Is Displayed    1
    Select Answer To Question    Are you having any symptoms?    No
    Click Next Button
    Send Symptom Questionnaire To Clinic
    Questionnaire Has Been Sent Modal Is Displayed
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E - Patient can close or discard changes in a symptom questionnaire
    [Documentation]    This test covers functionality of X/Cancel-button on a symptom questionnaire.
    [Tags]    nms9-ver-414-4    nms9-ver-414
    [Setup]    Set Application On Environment
    Add An Activated Patient Under Default Clinic    f10p01-e4
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Send QOL 15D Questionnaire Via API To Patient For Today
    Login As Patient    ${patient_email}
    Reload Until Questionnaire Is Available
    Select A Sent Questionnaire From Diary    ${BASELINE_QUESTIONNAIRE}
    Patient Closes The Questionnaire Without Touching The Form
    Select A Sent Questionnaire From Diary    ${BASELINE_QUESTIONNAIRE}
    Select Yes For Symptoms    ${OTHER_SYMPTOM}
    Cancel Questionnaire To Discard Changes    ${questionnaire_close_button}
    Discard Changes Modal Is Visible
    Select To Continue Editing
    Cancel Questionnaire To Discard Changes    ${questionnaire_cancel_button}
    Discard Changes Modal Is Visible
    Select To Discard Changes And Return To Diary Page
    # Checking close/cancel button on QoL questionnaire (e.g. Quality of life questionnaire, the actual name on patient UI)
    Select A Sent Questionnaire From Diary    Quality of life questionnaire
    Patient Closes The Questionnaire Without Touching The Form
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    # TODO: Adding a check on behaviour of close/cancel button on QoL when there are some changes on the form. Currently blocked by https://vocscs.atlassian.net/browse/NOONA-24609
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

*** Keywords ***
Create A New Patient And Update Email
    Login As Nurse
    Update Patient Data With Random Data
    Set To Dictionary    ${patient_dict}    email=${TEST_EMAIL_YAHOO_5}
    Nurse.Patients.Create Patient    &{patient_dict}    proxy=no
    Change Patient Password    ${DEFAULT_PASSWORD}

Login As Nurse And Select Patient
    [Arguments]    ${patient}
    Login As Nurse
    Search Patient By Identity Code    ${patient}
    Navigate To Questionnaires Tab

Report Other Symptom From Questionnaire In Diary
    Select Latest Questionnaire In Diary
    Select Yes For Symptoms    ${OTHER_SYMPTOM}
    Changes In Gen State Of Health Is Displayed
    Input Text    ${other_symptom_description_field}    Other symptom description
    Select Answer To Question    When did you have this symptom?    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Mild
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Sleep    1
    Click Next Button
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    The questionnaire has been sent to the clinic.
    Close All App Instances

Answer QOL Questionnaire As Patient
    Add An Activated Patient Under Default Clinic    f10p01-main    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F10P01}
    Send QOL 15D Questionnaire Via API To Patient For Today
    Login As Patient    ${patient_email}
    Select Latest Clinic Message
    Select Answer Questionnaire
    Complete QOL Questionnaire
    questionnaires.Save Questionnaire    ${QUALITY_OF_LIFE_QUESTIONNAIRE}

Set And Delete Email
    Set Test Variable    @{mailosaur_keys}    mlsm53vn    1HubFZVoITYcbGb9
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

Send Appointment To Patient With QOL
    [Documentation]    ${patient_id} is set when patient is created
    Set Appointment Date In The Future    0 days
    Set Test Variable    ${appointment_user_token}    ${AUTOMATED_TESTS_EHR_TOKEN}
    Send Appointments Via FHIR API    ${automated_tests_clinic}[tunit]    qol    ${patient_id}

Select Latest Questionnaire When It Appears In Diary
    [Documentation]    it takes time for questionnaire to be available to answer
    Reload Until Questionnaire Is Available
    Select Latest Questionnaire In Diary
    Click Next Button

Patient Closes The Questionnaire Without Touching The Form
    Try To Click Element    ${questionnaire_close_button}
    Wait Until Location Contains    ${PATIENT_URL}/patient/#/diary-timeline
