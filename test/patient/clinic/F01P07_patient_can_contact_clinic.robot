*** Settings ***
Documentation       F01P07 Patient can contact clinic from diary
...                 Preconditions:
...                 - Patient has logged in.
...                 - Patient has saved symptom to diary within the last 24 hours
...                 - [file:./test/patient/clinic/F01P01.robot|F01P01 Patient can report symptom]
...                 - Contact clinic feature must be enabled in clinic settings.
...                 [file:./doc/img/F01P07_diagram_contact_clinic_from_diary.png|F01P07 Diagram contact clinic from diary]

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Setup          Setup App Environment
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p07    patient-web    native-web


*** Test Cases ***
Main success scenario - Patient sends latest symptom entry from diary to clinic (web / app)
    [Tags]    nms9-ver-124
    Generate Random Patient Data
    Create An Activated Patient Via API
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    Login As Patient    ${patient_email}
    Patient Saves A Symptom Diary
    Navigate to Clinic
    Select Ask About Symptom Option
    Add A Symptom    Other symptom
    Select "Send This Symptom Entry" To Clinic
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    Symptom entry sent to the clinic
    Go To Patient Homepage
    Select Latest Symptom Diary Entry    Other symptom
    Wait Until Page Contains Element    ${symptom_diary_sent_to_clinic_label}
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
# TODO: Corner Case: Needs to confirm the lines below if still in use. Could not reproduce.
#    If the patient has added other diary symptom entries of type T whose occurrence dates are within the previous 14 days, and such entries have not yet been sent to the clinic, the patient can choose to include those entries along with the main symptom entry he's in the process of sending.
#    To not include, click the "No" radio button
#    Only the latest symptom entry will be sent
#    To include, click the "Yes" radio button
#    Symptom entries of type T from the previous 14 days are listed, and each entry has a checkbox which is already checked
#    The patient can uncheck the checkboxes of those symptom entries he or she does not want to be sent to the clinic

Extension A - Patient modifies latest symptom entry from diary and sends it to clinic (web / app)
    [Tags]    nms9-ver-125
    Add An Activated Patient Under Default Clinic    default
    Login As Patient    ${patient_email}
    Patient Saves A Symptom Diary
    Navigate to Clinic
    Select Ask About Symptom Option
    Add A Symptom    Other symptom
    Patient Modifies Symptom Entry
    Select Latest Symptom Diary Entry    Other symptom
    Wait Until Page Contains Element    ${symptom_diary_sent_to_clinic_label}
    Wait Until Page Contains    Moderate symptom
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Patient Saves A Symptom Diary
    Patient Adds Symptom From Diary    Other symptom
    Try To Input Text    ${other_symptom_description_field}    Mild symptom
    Select Symptomatic Day    Today
    Select Answer To Question    How would you rate the severity of your symptom?    Mild
    Select Answer To Question    Have you used any medication to alleviate your symptoms?    No
    Click Next Button
    Wait Until Element Is Visible    ${symptom_summary_section}
    Generic: Element Should Contain    ${symptom_summary_section}    Mild symptom
    Try To Click Element    ${symptom_report_save_button}
    Wait Until Page Contains    Symptom entry saved in your diary
    Click View Your Diary

Patient Modifies Symptom Entry
    Select "Modify This Symptom Entry"
    Click Next Button
    Try To Input Text    ${other_symptom_description_field}    Moderate symptom
    Select Answer To Question    How would you rate the severity of your symptom?    Moderate
    Click Next Button
    Wait Until Element Is Visible    ${symptom_summary_section}
    Generic: Element Should Contain    ${symptom_summary_section}    Mild symptom
    Try To Click Element    ${symptom_report_save_button}
    Wait Until Page Contains    Symptom entry sent to the clinic
    Go To Patient Homepage
