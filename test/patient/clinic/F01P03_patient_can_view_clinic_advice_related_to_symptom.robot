*** Settings ***
Documentation       F01P03 Patient can view clinic advice message related to symptom or question
...                 Preconditions:
...                 - Patient has symptom or question, it has been sent to clinic and clinic has responded.
...                 - Contact clinic feature must be enabled in clinic settings.

Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_education.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01p03    patient-web


*** Test Cases ***
Main - Clinic responded Instructions - Patient can view clinic's response to symptom and respond with instructions not sufficient
    [Documentation]    Also includes Main - <PERSON><PERSON> can view clinic advice message related to symptom or question (through log in screen and not notification)
    [Tags]    nms9-ver-117-1    nms9-ver-117    nms9-ver-8    nms9-ver-8-1    patient-email-link    native-web
    [Setup]    Setup App Environment
    Create New Patient With Mailosaur Email Via Api
    Patient Asks About Symptom
    Clinic User Has Responded With Contact Type    Instructions
    The Newly Created Patient Receives An Email About A New Message
    Setup App Environment
    Login As Patient    ${patient_email}
    Navigate To Clinic
    New Message Indicator Is Correct    1
    Select Latest Clinic Message
    Patient Responds If The Info Sufficient    No    Test not sufficient
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main - Clinic responded with Instructions sufficient - Open message from Diary - Patient can view clinic's response to symptom and respond with instructions sufficient
    [Documentation]    Also includes Main - Patient can view clinic advice message related to symptom or question (through log in screen and not notification)
    [Tags]    nms9-ver-117-2    nms9-ver-117    nms9-ver-8    nms9-ver-8-2    patient-email-link    native-web
    [Setup]    Setup App Environment
    Create New Patient With Mailosaur Email Via Api
    Patient Asks About Symptom
    Clinic User Has Responded With Contact Type    Instructions
    The Newly Created Patient Receives An Email About A New Message
    Setup App Environment
    Login As Patient    ${patient_email}
    Open Message
    Wait Until Element Is Visible    ${message_modal}
    Patient Responds If The Info Sufficient    Yes
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main - Clinic responded with question - Patient can view clinic's response to symptom and directly reply
    [Documentation]    Also includes Main - Patient can view clinic advice message related to symptom or question (through log in screen and not notification)
    [Tags]    nms9-ver-117-3    nms9-ver-117    nms9-ver-8    nms9-ver-8-3    patient-email-link    native-web
    [Setup]    Setup App Environment
    Create New Patient With Mailosaur Email Via Api
    Patient Asks About Other Issues
    Close All App Instances
    Clinic User Has Responded With Contact Type    Question
    The Newly Created Patient Receives An Email About A New Message
    Setup App Environment
    Login As Patient    ${patient_email}
    Navigate To Clinic
    New Message Indicator Is Correct    1
    Select Latest Clinic Message
    Wait Until Element Is Visible    ${message_modal}
    ${now}    Get Current Date    result_format=%Y-%m-%dT%H:%M:%S
    Reply To Clinic Message    Respond to clinic today ${now}
    Select Latest Clinic Message
    Wait Until Page Contains    Respond to clinic today ${now}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main - Clinic responded with invitation for physical examination - Patient can view clinic's response to symptom and directly reply
    [Documentation]    Also includes Main - Patient can view clinic advice message related to symptom or question (through log in screen and not notification)
    [Tags]    nms9-ver-117-4    nms9-ver-117    nms9-ver-8    nms9-ver-8-4    patient-email-link    native-web
    [Setup]    Setup App Environment
    Create New Patient With Mailosaur Email Via Api
    Patient Asks About Other Issues
    Close All App Instances
    Clinic User Has Responded With Contact Type    Invitation for a physical examination
    The Newly Created Patient Receives An Email About A New Message
    Setup App Environment
    Login As Patient    ${patient_email}
    Navigate To Clinic
    New Message Indicator Is Correct    1
    Select Latest Clinic Message
    Wait Until Element Is Visible    ${message_modal}
    ${now}    Get Current Date    result_format=%Y-%m-%dT%H:%M:%S
    Reply To Clinic Message    Respond to clinic today ${now}    new_message=yes
    Select Latest Clinic Message
    Wait Until Page Contains    Respond to clinic today ${now}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main - Clinic responded with invitation for clinical examination - Patient can view clinic's response to symptom and directly reply
    [Documentation]    Also includes Main - Patient can view clinic advice message related to symptom or question (through log in screen and not notification)
    [Tags]    nms9-ver-117-5    nms9-ver-117    nms9-ver-8    nms9-ver-8-5    patient-email-link    native-web
    [Setup]    Setup App Environment
    Create New Patient With Mailosaur Email Via Api
    Patient Asks About Other Issues
    Close All App Instances
    Clinic User Has Responded With Contact Type    Invitation for clinical tests
    The Newly Created Patient Receives An Email About A New Message
    Setup App Environment
    Login As Patient    ${patient_email}
    Navigate To Clinic
    New Message Indicator Is Correct    1
    Select Latest Clinic Message
    Wait Until Element Is Visible    ${message_modal}
    ${now}    Get Current Date    result_format=%Y-%m-%dT%H:%M:%S
    Reply To Clinic Message    Respond to clinic today ${now}    new_message=yes
    Select Latest Clinic Message
    Wait Until Page Contains    Respond to clinic today ${now}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main - Clinic responded with patient education with documents - Patient can view clinic's response to symptom and respond with instructions not sufficient
    [Documentation]    Also includes Main - Patient can view clinic advice message related to symptom or question (through log in screen and not notification)
    [Tags]    nms9-ver-117-6    nms9-ver-117    nms9-ver-8    nms9-ver-8-6    patient-email-link    native-web
    [Setup]    Setup App Environment
    Create New Patient With Mailosaur Email Via Api
    Patient Asks About Other Issues
    Close All App Instances
    Clinic User Has Responded With Contact Type    Patient Education
    The Newly Created Patient Receives An Email About A New Message    Patient Education
    Setup App Environment
    Login As Patient    ${patient_email}
    Navigate To Clinic
    New Message Indicator Is Correct    1
    Select Latest Clinic Message
    Wait Until Element Is Visible    ${document_attached_title}
    IF    'native' not in '${ENVIRONMENT}'    # browserstack is limited to native app interactions, cannot check web
        Try To Click Element    ${document_attached_title}
        Switch Window    NEW
        Sleep    1
        Wait Until Page Contains    Heart Health
        Switch Window    MAIN
        Sleep    1
    END
    Click See All Documents In The Library
    Select Latest Clinic Message
    Respond If The Info Sufficient As Patient    No    Test not sufficient
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - Clinic Initiated Message (Question)
    [Documentation]   this ext (nms9-ver-118) has been splitted into five parts to make it hybrid
    [Tags]     nms9-ver-118      nms9-ver-118-1      native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Create New Patient And Close Open Cases
        Search Patient By Identity Code    ${patient_ssn}
    ELSE
        Generate Random Patient Data
        Create An Activated Patient Via API
        ...    ${patient_education_clinic}[user_email]
        ...    ${PATIENT_EDUCATION_CLINIC_ID}
        ...    ${PATIENT_EDUCATION_SUB_ID_F01P03}
    END
    Contact Patient With Contact Type    ${case_type_filters}[question]
    [Teardown]    Delete Patient And Close Browser

Extension A - Clinic Initiated Message (Instructions)
    [Tags]     nms9-ver-118      nms9-ver-118-2      native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Create New Patient And Close Open Cases
        Search Patient By Identity Code    ${patient_ssn}
    ELSE
        Generate Random Patient Data
        Create An Activated Patient Via API
        ...    ${patient_education_clinic}[user_email]
        ...    ${PATIENT_EDUCATION_CLINIC_ID}
        ...    ${PATIENT_EDUCATION_SUB_ID_F01P03}
    END
    Contact Patient With Contact Type    ${case_type_filters}[instructions]
    [Teardown]    Delete Patient And Close Browser

Extension A - Clinic Initiated Message (Invitation To Clinical Tests)
    [Tags]     nms9-ver-118      nms9-ver-118-3      native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Create New Patient And Close Open Cases
        Search Patient By Identity Code    ${patient_ssn}
    ELSE
        Generate Random Patient Data
        Create An Activated Patient Via API
        ...    ${patient_education_clinic}[user_email]
        ...    ${PATIENT_EDUCATION_CLINIC_ID}
        ...    ${PATIENT_EDUCATION_SUB_ID_F01P03}
    END
    Contact Patient With Contact Type    ${case_type_filters}[invitation_clinical_tests]
    [Teardown]    Delete Patient And Close Browser

Extension A - Clinic Initiated Message (Invitation To Physical Examination)
    [Tags]     nms9-ver-118      nms9-ver-118-4      native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Create New Patient And Close Open Cases
        Search Patient By Identity Code    ${patient_ssn}
    ELSE
        Generate Random Patient Data
        Create An Activated Patient Via API
        ...    ${patient_education_clinic}[user_email]
        ...    ${PATIENT_EDUCATION_CLINIC_ID}
        ...    ${PATIENT_EDUCATION_SUB_ID_F01P03}
    END
    Contact Patient With Contact Type    ${case_type_filters}[invitation_physical_exam]
    [Teardown]    Delete Patient And Close Browser

Extension A - Clinic Initiated Message (Patient Education)
    [Tags]     nms9-ver-118      nms9-ver-118-5      native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Create New Patient And Close Open Cases
        Search Patient By Identity Code    ${patient_ssn}
    ELSE
        Generate Random Patient Data
        Create An Activated Patient Via API
        ...    ${patient_education_clinic}[user_email]
        ...    ${PATIENT_EDUCATION_CLINIC_ID}
        ...    ${PATIENT_EDUCATION_SUB_ID_F01P03}
    END
    Contact Patient With Contact Type    ${case_type_filters}[patient_education]
    [Teardown]    Delete Patient And Close Browser


*** Keywords ***
Create New Patient With Mailosaur Email Via Api
    Generate Random Patient Data    mailosaur=${f01p03_email_keys}[0]
    Generate Clinic Token
    ...    ${automated_tests_clinic}[default_user]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Add Patient To New Clinic Via API    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    Send Change User Password Request
    Log    ${patient_email}

Patient Asks About Symptom
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Ask About Symptom Option
    Complete Other Symptom Form    Mild
    Click Next Button
    Send Symptom Questionnaire To Clinic
    Go To Patient Homepage
    Close All App Instances

Patient Asks About Other Issues
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Ask About Other Issues

Clinic User Has Responded With Contact Type
    [Arguments]    ${response_type}
    Set Email And Delete Previous Messages    ${f01p03_email_keys}[0]    ${f01p03_email_keys}[1]
    ${value_in_api}    Set Variable If    '${response_type}'=='Instructions'    11
    ...    '${response_type}'=='Question'    18
    ...    '${response_type}'=='Invitation for a physical examination'    9
    ...    '${response_type}'=='Invitation for clinical tests'    10
    ...    '${response_type}'=='Patient Education'    38
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Search Patient By Identity Code    ${patient_ssn}
        IF    '${response_type}'=='Patient Education'
            Set Test Variable    ${with_docs}    yes
        END
        Select Symptom And Contact Patient With    ${response_type}
    ELSE
        Sleep    1s    # needed for case to reach clinic side
        Get First Patient Case via API    ${patient_id}
        IF    '${response_type}'=='Patient Education'
            Send Patient Education With Document To First Case Via API    ${first_case_id}
        ELSE IF    '${response_type}'!='Patient Education'
            Send Message Connected To First Case Via API    ${first_case_id}    ${value_in_api}
        END
    END

The Newly Created Patient Receives An Email About A New Message
    [Arguments]    ${type}=all others
    IF    'native' not in '${ENVIRONMENT}'
        IF    '${type}'=='Patient Education'
            Patient Received An Email About A New Message
            ...    ${patient_email}
            ...    New documents from your care team at ${automated_tests_clinic}[name]
            ...    Please log in to read the message and see the documents.
            ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
        ELSE
            Patient Received An Email About A New Message
            ...    ${patient_email}
            ...    New message from your care team at ${automated_tests_clinic}[name]
            ...    Your care team at ${automated_tests_clinic}[name] sent you a message
            ...    check_link=${PATIENT_URL_IN_MESSAGE}${PATIENT_PATH}
        END
    END

Patient Responds If The Info Sufficient
    [Arguments]    ${option}    ${message}=default
    IF    'native' not in '${ENVIRONMENT}'
        Verify Clinic Message Is Correct    ${random_string}
    ELSE
        Text Should Be In The Page    ${random_text}
    END
    Respond If The Info Sufficient As Patient    ${option}    ${message}

Contact Patient With Contact Type
    [Arguments]    ${contact_type}
    ${current_date}    Get Current Date
    IF    'native' not in '${ENVIRONMENT}'
        Contact Patient
        ...    ${contact_type}-${current_date}
        ...    ${contact_type}
        ...    pe_check=yes
        Close Browser
    ELSE
        IF    '${contact_type}'=='Question'
            Send Contact Patient Request     18
        ELSE IF    '${contact_type}'=='Instructions'
            Send Contact Patient Request     11
        ELSE IF    '${contact_type}'=='Invitation for clinical tests'
            Send Contact Patient Request     10
        ELSE IF    '${contact_type}'=='Invitation for a physical examination'
            Send Contact Patient Request     9
        ELSE IF    '${contact_type}'=='Patient Education'
            Send Contact Patient Request     38
        END
    END
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    IF    '${contact_type}'=='Question'
        Reply To Clinic Message    Reply to ${contact_type}-${current_date}
    ELSE IF    '${contact_type}'=='Instructions' or '${contact_type}'=='Patient Education'
        Respond If The Info Sufficient As Patient    No    Reply to ${contact_type}-${current_date}
    ELSE IF    '${contact_type}'=='Invitation for clinical tests' or '${contact_type}'=='Invitation for a physical examination'
        Reply To Clinic Message    Reply to ${contact_type}-${current_date}    yes
    END
    IF    'native' not in '${ENVIRONMENT}'
        Close Browser
        Login As Nurse    ${patient_education_clinic}[user_email]
        IF    '${contact_type}'=='Patient Education'
            ${contact_type}    Set Variable    Patient education
        END
        Select Case Type Filter In Work Queue    ${contact_type}
        Remove All Care Team Filter
        Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01p03_care_team]
        Click List View Button
        Case Type Is Displayed in List View    ${patient_ssn}    ${contact_type}
        Click Card View Button
        Sleep    1
        Select Patient Card    ${first_name}${SPACE}${family_name}
        Close Case
    END

Create New Patient And Close Open Cases
    Generate Random Patient Data
    Create An Activated Patient Via API
    ...    ${patient_education_clinic}[user_email]
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    ...    ${PATIENT_EDUCATION_SUB_ID_F01P03}
    Close Open Cases Per Care Team
    ...    ${automated_tests_clinic}[f01p03_care_team]
    ...    nurse_email=${patient_education_clinic}[user_email]

Delete Patient And Close Browser
    Close Browser
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${patient_education_clinic}[manager_email]
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    ...    ${PATIENT_EDUCATION_EHR_TOKEN}
