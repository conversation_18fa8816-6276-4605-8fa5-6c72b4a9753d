*** Settings ***
Documentation       F05P02 Patient can exchange anonymous messages with support about feedback
...                 Preconditions:
...                 - Patient has sent feedback to support and allowed support to contact the patient back
...                 - [file:./test/patient/more/F05P01.robot|F05P01 Patient has to be able to send anonymous feedback to support]
...                 - Support has replied to patient feedback
...                 - [file:./test/system_admin/support/F05SP01.robot|F05SP01 Support person can exchange anonymous messages with patient about feedback]

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}feedback.resource

Suite Setup         Set Libraries Order
Test Setup          Support Has Replied To F05P02 Patient Anonymous Feedback    ${f05p02_patient}[email]
Test Teardown       Close All Browsers

Force Tags          usecase-f05p02    manual


*** Test Cases ***
Main success scenario (web / app)
    [Tags]    nms9-ver-17    native-app-todo    management    manual
    Patient navigates to messages    ${f05p02_patient}[email]
    Patient opens message sent by support help desk person
    Patient enters reply message and selects send
    Support Receives Patient Reply
