*** Settings ***
Documentation       F02P03 Patient can register an user account
...                 Preconditions:
...                 - Patient self sign up is enabled in the environment.
...                 - Patient has IP-address of a country for which the feature is enabled.

Force Tags          usecase-f02p03    manual


*** Test Cases ***
Main success scenario
    Select Sign up
    Enter name and select Next
    Enter email address and select Next
    Enter mobile number and select Next
    Verification code is sent as SMS message
    Input verification code
    Next step is loaded automatically after the inputted code is correct
    Account is setup loading screen is displayed
    Set password andÂ select Next
    Set PIN code and confirm the code
    Confirmation screen is displayed
    If consent approval setting is enabled in clinic settings
    Approve terms of use by selecting the checkbox and select Next
    Approve data processing consent by selecting the checkbox and select Next
    Approve data use for research consent by selecting the checkbox and select Next
    If signup is done in native application, allow notification step is displayed
    Select Next and native notification setting dialog is displayed
    Select to allow or disallow notifications
    User is directed to Diary
    Default treatment module and care team is set for the patient account
    Module: Chemotherapy 18
    Care team: The first care team created in the self signup clinic
