*** Settings ***
Documentation       [F14P02] Patient can add diary events
...
...                 Main Scenario:
...                 - Login as a *********** patient
...                 - Patient adds an event
...                 - Patient selects a Treatment phase
...                 - Patient selects an Event type
...                 - Patient selects a date
...                 - Patient adds the location
...                 - Patient writes the notes of the event
...                 - Patient adds a photo
...                 - Patient saves the event and redirected to diary

Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}diary_events.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}appointment.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}common.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f14p02    patient-web


*** Variables ***
${file_sample_jpg_100kb}    file_example_JPG_100kB.jpg


*** Test Cases ***
Patient Can Add And Edit Diary Events
    [Documentation]    Combine Main success scenario and Ext A to avoid test case dependency
    ...    Main Success Scenario - Patient Can Add Diary Events
    ...    Extension A - Patient Can Display And Edit Event Details
    [Tags]    nms9-ver-292    nms9-ver-298    native-web    photo-upload
    [Setup]    F14P02 - Main - Test Setup
    Login As Patient    ${patient_email}
    Add An Event To Diary
    Personal Event Is Displayed In The Diary    ${personal_event_diagnosis}
    Compare Added Event In Timeline
    Open Selected Event
    Verify Event Details    ${expected_date_ddmm}
    Edit The Diary Event
    Personal Event Is Displayed In The Diary    ${personal_event_clinic_appointment}
    Compare Added Event In Timeline
    Open Selected Event
    Verify Event Details    ${expected_date_ddmm}
    Event Is Not Visible To Clinic Users    ${patient_ssn}
    [Teardown]    F14P02 - Test Teardown

Extension B - Types Of Events Displayed To The Patient
    [Documentation]    Also includes F12SY07 Extension A – Type of the events
    [Tags]    nms9-ver-299    nms9-ver-278    native-web
    [Setup]   F14P02 - Extension B - Test Setup
    Login As Patient    ${patient_email}
    Save To Diary Call To Action Is Displayed
    Send Appointment To Patient
    Add An Event To Diary
    Personal Event Is Displayed In The Diary    ${personal_event_diagnosis}
    Questionnaire Schedule Is Displayed In The Upcoming Events
    Appointment Schedule Is Displayed In The Upcoming Events    appointment_reason=Visit Dr. Jekyll for a cure
    Event Schedule Is Displayed In The Upcoming Events    Diagnosis date
    [Teardown]    F14P02 - Test Teardown


*** Keywords ***
Add An Event To Diary
    Select Event From Add Menu
    Select Treatment Phase    Diagnosis
    Select Event Type    ${personal_event_diagnosis}
    Select Tomorrow As Event Date
    Add Event Location    Helsinki
    Add Event Notes    Please bring your latest symptom form
    Add Event Photo    ${file_sample_jpg_100kb}
    Save Event

Edit The Diary Event
    Try To Click Element    ${event_modal_edit_button}
    Select Treatment Phase    Radiation therapy
    Select Event Type    Clinic appointment
    IF    'native' not in '${ENVIRONMENT}'    Remove Uploaded Photo
    Save Event
    Compare Added Event In Timeline
    Open Selected Event
    Verify Event Details    Tomorrow
    Try To Click Element    ${event_modal_close_button}

Compare Added Event In Timeline
    ${show_all_button_present}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${show_all_events}
    ...    timeout=5s
    IF    ${show_all_button_present}
        Try To Click Element    ${show_all_events}
        Wait Until Page Contains    ${end_of_event_list}
    END
    ${actual_event_type}    Get Text    //time[contains(text(),'${expected_time_label}')]/../preceding-sibling::*
    ${actual_date}    Get Text    //time[contains(text(),'${expected_time_label}')]/..
    ${actual_personal}    Get Text    //time[contains(text(),'${expected_time_label}')]/../following-sibling::*
    ${actual_timeline_event}    Convert To String    ${actual_event_type}${\n}${actual_date}${\n}${actual_personal}
    ${expected_timeline_event}    Convert To String    ${EVENT_TYPE_VALUE}${\n}${expected_time_tomorrow}${\n}PERSONAL
    Set Test Variable    ${actual_timeline_event}
    Should Be Equal    ${expected_timeline_event}    ${actual_timeline_event}

Personal Event Is Displayed In The Diary
    [Documentation]    Event added by the patient, marked as 'Personal', with calendar icon
    [Arguments]    ${event_type}
    ${show_all_button_present}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${show_all_events}
    ...    timeout=5s
    IF    ${show_all_button_present}
        Scroll Element Into View    ${show_all_events}
        Try To Click Element    ${show_all_events}
    END
    Sleep    1
    Wait Until Element Is Visible    ${show_all_upcoming_events}
    Scroll Element Into View    ${show_all_upcoming_events}
    Wait Until Page Contains    ${expected_time_label}    timeout=10s
    Wait Until Element Is Visible    //button[contains(@aria-label,'${event_type}')]
    Set Test Variable    ${expected_time_tomorrow}    Tomorrow, ${expected_time_label}
    ${visible_event_date_label}    Get Text    //button[contains(@aria-label,'${event_type}')]${event_tile_event_date}
    Should Be Equal    ${visible_event_date_label}    ${expected_time_tomorrow}
    ${visible_event_type_label}    Get Text    ${event_tile_event_personal_label}
    Should Be Equal     ${visible_event_type_label}    PERSONAL

Send Appointment To Patient
    Set Appointment Date In The Future    7 days
    Send Appointments Via FHIR API    ${location}    test2    ${patient_id}

F14P02 - Main - Test Setup
    Add An Activated Patient Under Appointment Clinic    f12p02-main
    Setup App Environment

F14P02 - Extension B - Test Setup
    Add An Activated Patient Under Appointment Clinic    f12p02-b
    Setup App Environment

F14P02 - Test Teardown
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${appointment_clinic}[manager_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_USER_TOKEN}
    Close All App Instances
