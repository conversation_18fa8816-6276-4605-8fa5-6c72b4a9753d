*** Settings ***
Documentation       F14P01 Patient can add diary notes
...
...                 Preconditions: - Patient *********** is logged in
...
...                 Main Scenario:
...                 - Login as a *********** patient
...                 - Patient adds a Diary Note
...                 - Patient selects a date
...                 - Patient selects Rate your well-being
...                 a. Rate the energy level
...                 b. Rate the appetite feeling
...                 c. Rate the optimistic feeling
...                 - Average of the set well-being rates is displayed in the note
...                 - <PERSON>ient writes notes about the day
...                 - Checks that note is saved in diary and user is directed to Diary
...                 - Patient uploads a photo after adding a note
...                 - Check that note is not visible to clinic users
...
...                 Extension A - Patient can display and edit note details
...                 - Patient selects Note from the timeline
...                 - Note details are displayed
...                 - Patient selects Edit
...                 - Patient edits note details
...                 - Patient selects Save
...                 - Patient is directed to Diary
...
...                 Extension B - Patient can view weekly well-being report in timeline
...                 - User added diary notes with well-being ratings at least once during a week.
...                 - On next Monday, weekly well-being report of the last week is displayed in patient diary timeline.
...                 - User can open daily notes from the weekly report.
...

Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}diary_note_wellbeing.resource
Resource            ${EXECDIR}${/}resources/nurse/compare_questionnaires.resource

Suite Setup         Set Libraries Order
Test Setup          Run Keywords    Setup App Environment    AND    Login As Patient    ${f14p01_patient1}[email]
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f14p01    usecase-f01p01c02    patient-web


*** Variables ***
${file_sample_jpg_100kb}    file_example_JPG_100kB.jpg


*** Test Cases ***
Report Todays Wellbeing
    [Tags]    nms9-ver-106    native-app-todo    pendo    nms9-ver-290    photo-upload
    Go To Diary
    User Selects Note From The Add Menu
    User Selects Date For The Note
    User Selects Rate Your Well-being
    User Sets Values For Well-being And Selects Next
    Average Of The Set Well-being Rates Is Displayed In The Note
    Verify Answered Wellbeing Rates
    User Writes Notes About The Day
    Verify Upload Button
    Add Diary Note Photo    ${file_sample_jpg_100kb}
    User Selects Save
    Note Is Saved In Diary And User Is Directed To Diary
    Verify Uploaded Image
    Note is not visible to clinic users    ${f14p01_patient1}[ssn]

Extension A - Patient can display and edit note details
    [Documentation]    This test case is dependent on the previous one
    [Tags]    nms9-ver-291    native-app-todo    pendo
    Go To Diary
    User Selects Note From The Timeline
    Note Details Are Displayed
    User Selects Edit
    User Updates Date For The Note
    User Clicks Wellbeing Average
    User Sets Values For Well-being And Selects Next    action=update
    Average Of The Set Well-being Rates Is Displayed In The Note
    Verify Answered Wellbeing Rates
    User Writes Notes About The Day
    Add Or Remove Photos    ${file_sample_jpg_100kb}
    User Selects Save
    Note Is Saved In Diary And User Is Directed To Diary
    User Is Directed To Diary

Extension B - Patient Can View Weekly Well-being Report In Timeline (web / app)
    [Documentation]    Verifies if the current timeline date is Monday and
    ...    checks if wellbeing summary is displayed, else skip the day
    ...    NOONA-17876: Summary of wellbeing report is displayed under a Sunday instead of Monday
    ...    Note: The tc was set back to manual due to the issue above that can only reproduce in automation
    [Tags]    nms9-ver-297    native-web    manual
    # TODO: Verify values for the week
    Go To Diary
    Verify Well-being Weekly Report On Monday Row

*** Keywords ***
User Sets Values For Well-being And Selects Next
    [Arguments]    ${action}=new
    IF    '${action}'=='update'    Reset Wellbeing Rating
    Rate With Vertical Slider
    Set Test Variable    ${ENERGY}    ${rating}
    Click Wellbeing Next Button
    IF    '${action}'=='update'    Reset Wellbeing Rating
    Rate With Vertical Slider
    Set Test Variable    ${APPETITE}    ${rating}
    Click Wellbeing Next Button
    IF    '${action}'=='update'    Reset Wellbeing Rating
    Rate With Vertical Slider
    Set Test Variable    ${OPTIMISM}    ${rating}
    Click Wellbeing Next Button

Average Of The Set Well-being Rates Is Displayed In The Note
    ${raw_ave}    Evaluate    (${ENERGY}+${APPETITE}+${OPTIMISM})/3
    ${raw_ave}    Convert To Number    ${raw_ave}    1
    ${raw_ave}    Convert To String    ${raw_ave}
    ${decimal}    Get Substring    ${raw_ave}    -2
    IF    '${decimal}'=='.0'
        ${expected_ave}    Fetch From Left    ${raw_ave}    .
    ELSE
        ${expected_ave}    Set Variable    ${raw_ave}
    END
    ${actual_ave}    Get Text    ${wellbeing_ave_modal}
    Should Be Equal    ${actual_ave}    ${expected_ave}
    Set Test Variable    ${actual_ave}

Reset Wellbeing Rating
    Wait Until Element Is Visible    ${vertical_slider_value_indicator}
    ${text}    Get Text    ${vertical_slider_value_indicator}
    ${current_value}    Convert To Number    ${text}
    ${current_value}    Convert To Integer    ${current_value}
    IF    ${current_value}>5
        ${iteration}    Evaluate    ${current_value}-5
        FOR    ${INDEX}    IN RANGE    0    ${iteration}
            Press Keys    ${vertical_slider_thumb}    ARROW_DOWN
            Sleep    1
        END
    ELSE IF    ${current_value}<5
        ${iteration}    Evaluate    5-${current_value}
        FOR    ${INDEX}    IN RANGE    0    ${iteration}
            Press Keys    ${vertical_slider_thumb}    ARROW_UP
            Sleep    1
        END
    END