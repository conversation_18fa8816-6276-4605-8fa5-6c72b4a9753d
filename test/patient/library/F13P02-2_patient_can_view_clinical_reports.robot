*** Settings ***
Documentation       F13P02-2 Patient can view clinical reports

Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinical_reports.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f13p02-2    patient-web


*** Variables ***
${sample_title_post}                Sample Clinical Report
${path_to_clinical_reports_json}    ${EXECDIR}${/}resources${/}patient${/}integration${/}clinical_reports.json
${loin_code}                        loin_code
${mrn_value}                        mrn_value
${report-title}                     report_title
${report-date}                      report_date
${sample_text}                      This is a test for Patient Document into Noona${\n}This should be in new line
&{cardiology_report}                loin=26441-6    name=Cardiology report
&{visit_summary_report}             loin=81218-0    name=Visit Summary Report


*** Test Cases ***
Main - Patient cannot view clinical reports - Setting Is Disabled
    [Tags]    nms9-ver-52-1    nms9-ver-52    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f13p02_patient_1}[email]
    Go To Library
    Wait Until Page Contains    Medical Records
    Page Should Not Contain Element    ${clinical_reports_bar}

Main - Patient can view clinical reports -Most Recent One Displayed First
    [Tags]    nms9-ver-52-2    nms9-ver-52    nms9-ver-401-1    native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Send Clinical Reports To Patient In Default Clinic
        ...    ${intake_service_patient_1}[ssn]
        ...    ${cardiology_report}[loin]
        ...    ${cardiology_report}[name]
        Login As Patient    ${intake_service_patient_1}[email]
        Go To Clinical Reports As Patient
    ELSE
        Set Header    ${NATIVE_APP_EHR_USER_TOKEN}
        Send Clinical Reports To Patient
        ...    ${intake_service_patient_app}[ssn]
        ...    ${cardiology_report}[loin]
        ...    ${cardiology_report}[name]
        Login As Patient    ${intake_service_patient_app}[email]
        Go To Clinical Reports As Patient
    END
    Wait Until Page Contains    ${clinical_report_title}    timeout=10s
    Reports are Displayed From The Most Recent One    ${clinical_report_title}
    # Next steps verify Extension B - Patient can’t see a Clinical report that has been deleted by the clinic
    IF    'native' not in '${ENVIRONMENT}'
        Remove Clinical Reports Sent To Patient    ${AUTOMATED_TESTS_EHR_TOKEN}
        Go To Library
        Go To Clinical Reports As Patient
    ELSE
        Remove Clinical Reports Sent To Patient    ${NATIVE_APP_EHR_USER_TOKEN}
        Navigate Back Using Header
        Go To Clinical Reports As Patient
    END
    IF    'native' not in '${ENVIRONMENT}'
        Page Should Not Contain    ${clinical_report_title}
    ELSE
        Page Should Not Contain Text    ${clinical_report_title}
    END

Main - Patient can view clinical reports
    [Tags]    nms9-ver-52-3    nms9-ver-52    native-web
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f13p02-2
    Button Is Displayed Even If Patient Doesn't Have Reports
    Send Clinical Reports To Patient In Default Clinic
    ...    ${patient_mrn}
    ...    ${cardiology_report}[loin]
    ...    ${cardiology_report}[name]
    Setup App Environment
    Login As Patient    ${patient_email}
    Go To Clinical Reports As Patient
    Wait Until Page Contains    ${clinical_report_title}    timeout=10s
    Select Clinical Report From List    ${clinical_report_title}
    User Can Click on Close Button or X icon
    Wait Until Element Is Visible    ${clinical_report_content}
    Generic: Element Should Contain    ${clinical_report_content}    ${sample_text}
    Generic: Element Should Contain    ${clinical_report_modal_title}    ${clinical_report_title}
    Try To Click Element    ${close_clinical_reports_modal_button}
    Patient Can Go Back To The Library Page

Extension A - Patient can open a PDF document in a clinical report
    [Tags]    nms9-ver-381-1    nms9-ver-381    nms9-ver-401-2    native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Add An Activated Patient Under Default Clinic    f13p02-2_exta
        Set Header
        Send Clinical Report With PDF document
        ...    ${patient_mrn}
        ...    ${visit_summary_report}[loin]
        ...    ${visit_summary_report}[name]
        Prepare Chrome for Downloads
        Login To Noona    patient    ${patient_email}    open_browser=False
    ELSE
        Add An Activated Patient Under Native App Clinic    f13p02-2_exta
        Set Header    ${NATIVE_APP_EHR_USER_TOKEN}
        Send Clinical Report With PDF document
        ...    ${patient_mrn}
        ...    ${visit_summary_report}[loin]
        ...    ${visit_summary_report}[name]
        Login As Patient    ${patient_email}
    END
    Go To Clinical Reports As Patient
    Wait Until Page Contains    ${visit_summary_report}[name]    timeout=10s
    Select Clinical Report From List    ${visit_summary_report}[name]
    Check Content Of The Clinical Pdf Report Modal
    IF    'native' not in '${ENVIRONMENT}'
        Try To Click Element    ${clinical_report_pdf_modal_download_button}
        # note: this step is skipped in Gitlab run, runs only locally
        IF    '${remote_url_exists}'=='False'    Verify PDF Download
    END
    Try To Click Element    ${clinical_report_pdf_modal_close_button}
    Wait Until Page Does Not Contain Element    ${clinical_report_pdf_modal_download_button}
    Wait Until Element Is Visible    ${clinical_report_header_page}
    # Next steps verify Extension B - Patient can’t see a Clinical report that has been deleted by the clinic
    IF    'native' not in '${ENVIRONMENT}'
        Remove Clinical Reports Sent To Patient    ${AUTOMATED_TESTS_EHR_TOKEN}
    ELSE
        Remove Clinical Reports Sent To Patient    ${NATIVE_APP_EHR_USER_TOKEN}
    END
    Go To Library
    Go To Clinical Reports As Patient
    Wait Until Page Contains    No documents yet.

Main - Patient can view clinical reports - delegate patient
    [Tags]    nms9-ver-52-4    nms9-ver-52    native-web
    [Setup]    Setup App Environment
    IF    'native' not in '${ENVIRONMENT}'
        Send Clinical Reports To Patient In Default Clinic
        ...    ${intake_service_patient_2}[ssn]
        ...    ${cardiology_report}[loin]
        ...    ${cardiology_report}[name]
        # Delegate patient can view diagnosticReport
        Login As Delegate    ${intake_service_patient_2}[delegate]
    ELSE
        Set Header    ${NATIVE_APP_EHR_USER_TOKEN}
        Send Clinical Reports To Patient
        ...    ${intake_service_patient_app}[ssn]
        ...    ${cardiology_report}[loin]
        ...    ${cardiology_report}[name]
        Login As Delegate    ${intake_service_patient_app}[delegate]
    END
    Go To Clinical Reports As Delegate User
    Wait Until Page Contains    ${clinical_report_title}    timeout=10s
    Reports are Displayed From The Most Recent One    ${clinical_report_title}
    Select Clinical Report From List    ${clinical_report_title}
    User Can Click on Close Button or X icon
    Wait Until Element Is Visible    ${clinical_report_content}
    Generic: Element Should Contain    ${clinical_report_content}    ${sample_text}
    Generic: Element Should Contain    ${clinical_report_modal_title}    ${clinical_report_title}
    Page Should Not Contain Element    ${clinical_report_pdf_modal_contact_clinic}
    Try To Click Element    ${close_clinical_reports_modal_button}

Extension A - Patient can open a PDF document in a clinical report - delegate patient
    [Tags]    nms9-ver-381-2    nms9-ver-381    native-web
    [Setup]    Setup App Environment
    Set Header
    IF    'native' not in '${ENVIRONMENT}'
        Set Header
        Send Clinical Report With PDF document
        ...    ${intake_service_patient_1}[ssn]
        ...    ${visit_summary_report}[loin]
        ...    ${visit_summary_report}[name]
        # Delegate patient can view documentReference
        Login As Delegate    ${intake_service_patient_1}[delegate]
    ELSE
        Set Header    ${NATIVE_APP_EHR_USER_TOKEN}
        Send Clinical Report With PDF document
        ...    ${intake_service_patient_app}[ssn]
        ...    ${visit_summary_report}[loin]
        ...    ${visit_summary_report}[name]
        # Delegate patient can view documentReference
        Login As Delegate    ${intake_service_patient_app}[delegate]
    END
    Go To Clinical Reports As Delegate User
    Wait Until Page Contains    ${visit_summary_report}[name]    timeout=10s
    Select Clinical Report From List    ${visit_summary_report}[name]
    Check Content Of The Clinical Pdf Report Modal    delegate
