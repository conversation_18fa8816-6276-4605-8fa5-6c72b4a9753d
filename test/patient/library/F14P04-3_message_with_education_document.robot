*** Settings ***
Documentation       F14P04-3 <PERSON><PERSON> can see the message sent with an education document
...                 - Main success scenario: See original message
...

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_education.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_url.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f14p04    patient-education    patient-web


*** Test Cases ***
Main success scenario: See Original Message
    [Tags]    nms9-ver-295    native-web
    [Setup]    Create A New Patient And Send Education Documents
    Login As Patient    ${patient_email}
    Go To Library
    Go To Education Documents
    Get Document Title    1
    Get Document Date    1
    See Original Message Of First Document
    Document Attached Title Is Correct    ${doc_title}
    Message Date Is Correct    ${doc_date}
    Wait Until Page Contains    Was the information provided helpful?
    Close Clinic's Message Dialog
    Wait Until Page Does Not Contain    Was the information provided helpful?
    IF    'native' not in '${ENVIRONMENT}'
        Location Should Contain    ${see_original_message_url_suffix}
    END
    Close All App Instances
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${patient_education_clinic}[manager_email]
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    ...    ${PATIENT_EDUCATION_EHR_TOKEN}

*** Keywords ***
Create A New Patient And Send Education Documents
    Set Application On Environment
    Generate Random Patient Data    name=f14p04-3-main
    Create An Activated Patient Via API
    ...    ${patient_education_clinic}[manager_email]
    ...    ${PATIENT_EDUCATION_CLINIC_ID}
    ...    ${PATIENT_EDUCATION_SUB_ID_CARETEAM_2}
    Send Contact Patient Request With Attachment
    ...    PE message to ${family_name}
    ...    ${attachment_name_survivorship_heart}
    ...    ${attachment_id_survivorship_heart}