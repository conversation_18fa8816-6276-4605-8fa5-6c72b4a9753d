*** Settings ***
Documentation       F13P02-3 Pat<PERSON> can view medication list

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}medication_list.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}integration_settings.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinic_settings.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f13p02-3    patient-web


*** Test Cases ***
Main - Patient can view medication list - Setting is disabled
    [Documentation]    Test case checks that medication list is not available in Library page if setting is disabled
    ...    Scenario A: ALL Library features are disabled
    ...    #TODO: Create a patient with only Medication List is disabled but other Library features are enabled
    [Tags]    nms9-ver-418-1    nms9-ver-418    native-web
    Verify Medication List Settings Is Disabled
    Login As Patient    ${f03na01_patient2}[email]
    Go To Empty Library
    Check That Medication List Is Not Displayed For Patient
    Logout As Patient

Main - Patient can view medication list - Setting is enabled
    [Documentation]    Test case checks that active medications are displayed for patient; step 2 in the main usecase is checked in the extension A
    [Tags]    nms9-ver-418-2    nms9-ver-418   native-web
    [Setup]    Setup App Environment
    Login As Patient    ${fiji_patient_1}[email]
    Go To Library
    Check That Medication List Is Displayed For Patient
    Check That All Medication List Page Elements Are Displayed
    Verify Medication List Content
    Verify Scroll Up Button Functionality
    Navigate Back Using Header

Extension A - Patient has no medications yet in ARIA
    [Tags]    nms9-ver-419    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${fiji_patient_2}[email]
    Go To Library
    Check That Medication List Is Displayed For Patient
    Check That Medication List Is Displayed With Empty State

Extension B - Error state for medication list
    [Tags]    nms9-ver-420    native-web
    [Documentation]    Not working correctly for the test patient - https://vocscs.atlassian.net/browse/NOONA-25964
    [Setup]    Setup App Environment
    Login As Patient    ${fiji_patient_3}[email]
    Go To Library
    Check That Medication List Is Displayed For Patient
    Check That Medication List Is Displayed With Error State

*** Keywords ***
Verify Medication List Settings Is Disabled
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Medication List Settings
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Disable Medication List Settings
        Save Settings
    ELSE IF    '${current_status}' == 'disabled'
        ${current_status}    Run Keyword    Check Status Of Medication List Settings
        Should Be Equal    ${current_status}    disabled
    END