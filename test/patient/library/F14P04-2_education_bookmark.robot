*** Settings ***
Documentation       F14P04-2 Pat<PERSON> can bookmark an education document
...                 - Main success scenario: Add a document to bookmarks
...                 - Extension A: Remove a document from bookmarks
...

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_education.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f14p04    patient-education    patient-web


*** Test Cases ***
Add And Remove Document To Bookmarks
    [Tags]    nms9-ver-293    nms9-ver-294    native-web
    [Setup]    Setup App Environment
    Add An Activated Patient Under Default Clinic    f14p04-2
    Send Patient Education To Patient
    Login As Patient    ${patient_email}
    Go To Library
    Go To Education Documents
    # nms9-ver-293: Add a document to bookmarks
    Add Document To Bookmarks    ${attachment_name_brain_tumors}
    Refresh Page To See Changes
    Document Is Bookmarked    ${attachment_name_brain_tumors}
    Document Is Listed In Other Documents    ${attachment_name_brain_metastases}
    # nms9-ver-294: Remove a document from bookmarks
    Remove Document To Bookmarks    ${attachment_name_brain_tumors}
    Refresh Page To See Changes
    Document Is Not Bookmarked    ${attachment_name_brain_tumors}
    Document Is Not Bookmarked    ${attachment_name_brain_metastases}
    Close All App Instances
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Send Patient Education To Patient
    Generate Clinic Token
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    Send Contact Patient Request With Attachment
    ...    f14p04-2-1 ${now}
    ...    ${attachment_name_brain_tumors}
    ...    ${attachment_id_brain_tumors}
    Send Contact Patient Request With Attachment
    ...    f14p04-2-2 ${now}
    ...    ${attachment_name_brain_metastases}
    ...    ${attachment_id_brain_metastases}

Refresh Page To See Changes
    [Documentation]    Needs refresh for changes to show
    Sleep    2s
    IF    'native' in '${ENVIRONMENT}'
        Navigate Back Using Header
        Go To Education Documents
    ELSE
        Reload Page
        Sleep    5s
    END
