*** Settings ***
Documentation       F14P05 Delegate User Can View Patient Library
...
...                 Preconditions:
...                 1. <PERSON><PERSON> has added a delegate user with a valid email address.
...                 2. Delegate user has activated their account.
...
...                 Main success scenario
...                 1. <PERSON><PERSON> has received at least one Patient Education message containing at least one attachment.
...                 2. <PERSON><PERSON> navigates to Library.
...                 3. <PERSON>ient clicks on the Education documents item.
...
...                 Extension A - Delegate user can see their last log in (web / app)
...                 Preconditions:
...                 - Delegate user has logged in to Noona previously



Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}account_preferences.resource



Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}


Force Tags          usecase-f14p05    patient-education      patient-web



*** Variables ***
${time_zone}                    Europe/Helsinki
${remote_time_zone}             Africa/Abidjan


*** Test Cases ***
#Main success scenario - Delegate user can view Patient Library
#    [Documentation]    test case scenarios are already checked as part of other tcs
#    [Tags]    nms9-ver-567
#    - Delegate User types the Noona Landing page URL
#    - Delegate User presses Email and password
#    - Delegate User enters their credentials and press Log in
#    - Noona Patient Library page is displayed with the content that the clinic has enabled



Extension A - Delegate user can see their last log in (web / app)
    [Tags]    nms9-ver-568
    Login As Patient    ${f02p01_last_login_patient}[delegate]
    Noona Patient Library Page Is Displayed
    Logout As Delegate User
    Close All App Instances
    Login As Patient    ${f02p01_last_login_patient}[delegate]
    Verify Last Login Time For Delegate



*** Keywords ***
Noona Patient Library Page Is Displayed
    Wait Until Element Is Visible      ${education_header}
    Wait Until Element Is Visible      ${delegate_logout_button}
    ${time}     Get Current Date
    ${current_date_time}      Convert Date      ${time}     result_format=%d.%m.%Y %H:%M
    Set Test Variable         ${current_date_time}

Verify Last Login Time For Delegate
    Wait Until Element Is Visible      ${education_header}
    Wait Until Element Is Visible      ${delegate_logout_button}
    Scroll Element Into View     ${delegate_logout_button}
    ${displayed_last_login}      Get Text      xpath=${delegate_last_login_element}
    ${remote_url_exists}     Run Keyword And Return Status      Variable Should Exist      ${REMOTE_URL}
    IF    ${remote_url_exists}
        ${expected}    Set Variable    Last Login: ${current_date_time} (Time Zone: ${remote_time_zone})
    ELSE
        ${expected}    Set Variable    Last Login: ${current_date_time} (Time Zone: ${time_zone})
    END
    ${status}     Run Keyword And Return Status    Should Be Equal    ${displayed_last_login}    ${expected}
    Run Keyword If    not ${status}    Capture Page Screenshot
    Run Keyword If    not ${status}    Fail    The actual Last login date and time (${displayed_last_login}) does not match expected (${expected})
