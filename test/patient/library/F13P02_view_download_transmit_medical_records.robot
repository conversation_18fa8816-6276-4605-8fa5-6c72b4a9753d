*** Settings ***
Documentation       F13P02 Patient can view, download and transmit medical records
...
...                 Preconditions:
...                 - Instructions: https://gitlab.com/varian-noona/development/core/noona/-/blob/master/docs/CCD/CCD.md#uploading-ccd-file
...                 - CCD data is uploaded in S3 bucket:
...                 - https://s3.console.aws.amazon.com/s3/buckets/noona-test-ccd?region=eu-west-1&prefix=16a4640a-424f-497c-acd2-1d5176273797/004-003-004-01/
...                 - Amazon S3 > noona-test-ccd > Folder = clinic id > patient MRN
...                 - TA clinic Patient_Education: clinic id = 16a4640a-424f-497c-acd2-1d5176273797
...                 - Test Patient MRN = 004-003-004-01
...                 - CCD file name format YYYY-MM-DDThh:mm:ss.000Z.xml = 2020-12-08T20:28:09.000Z.xml
...                 - Clinic Integration Settings has Medical Records feature enabled
...
...                 Steps:
...                 - Patient is logged in
...                 - Navigate to Library
...                 - View, Download, Transmit/Send (VDT) Medical Records

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All App Instances For Medical Records Cases
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f13p02    accessibility-wcag-a    chrome    patient-web


*** Variables ***
${download-wait}    2.5 min


*** Test Cases ***
Patient Can View Medical Records
    [Tags]    nms9-ver-285    native-web
    [Setup]    Setup App Environment
    Login As Patient    ${f13p02_patient}[email]
    Go To Library
    Click Medical Records Intro
    Select All Records
    Click View Patient Records
    Display CCD Record List
    Click CCD Record
    Close CCD Record Modals
    Click Open Access History
    Verify Medical History Details    ${f13p02_patient}[name]    Viewed medical records    ${MEDICAL_RECORD_1}

Extension A - Download records
    [Tags]    nms9-ver-286
    Prepare Chrome for Downloads
    Login To Noona    patient    ${f13p02_patient}[email]    open_browser=False
    Go To Library
    Click Medical Records Intro
    Select All Records
    Click Download Patient Records
    Display All CCD Record To Download
    Preview Record Before Download
    Download All Records
    IF    '${remote_url_exists}'=='False'    Dowloaded ZIP Contains HTML File
    # TODO: Download Individual Medical Records
    # The user downloaded an unencrypted zip file
    # THe zip file can be extracted without password
    Click Open Access History
    Verify Medical History Details
    ...    ${f13p02_patient}[name]
    ...    Downloaded medical records
    ...    ${ALL_DOWNLOAD_MEDICAL_RECORDS}

Extension B - Send records
    [Tags]    nms9-ver-287
    Set Test Variable    @{mailosaur_keys}
    ...    ${f13p02_email_keys}[0]
    ...    ${f13p02_email_keys}[1]
    Delete All Messages In Server
    ...    ${mailosaur_keys}[0]
    ...    ${mailosaur_keys}[1]
    Login As Patient    ${f13p02_patient}[email]
    Go To Library
    Click Medical Records Intro
    Select All Records
    Click View Patient Records
    Display CCD Record List
    Click CCD Record
    Close CCD Record Modals
    Click Send Records
    Display All CCD Record To Send
    Input Recipient Email    email=${f13p02_patient}[recipient_email]
    Click Send Medical Records
    Patient Received An Email About A New Message
    ...    ${f13p02_patient}[recipient_email]
    ...    Medical records
    ...    The medical records of ${f13p02_patient}[name] have been sent to you.
    ...    check_link=no
    # TODO: Check the attachement in the email and download the record
    Click Open Access History
    Verify Medical History Details
    ...    ${f13p02_patient}[name]
    ...    Sent medical records to ${f13p02_patient}[recipient_email]
    ...    ${ALL_MEDICAL_RECORDS}

Extension C - Open access history for records
    [Documentation]    Verification of each access history is at the end of Main scenario, Extensions A & B
    [Tags]    nms9-ver-288
    Login As Patient    ${f13p02_patient}[email]
    Go To Library
    Click Medical Records Intro
    Select All Records
    Click View Patient Records
    Display CCD Record List
    Click CCD Record
    Close CCD Record Modals
    Click Open Access History
    Verify Medical History Details    ${f13p02_patient}[name]    Viewed medical records    ${MEDICAL_RECORD_1}

Extension D - Launch DHIT portal
    [Tags]    nms9-ver-367
    Login As Patient    ${f13p02_patient_extd}[email]
    Go To Library
    Manage Medical Records
    Switch Window    NEW
    Location Should Be    https://www.varian.com/
    Close Browser
    Login As Patient    ${f13p02_patient_extd}[delegate]
    Manage Medical Records
    Switch Window    NEW
    Location Should Be    https://www.varian.com/


*** Keywords ***
Manage Medical Records
    Click Medical Records Intro
    Wait Until Page Contains    Connect other applications to your medical records
    Try To Click Element    ${manage_medical_records}
