*** Settings ***
Documentation       F13P02-1 Pat<PERSON> can view lab results
...
...                 Preconditions:
...                 - Test data under data/ehr_json is updated:
...                 - Diagnostic Report id and date should be unique for each successful post of data
...
...                 A. Initial Validation via API - Settings Enabled:
...                 - 1. Clinic Admin enables Laboratory Settings in Clinic settings > Integration tab
...                 - 2. Post EHR Laboratory report via API
...                 - 3. Verify Lab Report in Patient App via Library
...                 - 4. Delete EHR Laboratory report via API
...                 - 5. Verify Lab Report is not existing for the patient
...
...                 B. Initial Validation via API - Settings Disabled:
...                 - 1. Clinic Admin disables Laboratory Settings in Clinic settings > Integration tab
...                 - 2. Post EHR Laboratory report via API
...                 - 3. Verify Lab Report is not visible in Patient App via Library
...                 - 4. Delete EHR Laboratory report via API
...                 - 5. Verify Lab Report is not visible for the patient
...
...                 Main Scenario:    <PERSON><PERSON> can view lab results
...
...                 Native app:
...                 - 1. <PERSON><PERSON> gets notified once a day, when the first lab report is available to access in Noona.
...                     # TODO:- A. Noona message (visible in the inbox)
...                 - B. Push notification if user has app (Run "Verify Lab Result Push App Notification"
...                 while native app is open and user is logged in)
...                     # TODO:- C. If not then email as per patient’s notification settings.
...                 - 2. In settings push notifications can be toggled off.
...                     # TODO:- 3. Delegate user and proxy patient doesn't get notified.
...
...                 Test:
...                 - 1. Login as patient
...                 - 2. Navigate to Library
...                 - 3. Verify the latest laboratory result
...                 - a. Latest update date
...                 - b. Laboratory result history
...                 - c. Test name + datetime stamp
...                     # TODO: - d. Table with observation result
...                     # - d.1 the measured value with unit
...                     # - d.2 an indicator when the value is out of normal range
...                     # - d.3 when available the normal range intervals
...                     # - d.4 minichart shall be tested manually
...                     # - 4. Verify each row details in certain observation
...                     # - 5. Need to clarify HL7/JSON types for each observation results
...

Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}lab_reports.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}integration_settings.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}native_app${/}app_more.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource

Suite Setup         Set Libraries Order
Test Setup          Setup App Environment
Test Teardown       Close All App Instances
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f13p02-1    patient-web    native-web


*** Test Cases ***
Add Laboratory Report Via API
    [Tags]    nms9-ver-289-1    nms9-ver-289
    Add Laboratory Report Via API - Test Setup
    Set Report ID And Effective DateTime
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}
    IF    'native' not in '${ENVIRONMENT}'
        Patient Received An Email About New Lab Results    ${patient_email}    ${patient_education_clinic}[name]
    ELSE
        Patient Received An Email About New Lab Results    ${patient_email}    ${native_app_automated_tests}[name]
    END
    Login As Patient    ${patient_email}
    Verify Lab Report Is Visible In Patient UI    ${patient_email}    yes
    Latest Report Date Should Be Available
    Latest Date In Lab History Is Selected By Default
    Remove Laboratory Report Using FIHR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${lab_result_internal_id}
    ...    ${effectiveDateTime}
    [Teardown]    Add Laboratory Report Via API - Test Teardown

Disable Laboratory Reports
    [Tags]    nms9-ver-289-2    nms9-ver-289
    IF    'native' not in '${ENVIRONMENT}'
        Clinic Admin Disables Lab Results In Clinic Settings
        Set Test Variable    ${patient_mrn}    ${f13p02_1_patient}[ssn]
        Set Test Variable    ${patient_email}    ${f13p02_1_patient}[email]
        IF    'test' in '${ENVIRONMENT}'
            Set Test Variable    ${patient_id}    ${f13p02_1_patient}[id_test]
        ELSE
            Set Test Variable    ${patient_id}    ${f13p02_1_patient}[id_staging]
        END
        Set Test Variable    ${integration_user_token}    ${PATIENT_EDUCATION_EHR_TOKEN}
    ELSE    # for native app, patiet is under Appointment_Clinic and PE is disabled by default in settings
        Set Test Variable    ${patient_mrn}    ${f13p02_1_patient_disabled}[ssn]
        Set Test Variable    ${patient_email}    ${f13p02_1_patient_disabled}[email]
        IF    'test' in '${ENVIRONMENT}'
            Set Test Variable    ${patient_id}    ${f13p02_1_patient_disabled}[id_test]
        ELSE
            Set Test Variable    ${patient_id}    ${f13p02_1_patient_disabled}[id_staging]
        END
        Set Test Variable    ${integration_user_token}    ${APPOINTMENT_USER_TOKEN}
    END
    Set Report ID And Effective DateTime
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}
    Login As Patient    ${patient_email}
    Verify Lab Report Is Visible In Patient UI    ${patient_email}    no
    Remove Laboratory Report Using FIHR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${lab_result_internal_id}
    ...    ${effectiveDateTime}

Add Lab Results With HL7 Codes
    [Tags]    nms9-ver-289-3    nms9-ver-289
    # TODO: Compare HL7 codes
    Create New Patient To Receive Lab Reports    f13p02-1_main3
    Set Report ID And Effective DateTime
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}
    Login As Patient    ${patient_email}
    Verify Lab Report Is Visible In Patient UI    ${patient_email}    yes
    Latest Report Date Should Be Available
    Latest Date In Lab History Is Selected By Default
    Remove Laboratory Report Using FIHR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${lab_result_internal_id}
    ...    ${effectiveDateTime}

Update Lab Results
    [Documentation]    Includes also check for https://vocscs.atlassian.net/browse/NOONA-22286
    [Tags]    nms9-ver-289-4    nms9-ver-289
    Create New Patient To Receive Lab Reports    f13p02-1_main4
    Set Report ID And Effective DateTime
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}
    Login As Patient    ${patient_email}
    Verify Lab Report Is Visible In Patient UI    ${patient_email}    yes
    Text Should Be In The Page    ${report_title}
    Set New Effective DateTime
    Update Lab Report With FIHR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${lab_result_internal_id}
    ...    ${new_effectiveDateTime}
    Verify Lab Report Is Visible In Patient UI    ${patient_email}    yes
    Text Should Be In The Page    ${updated_report_title}
    IF    'native' not in '${ENVIRONMENT}'
        IF    'test' in '${ENVIRONMENT}'
            Update Lab Report With FIHR-R4 Endpoint With Wrong Patient ID
            ...    ${ehr_diagnostic_report_data_3}
            ...    ${f13p02_1_patient}[id_test]
            ...    ${lab_result_internal_id}
            ...    ${new_effectiveDateTime}
        ELSE
            Update Lab Report With FIHR-R4 Endpoint With Wrong Patient ID
            ...    ${ehr_diagnostic_report_data_3}
            ...    ${f13p02_1_patient}[id_staging]
            ...    ${lab_result_internal_id}
            ...    ${new_effectiveDateTime}
        END
    END

Verify Lab Result Push App Notification
    [Documentation]    Login as patient in native app to check the push app notification while running this test
    [Tags]    manual    nms9-ver-397
    Clinic Admin Enables Lab Results In Clinic Settings
    Set Report ID And Effective DateTime
    Post EHR Diagnostic Report WBC
    ...    ${ehr_diagnostic_report_data_2}
    ...    ${f13p02-1_patient}[ssn]
    ...    ${report_external_id}
    ...    ${effectiveDateTime}
    ## Test Data with Lab Results - can be used in Manual tests: Don't delete ##
    # ${mrn_id_patient_1}    MRN-695D
    # ${patient_email_1}    <EMAIL>
    # ${mrn_id_patient_2}    ***********
    # ${patient_email_2}    <EMAIL>
    # ${mrn_id_patient_3}    TESTS-LAB-001
    # ${patient_email_3}    <EMAIL>
    # ${mrn_id_patient_4}    NT3ST-001
    # ${patient_email_4}    <EMAIL>


*** Keywords ***
Add Laboratory Report Via API - Test Setup
    Set Test Variable    @{mailosaur_keys}    ${f13p02_1_email_keys}[0]    ${f13p02_1_email_keys}[1]
    Create New Patient To Receive Lab Reports    f13p02-1_main1    mailosaur_key=${f13p02_1_email_keys}[0]
    IF    'native' not in '${ENVIRONMENT}'
        @{message_data}    Patient Received Invitation To Use Noona
        ...    ${patient_email}
        ...    ${patient_education_clinic}[name]
    ELSE
        @{message_data}    Patient Received Invitation To Use Noona
        ...    ${patient_email}
        ...    ${native_app_automated_tests}[name]
    END
    Set Test Variable    ${invitation_email_id}    ${message_data}[1]
    Delete A Message In Server    ${mailosaur_keys}[1]    ${invitation_email_id}

Add Laboratory Report Via API - Test Teardown
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Close All App Instances
