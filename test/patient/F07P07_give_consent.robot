*** Settings ***
Documentation       F07P07 Patient can give consent for personal information processing
...                 and for utilising de-identified data
...                 1. Preconditions:
...                 - Clinic has enabled ask consent for personal information processing and/or utilising de-identified data feature
...                 2. Test clinic: TA Clinic Automated Test 4
...                 3. Basic settings, which are enabled, determine which consent content are shown to patients before entering
...                 <PERSON>ona's Dairy:
...                 "Ask patient if they accept Privacy Statement and Terms of Use"
...                 "Consent for process of personal information"
...                 "Patient can allow the use of their information for the development of cancer treatment and Noona"
...                 "E-signature is required from the patient"

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}single_patient_multi_clinic.resource
Resource            ${EXECDIR}${/}resources/patient/patient_common.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07p07    patient-web


*** Variables ***
${diary}    Diary
${email}    data:testid:emailAddress


*** Test Cases ***
Patient can give consent for personal information processing and for utilising de-identified data
    [Documentation]    For first time login, patient is prompted with request to approve terms of use and privacy statement
    ...    before giving consents
    ...    NOONA-22641: Fix to handle only the clicking of next button by adding re-ticking of checkbox after clicking Next.
    ...    Please see ticket's comments for details. Note that only Android is updated.
    [Tags]    nms9-ver-231    native-web
    [Setup]    Set Application On Environment
    Create New Patient And Login For The First Time
    Approve Terms And Click Next
    Give Consents And E-signature
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_4_clinic}[manager_email]
    ...    ${AUTOMATED_TESTS_4_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_4_TOKEN}

Extension A — Consent for personal information processing / research consent is updated (web / app)
    [Tags]    nms9-ver-232    native-app-todo
    Login As Patient    email=${f07p07_patient_a}[email]    remember_login=none
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${approve_elements_checkbox}
    ...    timeout=5s
    IF    ${status}    Give Consents And E-signature
    Close Browser
    Login As Nurse    clinic=${automated_tests_4_clinic}[name]    user_type=${USER_TYPE}[noona_admin]
    Modify Consents And Save
    Close Browser
    Login As Patient    email=${f07p07_patient_a}[email]    remember_login=none
    Give New Consents And E-signature
    Close Browser
    Returning Patient Can Login Without Consents    ${f07p07_patient_a}[email]
    Close Browser

Extension B — Consent for personal information processing / research consent is updated while patient is logged in (web / app)
    [Tags]    nms9-ver-233    native-app-todo
    Open URL In Chrome    ${PATIENT_LOGIN_URL}    alias=f07p07extb1
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${f07p07_patient_b}[email]
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${approve_elements_checkbox}
    IF    ${status}    Give Consents And E-signature
    Open URL In Chrome    ${NURSE_LOGIN_URL}    alias=f07p07extb2
    Accept All Cookies If Visible For Clinic
    Login Using SSO And Switch Clinic    ${automated_tests_4_clinic}[name]
    Modify Consents And Save
    Switch Browser    f07p07extb1
    Reload Page
    Give Consents And E-signature
    Close Browser
    Returning Patient Can Login Without Consents    ${f07p07_patient_b}[email]
    Close Browser

Extension C - Latest Consent for personal information processing of the clinic are not accepted while Patient is switching to the clinic (web / app)
    [Tags]    nms9-ver-234
    Open URL In Chrome    ${PATIENT_LOGIN_URL}    alias=f07p07extc1
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${f07p07_patient_c}[email]
    Approve Terms And Consent Only If Visible
    Wait Until Element Is Visible    ${spmc_clinic_toggle_header}
    ${first_clinic}    Get Text    ${spmc_clinic_toggle_header}
    IF    '${first_clinic}'!='${spmc_consent_a_clinic}[name]'
        Switch Clinic From Clinic Header    ${spmc_consent_a_clinic}[name]
        Wait Until Page Contains    ${spmc_consent_a_clinic}[name]
    END
    Open URL In Chrome    ${NURSE_LOGIN_URL}    alias=f07p07extc2
    Accept All Cookies If Visible For Clinic
    Login Using SSO And Switch Clinic    ${spmc_consent_b_clinic}[name]
    Modify Consents And Save
    Switch Browser    f07p07extc1
    Switch Clinic From Clinic Header    ${spmc_consent_b_clinic}[name]
    Approve Consents And Click Next
    Try To Click Banner Message
    Wait Until Page Contains    ${spmc_consent_b_clinic}[name]
    [Teardown]    Close All App Instances


*** Keywords ***
Create New Patient And Login For The First Time
    Generate Random Patient Data
    Create An Activated Patient Via API
    ...    ${automated_tests_4_clinic}[user_email]
    ...    ${AUTOMATED_TESTS_4_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_4_SUB_ID_CARETEAM_1}
    Login As Patient    ${patient_email}

Returning Patient Can Login Without Consents
    [Arguments]    ${patient_email}
    Login As Patient    email=${patient_email}    remember_login=none
    Wait Until Page Contains    ${diary}