*** Settings ***
Documentation       F07U03 User is notified if password is expiring
...                 Preconditions: User has received login link or temporary password and hasn't logged in.
...                 F11P01 Patient can respond to SMS invitation
...                 Preconditions: Patient has been invited to Noona with the SMS invitation feature.

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07u03    manual    patient-web


*** Variables ***
${phone_number_for_test}    +358408361997    # c/o JC


*** Test Cases ***
Main success scenario (web) - Reminder email - Patient
    [Documentation]    Note: password expiration is hardcoded in Noona to be 14 days after invitation is created
    ...    Also includes: F11P01 - Extension C - Email invitation link is expired (web)
    [Tags]    nms9-ver-28    nms9-ver-38    time-constraint    manual
    ${patient_identifier}    Get Current Date
    ...    result_format=%d-%m-%Y    # use date to identify when patient is created
    Set Test Variable    @{mailosaur_keys}    ${time_constraint_keys}[0]    ${time_constraint_keys}[1]
    Login As Nurse    ${time_constraint_testing}[manager_email]
    Set New Patient Mailosaur Email Server    ${mailosaur_keys}[0]
    Set To Dictionary    ${patient_dict}    first name=${patient_identifier}    care team=f07u03 care team
    Create New Patient With Robot
    Patient Received Invitation To Use Noona    ${patient_dict}[email]    ${time_constraint_testing}[name]
    # TODO: testing instructions nms9-ver-28:
    # 1. Log in as nurse (<EMAIL>)
    # 2. Select f07u03 care team
    # 3. Select a patient that was invited 2 weeks ago. Take note of email.
    # 4. Go to mailosaur with server: Single Patient Time Constraints Email
    # 5. Check for the notification 7, 5, 3, 1 day before the expiration date > Continue with usecase steps
    # TODO: testing instructions nms9-ver-38:
    # 1. Log in as nurse (<EMAIL>)
    # 2. Select f07u03 care team
    # 3. Select a patient that was invited more than 2 weeks ago. Take note of patient's email.
    # 4. Go to mailosaur with server: Single Patient Time Constraints Email
    # 5. Check for an expired invitation > Continue with usecase steps

Extension A - User receives reminder SMS - Patient
    [Documentation]    Note: password expiration is hardcoded in Noona to be 14 days after invitation is created
    ...    Also includes: F11P01 - Extension B - SMS invitation link is expired (web)
    [Tags]    nms9-ver-29    nms9-ver-37    time-constraint    manual
    ${patient_identifier}    Get Current Date
    ...    result_format=%d-%m-%Y    # use date to identify when patient is created
    Set Test Variable    @{mailosaur_keys}    ${time_constraint_keys}[0]    ${time_constraint_keys}[1]
    Login As Nurse    ${time_constraint_testing}[manager_email]
    Set New Patient Mailosaur Email Server    ${mailosaur_keys}[0]
    Set To Dictionary
    ...    ${patient_dict}
    ...    first name=${patient_identifier}
    ...    care team=f07u03 care team
    ...    mobile number=${phone_number_for_test}
    Create New Patient With Robot
    Patient Received Invitation To Use Noona    ${patient_dict}[email]    ${time_constraint_testing}[name]
    # TODO: testing instructions for nms9-ver-29:
    # 1. Log in as nurse (<EMAIL>)
    # 2. Select f07u03 care team
    # 3. Select a patient that was invited 2 weeks ago. Take note of invited date from the name.
    # 4. Check sms
    # 5. Check for the notification 7, 5, 3, 1 day before the expiration date > Continue with usecase steps
    # TODO: testing instructions for nms9-ver-37:
    # 1. Log in as nurse (<EMAIL>)
    # 2. Select f07u03 care team
    # 3. Select a patient that was invited 2 weeks ago. Take note of invited date from the name.
    # 4. Check sms
    # 5. Check for an expired invitation > Continue with usecase steps
