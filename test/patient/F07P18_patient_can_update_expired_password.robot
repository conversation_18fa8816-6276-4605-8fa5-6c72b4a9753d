*** Settings ***
Documentation       F07P18 Patient can update expired password
...                 Preconditions:
...                 - User has an active account
...                 - Clinic has set password expiry ON
...                 - Note: All active sessions will be terminated once the password is updated. After updating, an active session will be logged out automatically upon session renewal. A restart may be required to complete the session renewal for app.

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}update_password_page.resource
Resource            ${EXECDIR}${/}resources${/}common_setup.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          patient-web    usecase-f07p18


*** Test Cases ***
Main success scenario (WEB) - login
    [Documentation]    Checks update password at login.
    [Tags]    nms9-ver-528    nms9-ver-528-1    password_expiry
    Login As Patient    ${patient_expired_password_one}[email]
    Check That Update Password Page Is Displayed
    Add Valid Current Password
    Add Valid New Password
    Add Valid Confirmed Password
    Update Password From Login
    [Teardown]    F07P18 - Main success scenario - Test Teardown    patient_expired_password_one

Main success scenario (WEB) - links
    [Documentation]   NOONA-25248: patient blocked in Update pasword page and not directed to clinic page when coming from message link.
    [Tags]    nms9-ver-528    nms9-ver-528-2    password_expiry
    [Setup]    Test Setup For Links Sent To Patient
    Open URL In Chrome    ${link}
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${patient_expired_password_two}[email]
    Check That Update Password Page Is Displayed
    Add Valid Current Password
    Add Valid New Password
    Add Valid Confirmed Password
    Update Password From Message
    [Teardown]   F07P18 - Main success scenario - Test Teardown    patient_expired_password_two

Extension A Patient password expiry - Password change (APP)
    [Tags]    nms9-ver-532    manual
    # Patient opens the Noona app. Noona landing page is displayed.
    # Patient presses Log in. Noona log in page is displayed.
    # Patient enters Email address and Password and presses Log in. Patient is successfully logged into Noona in Keycloak and arrives to Noona. If the password has expired, Update password screen is displayed to the patient
    # Patient enters Current password. Patient can press SHOW to see the characters and press HIDE to not show those. NEXT button is disabled. NOTE: SHOW, HIDE buttons do not currently work consistently on all platforms, this will be fi xed in 9.7 release.
    # Patient enters New password. Patient can press SHOW to see the characters and press HIDE to not show those. NEXT button is disabled. Password validation fields are all checked once fulfilled.
    # Password validation starts when the fi rst letter of the new password is written. PW requirements are: At least 8 characters, At least one lowercase letter, At least one capital letter, At least one number, At least one special character (.!,:;?%/*+-$()@#), At least one character should be diff erent between the new and old passwords, Note: & " > < are not considered as special characters and are not allowed in a password
    # Patient enters Confirm password. Patient can press SHOW to see the characters and press HIDE to not show those. When no input fields are invalid, NEXT button is enabled
    # Patient presses Next-button. Spinner animation is displayed until the update is successful. When password has been successfully updated, Password updated modal is shown.
    # Patient closes the password updated modal. Patient is directed to Noona diary page. If the patient used a push notifi cation to log in, they are directed to the Noona Diary page.

Extension B - Patient password expiry - Password change incorrect flow
    [Tags]    nms9-ver-529    manual
    # Patient goes to Noona website url or Noona app. Noona landing page is displayed.
    # Patient presses Log in. Noona log in page is displayed.
    # Patient enters Email address and Password and presses Log in. Patient is successfully logged into Noona in Keycloak and arrives to Noona. If the password has expired, Update password screen is displayed to the patient
    # Patient enters incorrect Current password. Patient can press SHOW to see the characters and press HIDE to not show those. NEXT button is disabled. If no input is given and patient goes to some other field, This field is mandatory, please fill in the information. message is shown.
    # Patient enters New password that does not fulfill PW requirements. Patient can press SHOW to see the characters and press HIDE to not show those. NEXT button is disabled. If no input is given and patient goes to some other field, This fi ld is mandatory, please fill in the information. message is shown.
    # If patient enters one of these characters (& " > <) & " > < are not allowed message is displayed Password validation fields that are not fulfilled are not checked.
    # Password validation starts when the fi rst letter of the new password is written. PW requirements are:
    # At least 8 characters
    # At least one lowercase letter
    # At least one capital letter
    # At least one number
    # At least one special character (.!,:;?%/*+-$()@#)
    # At least one character should be different between the new and old passwords
    # Note: & " > < are not considered as special characters and are not allowed in a password
    # Patient enters New password that fulfill the PW requirements. Password validation fields are all checked once fulfilled.
    # Patient enters Confirm password that does not match the new PW. Patient can press SHOW to see the characters and press HIDE to not show those other.If no input is given and patient goes to some field, This field is mandatory, please fill in the information. message is shown.
    # Both password need to match message is displayed if the patient stops typing after 8 characters.
    # Patient enters Confirm password with a PW that matches the New password. If no input fields are invalid, NEXT button is enabled. Both password need to match message is displayed if the patient stops typing after 8 characters
    # Patient presses Next-button. Spinner animation is displayed until the system informs on the Current password field that The entered password is incorrect. NEXT button is disabled.
    # Patient makes a change to the Current password. The entered password is incorrect message disappears. NEXT button is enabled.
    # Patient presses NEXT button. Spinner animation is displayed. If the Current password was correct, Password updated modal is shown. If the current password was not correct, patient goes to step 9.
    # Patient closes the password updated modal. Patient is directed to Noona diary page.

Extension C - Patient password expiry - Forgot password flow
    [Tags]    nms9-ver-530    manual
    # Patient is on the Update password screen, they do not remember their password and press FORGOT PASSWORD? Check your email modal is shown, with the patients' email address visible.
    # Patient is taken back to the Update password page. The email is sent immediately once the Check your email modal is closed.
    # Patient receives the email, follow the link and update their password. Successfully reset password to a new one will count as a password change, and the updated timestamp is updated.

Extension D - Patient password expiry - Forgot password flow - patient enters Current password wrong 6 times
    [Tags]    nms9-ver-531    manual
    # Patient follows the Extension B till step 4
    # Patient enters New password that fulfill the PW requirements. Password validation fields are all checked once fulfilled.
    # Patient enters Confirm password with a PW that matches the New password. If no input fields are invalid, NEXT button is enabled. Both password need to match message is displayed if the patient stops typing after 8 characters.
    # Patient presses Next-button. Spinner animation is displayed until the system informs on the Current password field that The entered password is incorrect. NEXT button is disabled.
    # Patient makes a change to the Current password that is not correct. The entered password is incorrect message disappears. NEXT button is enabled.
    # Patient presses NEXT button. Spinner animation is displayed until the system informs on the Current password field that The entered password is incorrect. NEXT button is disabled.
    # Patient repeats steps 5 and 6 four (4) more times. Patient is directed to CHECK YOUR EMAIL modal. Patient is not locked due to too many failed log in attempts.
    # Patient closes the Check your email modal with Close button or X. Patient is taken back to the Update password page. The email is sent immediately once the Check your email modal is closed.
    # Patient receives the email, they follow the link and update their password. Successfully reset password to a new one will count as a password change, and the updated timestamp is updated.

*** Keywords ***
F07P18 - Main success scenario - Test Teardown
    [Arguments]    ${patient}
    Close Browser
    IF    'test' in '${ENVIRONMENT}' and '${patient}' == 'patient_expired_password_one'
        Set Test Variable    ${patient_id}    ${patient_expired_password_one}[id_test]
    ELSE IF    'staging' in '${ENVIRONMENT}' and '${patient}' == 'patient_expired_password_one'
        Set Test Variable    ${patient_id}    ${patient_expired_password_one}[id_staging]
    ELSE IF    'test' in '${ENVIRONMENT}' and '${patient}' == 'patient_expired_password_two'
        Set Test Variable    ${patient_id}    ${patient_expired_password_two}[id_test]
    ELSE IF    'staging' in '${ENVIRONMENT}' and '${patient}' == 'patient_expired_password_two'
        Set Test Variable    ${patient_id}    ${patient_expired_password_two}[id_staging]
    END
    Generate Clinic Token    ${ta_pasword_expiry}[clinic_manager]    ${DEFAULT_PASSWORD}    ${PASSWORD_EXPIRY_CLINIC_ID}
    Send Change User Password Request

Test Setup For Links Sent To Patient
    Set Email And Delete Previous Messages    ${f07p18_email_keys}[0]    ${f07p18_email_keys}[1]
    Generate Clinic Token
    ...    ${ta_pasword_expiry}[clinic_user_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${PASSWORD_EXPIRY_CLINIC_ID}
    IF    'test' in '${ENVIRONMENT}'
        Set Test Variable    ${patient_id}    ${patient_expired_password_two}[id_test]
    ELSE
        Set Test Variable    ${patient_id}    ${patient_expired_password_two}[id_staging]
    END
    Send Contact Patient Request    18
    @{message_data}    Patient Received An Email About A New Message
    ...    ${patient_expired_password_two}[email]
    ...    New message from your care team at ${ta_pasword_expiry}[name]
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/
    Set Test Variable    ${link}    ${message_data}[2]
    ${link}    Convert To String    ${link}
