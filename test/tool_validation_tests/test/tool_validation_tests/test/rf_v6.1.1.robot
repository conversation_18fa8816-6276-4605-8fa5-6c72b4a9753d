*** Settings ***
Resource        ${EXECDIR}${/}test/tool_validation_tests/test/tool_validation_tests/resources/resources1.resource
Resource        ${EXECDIR}${/}resources/patient/login.resource
Resource        ${EXECDIR}${/}resources/nurse/login.resource
Resource        ${EXECDIR}${/}resources/management/login.resource

Suite Setup     Set Libraries Order

Test Tags       run-all    manual


*** Test Cases ***
Check Patient Landing Page
    [Tags]    step4
    Go to the patient login in    chrome

Check Nurse Login Page
    [Tags]    step4
    Go to the nurse login in    firefox

Check Admin Login Page
    [Tags]    step4
    Go to the admin login in    chrome

Support item assignment with lists and dictionaries
    [Tags]    step5
    ${users}    Create List    <EMAIL>    <EMAIL>
    ${users}[1]    Set Variable    <EMAIL>
    Log List    ${users}
    ${roles}    Create Dictionary    manager=<EMAIL>    admin=<EMAIL>
    ${roles}[manager]    Set Variable    <EMAIL>
    Log Dictionary    ${roles}

Test with new while - FAIL
    [Documentation]    The test case is expected to fail
    [Tags]    step6
    Go to the patient login in    chrome
    ${login_button_visible}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${landing_page_login_button}
    WHILE    ${login_button_visible}    limit=3    on_limit_message=Button is still there
        ${login_button_visible}    Run Keyword And Return Status
        ...    Wait Until Element Is Visible
        ...    ${landing_page_login_button}
    END

Test with new while - PASS
    [Documentation]    The test case is expected to pass
    [Tags]    step6
    Go to the patient login in    chrome
    ${login_button_visible}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${landing_page_login_button}
    WHILE    ${login_button_visible}    limit=3    on_limit=PASS
        ${login_button_visible}    Run Keyword And Return Status
        ...    Wait Until Element Is Visible
        ...    ${landing_page_login_button}
    END

FOR IN ZIP loop behavior if lists lengths differ can be configured
    [Tags]    step7
    @{USERS}    Create List    clinic user    patient    clinic manager    clinic admin
    @{EMAILS}    Create List    <EMAIL>    <EMAIL>    <EMAIL>
    FOR    ${user}    ${email}    IN ZIP    ${USERS}    ${EMAILS}
        IF    'user' in '${user}'
            Login As Nurse    ${email}
        ELSE IF    'patient' in '${user}'
            Login As Patient    ${email}
        ELSE IF    'admin' in '${user}'
            Login As Admin    ${email}
        END
    END

New pseudo log level CONSOLE
    [Tags]    step8
    @{USERS}    Create List    clinic user    patient    clinic manager    clinic admin
    Log List    ${users}    level=CONSOLE


*** Keywords ***
Go to the ${user} login in
    [Arguments]    ${browser}
    IF    '${user}'=='patient'
        Open Browser    ${PATIENT_LOGIN_URL}    ${browser}
    ELSE IF    '${user}'=='nurse'
        Open Browser    ${NURSE_LOGIN_URL}    ${browser}
    ELSE IF    '${user}'=='admin'
        Open Browser    ${MANAGEMENT_LOGIN_URL}    ${browser}
    END
