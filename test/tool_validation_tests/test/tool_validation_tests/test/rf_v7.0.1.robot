*** Settings ***
Resource        ${EXECDIR}${/}test/tool_validation_tests/test/tool_validation_tests/resources/resources1.resource
Resource        ${EXECDIR}${/}resources/patient/login.resource
Resource        ${EXECDIR}${/}resources/nurse/login.resource
Resource        ${EXECDIR}${/}resources/management/login.resource

Suite Setup     Set Libraries Order
Test Teardown    Close Browser
Test Tags       all    manual    ask-about-other-issues

*** Test Cases ***
RF v7.0.1 - Test Case 1
    [Tags]    tc1
    VAR    ${message1}
    ...    This value is rather long.
    ...    It has been split to multiple lines.
    ...    Parts will be joined together with a space.
    Go to patient login page
    Enter login credentials    <EMAIL>    Password-1
    Navigate To Clinic
    Ask about other issues    Information about side effects    ${message1}
    Select Latest Clinic Message
    Wait Until Page Contains    ${message1}

RF v7.0.1 - Test Case 2
    [Tags]    tc2
    VAR    ${message2}
    ...    First line.
    ...    Second line.
    ...    Last line.
    ...    separator=\n
    Go to patient login page
    Enter login credentials    <EMAIL>    Password-1
    Navigate To Clinic
    Ask about other issues    Appointments and scheduling    ${message2}
    Select Latest Clinic Message
    Wait Until Element Is Visible    //div[@class='message-text ng-star-inserted']
    ${text}    Get Text    //div[@class='message-text ng-star-inserted']
    Should Be Equal    ${text}    ${message2}

RF v7.0.1 - Test Case 3
    [Tags]    tc3
    VAR    @{list_message}    first message    second message    third message
    Go to patient login page
    Enter login credentials    <EMAIL>    Password-1
    Navigate To Clinic
    Ask about other issues    Follow-up program    ${list_message}[2]
    Select Latest Clinic Message
    Wait Until Page Contains    ${list_message}[2]

RF v7.0.1 - Test Case 4
    [Tags]    tc4    -ask-about-other-issues
    Go to patient login page
    Enter login credentials    <EMAIL>    Password-1
    Navigate To Clinic
    ${loc}    Navigate To Patient Page And Get URL    library
    Should Be Equal    ${loc}    https://test.patient.noonatest.com/patient/#/library
    Logout As Patient


*** Keywords ***
Go to patient login page
    VAR    ${url_patient}    https://test.patient.noonatest.com/patient/#/sign-in
    Open Browser  ${url_patient}        headlesschrome

Enter login credentials
    [Arguments]    ${username}    ${password}
    Wait Until Element Is Visible    ${landing_page_login_button}
    Click element    ${landing_page_login_button}
    Input text    ${oidc_username_input}    ${username}
    Input text    ${oidc_password_input}    ${password}
    Click Element    ${oidc_login_button}

Ask about other issues
    [Arguments]    ${topic}    ${message}
    Select Ask about other issues
    VAR    ${ask_about_other_issues_button}    //label[contains(text(),'${topic}')]/..
    Wait Until Element Is Visible    ${ask_about_other_issues_button}
    Click Element    ${ask_about_other_issues_button}
    Input text    ${question_content_field}    ${message}
    Send question to clinic

Navigate To Patient Page And Get URL
    [Arguments]    ${page}
    IF    '${page}'=='clinic'
        Navigate To Clinic
    ELSE IF    '${page}'=='diary'
        Go To Diary
    ELSE IF    '${page}'=='library'
        Go To Library
    END
    ${loc}  Get Location
    RETURN    ${loc}