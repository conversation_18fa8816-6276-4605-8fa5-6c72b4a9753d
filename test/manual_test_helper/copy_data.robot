*** Settings ***
Documentation       Use the tests below to copy create data copying from test to staging

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource

Suite Setup         Set Libraries Order


*** Variables ***
${test_nurse}       <EMAIL>
${test_ssn}         N1987931    # update with the ssn of the patient you want to copy
${path}             ${EXECDIR}${/}data${/}analytics${/}all_patients_data${/}all_patients_data.xlsx


*** Test Cases ***
Create Test Data In Staging Environment
    [Tags]    copy_patient    manual
    Login As Nurse In Env    ${test_nurse}    test
    Get Patient Data From Test    ${test_nurse}    ${test_ssn}
    Close Browser
    Login As Nurse In Env    ${test_nurse}    staging
    Create Patient Data In Staging    ${test_nurse}
    Close Browser

Create Patient In Another Clinic
    [Tags]    manual
    Login As Nurse    ${test_nurse}
    ${patient_dict_2}    Get File    ${EXECDIR}${/}resources${/}nurse${/}patient_list.json
    ${patient_dict_2}    Evaluate    json.loads("""${patient_dict_2}""")    json
    Convert To Dictionary    ${patient_dict_2}
    Log Dictionary    ${patient_dict_2}
    Set Test Variable    ${patient_dict_2}
    @{patient_keys}    Get Dictionary Keys    ${patient_dict_2}
    FOR    ${patient_key}    IN    @{patient_keys}
        Run Keyword And Ignore Error    Create Patient In New Clinic    ${patient_key}
    END

Get Patient Details And Write In Excel
    [Tags]    get_all_patients    manual
    Get Patient Data From Test    ${test_nurse}    ${test_ssn}
    Open Excel Document    ${path}    all_patients
    ${next_row}    Get Next Empty Row
    ${next_row}    Convert To Integer    ${next_row}
    FOR    ${INDEX}    IN RANGE    1    13
        ${list_index}    Evaluate    ${INDEX}-1
        ${columns}    Read Excel Row    1    sheet_name=all_patients
        ${value}    Get From Dictionary    ${patient_dict}    ${columns}[${list_index}]
        Write Excel Cell    ${next_row}    ${INDEX}    ${value}    all_patients
    END
    Save Excel Document    ${path}

Copy Care Teams
    [Tags]    testcare    manual
    Login As Nurse In Env    <EMAIL>    test
    Try To Click Element    manage-nurses-link
    Try To Click Element    users-care-teams
    @{care_teams}    Create List
    Wait Until Element Is Visible    (//tbody/tr)[25]
    ${team_count}    Get Element Count    //tbody/tr
    FOR    ${INDEX}    IN RANGE    3    ${team_count}
        ${text}    Get Text    care-team-${INDEX}
        Append To List    ${care_teams}    ${text}
    END
    Login As Nurse In Env    <EMAIL>    staging
    Try To Click Element    manage-nurses-link
    Try To Click Element    users-care-teams
    FOR    ${care_team}    IN    @{care_teams}
        Try To Click Element    //*[@data-testid="add-care-team-template"]
        Try To Input Text    //*[@data-testid="name"]    ${care_team}
        Sleep    1
        Try To Click Element    //*[@data-testid="save-care-team"]
        Wait Until Page Contains    Changes saved
        Try To Click Banner Message
    END


*** Keywords ***
Get Patient Data From Test
    [Arguments]    ${email}    ${ssn}
    Search Patient By Identity Code    ${ssn}
    Choose General Information Tab
    Get Patient General Info

Login As Nurse In Env
    [Arguments]    ${email}    ${env}
    IF    '${env}'=='staging'
        Open Browser
        ...    https://staging.clinic.noonatest.com/nurse#/login
        ...    ${BROWSER}
        ...    options=add_experimental_option("detach", True)
    ELSE
        Open Browser
        ...    https://test.clinic.noonatest.com/nurse#/login
        ...    ${BROWSER}
        ...    options=add_experimental_option("detach", True)
    END
    Accept All Cookies If Visible For Clinic
    Try To Input Text    ${email_textbox}    ${email}
    Input Password    ${password_textbox}    ${DEFAULT_PASSWORD}
    Try To Click Element    ${login_button}
    Keep Me Logged In    Yes

Get Patient General Info
    Wait Until Element Is Visible    //*[@data-testid='ssn']
    ${get_status}    Get Text    ${User_status}
    ${get_ssn}    Get Value    //*[@data-testid='ssn']
    ${get_bday}    Get Value    //*[@data-testid='birthDate']
    ${attr_female}    Get Element Attribute    ${female_gender_radio_button}    class
    ${attr_male}    Get Element Attribute    ${male_gender_radio_button}    class
    ${get_gender}    Set Variable If    "radio-checked" in "${attr_female}"    Female    Male
    ${get_fname}    Get Value    //*[@data-testid='fname']
    ${get_lname}    Get Value    //*[@data-testid='lname']
    ${get_email}    Get Value    ${email_address_field}
    ##Wait Until Element Is Visible    //*[@data-testid='phoneNumberType1']/div
    ${get_phone_dropdown}    Get Text    //*[@data-testid='phoneNumberType1']/div/ng-select/div/div/div/div
    ${get_phone_number}    Get Value    //*[@data-testid='phoneNumber']
    Navigate To Questionnaires Tab
    Wait Until Element Is Visible    //div[@class='care-team']
    ${get_care_team}    Get Text    //div[@class='care-team']
    ${get_module}    Get Text    //div[@class='module-name link']
    Navigate To Diagnosis-tab
    Wait Until Element Is Visible    diagnosisCode
    Sleep    2
    ${get_icd}    Execute Javascript    return document.getElementById('diagnosisCode').innerText;
    ${get_icd}    Get Substring    ${get_icd}    0    3
    &{patient_dict}    Create Dictionary
    ...    status=${get_status}
    ...    social security number=${get_ssn}
    ...    date of birth=${get_bday}
    ...    gender=${get_gender}
    ...    first name=${get_fname}
    ...    last name=${get_lname}
    ...    email=${get_email}
    ...    mobile type=${get_phone_dropdown}
    ...    mobile number=${get_phone_number}
    ...    care team=${get_care_team}
    ...    module=${get_module}
    ...    icd-10 classification=${get_icd}
    Log Dictionary    ${patient_dict}
    Set Test Variable    ${patient_dict}

Create Patient Data In Staging
    [Arguments]    ${email}
    IF    "Proxy" in "${patient_dict}[status]"
        Create New Patient With Robot    proxy=${True}
    ELSE IF    "Username active" in "${patient_dict}[status]"
        Create New Patient With Robot
        Change Password    ${DEFAULT_PASSWORD}
    ELSE IF    "Username locked" in "${patient_dict}[status]"
        Create New Patient With Robot
    ELSE IF    "Username sent" in "${patient_dict}[status]"
        Create New Patient With Robot
    ELSE
        Fail    Please create patient manually or update the keyword.
    END
    Close Browser

Get Next Empty Row
    FOR    ${INDEX}    IN RANGE    1    100
        ${row}    Read Excel Cell    ${INDEX}    1    sheet_name=all_patients
        IF    '${row}'=='${None}'    RETURN    ${INDEX}
    END

Create Patient In New Clinic
    [Arguments]    ${patient_key}
    Set New Patient Data With Random Data
    ${email}    Get From Dictionary    ${patient_dict_2}    ${patient_key}
    Set To Dictionary    ${patient_dict}    social security number=${patient_key}    email=${email}
    Create New Patient With Robot
    Change Password    ${DEFAULT_PASSWORD}
    Reload Page
    Return To Patients
