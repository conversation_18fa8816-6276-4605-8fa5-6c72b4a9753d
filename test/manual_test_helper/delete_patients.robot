*** Settings ***
Documentation       Use this as a helper to manager test data
...                 This deletes patients from the list of patients that requested deletion.
...                 Update the loop or the script if needed.

Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers

*** Variables ***
${first_delete_patient_link}    (//*[@data-testid='deleted-patient-list-table']//td[7])[1]
${delete_patient_confirm}       //*[@data-testid='ok-confirm']

*** Test Cases ***

Delete Patients From Clinic 1
    [Tags]    deletepatients    manual
    Login As Nurse    ${automated_tests_clinic}[default_manager]
    Navigate To Patient Page
    Go To List Of Patients Tab
    ${status}    Run keyword and return status    Wait Until Element is Visible    ${first_delete_patient_link}    timeout=100s
    WHILE    ${status}    limit=50
        Wait Until Element Is Visible    ${first_delete_patient_link}
        Try To Click Element    ${first_delete_patient_link}
        Try To Click Element    ${delete_patient_confirm}
        Wait Until Element Is Visible     ${banner_toast_message}    timeout=100s
        Try To Click Element    ${banner_toast_message}    wait_in_seconds=15x
    END

Delete Patients From Clinic 2
    [Tags]    deletepatients1    manual
    Login As Nurse    ${appointment_clinic}[manager_email]
    Navigate To Patient Page
    Go To List Of Patients Tab
    ${status}    Run keyword and return status    Wait Until Element is Visible    ${first_delete_patient_link}    timeout=100s
    WHILE    ${status}    limit=50
        Wait Until Element Is Visible    ${first_delete_patient_link}
        Wait Until Element Is Enabled    ${first_delete_patient_link}
        Try To Click Element    ${first_delete_patient_link}    wait_in_seconds=15x
        Try To Click Element    ${delete_patient_confirm}
        ${status}    Run keyword and return status      Wait Until Element Is Visible     ${banner_toast_message}    timeout=100s
        IF    ${status}    Try To Click Element    ${banner_toast_message}    wait_in_seconds=15x
    END
    Close Browser