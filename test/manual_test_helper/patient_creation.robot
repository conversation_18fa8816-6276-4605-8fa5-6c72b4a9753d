*** Settings ***
Documentation       Helper for manual test using random data and mailosaur email/sms
...
...                 Steps:
...
...                 1. Change ${set_clinic_user} - clinic user that will create the patient
...
...                 2. Patient’s account will be created
...
...                 3. Password will be changed by the clinic user
...
...                 4. If enabled - patient login can be verified (commented as default)
...
...                 NOTE:
...                 Mailosaur Server - Noona Automated SMS,    Mobile Number - +*************
...                 Mailosaur Server - Noona Automated Email Random User
...

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource

Test Setup          Login As Nurse    ${patient_education_clinic}[user_email]
Test Teardown       Close All Browsers

# Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
# Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Force Tags          create-patient-helper    manual


*** Test Cases ***
Create Random Patient And Update Password
    Set New Patient Random Data With Mailosaur Email
    Create New Patient With Robot
    Change Password    password=${DEFAULT_PASSWORD}
    # Login As Patient    ${patient_dict}[email]
