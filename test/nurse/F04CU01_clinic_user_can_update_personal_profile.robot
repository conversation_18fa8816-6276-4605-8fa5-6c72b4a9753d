*** Settings ***
Documentation       Clinic manager can update personal profile
...                 - TODO: Update the other details when usecase is ready
...                 - For 2FA test case, check in mailosaur that there's no sms message
...                 - Receiving SMS is inconsistent and may fail. Debug manually
...
...                 - Test Clinic: Mailosaur - SMS
...                 - Clinic users:
...                 - <EMAIL>
...                 - <EMAIL>
...                 - <EMAIL>
...                 - <EMAIL>

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}nurse_profile.resource
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources/nurse/clinic_common.resource

Suite Setup         Set Libraries Order
Test Setup          Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f04cu01    clinic-web


*** Variables ***
${default_number}                           +35800000
${new_phone_number}                         +3581000011
${login_verification_code_header}           //h2[contains(text(),'Verification code')]
${login_verification_code_next_button}      //*[@id="two-factor-auth-next"]
${profile_incorrect_2FA_code_error}         //div[contains(text(),'Incorrect verification code.')]
${login_incorrect_2FA_code_error}           //p[contains(text(),'Incorrect verification code.')]
${contact_clinic_manager_text}              Contact clinic manager to make changes to your name and contact information


*** Test Cases ***
Clinic user can update personal profile - Main scenario (Lock Ability For Clinic User To Change Profile)
    [Tags]    nms9-ver-200-1    nms9-ver-200
    Login As Nurse    ${automated_tests_clinic}[f04cu01_nurse_email]
    Go To Nurse Profile
    Wait Until Page Contains    ${contact_clinic_manager_text}
    Profile Field Is Disabled    ${nurse_fname_field}
    Profile Field Is Disabled    ${nurse_lname_field}
    Profile Field Is Disabled    ${nurse_email_field}
    Update Nurse Phone Number    ${new_phone_number}
    ${email}    Convert To Lower Case    ${automated_tests_clinic}[f04cu01_nurse_email]
    Save Nurse Profile
    Input Password To Update Profile
    Verify If Contact Values Are Correct
    ...    ${automated_tests_clinic}[f04cu01_fname]
    ...    ${automated_tests_clinic}[f04cu01_lname]
    ...    ${email}
    ...    ${new_phone_number}
    [Teardown]    Reset Nurse Profile Phone Number

Clinic user can update personal profile - Main scenario (Lock Ability For Clinic Manager To Change Profile)
    [Tags]    nms9-ver-200-2    nms9-ver-200
    Login As Nurse    ${automated_tests_clinic}[f04cu01_manager_email]
    Go To Nurse Profile
    Wait Until Page Does Not Contain    ${contact_clinic_manager_text}
    Profile Field Is Enabled    ${nurse_fname_field}
    Profile Field Is Enabled    ${nurse_lname_field}
    Profile Field Is Enabled    ${nurse_email_field}
    Update Clinic User First Name    ${automated_tests_clinic}[f04cu01_manager_new_fname]
    Update Clinic User Last Name    ${automated_tests_clinic}[f04cu01_manager_new_lname]
    ${email_new}    Convert To Lower Case    ${automated_tests_clinic}[f04cu01_manager_new_email]
    Update Clinic User's Email    ${email_new}
    Update Nurse Phone Number    ${new_phone_number}
    Save Nurse Profile
    Input Password To Update Profile
    Verify If Contact Values Are Correct
    ...    ${automated_tests_clinic}[f04cu01_manager_new_fname]
    ...    ${automated_tests_clinic}[f04cu01_manager_new_lname]
    ...    ${email_new}
    ...    ${new_phone_number}
    [Teardown]    Reset Nurse Profile Values    ${automated_tests_clinic}[f04cu01_manager_fname]    ${automated_tests_clinic}[f04cu01_manager_lname]    ${automated_tests_clinic}[f04cu01_manager_email]

Two Factor Authentication For Updating Nurse's Profile
    [Documentation]    Note: Two-factor authentication cannot be disabled/enabled when mobile phone is edited. Edit has to be saved first.
    ...    - Mobile phone cannot be edited if two-factor authentication is enabled/disabled. Edit has to be saved first.
    ...    nurse clinic: Mailosaur SMS
    [Tags]    nms9-ver-200-3    nms9-ver-200    nurse-2fa    sms
    Login As Nurse With or Without 2FA
    IF    ${status}    Disable 2FA In Nurse Profile
    Delete All SMS From Mailosaur
    Login As Nurse    ${mailosaur_sms}[f04cu01_user2_email]
    Go To Nurse Profile
    Update Nurse Phone Number    ${mailosaur_number}
    Wait Until Keyword Succeeds    1x    3s    Scroll Element Into View    ${update_profile_save_button}
    Select Pasword Only Or With Verification Code Sent Via SMS    2FA
    Save Nurse Profile
    Input Password To Modal
    Input Verification Code
    Clinic User Clicks Log Out
    Close Browser
    ##Verify 2FA on login
    Login As Nurse With 2FA    ${mailosaur_sms}[f04cu01_user2_email]
    Verify Nurse Login Is Successful After 2FA    ${mailosaur_sms}[nurse_name]

User profile shows the last login time of the user
    [Tags]    nms9-ver-200-4    nms9-ver-200
    ${current_date_time}    Log In As A Clinic User And Get Last Login DateTime  clinic user    ${ENVIRONMENT}
    Relogin And Verify If Last Login Date And Time Is Correct  clinic user    ${current_date_time}    ${ENVIRONMENT}
    ${current_date_time}    Log In As A Clinic User And Get Last Login DateTime  clinic user oidc    ${ENVIRONMENT}
    Relogin And Verify If Last Login Date And Time Is Correct  clinic user oidc    ${current_date_time}    ${ENVIRONMENT}
    ${current_date_time}    Log In As A Clinic User And Get Last Login DateTime  clinic manager    ${ENVIRONMENT}
    Relogin And Verify If Last Login Date And Time Is Correct  clinic manager    ${current_date_time}    ${ENVIRONMENT}
    ${current_date_time}    Log In As A Clinic User And Get Last Login DateTime  clinic manager oidc    ${ENVIRONMENT}
    Relogin And Verify If Last Login Date And Time Is Correct  clinic manager oidc    ${current_date_time}    ${ENVIRONMENT}
    ${current_date_time}    Log In As A Clinic User And Get Last Login DateTime  clinic admin    ${ENVIRONMENT}
    Relogin And Verify If Last Login Date And Time Is Correct  clinic admin    ${current_date_time}    ${ENVIRONMENT}
    ${current_date_time}    Log In As A Clinic User And Get Last Login DateTime  noona admin    ${ENVIRONMENT}
    Relogin And Verify If Last Login Date And Time Is Correct  noona admin    ${current_date_time}    ${ENVIRONMENT}




*** Keywords ***
Nurse New Email Is Correct
    [Arguments]    ${expected_email}
    ${new_value}    Get Nurse Email Value
    Should Be Equal    ${new_value}    ${expected_email}

Reset Nurse Profile Values
    [Arguments]    ${fname}    ${lname}    ${email}
    Update Clinic User First Name    ${fname}
    Update Clinic User Last Name    ${lname}
    Update Clinic User's Email    ${email}
    Update Nurse Phone Number    ${default_number}
    Save Nurse Profile
    Input Password To Update Profile
    Close Browser

Login As Nurse With 2FA
    [Documentation]    Note: SMS verification code is automatically sent upon login
    [Arguments]    ${email}    ${remember_login}=Yes
    Delete All SMS From Mailosaur
    Login To Noona    nurse    ${email}
    Wait Until Element Is Visible    ${login_verification_code_header}
    Input Verification Code
    Try To Click Element    ${login_verification_code_next_button}
    Wait Until Keyword Succeeds    3x    1s    Keep Me Logged In    ${remember_login}

Verify Nurse Login Is Successful After 2FA
    [Arguments]    ${nurse_name}
    ${nurse_name_element}    Format String    ${nurse_name_in_header}    ${nurse_name}
    Wait Until Page Contains Element    ${nurse_name_element}
    Close Browser

Login As Nurse With or Without 2FA
    Delete All SMS From Mailosaur
    Login To Noona    nurse    ${mailosaur_sms}[f04cu01_user2_email]
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${login_verification_code_header}
    Set Test Variable    ${status}
    IF    ${status}
        Input Verification Code
        Try To Click Element    ${login_verification_code_next_button}
        Wait Until Keyword Succeeds    3x    1s    Keep Me Logged In    Yes
    END

Reset Nurse Profile Phone Number
    Update Nurse Phone Number    ${default_number}
    Save Nurse Profile
    Input Password To Update Profile
    Close Browser

Log In As A Clinic User And Get Last Login DateTime
    [Arguments]    ${user}    ${environment}
    IF    '${user}'=='clinic user'
        Login As Nurse    email=${automated_tests_clinic}[f04cu01_nurse_email]
    ELSE IF    '${user}'=='clinic user oidc'
        Log In To Clinic With SSO    ${SSO_CLINIC_USER}
    ELSE IF    '${user}'=='clinic manager'
        Login As Nurse    email=${automated_tests_clinic}[f04cu01_manager_email]
    ELSE IF    '${user}'=='clinic manager oidc'
        Log In To Clinic With SSO    ${SSO_CLINIC_MANAGER}
    ELSE IF    '${user}'=='clinic admin'
        Login As Nurse    email=${CLINIC_ADMIN}
    ELSE IF    '${user}'=='noona admin'
        IF    '${environment}'=='test'
            Log In To Clinic With SSO    ${automated_tests_clinic}[automatedtests_admin_oidc_test]
        ELSE IF    '${environment}'=='staging'
            Log In To Clinic With SSO    ${automated_tests_clinic}[automatedtests_admin_oidc_staging]
        END
    END
    ${current_date_time}    Get Current Date    result_format=%d.%m.%Y %H:%M
    Go To Nurse Profile
    Wait Until Element Is Visible    ${clinic_last_login_time}
    Close Browser
    RETURN    ${current_date_time}

Relogin And Verify If Last Login Date And Time Is Correct
    [Arguments]    ${user}    ${current_date_time}    ${environment}
    Sleep    60s    #to see the difference between previous login and now
    IF    '${user}'=='clinic user'
        Login As Nurse    email=${automated_tests_clinic}[f04cu01_nurse_email]
    ELSE IF    '${user}'=='clinic user oidc'
        Log In To Clinic With SSO    ${SSO_CLINIC_USER}
    ELSE IF    '${user}'=='clinic manager'
        Login As Nurse    email=${automated_tests_clinic}[f04cu01_manager_email]
    ELSE IF    '${user}'=='clinic manager oidc'
        Log In To Clinic With SSO    ${SSO_CLINIC_MANAGER}
    ELSE IF    '${user}'=='clinic admin'
        Login As Nurse    email=${CLINIC_ADMIN}
    ELSE IF    '${user}'=='noona admin'
        IF    '${environment}'=='test'
            Log In To Clinic With SSO    ${automated_tests_clinic}[automatedtests_admin_oidc_test]
        ELSE IF    '${environment}'=='staging'
            Log In To Clinic With SSO    ${automated_tests_clinic}[automatedtests_admin_oidc_staging]
        END
    END
    Wait Until Noona Loader Is Not Visible
    Go To Nurse Profile
    Last Login Date And Time Is Displayed    ${current_date_time}
    Close Browser