*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-9    api    manual


*** Test Cases ***
POST Request: Case Volumes
    Get Token
    Compare data: Case Volumes

Check status code, content type and reponse time
    Create a Session    4    2021    patient-cases    case-volumes    ${clinic}
    Check Response status code    5    39
    Check Response content type    6    39
    Check Response time    7    39

Status code tests
    Create a Session for fail test    patient-cases    case-volumes    ${clinic}    10    39
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
