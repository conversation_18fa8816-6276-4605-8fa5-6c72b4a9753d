*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-8    api    manual


*** Test Cases ***
POST Request: Cases answered in time
    Get Token
    Compare data: Cases answered in time

Check status code, content type and reponse time
    Create a Session    7    2021    patient-cases    answered-cases    ${clinic}
    Check Response status code    5    29
    Check Response content type    6    29
    Check Response time    7    29

Status code tests
    Create a Session for fail test    patient-cases    answered-cases    ${clinic}    10    29
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
