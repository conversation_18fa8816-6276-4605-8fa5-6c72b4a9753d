*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-7    api    manual


*** Test Cases ***
POST Request: Answered Cases Per Careteam
    Get Token
    Compare data: Answered Cases Per Careteam

Check status code, content type and reponse time
    Create a Session    4    2021    patient-cases    answered-cases-per-care-team    ${clinic}
    Check Response status code    5    34
    Check Response content type    6    34
    Check Response time    7    34

Status code tests
    Create a Session for fail test    patient-cases    answered-cases-per-care-team    ${clinic}    10    34
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
