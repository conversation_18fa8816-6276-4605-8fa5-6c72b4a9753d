*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-10    api    manual


*** Test Cases ***
POST Request: Delay Reasons
    Get Token
    Compare data: Delay Reasons

Check status code, content type and reponse time
    Create a Session    4    2021    patient-cases    delay-reasons    ${clinic}
    Check Response status code    5    49
    Check Response content type    6    49
    Check Response time    7    49

Status code tests
    Create a Session for fail test    patient-cases    delay-reasons    ${clinic}    10    49
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
