*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-11    api    manual


*** Test Cases ***
POST Request: Delayed Cases
    Get Token
    Compare data: Delayed Cases

Check status code, content type and reponse time
    Create a Session    4    2021    patient-cases    delayed-cases    ${clinic}
    Check Response status code    5    44
    Check Response content type    6    44
    Check Response time    7    44

Status code tests
    Create a Session for fail test    patient-cases    delayed-cases    ${clinic}    10    44
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month (delayed cases)
    Create a Session With empty request body
