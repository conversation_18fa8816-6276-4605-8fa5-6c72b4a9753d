*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-4    api    manual


*** Test Cases ***
POST Request: Reponse Rate
    Get Token
    Compare data: Response Rate

Check status code, content type and reponse time
    Create a Session    4    2021    engagement    response-rate    ${clinic}
    Check Response status code    5    19
    Check Response content type    6    19
    Check Response time    7    19

Status code tests
    Create a Session for fail test    engagement    response-rate    ${clinic}    10    19
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
