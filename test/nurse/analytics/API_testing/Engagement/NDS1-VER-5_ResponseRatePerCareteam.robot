*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-5    api    manual


*** Test Cases ***
POST Request: Response Rate Per Careteam
    Get Token
    Compare data: Response Rate Per Careteam

Check status code, content type and reponse time
    Create a Session    4    2021    engagement    response-rate-per-care-team    ${clinic}
    Check Response status code    5    24
    Check Response content type    6    24
    Check Response time    7    24

Status code tests
    Create a Session for fail test    engagement    response-rate-per-care-team    ${clinic}    10    24
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
