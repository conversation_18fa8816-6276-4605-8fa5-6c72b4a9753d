*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-6    api    manual


*** Test Cases ***
POST Request: Drop off Rate
    Get Token
    Compare data: Drop off Rate

Check status code, content type and reponse time
    Create a Session    3    2021    engagement    drop-off-rate    ${clinic}
    Check Response status code    5    69
    Check Response content type    6    69
    Check Response time    7    69

Status code tests
    Create a Session for fail test    engagement    drop-off-rate    ${clinic}    10    69
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
