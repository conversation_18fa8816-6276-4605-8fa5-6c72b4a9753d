*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-2    api    manual


*** Test Cases ***
POST Request: Touched Patients
    Get Token
    Compare data: Touched Patients

Check status code, content type and reponse time
    Create a Session    7    2021    enrolment    patients-touched    ${clinic}
    Check Response status code    5    14
    Check Response content type    6    14
    Check Response time    7    14

Status code tests
    Create a Session for fail test    enrolment    patients-touched    ${clinic}    10    14
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
