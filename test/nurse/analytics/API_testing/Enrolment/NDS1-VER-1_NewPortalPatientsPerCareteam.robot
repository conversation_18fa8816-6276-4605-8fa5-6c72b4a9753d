*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-1    api    manual


*** Test Cases ***
POST Request: New portal patients per care team
    Get Token
    Compare data: New Portal Patients per care team

Check status code, content type and reponse time
    Create a Session    7    2021    enrolment    new-portal-patients-per-care-team    ${clinic}
    Check Response status code    5    9
    Check Response content type    6    9
    Check Response time    7    9

Status code tests
    Create a Session for fail test    enrolment    new-portal-patients-per-care-team    ${clinic}    10    9
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
