*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-3    api    manual


*** Test Cases ***
POST Request: New portal patients
    Get Token
    Compare data: New Portal Patients

Check status code, content type and reponse time
    Create a Session    7    2021    enrolment    new-portal-patients    ${clinic}
    Check Response status code    5    4
    Check Response content type    6    4
    Check Response time    7    4

Status code tests
    Create a Session for fail test    enrolment    new-portal-patients    ${clinic}    10    4
    Create a Session With Expired Token
    Create a Session With Incorrect URL
    Create a Session With non-existent month
    Create a Session With empty request body
