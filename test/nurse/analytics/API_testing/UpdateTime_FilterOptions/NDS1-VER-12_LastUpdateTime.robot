*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-12    api    manual


*** Test Cases ***
GET Request: Filter Options
    Get Token
    Get Last Update time

Check status code, content type and reponse time
    # Create a Session    4    2021    patient-cases    delay-reasons
    Check Response status code    5    59
    Check Response content type    6    59
    Check Response time    7    59

Status code tests
    Create a Session for fail test    last-update    /    ${clinic}    10    59
    Last Update Time With Expired Token
    Last Update Time With Incorrect URL
    Last Update Time With Missing Header
