*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-13    api    manual


*** Test Cases ***
GET Request: Filter Options
    Get Token
    Get Filter Options    ${clinic}

Check status code, content type and reponse time
    # Create a Session    4    2021    patient-cases    delay-reasons
    Check Response status code    5    64
    Check Response content type    6    64
    Check Response time    7    64

Status code tests
    Create a Session for fail test    filters    options    ${clinic}    10    64
    Filter Options With Expired Token
    Filter Options With Incorrect URL
