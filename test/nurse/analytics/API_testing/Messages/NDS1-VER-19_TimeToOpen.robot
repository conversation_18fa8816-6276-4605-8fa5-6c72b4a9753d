*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-19    api    manual


*** Test Cases ***
POST Request: Time to Open
    Get Token
    Compare data: Time To Open

Check status code, content type and reponse time
    Create a Session    7    2019    clinic-messages    time-to-open    ${clinic_msg}
    Check Response status code    5    89
    Check Response content type    6    89
    Check Response time    7    89

Status code tests
    Create a Session for fail test    clinic-messages    time-to-open    ${clinic_msg}    10    89
    Create a Session With Expired Token for clinic-messages
    Create a Session With Incorrect URL clinic-messages
    Create a Session With non-existent month clinic-messages
    Create a Session With empty request body clinic-messages
