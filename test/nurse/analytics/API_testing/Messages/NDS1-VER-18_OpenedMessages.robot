*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-18    api    manual


*** Test Cases ***
POST Request: Opened Messages
    Get Token
    Compare data: Opened Messages

Check status code, content type and reponse time
    Create a Session    3    2019    clinic-messages    opened-messages    ${clinic_msg}
    Check Response status code    5    79
    Check Response content type    6    79
    Check Response time    7    79

Status code tests
    Create a Session for fail test    clinic-messages    opened-messages    ${clinic_msg}    10    79
    Create a Session With Expired Token for clinic-messages
    Create a Session With Incorrect URL clinic-messages
    Create a Session With non-existent month clinic-messages
    Create a Session With empty request body clinic-messages
