*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-17    api    manual


*** Test Cases ***
POST Request: Sent Messages Per Careteam
    Get Token
    Compare data: Sent Messages Per Careteam

Check status code, content type and reponse time
    Create a Session    3    2019    clinic-messages    messages-per-careteam    ${clinic_msg}
    Check Response status code    5    84
    Check Response content type    6    84
    Check Response time    7    84

Status code tests
    Create a Session for fail test    clinic-messages    messages-per-careteam    ${clinic_msg}    10    84
    Create a Session With Expired Token for clinic-messages
    Create a Session With Incorrect URL clinic-messages
    Create a Session With non-existent month clinic-messages
    Create a Session With empty request body clinic-messages
