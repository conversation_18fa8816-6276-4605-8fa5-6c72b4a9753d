*** Settings ***
Resource            ${EXECDIR}${/}resources${/}libraries.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}api_test.resource

Suite Setup         Open Excel Document    ${path}    test_summary
Suite Teardown      Close All Excel Documents

Force Tags          nds_test    nds1-ver-16    api    manual


*** Test Cases ***
POST Request: Sent Messages
    Get Token
    Compare data: Sent Messages

Check status code, content type and reponse time
    Create a Session    7    2019    clinic-messages    sent-messages    ${clinic_msg}
    Check Response status code    5    74
    Check Response content type    6    74
    Check Response time    7    74

Status code tests
    Create a Session for fail test    clinic-messages    sent-messages    ${clinic_msg}    10    74
    Create a Session With Expired Token for clinic-messages
    Create a Session With Incorrect URL clinic-messages
    Create a Session With non-existent month clinic-messages
    Create a Session With empty request body clinic-messages
