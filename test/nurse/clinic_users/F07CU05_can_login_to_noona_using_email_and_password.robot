*** Settings ***
Documentation       F07CU05 Clinic user can login to Noona using email and password
...                 Extension A - Clinic user logs in with a link that points to a page where they don't have access rights
...                 Extension B - Clinic user tries to log in to a deactivated clinic

Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07cu05    accessibility-wcag-a    login    clinic-user-login    clinic-web


*** Variables ***
${work_queue}                           new-messages
${invalid_code}                         777666
${clinic_2FA_status_incorrect_text}     //p[contains(text(),'Incorrect verification code.')]


*** Test Cases ***
Clinic user can login to Noona using email and password - Main success scenario
    [Tags]    manual    nms9-ver-472
    Precondition: Clinic user has accepted the cookie policy, and the the Noona login page is open.
    User types in email address
    Email address is shown.
    User types in password
    Indication that password is typed in is shown, but password is hidden.
    User presses log in OR presses enter on the keyboard
    Page opens up that asks user if they want to stay logged in if browser or tab are closed.
    If user chooses to remain logged in, they don't need to do these steps again. And Noona clinic home page opens
    If user chooses not to remain logged in, they will be logged out, if browser or tab is closed.

Extension A - Clinic user logs in with a link that points to a page where they don't have access rights - Clinic user
    [Tags]    nms9-ver-473-1    nms9-ver-473
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic user    Clinic users    ${CLINIC_USERS_LIST_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic user    Clinic information    ${CLINIC_INFORMATION_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic user    Automation rules    ${AUTOMATIC_RULES_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic user    Study sites    ${CLINIC_LINKING_REQUESTS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic user    Clinic data export    ${CLINIC_DATA_EXPORT}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic user    Operational analytics    ${OPERATION_ANALYTICS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic user    Patient user analytics    ${PATIENT_USER_ANALYTICS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic user    Reporting   ${REPORTING_LINK}

Extension A - Clinic user logs in with a link that points to a page where they don't have access rights - Clinic manager
    [Tags]    nms9-ver-473-2    nms9-ver-473
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic manager    Clinic settings    ${BASE_SETTINGS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic manager    Automation rules    ${AUTOMATIC_RULES_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic manager    Automation rules    ${AUTOMATIC_RULES_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic manager    Clinic data export    ${CLINIC_DATA_EXPORT}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic manager    Operational analytics    ${OPERATION_ANALYTICS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic manager    Patient user analytics    ${PATIENT_USER_ANALYTICS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic manager    Reporting   ${REPORTING_LINK}

Extension A - Clinic user logs in with a link that points to a page where they don't have access rights - Clinic admin
    [Tags]    nms9-ver-473-3    nms9-ver-473
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic admin    Clinic settings    ${CLINICAL_SETTINGS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic admin    Automation rules    ${AUTOMATIC_RULES_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic admin    Automation rules    ${AUTOMATIC_RULES_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic admin    Clinic data export    ${CLINIC_DATA_EXPORT}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic admin    Operational analytics    ${OPERATION_ANALYTICS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic admin    Patient user analytics    ${PATIENT_USER_ANALYTICS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    clinic admin    Reporting   ${REPORTING_LINK}

Extension A - Clinic user logs in with a link that points to a page where they don't have access rights - Noona admin
    [Tags]    nms9-ver-473-4    nms9-ver-473
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    noona admin    Patients    ${PATIENT_LIST_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    noona admin    Clinic data export    ${CLINIC_DATA_EXPORT}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    noona admin    Operational analytics    ${OPERATION_ANALYTICS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    noona admin    Patient user analytics    ${PATIENT_USER_ANALYTICS_LINK}
    User Is Directed To Homepage When Navigating To A Page They Don't Have Access To    noona admin    Reporting   ${REPORTING_LINK}

Extension B - Clinic user tries to log in to a deactivated clinic
    [Tags]    nms9-ver-425
    Open Page And Login    ${NURSE_LOGIN_URL}    ${disabled_clinic}[nurse]
    Wait Until Page Contains Element    ${login_page_loader}
    Wait Until Page Does Not Contain Element    ${login_page_loader}
    Location Should Be    ${current_url}
    Page Should Contain    ${welcome_to_noona_text}

Extension C - Clinic user logins with two-factor authentication
    [Tags]    nms9-ver-490    nurse-2fa    sms
    Login As Nurse With 2FA    ${automated_tests_clinic}[f07u01_2fa_user2]
    Wait Until Page Contains Element    ${work_queue}

Extension D - Several failed two-factor authentication attempts
    [Tags]    nms9-ver-483
    Open Page And Login    ${NURSE_LOGIN_URL}    ${automated_tests_clinic}[f07u01_2fa_user]
    Fail 2FA 5 times
    User Is Directed Login Main Page
    Close Browser

Extension E - User resends verification code
    [Tags]    nms9-ver-484    nurse-2fa    sms
    Nurse Logins And Resends Verification Code    ${automated_tests_clinic}[f07u01_2fa_user3]
    Wait Until Page Contains Element    ${work_queue}

Test following flows with all clinic setting cases
    [Tags]    manual
    Flow1: Login at https://test.noona.fi/patient/#/login, set remember me as YES, close tab/browser, go to https://test.noona.fi/patient/#/login to login again
    Flow2: Login with login link (with hash), set remember me as YES, close tab/browser, go to login link (with hash) to login again
    Flow3: Login at https://test.noona.fi/patient/#/login, set remember me as NO, close tab/browser, go to https://test.noona.fi/patient/#/login to login again
    Flow4: Login with login link (with hash), set remember me as NO, close tab/browser, go to login link (with hash) to login again
    Case 1: Authentication = Password; Remember password = 7 days; Remember login = 0 days
    Case 2: Authentication = Password; Remember password = 0 days; Remember login = 7 days
    Case 3: Authentication = Password; Remember password = 0 days; Remember login = 0 days
    Case 4: Authentication = Password; Remember password = 7 days; Remember login = 7 days


*** Keywords ***
Fail 2FA 5 times
    FOR    ${i}    IN RANGE    5
        Wait Until Page Contains Element    ${verification_code_first_input}
        Try To Click Element    ${verification_code_first_input}
        Press Keys    None    ${invalid_code}
        Try To Click Element    ${2fa_next_button}
        Wait Until Page Contains    Incorrect verification code
    END

User Is Directed Login Main Page
    Wait Until Page Contains Element    ${email_textbox}    20s

Nurse Logins And Resends Verification Code
    [Arguments]
    ...    ${email}=${automated_tests_clinic}[default_user]
    ...    ${clinic}=${automated_tests_clinic}[name]
    ...    ${remember_login}=Yes
    Delete All SMS From Mailosaur
    Login To Noona    nurse    ${email}
    ${clinic-check}    ${value}    Run Keyword And Ignore Error
    ...    Wait Until Element Is Visible    clinic    timeout=3s
    IF    '${clinic-check}'=='PASS'
        Wait until keyword succeeds    3x    1s    Choose Clinic    ${clinic}
    END
    Input Clinic User Verification Code
    Try To Click Element    ${2fa_next_button}
    # Need to resend verification code if 1st one failed due to one server sharing 2FA in mailosaur
    ${2fa_incorrect_status}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${clinic_2FA_status_incorrect_text}
    IF    ${2fa_incorrect_status}
        Run Keyword    Resend Clinic Verification Code
    END
    IF    '${remember_login}'!='none'
        Wait until keyword succeeds    3x    1s    Keep Me Logged In    ${remember_login}
    END

Resend Clinic Verification Code
    Delete All SMS From Mailosaur
    Try To Click Element    ${clinic_send_new_verification_code_link}
    Wait Until Page Contains    A new verification code has been sent
    Input Clinic User Verification Code
    Try To Click Element    ${2fa_next_button}

Input Email Address And Verify That It Is Shown
    [Arguments]    ${email}=${automated_tests_clinic}[default_user]
    Wait Until Element Is Visible    ${email_textbox}
    Input Text    ${email_textbox}    ${email}
    ${value}    Get Value    ${email_textbox}
    Should Be Equal    ${value}    ${email}

Input Password And Verify That It Is Hidden
    Input Password    ${password_textbox}    ${DEFAULT_PASSWORD}
    ${attr}    Get Element Attribute    ${password_textbox}    type
    Should Be Equal    ${attr}    password

Clinic User Chooses To Remain Logged In
    ${option}    Format String    ${keep_me_logged_in_radio}    ${keep_me_logged_in_yes}
    Try To Click Element    ${option}
    Try To Click Element    ${keep_me_logged_in_next}

Clinic User Chooses Not To Remain Logged In
    ${option}    Format String    ${keep_me_logged_in_radio}    ${keep_me_logged_in_no}
    Wait Until Element Is Visible    ${option}
    Try To Click Element    ${option}
    Try To Click Element    ${keep_me_logged_in_next}

User Is Directed To Homepage When Navigating To A Page They Don't Have Access To
    [Arguments]    ${user}    ${accessed_page}    ${accessed_link}
    IF    '${user}'=='clinic user'
        ${homepage_indicator}    Set Variable    ${nurse_work_queue}
        ${homepage_location}    Set Variable    ${WORK_QUEUE_OPEN_CASES_LINK}
    ELSE IF    '${user}'=='clinic manager'
        ${homepage_indicator}    Set Variable    ${nurse_work_queue}
        ${homepage_location}    Set Variable    ${WORK_QUEUE_OPEN_CASES_LINK}
    ELSE IF    '${user}'=='noona admin'
        ${homepage_indicator}    Set Variable    ${clinic_users_header_text}
        ${homepage_location}    Set Variable    ${CLINIC_USERS_LIST_LINK}
    ELSE IF    '${user}'=='clinic admin'
        ${homepage_indicator}    Set Variable    ${clinic_users_header_text}
        ${homepage_location}    Set Variable    ${CLINIC_USERS_LIST_LINK}
    END
    Open URL In Chrome    ${accessed_link}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Accept All Cookies If Visible For Clinic
    Login According User Role    ${user}
    Wait Until Element Is Visible    ${access_restricted_label}
    Page Should Contain    You are trying to access:
    Page Should Contain    ${accessed_page}
    Page Should Contain    Unfortunately you do not have the right to see this content
    Try To Click Element    ${access_restricted_go_to_homepage}
    Wait Until Element Is Visible    ${homepage_indicator}
    Location Should Be    ${homepage_location}
    Close Browser

Login According User Role
    [Arguments]    ${user}
    IF    '${user}'=='noona admin'
        Login To Clinic Using SSO    ${SSO_NOONA_ADMIN}
    ELSE
        IF    '${user}'=='clinic manager'
            Input Email Address And Verify That It Is Shown    email=${automated_tests_4_clinic}[auto4_manager]
        ELSE IF    '${user}'=='clinic admin'
            Input Email Address And Verify That It Is Shown    email=${automated_tests_4_clinic}[admin_email]
        ELSE IF    '${user}'=='clinic user'
            Input Email Address And Verify That It Is Shown    email=${automated_tests_4_clinic}[user_email]
        END
        Input Password And Verify That It Is Hidden
        Try To Click Element    ${login_button}
        Clinic User Chooses To Remain Logged In
    END
