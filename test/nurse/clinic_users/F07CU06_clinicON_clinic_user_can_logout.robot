*** Settings ***
Documentation       F07CU06 Clinic user can logout (Clinic OIDC)

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07cu06    noona_admin    clinic-oidc-on


*** Variables ***
${clinic_user_profile}                 //*[@class="profile-content"]
${clinic_user_profile_name}            //*[@id="user-name"]
${clinic_user_profile_clinic_name}     //*[@id="user-clinic"]

*** Test Cases ***
Main success scenario - clinic UI
    [Tags]    nms9-ver-516-1    nms9-ver-516    -noona_admin
    Log In To Clinic With SSO    ${SSO_CLINIC_USER}
    Wait Until Location Is    ${WORK_QUEUE_OPEN_CASES_LINK}
    Wait Until Noona Loader Is Not Visible
    Clinic User Is In Correct Clinic
    Logout As Nurse
    Wait Until Element Is Visible    ${sso_login_link}
    [Teardown]    Close Browser

Main success scenario - admin UI
    [Tags]    nms9-ver-516-2    nms9-ver-516
    Login As Admin
    Wait Until Location Is    ${MANAGEMENT_CLINICS_BASE_URL}/clinics
    Logout As Admin
    [Teardown]    Close Browser

*** Keywords ***
Clinic User Is In Correct Clinic
    [Arguments]    ${clinic_user_name}=${automated_tests_clinic_oidc_on}[clinic_user_name]    ${clinic_name}=${automated_tests_clinic_oidc_on}[name]
    Wait Until Page Contains Element    ${clinic_user_profile}
    ${clinic_user_name_label}    Get Text    ${clinic_user_profile_name}
    ${clinic_name_label}    Get Text    ${clinic_user_profile_clinic_name}
    Should Be Equal    ${clinic_user_name_label}    ${clinic_user_name}
    Should Be Equal    ${clinic_name_label}    ${clinic_name}
