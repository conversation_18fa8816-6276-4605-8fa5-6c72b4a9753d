*** Settings ***
Documentation       F04CA02_1 Noona administrator can update clinic user information (Clinic OIDC)

Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource
Resource            ${EXECDIR}${/}resources/nurse/clinic_users.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          clinic-web    clinic-oidc-on

*** Test Cases ***
Main Success Scenario
    [Tags]    nms9-ver-523
    Log In To Clinic With SSO    ${SSO_NOONA_ADMIN}    switch_to_clinic=${automated_tests_clinic_oidc_on}[name]
    Go To Clinic Users List
    Select Clinic User By Start Name    F04CA02_1
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    ${initial_email}    Set Variable    F04CA02_1.${now}@noona.fi
    ${expected_email}    Convert To Lower Case    ${initial_email}
    Update Clinic User Email    ${initial_email}
    User Roles Group Should Be Disabled
    Save User Information
    Try To Click Banner Message
    Data Is Visible In User List    ${expected_email}