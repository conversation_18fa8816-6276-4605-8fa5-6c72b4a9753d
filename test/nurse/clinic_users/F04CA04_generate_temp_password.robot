*** Settings ***
Documentation       F04CA04 Clinic administrator can generate temporary password for clinic user, unlock if locked and send the login information to the clinic user
...                 - Clinic administrator has logged in.
...                 - #TODO : Check from email

Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse    clinic=${custom_branding_automated_test}[name]    user_type=${USER_TYPE}[noona_admin]
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f04ca04    clinic-web    noona_admin


*** Variables ***
${user_email}       <EMAIL>


*** Test Cases ***
Generate Temporary Password
    [Tags]    nms9-ver-203
    Navigate To Clinic Users
    Select User    ${user_email}
    Send Random Password

System Unlocks User Using Random Password
    [Tags]    nms9-ver-204
    Navigate To Clinic Users
    Select User    ${user_email}
    Toggle Lock    Lock
    Verify User From List    ${user_email}    locked=Yes
    Select User    ${user_email}
    Send Random Password
    Wait Until Page Contains    New password sent to user
    Cancel User Information
    Reload Page
    Verify User From List    ${user_email}    locked=No
