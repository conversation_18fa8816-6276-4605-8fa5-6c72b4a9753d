*** Settings ***
Documentation       F04CA06 Clinic administrator can manage clinic care teams
...                 Preconditions:
...                 - Clinic administrator has logged in.
...                 - Care Team 1 is linked to an active patient treatment module
...                 For Ext-B: Mailosaur Server - New Case Email

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_emails.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_users.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f04ca06    clinic-web


*** Variables ***
${CARE_TEAM}                Set in Create care team
${f04ca06-ext-b_email}      <EMAIL>
${ext-b_care_team}          EmailCareTeam
${ext-b_server_id}          w9woemno
${ext-b_server_domain}      w9woemno.mailosaur.net
${ext-b_api_key}            AxlrVhpKytP6bCnE


*** Test Cases ***
Clinic Administrator Can manage Clinic Care Teams
    [Documentation]    This test covers 3 scenarios: main, extension A - edit a care team & extension C - remove a care team
    [Tags]    nms9-ver-206    nms9-ver-207    nms9-ver-208    noona_admin
    # nms9-ver-206 Main success scenario
    Login As Nurse    user_type=${USER_TYPE}[noona_admin]
    Navigate to "Clinic users"
    Select The "Care teams" Tab
    Click "Add new care team"
    The "Add new care team" dialog opens
    Enter a new care team name and click "Save"
    The name of the new care team is displayed in the list of care teams
    Click the names of one of the care teams on the list    ${CARE_TEAM_1}
    # nms9-ver-207 Extension A - Edit care team
    Noona opens the "Edit care team" dialog
    Edit the name of the care team name and click "Save"
    Noona displays a confirmation toaster message
    The edited name now appears in the list of care teams
    # nms9-ver-208 Extension C - Remove care team
    Click the names of one of the care teams on the list    ${CARE_TEAM_1}
    Noona opens the "Edit care team" dialog
    Click "Remove"
    If the care team can be removed, Noona displays a confirmation toaster message
    # and the removed care team no longer appears on the list of care teams
    # The care team can be removed only if it's not linked to any active patient treatment module
    Click The Names Of One Of The Care Teams On The List    Care Team 1
    Noona Opens The "Edit care team" Dialog
    Click "Remove"
    Verify Care Team 1 cannot be removed
    [Teardown]    Close Browser

Clinic Administrator Can manage Clinic Care Teams - Extension B — Add New Case Email To Care Team
    [Tags]    nms9-ver-15    nms9-ver-15-1    noona_admin
    Delete All Messages In Server    ${ext-b_server_id}    ${ext-b_api_key}
    Login As Nurse    user_type=${USER_TYPE}[noona_admin]
    # Precondition: Enable "Email notification for new work queue cases" from case management settings
    Navigate to "Clinic users"
    Select The "Care teams" Tab
    Click The Names Of One Of The Care Teams On The List    ${ext-b_care_team}
    Noona Opens The "Edit care team" Dialog
    Verify Checkbox "Send an email notification for new cases assigned to this team" Is Displayed
    Select Checkbox "Send an email notification for new cases assigned to this team" And Add Email Address To Care Team
    Close Browser
    Emails Will Be Now Sent Whenever New Case Or Report Is Created For This Care Team
    [Teardown]    Disable "Send an email notification for new cases assigned to this team"    ${ext-b_care_team}


*** Keywords ***
Create Suite Care Team Name
    ${date}    Get Current Date    result_format=%Y%m%d%H%M
    Set Suite Variable    ${CARE_TEAM_1}    Autotest Team-${date}

Navigate to "Clinic users"
    Try To Click Element    ${user_list_tab}

Select the "Care teams" tab
    Wait Until Element Is Enabled    ${care_teams_tab}
    Try To Click Element    ${care_teams_tab}

Click "Add new care team"
    Wait Until Element Is Enabled    ${care_teams_tab}
    Try To Click Element    ${add_care_team_button}

The "Add new care team" dialog opens
    Wait Until Element Contains    ${dialog_title}    Add new care team
    Wait Until Element Is Enabled    ${care_team_input}

Enter a new care team name and click "Save"
    Create Suite Care Team Name
    Try To Input Text    ${care_team_input}    ${CARE_TEAM_1}
    Textfield Value Should Be    ${care_team_input}    ${CARE_TEAM_1}
    Try To Click Element    ${save_care_team_button}
    Try To Click Banner Message

The name of the new care team is displayed in the list of care teams
    Wait Until Page Contains    ${CARE_TEAM_1}

Click The Names Of One Of The Care Teams On The List
    [Arguments]    ${care_team_name}
    Wait Until Element Is Enabled    //td[contains(text(),'${care_team_name}')]
    Try To Click Element    //td[contains(text(),'${care_team_name}')]

Noona Opens The "Edit care team" Dialog
    Wait Until Page Contains    Edit care team

Edit the name of the care team name and click "Save"
    Try To Input Text    ${care_team_input}    -0- ${CARE_TEAM_1} - edited
    Try To Click Element    ${save_care_team_button}

Noona displays a confirmation toaster message
    Wait Until Page Contains    ${label-changes-saved}
    Wait Until Page Does Not contain    ${label-changes-saved}

The edited name now appears in the list of care teams
    Wait Until Page Contains    -0- ${CARE_TEAM_1} - edited

Click "Remove"
    Try To Click Element    ${remove_care_team_button}
    Try To Click Element    ${confirm_button}

If the care team can be removed, Noona displays a confirmation toaster message
    # and the removed care team no longer appears on the list of care teams
    # The care team can be removed only if it's not linked to any active patient treatment module
    Wait Until Page Contains    ${label-team-removed}
    Try To Click Banner Message
    Wait Until Page Does Not Contain    ${CARE_TEAM_1}

Verify Care Team 1 cannot be removed
    Wait Until Page Contains    ${label-team-cannot-be-removed}
    Try To Click Banner Message
    Page Should Contain    Care Team 1

Verify Checkbox "Send an email notification for new cases assigned to this team" Is Displayed
    Wait Until Element Is Visible    ${notofication_email_checkbox_text}

Select Checkbox "Send an email notification for new cases assigned to this team" And Add Email Address To Care Team
    Wait Until Page Does Not Contain Element    ${loader}    5s
    ${status}    Execute Javascript    return document.querySelector("input[type='checkbox']").checked
    IF    ${status}
        Wait Until Page Contains    ${f04ca06-ext-b_email}
        Try To Click Element    ${cancel_care_team}
    ELSE
        Try To Click Element    ${notofication_email_checkbox}
        Try To Input Text    ${notification_email_box}    ${f04ca06-ext-b_email}
        Save Changes
    END

Select Checkbox "Send an email notification for new cases assigned to this team"
    Wait Until Page Does Not Contain Element    ${loader}    5s
    ${status}    Execute Javascript    return document.querySelector("input[type='checkbox']").checked
    IF    ${status}
        Try To Click Element    ${notofication_email_checkbox}
        Wait Until Element Is Not Visible    ${f04ca06-ext-b_email}
        Save Changes
    END

Add Email Address To Care Team
    ${status}    Run Keyword And Return Status    Wait Until Page Conitains    ${f04ca06-ext-b_email}
    IF    ${status}==${FALSE}
        Wait Until Page Does Not Contain Element    ${loader}    2s
        Input Text    ${notification_email_box}    ${f04ca06-ext-b_email}
    END

Save Changes
    Wait Until Element Is Enabled    ${save_care_team_button}
    Try To Click Element    ${save_care_team_button}
    Wait Until Page Contains    ${label-changes-saved}
    Try To Click Banner Message

Disable "Send an email notification for new cases assigned to this team"
    [Arguments]    ${care_team_name}
    Login As Nurse    user_type=${USER_TYPE}[noona_admin]
    Navigate to "Clinic users"
    Select The "Care teams" Tab
    Click The Names Of One Of The Care Teams On The List    ${care_team_name}
    Noona Opens The "Edit care team" Dialog
    Select Checkbox "Send an email notification for new cases assigned to this team"
    Close Browser

Create New Case
    [Arguments]    ${case_type}
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=${ext-b_care_team}
    ...    case_description=Test case description    case_priority=Medium
    ...    case_origin=Patient Initiated Call    case_status=New
    Set Test Variable    &{case_details}
    Open A New Case    ${f04ca06_patient_extb}[ssn]    save

Emails Will Be Now Sent Whenever New Case Or Report Is Created For This Care Team
    Close Open Cases    ${f04ca06_patient_extb}[ssn]    nurse_email=${automated_tests_clinic}[default_user]
    Create New Case    Chemo / Tx Questions
    Wait Until Page Contains    ${case_created_successfully_banner}
    Verify Email Is Received In Mailosaur
    Open Link From Received Email And Verify It Redirects To Correct Case Without Login
    Verify That Assignee Is Correct For Opened Case
    Verify That Care Team Is Correct For Opened Case
    Verify That Update Date Is Correct For Opened Case

Verify Email Is Received In Mailosaur
    ${view_case_link}    Care Team User Received An Email About A New Case
    ...    ${ext-b_server_id}
    ...    ${ext-b_api_key}
    ...    ${f04ca06-ext-b_email}
    Set Test Variable    ${view_case_link}

Open Link From Received Email And Verify It Redirects To Correct Case Without Login
    Go To    ${view_case_link}
    Wait Until Page Contains    ${case_assigned_text}
    Try To Click Banner Message
    Wait Until Page Contains    ${label-changes-saved}

Verify That Assignee Is Correct For Opened Case
    Wait Until Page Contains Element
    ...    (${opened_case}//*[contains(text(), 'Clinic User')])[1]

Verify That Care Team Is Correct For Opened Case
    Wait Until Page Contains Element
    ...    ${opened_case}//*[contains(text(), '${ext-b_care_team}')]

Verify That Update Date Is Correct For Opened Case
    ${dat1}    Get Current Date    result_format=%d.%m.%Y
    Wait Until Page Contains Element
    ...    (${opened_case}//*[contains(text(), '${dat1}')])[3]
