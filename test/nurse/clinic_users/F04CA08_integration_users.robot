*** Settings ***
Documentation       F04CA08 Clinic administrator can create an api user: No preconditions

Resource            ${EXECDIR}/resources/nurse/login.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f04ca08    clinic-web


*** Variables ***
${integration_user_name_field}      //*[@data-testid="name"]
${token_input}                      //*[@data-testid="token"]
${add_integration_user_button}      //*[@data-testid="add-integration-users-link"]
${save_button}                      //*[@id='save-user']/span[2]


*** Test Cases ***
Adding integration user is successful
    [Tags]    nms9-ver-210
    Clinic administrator navigates to Clinic users
    Clinic administrator clicks Add integration user
    Clinic administrator enters integration name
    Wait Until Keyword Succeeds    5s    0.3s    Dialog displays Oauth token
    [Teardown]    Close browser


*** Keywords ***
Clinic administrator navigates to Clinic users
    Login as Nurse    clinic=${appointment_clinic}[name]    user_type=${USER_TYPE}[noona_admin]

Clinic administrator clicks Add integration user
    Try To Click Element    ${add_integration_user_button}

Clinic administrator enters integration name
    Try To Input Text    ${integration_user_name_field}    ehr_user
    Wait Until Element Is Enabled    ${save_button}
    Try To Click Element    ${save_button}

Dialog displays Oauth token
    Wait Until Page Contains Element    ${token_input}
    ${actual value}    Get value    ${token_input}
    Should not be empty    ${actual value}    Token field was unexpectedly empty
    [Teardown]    Run keyword if    $KEYWORD_STATUS == 'FAIL'    Capture Screenshot
