*** Settings ***
Documentation       F04CA10 Clinic administrator can manage clinic user data export rights

Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f04ca10    clinic-web    noona_admin


*** Variables ***
${data_export_link}     clinic-data-export-link


*** Test Cases ***
Manage Data Export Rights
    [Tags]    nms9-ver-211
    Navigate To Clinic Users
    Select User    ${test_clinic_setting_1}[user_email]
    Toggle Data Export Requester    enable
    Save User Information
    Try To Click Banner Message
    Wait Until Page Does Not Contain    Updated
    Verify User From List    user_email=${test_clinic_setting_1}[user_email]    role=Data export requester
    Login As Nurse    email=${test_clinic_setting_1}[user_email]
    Data Export Available For User
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Select User    ${test_clinic_setting_1}[user_email]
    Toggle Data Export Requester    disable
    Save User Information
    Verify User From List    user_email=${test_clinic_setting_1}[user_email]    role=Clinic user
    Login As Nurse    email=${test_clinic_setting_1}[user_email]
    Data Export Should Not Be Available For User


*** Keywords ***
Data Export Available For User
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${data_export_link}
    Wait Until Page Contains
    ...    Please enable two factor authentication from your profile settings to create a data export request.
    Close All Browsers

Data Export Should Not Be Available For User
    Try To Click Element    ${clinic_menu}
    Element Should Not Be Visible    ${data_export_link}
