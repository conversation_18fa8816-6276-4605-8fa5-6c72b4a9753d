*** Settings ***
Documentation       [F04CA02] Clinic administrator can update clinic user information
...
...                 *Preconditions:* Clinic administrator is logged in.
...
...                 Main success scenario:
...                 - Navigate to "Clinic users".
...                 - Select user.
...                 - Choose to edit user information.
...                 - Update user information.
...                 - Added: Email address is automatically cast to lower-case
...                 - Update user role.
...                 - Update user care teams.
...                 - Save user information.
...                 Extra:
...                 - Login with new email address
...                 - Try to login with old email address
...                 - Reset clinic user info back to original

Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource

Suite Setup         Set Libraries Order
Suite Teardown      Reset Clinic User Information And Close Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          clinic-web


*** Test Cases ***
Change Clinic User Information
    [Tags]    nms9-ver-202    usecase-f04ca02    noona_admin
    Login As Nurse    email=${CLINIC_ADMIN}
    Navigate To Clinic Users
    Verify User From List
    ...    ${email_original}
    ...    ${firstname_original}
    ...    ${lastname_original}
    ...    ${email_original}
    ...    ${role_original}
    ...    ${team_original}
    ...    ${locked_original}
    Select User    ${email_original}
    Verify User Information
    ...    ${email_original}
    ...    ${firstname_original}
    ...    ${lastname_original}
    ...    ${email_original}
    ...    ${phone_original}
    Change User Information
    Verify User From List
    ...    ${email_changed}
    ...    ${firstname_changed}
    ...    ${lastname_changed}
    ...    ${email_changed}
    ...    ${role_changed}
    ...    ${team_changed}
    ...    ${locked_original}
    User Logs In With New Info    ${email_changed}
    User Tries To Login

Noona Administrator Can Change Clinic User Information
    [Tags]    nms9-ver-466    usecase-f04na10    noona_admin
    Login As Nurse    user_type=${USER_TYPE}[noona_admin]
    Navigate To Clinic Users
    Verify User From List
    ...    ${email_original}
    ...    ${firstname_original}
    ...    ${lastname_original}
    ...    ${email_original}
    ...    ${role_original}
    ...    ${team_original}
    ...    ${locked_original}
    Select User    ${email_original}
    Verify User Information
    ...    ${email_original}
    ...    ${firstname_original}
    ...    ${lastname_original}
    ...    ${email_original}
    ...    ${phone_original}
    Change User Information
    Verify User From List
    ...    ${email_changed}
    ...    ${firstname_changed}
    ...    ${lastname_changed}
    ...    ${email_changed}
    ...    ${role_changed}
    ...    ${team_changed}
    ...    ${locked_original}
    User Logs In With New Info    ${email_changed}
    User Tries To Login


*** Keywords ***
Reset Clinic User Information And Close Browsers
    Reset Clinic User Information
    Close All Browsers
