*** Settings ***
Documentation       F04CA01 Clinic administrator can register new clinic user
...                 - Clinic administrator is logged in.
...                 - TODO: email check
...                 F05N01 Nurse can answer customer satisfaction survey

Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f04ca01    usecase-f05n01    clinic-web


*** Variables ***
${how_satisfied_are_you_text}               How satisfied have you been using Noona?
${contact_patient_message_topic}            f04ca01 contact patient
${happy_rating}                             //*[@id='happy']/following-sibling::label
${save_your_rating_button}                  //button[contains(text(),"Save your rating")]
${help_us_improve_noona_text}               Help us improve Noona
${please_submit_your_feedback_button}       answer
${clinic_user_survey_url}                   https://my.surveypal.com/


*** Test Cases ***
Register New Clinic User And Answer Survey
    [Tags]    nms9-ver-201    noona_admin
    Login As Nurse    clinic=${ta_clinic_users_test}[name]    user_type=${USER_TYPE}[noona_admin]
    Wait Until Noona Loader Is Not Visible
    Navigate To Clinic Users
    Add New Clinic User
    Save User Information
    Verify User From List
    ...    ${user_email}
    ...    ${user_first_name}
    ...    ${user_last_name}
    ...    ${user_email}
    ...    ${role_original}
    ...    ${team_original}
    manage_users.Select Clinic User    ${user_email}
    Change User's Password
    Try To Click Banner Message
    Close Browser
    Login As Nurse    ${user_email}    ${ta_clinic_users_test}[name]
    Search Patient By Identity Code    ${f05n01_patient}[ssn]
    Contact Patient    ${contact_patient_message_topic}    Instructions
