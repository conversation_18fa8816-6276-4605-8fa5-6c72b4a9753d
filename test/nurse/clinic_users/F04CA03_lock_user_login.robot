*** Settings ***
Documentation       [F04CA03] Clinic administrator can lock and unlock clinic user login
...
...                 *Preconditions:* Clinic administrator is logged in.
...
...                 Main success scenario:
...                 - Navigate to "Clinic users".
...                 - Select user.
...                 - Lock or unlock user.
...                 Cases also test if the user can get in.

Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse    user_type=${USER_TYPE}[noona_admin]
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f04ca03    clinic-web    noona_admin


*** Test Cases ***
Admin Sets User Locked
    [Tags]    nms9-ver-203-1    nms9-ver-203
    Navigate To Clinic Users
    Select User    ${email_lock_user}
    Toggle Lock    Lock
    Verify User From List    ${email_lock_user}    locked=${locked_changed}
    Close Browser
    User Tries To Login    ${email_lock_user}
    Close Browser
    Set Status Back To Unlocked

Admin Sets User Unlocked
    [Tags]    nms9-ver-203-2    nms9-ver-203
    Navigate To Clinic Users
    Select User    ${email_original_2}
    Toggle Lock    Unlock
    Verify User From List    ${email_original_2}    locked=${locked_original}
    Close Browser
    User Logs In With New Info    ${email_original_2}
    Close Browser
    Set Status Back To Locked
