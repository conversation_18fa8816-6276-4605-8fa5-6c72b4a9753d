*** Settings ***
Documentation       F03NA01 Noona administrator can edit clinic settings -> Clinical Settings

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinical_settings.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinic_settings.resource
Resource            ${EXECDIR}${/}resources/nurse/list_of_patients_tab.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_cases.resource
Resource            ${EXECDIR}${/}resources/nurse/case_management.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_group.resource

Suite Setup         Run Keywords    Set Libraries Order    AND    Add An Activated Patient Under Clinic Settings 3    f03na01clinical    mailosaur=${settings_email_keys}[0]
Suite Teardown      Remove Patient As Suite Teardown    ${TEST_CLINIC_SETTING_3_EHR_TOKEN}    ${patient_email}    ${TEST_CLINIC_SETTING_3_CLINIC_ID}    ${test_clinic_setting_3}[manager_email]
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na01    clinic-web


*** Variables ***
${confirmation_message_sq_and_question}         Test confirmation message after sending SQ and Question
${confirmation_message_urgent_symptom}          Test confirmation message after sending an urgent symptom
${modal_text_distress_problem_list}             Test for modal text after sending disress and problem list questionnaire
${modal_text_support_and_guidance_followup}     Test for modal text after sending support and guidance follow-up


*** Test Cases ***
Main success scenario - Clinical Settings - Pharmacotherapy
    [Tags]    nms9-ver-451-1    nms9-ver-451
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${pharmacotherapy_module}    Yes
    Save Settings
    Close Browser
    Verify Pharmacotherapy Settings    Yes
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${pharmacotherapy_module}    No
    Save Settings
    Close Browser
    Verify Pharmacotherapy Settings    No

Main success scenario - Clinical Settings - Options in the "Ask about other issues" menu
    [Tags]    nms9-ver-451-2    nms9-ver-451
    @{other_issues}    Create List    Information about side effects    Other
    Go To Clinical Settings And Reset
    Select Clinic Settings Checkbox    ${other_issues}[0]
    Select Clinic Settings Checkbox    ${other_issues}[1]
    Save Settings
    Close Browser
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Ask about other issues
    Verify If Other Issues Topics Are Displayed    Yes    @{other_issues}
    Close Browser
    Go To Clinical Settings And Reset
    Unselect Clinic Settings Checkbox    ${other_issues}[0]
    Unselect Clinic Settings Checkbox    ${other_issues}[1]
    Save Settings
    Close Browser
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Ask about other issues
    Verify If Other Issues Topics Are Displayed    No    @{other_issues}
    Close Browser

Main success scenario - Clinical Settings - Enable Treatment Modules
    [Tags]    nms9-ver-451-3    nms9-ver-451
    Go To Clinical Settings And Reset
    Select Clinic Settings Checkbox    ${BONE_RADIOTHERAPY}[name]
    Save Settings
    Close Browser
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Navigate To Patient Page
    Verify If Module Is Displayed In the List    ${BONE_RADIOTHERAPY}[name]    Yes
    Close Browser
    Go To Clinical Settings And Reset
    Unselect Clinic Settings Checkbox    ${BONE_RADIOTHERAPY}[name]
    Save Settings
    Close Browser
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Navigate To Patient Page
    Verify If Module Is Displayed In the List    ${BONE_RADIOTHERAPY}[name]    No

Main success scenario - Clinical Settings - Enable status check questionnaire
    [Tags]    nms9-ver-451-4    nms9-ver-451
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${enable_status_questionnaire_text}    Yes
    Save Settings
    Close Browser
    Verify If Questionnaire Is Existing In The List In Module    ${STATUS_CHECK}    Yes
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${enable_status_questionnaire_text}    No
    Save Settings
    Close Browser
    Verify If Questionnaire Is Existing In The List In Module    ${STATUS_CHECK}    No

Main success scenario - Clinical Settings - Enable Questionnaire
    [Tags]    nms9-ver-451-5    nms9-ver-451
    Go To Clinical Settings And Reset
    Select Clinic Settings Checkbox    ${A_GENERAL_SELF_EFFICACY}
    Save Settings
    Close Browser
    Verify If Questionnaire Is Existing In The List In Module    ${A_GENERAL_SELF_EFFICACY}    Yes
    Go To Clinical Settings And Reset
    Unselect Clinic Settings Checkbox    ${A_GENERAL_SELF_EFFICACY}
    Save Settings
    Close Browser
    Verify If Questionnaire Is Existing In The List In Module    ${A_GENERAL_SELF_EFFICACY}    No

Main success scenario - Clinical Settings - Standford patient statuses
    [Tags]    nms9-ver-451-6    nms9-ver-451
    Go To Clinical Settings And Reset
    Select Clinic Settings Checkbox    ${stanford_patient_statuses_text}
    Save Settings
    Close Browser
    Verify Stanford Patient Statuses    Yes
    Go To Clinical Settings And Reset
    Unselect Clinic Settings Checkbox    ${stanford_patient_statuses_text}
    Save Settings
    Close Browser
    Verify Stanford Patient Statuses    No

Main success scenario - Clinical Settings - Information reported by a caregiver
    [Tags]    nms9-ver-451-7    nms9-ver-451
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${enable-ask-next-of-kin}    Yes
    Save Settings
    Close Browser
    Verify Information Reported By Caregiver Settings    Yes
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${enable-ask-next-of-kin}    No
    Save Settings
    Close Browser
    Verify Information Reported By Caregiver Settings    No

Main success scenario - Clinical Settings - Additional information for the symptom questionnaire
    [Tags]    nms9-ver-451-8    nms9-ver-451
    Send Symptom Questionnaire Via Api To Patient    ${clinic_appointment_quest}
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${discussion_point_field}    Yes
    Save Settings
    Close Browser
    Login As Patient And Answer Symptom Questionnaire    Mild    wait_more=yes
    Wait Until Page Contains    ${discussion_point_field_text}
    Close Browser
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${discussion_point_field}    No
    Save Settings
    Close Browser
    Login As Patient And Answer Symptom Questionnaire    Mild    wait_more=yes
    Page Should Not Contain    ${discussion_point_field_text}
    Close Browser

Main success scenario - Clinical Settings - Case processing - Part 1
    [Tags]    nms9-ver-451-9    nms9-ver-451
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${nurse_can_consult_ct}    Yes
    Select Clinic Settings Radio Button    ${cu_get_notification}    Yes
    Select Clinic Settings Radio Button    ${export_text_enabled}    Yes
    Select Clinic Settings Radio Button    ${cu_last_name_displayed}    Yes
    Save Settings
    Close Browser
    Login As Patient And Answer Symptom Questionnaire    Severe    wait_more=yes
    Verify Case Processing Settings    Yes    Yes    Yes    Yes

Main success scenario - Clinical Settings - Case processing - Part 2
    [Tags]    nms9-ver-451-10    nms9-ver-451
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${nurse_can_consult_ct}    Yes
    Select Clinic Settings Radio Button    ${cu_get_notification}    No
    Select Clinic Settings Radio Button    ${cu_last_name_displayed}    No
    Save Settings
    Close Browser
    Login As Patient And Answer Symptom Questionnaire    Severe    wait_more=yes
    Search Patient As Nurse And SQ Case
    Verify Consultation Settings    Yes
    Verify Consult Notification    No
    Verify Clinic User Last Name Setting    No    ${test_clinic_setting_3}[name]
    Close Case
    Close Browser

Main success scenario - Clinical Settings - Case processing - Part 3
    [Tags]    nms9-ver-451-11    nms9-ver-451
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${nurse_can_consult_ct}    No
    Select Clinic Settings Radio Button    ${export_text_enabled}    No
    Save Settings
    Close Browser
    Login As Patient And Answer Symptom Questionnaire    Severe
    Search Patient As Nurse And SQ Case
    Verify Consultation Settings    No
    Verify Export Enabled For Extended SQ    No
    Close Browser

Main success scenario - Clinical Settings - Customised clinical contents
    [Tags]    nms9-ver-451-12    nms9-ver-451
    Send QOL Questionnaire Via API To Patient For Today    ${SUPPORT_AND_GUIDANCE}
    Send QOL Questionnaire Via API To Patient For Today    ${SUPPORT_AND_GUIDANCE_FOLLOW_UP}
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    Go To Clinical Settings And Reset
    Update Confirmation Message After Sending Symptom Or Question    ${now}-${confirmation_message_sq_and_question}
    Update Confirmation Message After Sending Urgent Symptom    ${now}-${confirmation_message_urgent_symptom}
    Update Modal Text For Distress And Problem List Questionnaire    ${now}-${modal_text_distress_problem_list}
    Update Modal Text For Support And Guidance Follow Up Questionnaire
    ...    ${now}-${modal_text_support_and_guidance_followup}
    Save Settings
    Close Browser
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Ask About Other Symptom    go_to_homepage=no
    Wait Until Page Contains    ${now}-${confirmation_message_sq_and_question}    timeout=5s
    Go To Patient Homepage
    Ask About Stomach Eating And Bowel Symptom
    Wait Until Page Contains    ${now}-${confirmation_message_urgent_symptom}    timeout=5s
    Go To Patient Homepage
    Answer Support And Guidance Questionnaire As Patient    ${SUPPORT_AND_GUIDANCE}
    Wait Until Page Contains    ${now}-${modal_text_distress_problem_list}    timeout=5s
    Click View Your Diary
    Navigate To Clinic
    Answer Support And Guidance Questionnaire As Patient    ${SUPPORT_AND_GUIDANCE_FOLLOW_UP}
    Wait Until Page Contains    ${now}-${modal_text_support_and_guidance_followup}

Main success scenario - Clinical Settings - Patient group
    [Tags]    nms9-ver-451-13    nms9-ver-451
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${patient_group_enable_setting_text}    Yes
    Save Settings
    Close Browser
    Verify Patient Groups Settings    Yes
    Go To Clinical Settings And Reset
    Select Clinic Settings Radio Button    ${patient_group_enable_setting_text}    No
    Save Settings
    Close Browser
    Verify Patient Groups Settings    No


*** Keywords ***
Verify Pharmacotherapy Settings
    [Arguments]    ${settings}
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Go To List Of Patient And Select First Patient
    Navigate To Questionnaires Tab
    Try To Click Element    ${module_edit_button}
    Wait Until Element Is Visible    ${add_schedule_button}
    Wait Until Element Is Enabled    ${add_schedule_button}
    IF    '${settings}'=='Yes'
        Wait Until Element Is Visible    ${mediconal_product_dosage_header}
    ELSE
        Element Should Not Be Visible    ${mediconal_product_dosage_header}
    END
    Try To Click Element    ${add_schedule_button}
    Try To Click Element    ${schedule_content_dropdown}
    Try To Input Text    ${schedule_content_dropdown_input}    ${BASELINE_QUESTIONNAIRE}
    Click Questionnaire From List    ${BASELINE_QUESTIONNAIRE}
    Wait Until Element Is Visible    ${treatment_date_input}
    IF    '${settings}'=='Yes'
        Wait Until Element Is Visible    ${substances_dropdown}
        Wait Until Element Is Visible    ${amount_text_field}
        Wait Until Element Is Visible    ${unit_text_dropdown}
    ELSE
        Element Should Not Be Visible    ${substances_dropdown}
        Element Should Not Be Visible    ${amount_text_field}
        Element Should Not Be Visible    ${unit_text_dropdown}
    END
    Close Browser

Go To Clinical Settings And Reset
    Login As Nurse    clinic=${test_clinic_setting_3}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Clinical Settings Tab
    Select Clinic Settings Radio Button    ${pharmacotherapy_module}    No    # to make sure that save button is enabled
    Select Clinic Settings Radio Button    ${pharmacotherapy_module}    Yes

Verify If Questionnaire Is Existing In The List In Module
    [Arguments]    ${questionnaire}    ${is_displayed}
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Go To List Of Patient And Select First Patient
    Navigate To Questionnaires Tab
    Try To Click Element    ${module_edit_button}
    Wait Until Element Is Visible    ${add_schedule_button}
    Wait Until Element Is Enabled    ${add_schedule_button}
    Try To Click Element    ${add_schedule_button}
    Try To Click Element    ${schedule_content_dropdown}
    IF    '${is_displayed}'=='Yes'
        Wait Until Page Contains    ${questionnaire}
    ELSE
        Wait Until Page Contains    ${BASELINE_QUESTIONNAIRE}
        Page Should Not Contain    ${questionnaire}
    END
    Close Browser

Verify Stanford Patient Statuses
    [Arguments]    ${is_enabled}
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Go To List Of Patient And Select First Patient
    Wait Until Element Is Visible    ${User_status}
    IF    '${is_enabled}'=='Yes'
        Wait Until Element Is Visible    ${my_health_status_id}
        Wait Until Element Is Visible    ${family_resource_guide_id}
        Wait Until Element Is Visible    ${completed_education_modules_area}
    ELSE
        Element Should Not Be Visible    ${my_health_status_id}
        Element Should Not Be Visible    ${family_resource_guide_id}
        Element Should Not Be Visible    ${completed_education_modules_area}
    END
    Close Browser

Verify Information Reported By Caregiver Settings
    [Arguments]    ${is_enabled}
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Go To List Of Patient And Select First Patient
    Navigate To Questionnaires Tab
    Add Questionnaire To Schedule    ${QUALITY_OF_LIFE_QUESTIONNAIRE}
    Click Complete Questionnaire Button
    Answer QOL Questions And Click Next
    IF    '${is_enabled}'=='Yes'
        Wait Until Element Is Visible    ${info_entered_by_caregiver}    timeout=3s
    ELSE
        Wait Until Element Is Visible    ${qol_summary_container}
        Element Should Not Be Visible    ${info_entered_by_caregiver}
    END

Verify Case Processing Settings
    [Arguments]    ${consultation}    ${consult_notif}    ${export_enabled}    ${clinic_user_last}
    Search Patient As Nurse And SQ Case
    Verify Consultation Settings    ${consultation}
    Verify Consult Notification    ${consult_notif}
    Verify Export Enabled For Extended SQ    ${export_enabled}
    Verify Clinic User Last Name Setting    ${clinic_user_last}    ${test_clinic_setting_3}[name]
    Close Case

Verify Consultation Settings
    [Arguments]    ${setting}
    Set Email And Delete Previous Messages    ${settings_email_keys}[0]    ${settings_email_keys}[1]
    IF    '${setting}'=='Yes'
        Wait Until Element Is Visible    ${consult_care_team_button}    timeout=3s
        Consult A Care Team    Care Team 2    Consulted Nurse    Test Consult
    ELSE
        Wait Until Element Is Visible    ${close_case_button}
        Element Should Not Be Visible    ${consult_care_team_button}
    END

Verify Consult Notification
    [Arguments]    ${setting}
    IF    '${setting}'=='Yes'
        Nurse Received Consultation Request Email
        ...    ${test_clinic_setting_3}[clinic_user_email]
        ...    Care Team 2
        ...    Consulted Nurse
    ELSE
        Email Notification Is Not Received
        ...    ${settings_email_keys}[0]
        ...    ${settings_email_keys}[1]
        ...    ${test_clinic_setting_3}[clinic_user_email]
    END

Verify Export Enabled For Extended SQ
    [Arguments]    ${setting}
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    (${clipboard_icon})[last()]
    Wait Until Noona Loader Is Not Visible
    IF    '${setting}'=='Yes'
        Compare Clipboard Content    f03na01_clipboard_content    should_contain=Yes
    ELSE
        Compare Clipboard Content    f03na01_clipboard_content    should_contain=No
    END
    Try To Click Element    ${close_clipboard_button}

Verify Clinic User Last Name Setting
    [Arguments]    ${setting}    ${clinic}
    ${clinic_user_name}    Set Variable If    '${setting}'=='Yes'
    ...    ${test_clinic_setting_3}[manager_name]
    ...    Settings N.
    @{message_data}    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    Your care team at ${test_clinic_setting_3}[name] is now discussing your question
    ...    ${clinic_user_name}
    Should Contain
    ...    ${message_data}[1]
    ...    is now discussing your question with your care team at ${clinic}, and will get back to you soon.

Login As Patient And Answer Symptom Questionnaire
    [Arguments]    ${severity}    ${wait_more}=no
    Login As Patient    ${patient_email}
    IF    '${wait_more}'=='yes'
        ${status}    Run Keyword And Return Status
        ...    Wait Until Element Is Visible
        ...    ${unanswered_questionnaire_status_text}
        WHILE    ${status}==${FALSE}    limit=3
            Sleep    5s
            Reload Page
            ${status}    Run Keyword And Return Status
            ...    Wait Until Element Is Visible
            ...    ${unanswered_questionnaire_status_text}
        END
    END
    IF    '${severity}'=='Severe'
        Answer Latest Symptom Questionnaire With Other Symptom    new_entry    ${severity}
        Click View Your Diary
        Close Browser
    ELSE
        Answer Latest Symptom Questionnaire With Other Symptom    new_entry    Mild    send=no
    END

Search Patient As Nurse And SQ Case
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Select Case From List    ${BASELINE_QUESTIONNAIRE}    Open

Answer Support And Guidance Questionnaire As Patient
    [Arguments]    ${questionnaire}
    Select Latest Clinic Message    ${questionnaire}
    Select Answer Questionnaire
    Rate With Vertical Slider    7
    Try To Click Element    ${questionnaire_next_button}
    IF    '${questionnaire}'=='${SUPPORT_AND_GUIDANCE}'
        Select No To All Problem List
    END
    Select Answer To Question    Would you like a Care Navigator to call you back based on your responses today?    No
    Try To Click Element    ${questionnaire_next_button}
    Try To Click Element    ${save_questionnaire_button}

Verify Patient Groups Settings
    [Arguments]    ${setting}
    Login As Nurse    ${test_clinic_setting_3}[manager_email]
    Try To Click Element    ${clinic_menu}
    IF    '${setting}'=='Yes'
        Wait Until Element Is Visible    ${patient_groups_link}    timeout=5s
    ELSE
        Wait Until Element Is Visible    ${patient_status_analytics}    timeout=5s
        Element Should Not Be Visible    ${patient_groups_link}
    END
    Go To List Of Patient And Select First Patient
    IF    '${setting}'=='Yes'
        Wait Until Element Is Visible    ${open_groups_link}    timeout=5s
    ELSE
        Wait Until Element Is Visible    ${back_to_button}    timeout=5s
        Element Should Not Be Visible    ${open_groups_link}
    END
    Close Browser
