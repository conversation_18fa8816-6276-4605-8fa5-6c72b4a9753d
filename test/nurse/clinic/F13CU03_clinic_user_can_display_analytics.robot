*** Settings ***
Documentation       F13CU03 Clinic user can display clinic analytics
...                 Preconditions:
...                 - User is logged in.
...                 - Clinic analytics is enabled in the clinic settings.
...                 - Medical records are visible to the patients.


Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources/patient/calendar.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers

Force Tags          usecase-f13cu03


*** Variables ***
${download-wait}                 2.5 min
${patient_portal_usage}          //h4[contains(text(), 'Patient portal usage')]
${patient_name}                  //td[contains(text(), 'Clinic Analytics1')]
${main_date_picker}              //*[@data-testid='date-picker-toggle']/button
${sec_date_picker}               //span[@class='mdc-button__label']/span[@aria-hidden='true']
${patient_name_row}              //*[@id="main"]/ps-analytics-viewer/div/div[2]/div/table/tbody/tr/td[contains(text(), "{}")]
${number_of_views_column}        //*[@id="main"]/ps-analytics-viewer/div/div[2]/div/table/tbody/tr/td[contains(text(), "{}")]/following-sibling::td[1]
${number_of_downloads_column}    //*[@id="main"]/ps-analytics-viewer/div/div[2]/div/table/tbody/tr/td[contains(text(), "{}")]/following-sibling::td[2]
${send_count}                    //*[@id="main"]/ps-analytics-viewer/div/div[2]/div/table/tbody/tr/td[contains(text(), "{}")]/following-sibling::td[3]
${ccd_record_1}                  //*[@for="ccd-selection-0"]
${ccd_record_2}                  //*[@for="ccd-selection-1"]
${ccd_record_3}                  //*[@for="ccd-selection-2"]
${ccd_record_4}                  //*[@for="ccd-selection-3"]
${ccd_record_5}                  //*[@for="ccd-selection-4"]
${ccd_record_6}                  //*[@for="ccd-selection-5"]
${ccd_record_7}                  //*[@for="ccd-selection-6"]
${ccd_record_8}                  //*[@for="ccd-selection-7"]
${ccd_record_9}                  //*[@for="ccd-selection-8"]
${ccd_record_10}                 //*[@for="ccd-selection-9"]



*** Test Cases ***
Main success scenario - Clinic User Can Display Clinic Analytics
    [Tags]    nms9-ver-46
    Navigate To Clinic - Clinic Analytics
    Previous Six (6) Weeks Are Selected By Default
    Clinic User Can Define Date Range
    Patient Views, Downloads, And Sends Their Medical Record Today
    Page Displays Patients Who Have Viewed, Downloaded Or Sent CCD Information

#Clinic User Can Display Clinic Analytics - Page Displays Providers And Numerator Value
#    [Documentation]    This test case checks if Provider and Numerator value are displayed
#    ...                WIP: will complete on another MR
#    [Tags]    nms9-ver-525      manual
#    Login As Nurse      email=${hla_clinic}[analytics_email]
#    Wait Until Noona Loader Is Not Visible
#    Try To Click Element                  ${clinic_menu}
#    Wait Until Element Is Visible         ${patient_status_analytics}    timeout=3s
#    Try To Click Element                  ${patient_status_analytics}
#    Wait Until Page Contains Element      ${patient_portal_usage}
#    Page Displays Providers - Clinic Users That Are Assigned To Any Patient In The Clinic
#    Page Displays Numerator Value - Shows Total Number Of Patients Who Are Assigned To The Provider



*** Keywords ***
Navigate To Clinic - Clinic Analytics
    Login As Nurse      email=${patient_education_clinic}[user_email]
    Wait Until Noona Loader Is Not Visible
    Try To Click Element                  ${clinic_menu}
    Wait Until Element Is Visible         ${patient_status_analytics}
    Try To Click Element                  ${patient_status_analytics}
    Wait Until Page Contains Element      ${patient_portal_usage}
    ${test_patient_before_viewing_record_data_cell}     Format String    ${number_of_views_column}    ${f13p02_patient}[name]
    ${before_viewing_record}     Get Text     ${test_patient_before_viewing_record_data_cell}
    ${before_viewing_value}      Convert To Integer     ${before_viewing_record}
    Set Test Variable    ${before_viewing_value}
    ${test_patient_after_sending_count_data_cell}    Format String    ${send_count}    ${f13p02_patient}[name]
    ${before_sending_record}     Get Text      ${test_patient_after_sending_count_data_cell}
    ${before_sending_value}     Convert To Integer      ${before_sending_record}
    Set Test Variable       ${before_sending_value}
    ${test_patient_after_downloading_record_data_cell}    Format String    ${number_of_downloads_column}    ${f13p02_patient}[name]
    ${before_downloading_record}     Get Text      ${test_patient_after_downloading_record_data_cell}
    ${before_downloading_value}     Convert To Integer     ${before_downloading_record}
    Set Test Variable       ${before_downloading_value}

Previous Six (6) Weeks Are Selected By Default
    ${todays_date}        Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y
    Set Test Variable     ${todays_date}
    ${date_6weeks_ago}    Subtract Time From Date    ${todays_date}    42 days    result_format=%d.%m.%Y    exclude_millis=yes    date_format=%d.%m.%Y
    Log     ${date_6weeks_ago}
    Page Should Contain Element      //span[@class='mat-date-range-input-mirror' and contains(text(), '${date_6weeks_ago}')]
    Page Should Contain Element      //span[@class='mat-date-range-input-mirror' and contains(text(), '${todays_date}')]

Clinic User Can Define Date Range
    ${current_year}     Get Current Date    result_format=%Y
    Set Test Variable   ${current_year}
    ${current_day}      Get Current Date    result_format=%-d
    Set Test Variable    ${current_day}
    Try To Click Element                   ${main_date_picker}
    Wait Until Page Contains Element       ${sec_date_picker}
    Try To Click Element                   ${sec_date_picker}
    Wait Until Page Contains Element       //span[contains(@class, 'mat-calendar-body-today') and contains(text(), ' ${current_year} ')]
    Try To Click Element                   //span[contains(@class, 'mat-calendar-body-today') and contains(text(), ' ${current_year} ')]
    ${month}    Get Current Date    result_format=%b
    ${uppercase_month}    Convert To Upper Case    ${month}
    Set Test Variable      ${uppercase_month}
    Wait Until Page Contains Element       //span[contains(@class, 'mat-calendar-body-today') and contains(text(), ' ${uppercase_month} ')]
    Try To Click Element                   //span[contains(@class, 'mat-calendar-body-today') and contains(text(), ' ${uppercase_month} ')]
    Wait Until Page Contains Element       //span[contains(@class, 'mat-focus-indicator') and text()=' ${current_day} ']
    Try To Click Element                   //span[contains(@class, 'mat-focus-indicator') and text()=' ${current_day} ']
    Select Today's Date Again
    Wait Until Page Contains Element              ${patient_portal_usage}
    Wait Until Noona Loader Is Not Visible

Select Today's Date Again
    Sleep    0.5s
    Try To Click Element        //span[contains(@class, 'mat-focus-indicator') and text()=' ${current_day} ']

Patient Views, Downloads, And Sends Their Medical Record Today
    Patient Opens And Views A Medical Record
    Patient Downloads Medical Records
    Patient Sends Medical Record

Patient Opens And Views A Medical Record
    Login As Patient    ${f13p02_patient}[email]
    Go To Library
    Click Medical Records Intro
    Select All Records
    Click View Patient Records
    Wait Until Element Is Visible    ${ccd_record_1}
    Click CCD Record
    Close CCD Record Modal

Patient Downloads Medical Records
    Prepare Chrome for Downloads
    Login To Noona    patient    ${f13p02_patient}[email]    open_browser=False
    Go To Library
    Click Medical Records Intro
    Select All Records
    Click Download Patient Records
    Display All CCD Record To Download
    Randomly Uncheck Two Records Before Downloading
    Download All Records
    IF    '${remote_url_exists}'=='False'    Dowloaded ZIP Contains HTML File

Randomly Uncheck Two Records Before Downloading
    [Documentation]    this keyword/step is needed for testcase to be repeatable
    ${records_list}    Create List    ${ccd_record_1}
    ...    ${ccd_record_2}
    ...    ${ccd_record_3}
    ...    ${ccd_record_4}
    ...    ${ccd_record_5}
    ...    ${ccd_record_6}
    ...    ${ccd_record_7}
    ...    ${ccd_record_8}
    ...    ${ccd_record_9}
    ...    ${ccd_record_10}
    # Randomly selects two elements from the list to uncheck. this step makes this test case repeatable
    ${two_random_records}     Evaluate    random.sample(${records_list}, 2)    modules=random
    Try To Click Element    ${two_random_records[0]}
    Try To Click Element    ${two_random_records[1]}

Patient Sends Medical Record
    Login As Patient    ${f13p02_patient}[email]
    Go To Library
    Click Medical Records Intro
    Select All Records
    Click Send Records
    Display All CCD Record To Send
    Input Recipient Email    email=${f13p02_patient}[recipient_email]
    Click Send Medical Records

Page Displays Patients Who Have Viewed, Downloaded Or Sent CCD Information
    Login As Nurse      email=${patient_education_clinic}[user_email]
    Wait Until Noona Loader Is Not Visible
    Try To Click Element                  ${clinic_menu}
    Wait Until Element Is Visible         ${patient_status_analytics}
    Try To Click Element                  ${patient_status_analytics}
    Wait Until Page Contains Element      ${patient_portal_usage}
    ${expected_patient_name_cell}    Format String    ${patient_name_row}    ${f13p02_patient}[name]
    Page Should Contain Element       ${expected_patient_name_cell}
    Verify Number Of Views    ${f13p02_patient}[name]
    Verify Number Of Times Data Was Sent    ${f13p02_patient}[name]
    Verify Number Of Downloads    ${f13p02_patient}[name]

Verify Number Of Views
    [Arguments]    ${patient_name}
    ${test_patient_after_viewing_record_data_cell}    Format String    ${number_of_views_column}    ${patient_name}
    ${after_viewing_record}     Get Text     ${test_patient_after_viewing_record_data_cell}
    ${after_viewing_value}      Convert To Integer       ${after_viewing_record}
    Set Test Variable     ${after_viewing_value}
    ${expected_views}    Evaluate   ${before_viewing_value} + 1
    Should Be Equal     ${after_viewing_value}      ${expected_views}

Verify Number Of Downloads
    [Arguments]    ${patient_name}
    ${test_patient_after_downloading_record_data_cell}    Format String    ${number_of_downloads_column}    ${patient_name}
    ${after_downloading_record}     Get Text     ${test_patient_after_downloading_record_data_cell}
    ${after_downloading_value}      Convert To Integer     ${after_downloading_record}
    Set Test Variable    ${after_downloading_value}
    ${expected_downloads}    Evaluate   ${before_downloading_value} + 1
    Should Be Equal    ${after_downloading_value}     ${expected_downloads}

Verify Number Of Times Data Was Sent
    [Arguments]    ${patient_name}
    ${test_patient_after_sending_count_data_cell}    Format String    ${send_count}    ${patient_name}
    ${after_sending_record}    Get Text      ${test_patient_after_sending_count_data_cell}
    ${after_sending_value}     Convert To Integer      ${after_sending_record}
    Set Test Variable    ${after_sending_value}
    ${expected_send}    Evaluate   ${before_sending_value} + 1
    Should Be Equal    ${expected_send}      ${after_sending_value}
