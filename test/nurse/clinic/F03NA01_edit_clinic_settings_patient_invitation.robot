*** Settings ***
Documentation       F03NA01 Noona administrator can edit clinic settings -> Patient Invitation

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}patient_invitation.resource

Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na01    clinic-web


*** Test Cases ***
# Main success scenario - Patient Invitation
#    Login As Nurse    email=<EMAIL>    clinic=TA clinic Test_Clinic_Setting_1
#    Select Patient invitation and modify patient account invitation related content
#    Invitation message SMS and email content
#    Account activation page content
