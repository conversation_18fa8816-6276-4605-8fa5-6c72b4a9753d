*** Settings ***
Documentation       F03CM01 Clinic manager can manage clinic organization information
...                 Preconditions:
...                 - Clinic has clinic organization information integration enabled.

Resource            ${EXECDIR}/resources/nurse/login.resource
Resource            ${EXECDIR}/resources/management/admin_common.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse    email=${automated_tests_clinic}[default_manager]
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03cm01    clinic-web


*** Variables ***
${clinic_organization_link}     clinic-organization
${checkbox}                     //*[@data-testid='enabled']
${checkbox_input}               //*[@data-testid='enabled']//input
${save_button}                  save


*** Test Cases ***
Manage Clinic Organization Information
    [Tags]    nms9-ver-185
    Go To Clinic Organization
    Disable Provider
    Reload Page And Check Settings    false
    Enable Provider
    Reload Page And Check Settings    true
#    TODO:Disabled providers are displayed as Not available in provider selection lists or when the provider information is displayed


*** Keywords ***
Go To Clinic Organization
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_organization_link}

Disable Provider
    Wait Until Page Contains Element    ${checkbox}//label
    ${provider_enabled}    Get Element Attribute    ${checkbox}    class
    IF    'checkbox-checked' in '${provider_enabled}'
        Try To Click Element    ${checkbox}//label
    ELSE
        Try To Click Element    ${checkbox}//label    # enable and disable to test checkbox
        Sleep    1
        Try To Click Element    ${checkbox}//label
    END
    Save Settings

Enable Provider
    Wait Until Page Contains Element    ${checkbox}//label
    ${provider_enabled}    Get Element Attribute    ${checkbox}    class
    IF    'checkbox-checked' not in '${provider_enabled}'
        Try To Click Element    ${checkbox}//label
    ELSE
        Try To Click Element    ${checkbox}//label    # disable and enable to test checkbox
        Sleep    1
        Try To Click Element    ${checkbox}//label
    END
    Save Settings

Save Settings
    Try To Click Element    ${save_button}
    Wait Until Page Contains    Changes saved
    Try To Click Banner Message

Reload Page And Check Settings
    [Arguments]    ${enabled_disabled}
    Reload Page
    Wait Until Element Is Visible    ${checkbox}
    Wait For Element To Be Present    ${checkbox}
    ${provider_enabled}    Get Element Attribute    ${checkbox}    class
    IF    '${enabled_disabled}'=='true'
        Should Contain    ${provider_enabled}    checkbox-checked
    ELSE
        Should Not Contain    ${provider_enabled}    checkbox-checked
    END
