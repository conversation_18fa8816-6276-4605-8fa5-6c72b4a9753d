*** Settings ***
Documentation       F03CA02 Clinic Administrator Can Edit Case Note Templates
...                 Case Note Template Management

Resource            ${EXECDIR}/resources/nurse/login.resource
Resource            ${EXECDIR}/resources/nurse/clinic_common.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03ca02    clinic-web


*** Variables ***
${manage_templates}                     manage-templates-link
${case_note_templates_link}             case-note-templates
${message_template_link}                message-templates
${questionnaire_template_link}          questionnaire-templates
${add_case_button}                      add-case-note-template
${title_input_field}                    //*[@data-testid="case-note-template-title"]
${content_input_field}                  //*[@data-testid="case-note-template-content"]
${case_type_checkbox}                   //*[@data-testid="template-case-types"]
${symptom_management_checkbox}          //*[@data-testid="template-care-team-0"]
${medication_instructions_checkbox}     //*[@data-testid="template-care-team-2"]
${save_case_note_button}                save-case-note-template
${case_note_template_list}              //tr[contains(@id, "case-note-template")]
${clinic_manager_name}                  Clinic Manager
${toast_message}                        toast-container
${delete_case_note_button}              delete-case-note-template
${confirm_button}                       ok-confirm
${template_name_in_table}               //tr[contains(@id, "case-note-template")]//td[contains(text(), "{}")]/..


*** Test Cases ***
Main success scenario — Template creation
    [Documentation]    Also includes Extension A — Editing a template
    [Tags]    nms9-ver-335-1    nms9-ver-335    nms9-ver-336    nms9-ver-337
    #nms9-ver-335
    Login As Nurse    email=${automated_tests_clinic}[default_manager]    clinic=${automated_tests_clinic}[name]
    Navigate To Case Note Templates
    Select Add New Template
    Input Case Note Information
    Save Template
    Verify Template    Symptom Management
    #nms9-ver-336
    Edit Template
    Save Template
    Verify Edited Template    Medication Instructions
    #nms9-ver-337
    Delete Template

Only Clinic Managers Should Manage Case Note Templates
    [Tags]    nms9-ver-335-2    nms9-ver-335
    Login As Nurse
    Only Message Template Is Shown


*** Keywords ***
Navigate To Case Note Templates
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${manage_templates}
    Try To Click Element    ${case_note_templates_link}

Select Add New Template
    Try To Click Element    ${add_case_button}

Input Case Note Information
    ${random_string}    Generate Random String
    ${random_string_2}    Generate Random String
    ${title_name}    Set Variable    Case Note Title-${random_string}
    ${content}    Set Variable    Case Note Content-${random_string_2}
    Set Test Variable    ${title_name}
    Set Test Variable    ${content}
    Try To Input Text    ${title_input_field}    ${title_name}
    Try To Input Text    ${content_input_field}    ${content}
    Try To Click Element    ${case_type_checkbox}
    Try To Click Element    ${symptom_management_checkbox}

Save Template
    Try To Click Element    ${save_case_note_button}
    Wait Until Page Contains    Changes saved
    Try To Click Element    ${toast_message}

Verify Template
    [Arguments]    ${case_type}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    ${template_element}    Format String    ${template_name_in_table}    ${title_name}
    Set Test Variable    ${template_element}
    Wait Until Page Contains Element    ${template_element}
    Element Should Contain    ${template_element}    ${content}
    Element Should Contain    ${template_element}    ${case_type}
    Element Should Contain    ${template_element}    ${clinic_manager_name}
    Element Should Contain    ${template_element}    ${current_date}

Edit Template
    Try To Click Element    ${template_element}
    ${random_string}    Generate Random String
    ${random_string_2}    Generate Random String
    ${title_name_2}    Set Variable    Case Note Title-${random_string}
    ${content_2}    Set Variable    Case Note Content-${random_string_2}
    Set Test Variable    ${title_name_2}
    Set Test Variable    ${content_2}
    Try To Input Text    ${title_input_field}    ${title_name_2}
    Try To Input Text    ${content_input_field}    ${content_2}
    Try To Click Element    ${symptom_management_checkbox}
    Try To Click Element    ${medication_instructions_checkbox}

Verify Edited Template
    [Arguments]    ${case_type}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    ${template_element_2}    Format String    ${template_name_in_table}    ${title_name_2}
    Set Test Variable    ${template_element_2}
    Wait Until Page Contains Element    ${template_element_2}
    Wait Until Page Does Not Contain Element    ${template_element}    3s
    Element Should Contain    ${template_element_2}    ${content_2}
    Element Should Contain    ${template_element_2}    ${case_type}
    Element Should Contain    ${template_element_2}    ${clinic_manager_name}
    Element Should Contain    ${template_element_2}    ${current_date}

Delete Template
    Try To Click Element    ${template_element_2}
    Try To Click Element    ${delete_case_note_button}
    Try To Click Element    ${confirm_button}
    Wait Until Page Contains    The case note template has been deleted
    Try To Click Element    ${toast_message}
    Wait Until Page Does Not Contain Element    ${template_element_2}    3s

Only Message Template Is Shown
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${manage_templates}
    Wait Until Page Contains Element    ${message_template_link}
    Page Should Not Contain Element    ${case_note_templates_link}
    Page Should Not Contain Element    ${questionnaire_template_link}
