*** Settings ***
Documentation       F03CA01 Clinic administrator can manage schedule templates
...                 Preconditions:
...                 - Clinic is created.
...
...                 Related use cases: [file:./test/nurse/patients/F02N09.robot|F02N09 Nurse can manage patient treatment modules]

Resource            ${EXECDIR}/resources/nurse/login.resource
Resource            ${EXECDIR}/resources/nurse/clinic_common.resource
Resource            ${EXECDIR}/resources/nurse/templates.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03ca01    clinic-web    noona_admin


*** Variables ***
${manage_templates}                                     manage-templates-link
${template_name_input}                                  //*[@data-testid="name"]
${template_enabled_radio_button}                        //*[@data-testid="template-status-enabled"]
${template_enabled_radio_button_checked}
...                                                     //*[@data-testid="template-status-enabled" and contains(@class, "radio-checked")]
${template_enabled_radio_button_2}                      //*[@id="template-status-enabled"]/..
${template_disabled_radio_button}                       //*[@data-testid="template-status-disabled"]//label
${responsible_unit_dropwdown}                           //*[@data-testid="responsible-unit"]
${responsible_unit_input}                               //*[@data-testid="responsible-unit"]//input
${responsible_unit}                                     Care Team 1
${responsible_person_dropwdown}                         //*[@data-testid="responsible-nurse"]
${responsible_person_input}                             //*[@data-testid="responsible-nurse"]//input
${treatment_module_dropwdown}                           //*[@data-testid="adverse-event-module"]
${treatment_module_input}                               //*[@data-testid="adverse-event-module"]//input
${treatment_module}                                     Acute Leukemia and MDS
${add_questionnaire_button}                             //*[@data-testid="scheduling-entry-add-button"]
${add_qol_questionnaire_button}                         //*[@data-testid="questionnaire-scheduling-entry-add-button"]
${add_message_button}                                   //*[@data-testid="message-scheduling-entry-add-button"]
${symptom_questionnaire_content_dropdown}               //*[@data-testid="entry-aeq-type"]
${symptom_questionnaire_content_input}                  //*[@data-testid="entry-aeq-type"]//input
${symptom_questionnaire_content}                        Baseline questionnaire
${symptom_questionnaire_interval_input}                 //*[@data-testid="entry-interval"]
${symptom_questionnaire_interval}                       1
${symptom_treatment_start_dropdown}                     //*[@data-testid="entry-trigger-type"]
${symptom_treatment_start_dropdown_input}               //*[@data-testid="entry-trigger-type"]//input
${symptom_treatment_start}                              Before treatment start date
${symptom_questionnaire_content_dropdown_2}             (//*[@data-testid="entry-aeq-type"])[2]
${symptom_questionnaire_content_input_2}                (//*[@data-testid="entry-aeq-type"]//input)[2]
${symptom_questionnaire_content_2}                      Clinic appointment
${symptom_questionnaire_interval_input_2}               (//*[@data-testid="entry-interval"])[2]
${weekly_checkup_checkbox_2}                            (//*[@data-testid="weekly-chekup-enabled"])[2]
${repeat_checkbox_2}                                    (//*[@data-testid="repeat-enabled"])[2]
${symptom_questionnaire_duration_input}                 (//*[@data-testid="repeat-count"])[1]
${symptom_questionnaire_duration}                       1
${symptom_questionnaire_duration_dropdown_2}            (//*[@data-testid="entry-repeat-unit"])[1]
${symptom_questionnaire_duration_dropdown_input_2}      (//*[@data-testid="entry-repeat-unit"]//input)[1]
${symptom_questionnaire_duration_time}                  weeks
${qol_questionnaire_content_dropdown}                   //*[@data-testid="questionnaire-aeq-type"]
${qol_questionnaire_content_input}                      //*[@data-testid="questionnaire-aeq-type"]//input
${qol_questionnaire_content}                            Quality of life (15D)
${qol_questionnaire_interval_input}                     //*[@data-testid="questionnaire-interval"]
${qol_questionnaire_interval}                           1
${qol_treatment_start_dropdown}                         //*[@data-testid="questionnaire-trigger-type"]
${qol_treatment_start_dropdown_input}                   //*[@data-testid="questionnaire-trigger-type"]//input
${qol_treatment_start}                                  Before treatment start date
${message_contact_type_dropdown}                        //*[@data-testid="topic-type"]
${message_contact_type_input}                           //*[@data-testid="topic-type"]//input
${message_contact_type}                                 Instructions
${message_interval_input}                               //*[@data-testid="message-interval"]
${message_interval}                                     1
${message_start_dropdown}                               //*[@data-testid="message-trigger-type"]
${message_start_dropdown_input}                         //*[@data-testid="message-trigger-type"]//input
${message_start}                                        Before treatment start date
${message_title_input}                                  //*[@data-testid="title-en_GB"]
${message_body_input}                                   //*[@data-testid="body-en_GB"]
${save_button}                                          save-template-edit
${option_4}                                             //*[@data-testid="option-5"]
${option_3}                                             //*[@data-testid="option-4"]
${option_2}                                             //*[@data-testid="option-2"]
${option_1}                                             //*[@data-testid="option-1"]
${option_0}                                             //*[@data-testid="option-0"]
${qol_first_option}
...                                                     (//*[@data-testid="questionnaire-aeq-type"]//div[ contains(@role, "option")])[1]
${qol_treatment_start_first_option}
...                                                     (//*[@data-testid="questionnaire-trigger-type"]//div[ contains(@role, "option")])[1]


*** Test Cases ***
Main Success Scenario - Add A New Schedule Template
    [Documentation]    Also includes
    ...    Extension A - Edit Schedule Template
    ...    Extension B - Repeating Schedule
    [Tags]    nms9-ver-182    nms9-ver-183    nms9-ver-184
    Navigate To Schedule Templates
    Select Add New Template
    Input Template Name, Add Care Team And Treatment Module
    Status Is Enabled By Default
    Add Symptom Questionnaire Schedule
    Add QOL Schedule
    Add Message Schedule
    Save Template
    Template Is Listed    ${template_name}
    # nms9-ver-183 - edit schedule template
    Select Template From The List    ${template_name}
    Edit Template Information
    Save Template
    Template Is Listed    ${template_name_2}
    Template Is Disabled    ${template_name_2}
    # nms9-ver-184 - repeating schedule
    Select Template From The List    ${template_name_2}
    Enabled Status, Add Content With Checkup And Repeat
    Save Template
    Template Is Listed    ${template_name_2}
#    TODO: When template is applied, the repeating schedule is displayed as separate questionnaire rows


*** Keywords ***
Select Add New Template
    ${status}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${add_schedule_template_button}
    ...    timeout=3s
    IF    ${status} == ${FALSE}
        Try To Click Element    ${manage_questionnaire_templates_link}
        Wait Until Page Contains Element    ${add_schedule_template_button}    timeout=3s
    END
    Wait Until Element Is Visible    ${add_schedule_template_button}
    Wait Until Element Is Enabled    ${add_schedule_template_button}
    Wait Until Element Is Visible    ${first_schedule_template}
    Try To Click Element    ${add_schedule_template_button}

Input Template Name, Add Care Team And Treatment Module
    ${random_string}    Generate Random String
    ${template_name}    Set Variable    Template-${random_string}
    Set Suite Variable    ${template_name}
    Try To Input Text    ${template_name_input}    ${template_name}
    Try To Click Element    ${responsible_unit_dropwdown}
    Try To Input Text    ${responsible_unit_input}    ${responsible_unit}
    Try To Click Element    //div[@title='${responsible_unit}']
    Scroll Element Into View    ${responsible_person_dropwdown}
    Try To Click Element    ${responsible_person_dropwdown}
    Try To Input Text    ${responsible_person_input}    ${test_clinic_setting_1}[f03ca01_user_name]
    Try To Click Element    //div[@title='${test_clinic_setting_1}[f03ca01_user_name]']
    Scroll Element Into View    ${add_questionnaire_button}
    Try To Click Element    ${treatment_module_dropwdown}
    Try To Input Text    ${treatment_module_input}    ${treatment_module}
    Try To Click Element    //div[@title='${treatment_module}']

Add Symptom Questionnaire Schedule
    Scroll Element Into View    ${add_message_button}
    Try To Click Element    ${add_questionnaire_button}
    Try To Click Element    ${symptom_questionnaire_content_dropdown}
    Try To Input Text    ${symptom_questionnaire_content_input}    ${symptom_questionnaire_content}
    Set Focus To Element    ${option_0}
    Try To Click Element    ${option_0}
    Try To Click Element    ${symptom_questionnaire_interval_input}
    Try To Input Text    ${symptom_questionnaire_interval_input}    ${symptom_questionnaire_interval}
    Try To Click Element    ${symptom_treatment_start_dropdown}
    Try To Input Text    ${symptom_treatment_start_dropdown_input}    ${symptom_treatment_start}
    Try To Click Element    ${option_0}

Add Second Symptom Questionnaire Schedule
    Scroll Element Into View    ${add_message_button}
    Try To Click Element    ${add_questionnaire_button}
    Try To Click Element    ${symptom_questionnaire_content_dropdown_2}
    Try To Input Text    ${symptom_questionnaire_content_input_2}    ${symptom_questionnaire_content_2}
    Set Focus To Element    ${option_2}
    Try To Click Element    ${option_2}
    Try To Click Element    ${symptom_questionnaire_interval_input_2}
    Try To Input Text    ${symptom_questionnaire_interval_input_2}    ${symptom_questionnaire_interval}

Add QOL Schedule
    Try To Click Element    ${add_qol_questionnaire_button}
    Try To Click Element    ${qol_questionnaire_content_dropdown}
    Try To Input Text    ${qol_questionnaire_content_input}    ${qol_questionnaire_content}
    Try To Click Element    ${qol_first_option}
    Try To Click Element    ${qol_questionnaire_interval_input}
    Try To Input Text    ${qol_questionnaire_interval_input}    ${qol_questionnaire_interval}
    Try To Click Element    ${qol_treatment_start_dropdown}
    Try To Input Text    ${qol_treatment_start_dropdown_input}    ${qol_treatment_start}
    Try To Click Element    ${qol_treatment_start_first_option}

Add Message Schedule
    Try To Click Element    ${add_message_button}
    Try To Click Element    ${message_contact_type_dropdown}
    Scroll Element Into View    ${add_message_button}
    Try To Input Text    ${message_contacttype__input}    ${message_contact_type}
    Try To Click Element    ${option_0}
    Try To Click Element    ${message_interval_input}
    Try To Input Text    ${message_interval_input}    ${message_interval}
    Try To Click Element    ${message_start_dropdown}
    Try To Input Text    ${message_start_dropdown_input}    ${message_start}
    Try To Click Element    ${option_0}
    ${random_string}    Generate Random String
    ${message_title}    Set Variable    Message-Title-${random_string}
    Set Suite Variable    ${message_title}
    ${random_string}    Generate Random String
    ${message_body}    Set Variable    Message-${random_string}
    Set Suite Variable    ${message_body}
    Try To Input Text    ${message_title_input}    ${message_title}
    Try To Input Text    ${message_body_input}    ${message_body}

Save Template
    Try To Click Element    ${save_button}
    Wait Until Page Contains    Template saved
    Try To Click Banner Message

Template Is Listed
    [Arguments]    ${name}
    Wait Until Page Contains    ${name}    7s

Status Is Enabled By Default
    Page Should Contain Element    ${template_enabled_radio_button_checked}

Select Template From The List
    [Arguments]    ${name}
    Try To Click Element    //td[contains(text(), "${name}")]

Edit Template Information
    ${random_string}    Generate Random String
    ${template_name_2}    Set Variable    Template-${random_string}
    Set Suite Variable    ${template_name_2}
    Try To Input Text    ${template_name_input}    ${template_name__2}
    Try To Click Element    ${template_disabled_radio_button}

Template Is Disabled
    [Arguments]    ${name}
    Wait Until Element Contains    //td[contains(text(), "${name}")]/../td[3]    Disabled

Enabled Status, Add Content With Checkup And Repeat
    Try To Click Element    ${template_enabled_radio_button}
    Add Second Symptom Questionnaire Schedule
    Try To Click Element    ${weekly_checkup_checkbox_2}
    Try To Click Element    ${repeat_checkbox_2}
    Try To Input Text    ${symptom_questionnaire_duration_input}    ${symptom_questionnaire_duration}
    Try To Click Element    ${symptom_questionnaire_duration_dropdown_2}
    Try To Input Text    ${symptom_questionnaire_duration_dropdown_input_2}    ${symptom_questionnaire_duration_time}
    Try To Click Element    ${option_1}
