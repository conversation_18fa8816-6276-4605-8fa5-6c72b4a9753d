*** Settings ***
Documentation       F16CM01 Clinic manager can manage intervention recommendations
...                 Preconditions:
...                 - Clinic Manager is logged in

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource

Suite Setup         Run Keywords    Set Libraries Order
...                     AND    Login As Nurse    email=${automated_tests_clinic}[default_manager]
Suite Teardown      Close Browser
Test Teardown       Reload Page
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f16cm01    clinic-web


*** Variables ***
${triage_rule_row}          //*[@class="table table-hover table-noona"]//tbody//*
${add_triage}               //*[contains(text(),"Add triage rule")]
${grading_checkbox1}        //p[@id='select-enum-grading-ctcaeFeverSeverity-normal']
${grading_checkbox2}        //p[@id='select-enum-grading-ctcaeFeverSeverity-under38']
${last_rule_instruction}    //*[@id = "triage-management-component"]//tr[last()]/td[4]
${last_rule}                (//div[@id = "triage-management-component"]//tr[last()])[2]
${add_modal}                //*[@id="triageRule-modal"]
${cancel_modal_button}      //*[text()[contains(., "Cancel")]]
${confirm_modal}            //*[@id="confirm-modal"]
${confirm_discard}          //*[@id="ok-confirm"]
${module_dropdown}          //input[@id='one-time-code']
${module_option}            //div[@data-testid="option-11"]
${symptom_dropdown}         //triage-select-symptom
${enable_rule}              //*[@name="selectStatus" and @value="enabled"]
${disable_rule}             //*[@name="selectStatus" and @value="disabled"]
${symptom_list}             //*[@id="triageRule-modal"]//triage-select-symptom//noona-ng-select//div[@class="ng-input"]//input[@id="one-time-code"]
${symptom_option}           //div[@data-testid="option-4"]
${triage_mgmt}              //h2[text()="Triage Management"]
${remove_modal_button}      //*[text()[contains(., "Remove")]]
${FeverSeverity38}
...                         //*[contains(@id, "select-enum-grading-ctcaeFeverSeverity-under38") and contains(@class, "mdc-checkbox-checked")]
${status_enable_option}     //*[contains(@id, "mat-radio") and contains(@class, "mdc-radio-checked")]
${instructions_text2}       (//textarea)[1]
${instructions_text}        //triage-note-component[1]//textarea
${case_text}                (//triage-note-component[1]//textarea)[2]
${save_modal_button}        //*[text()[contains(., "Save")]]
${save_toast}               //*[@id="toast-container"]//*[contains(text(), "Changes saved")]


*** Test Cases ***
Extension A - Clinic manager can add a triage rule
    [Documentation]    Also includes: Main - Clinic manager can manage intervention recommendations & Extension B - Clinic manager can edit a triage rule
    [Tags]    nms9-ver-331    nms9-ver-332    nms9-ver-333
    # nms9-ver-332
    Create Unique Global Instruction
    Clinic manager navigates to Triage Management screen
    Clinic manager click on the "Add triage rule" button
    The "Add rule" modal window opens
    Clinic manager selects a module in the modules dropdown list
    Clinic Manager clicks on the "Cancel" button
    The "Discard changes" modal window opens
    Clinic Manager discards changes
    Clinic manager click on the "Add triage rule" button
    Clinic manager selects a module in the modules dropdown list
    The second part of the form loads depending on the module Clinic manager has selected
    The languages tabs displayed are the languages defined in the clinic settings and the primary language is the first tab in the list
    Clinic Manager chooses if the rule is Enabled or Disabled
    Clinic Manager selects applicable symptom in a list of symptoms
    Noona displays the "applicable gradings" part of the form
    The content is customized depending on the selected symptom
    Clinic Manager indicates the applicable gradings for the selected symptom
    Clinic Manager types the instructions content for every language set for the clinic
    Clinic Manager clicks on the "Save" button
    Modal window closes
    A message "Changes saved" is displayed to the user
    The added rule is displayed at the end of the clinic's rules list
    # nms9-ver-331
    Noona displays the list of all the clinic's triage rules
    # nms9-ver-333
    Open last added triage rule in list
    The "Edit rule" modal window opens and contains the same fields as the "Add rule" modal window
    Inputs are set to the selected rule values
    Clinic manager can change the values of every displayed fields
    Clinic Manager clicks on the "Cancel" button
    The "Discard changes" modal window opens
    Clinic Manager discards changes
    [Teardown]    Run Keywords
    ...    Open last added triage rule in list
    ...    Clinic manager clicks the "Remove" button
    ...    The "Remove rule" modal opens
    ...    Clinic Manager deletes the case
    ...    Modal window closes
    ...    A message "Changes saved" is displayed to the user
    ...    The rule is no longer visible in the list


*** Keywords ***
Create Unique Global Instruction
    ${date}=    Get Current Date    exclude_millis=True
    Set Test Variable    ${instructions}    Autotest instruction ${date}

Clinic manager click on the "Add triage rule" button
    Wait Until Element Is Enabled    ${add_triage}
    Try To Click Element    ${add_triage}

The "Add rule" modal window opens
    Wait For Element To Be Present    ${add_modal}

Clinic Manager clicks on the "Cancel" button
    Try To Click Element    ${cancel_modal_button}

The "Discard changes" modal window opens
    Wait For Element To Be Present    ${confirm_modal}

The "Remove rule" modal opens
    Wait For Element To Be Present    ${confirm_modal}

Clinic Manager discards changes
    Try To Click Element    ${confirm_discard}

Clinic manager selects a module in the modules dropdown list
    Try To Input Text    ${module_dropdown}    Chemotherapy 11 symptoms
    Wait Until Element Is Visible    ${module_option}
    Try To Click Element    ${module_option}

The second part of the form loads depending on the module Clinic manager has selected
    Wait For Element To Be Present    ${symptom_dropdown}

The languages tabs displayed are the languages defined in the clinic settings and the primary language is the first tab in the list
    Verify Language Tabs

Clinic Manager chooses if the rule is Enabled or Disabled
    Wait For Element To Be Present    ${enable_rule}
    Wait For Element To Be Present    ${disable_rule}

Clinic Manager selects applicable symptom in a list of symptoms
    Try To Input Text    ${symptom_list}    Fever
    Wait Until Element Is Visible    ${symptom_option}
    Try To Click Element    ${symptom_option}

Noona displays the "applicable gradings" part of the form
    Wait For Element To Be Present    ${grading_checkbox1}

The content is customized depending on the selected symptom
    Wait For Element To Be Present    ${grading_checkbox1}

Clinic Manager indicates the applicable gradings for the selected symptom
    Try To Click Element    ${grading_checkbox2}

Clinic Manager types the instructions content for every language set for the clinic
    Try To Input Text    ${instructions_text}    ${instructions}
    Try To Input Text    ${case_text}    Default case notes text for automated testing

Clinic Manager clicks on the "Save" button
    Try To Click Element    ${save_modal_button}

Modal window closes
    Wait For Element To Not Be Present    ${add_modal}

A message "Changes saved" is displayed to the user
    Wait For Element To Be Present    ${save_toast}
    Try To Click Element    ${save_toast}
    Wait Until Page Does Not Contain    ${save_toast}

The added rule is displayed at the end of the clinic's rules list
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Wait Until Keyword Succeeds    9    1    Element Text Should Be    ${last_rule_instruction}    ${instructions}

Open last added triage rule in list
    Try To Click Element    //td[contains(text(),"${instructions}")]

The "Edit rule" modal window opens and contains the same fields as the "Add rule" modal window
    The second part of the form loads depending on the module Clinic manager has selected
    The languages tabs displayed are the languages defined in the clinic settings and the primary language is the first tab in the list
    Clinic Manager chooses if the rule is Enabled or Disabled
    Noona displays the "applicable gradings" part of the form
    The content is customized depending on the selected symptom

Inputs are set to the selected rule values
    Wait Until Page Contains Element    ${instructions_text2}
    Page Should Contain Element    ${status_enable_option}
    Wait Until Page Contains Element    ${grading_checkbox2}
    Wait Until Element Is Visible    ${FeverSeverity38}    timeout=5s

Clinic Manager deletes the case
    Try To Click Element    ${confirm_discard}

The rule is no longer visible in the list
    Run Keyword And Expect Error    *    The added rule is displayed at the end of the clinic's rules list

Clinic manager can change the values of every displayed fields
    The second part of the form loads depending on the module Clinic manager has selected
    The languages tabs displayed are the languages defined in the clinic settings and the primary language is the first tab in the list
    Clinic Manager chooses if the rule is Enabled or Disabled
    Clinic Manager selects applicable symptom in a list of symptoms when editing
    Noona displays the "applicable gradings" part of the form
    The content is customized depending on the selected symptom
    Clinic Manager indicates the applicable gradings for the selected symptom
    Clinic Manager types the instructions content for every language set for the clinic

Clinic manager clicks the "Remove" button
    Try To Click Element    ${remove_modal_button}

Clinic manager navigates to Triage Management screen
    Wait Until Page Does Not Contain Element    ${loader}    10s
    Wait Until Element Is Visible    ${clinic_menu}
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${triage_management_link}
    Wait until element is visible    ${triage_mgmt}

Noona displays the list of all the clinic's triage rules
    Wait until keyword succeeds    10x    1s    Verify more than zero triage rules
    [Teardown]    Take screenshot if keyword failed

Verify more than zero triage rules
    @{triage_rule_rows}=    Get webelements    ${triage_rule_row}
    Should be true    len($triage_rule_rows) > 0    No triage rules were found on the Triage Management screen

Take screenshot if keyword failed
    IF    $KEYWORD_STATUS == 'FAIL'    Capture Screenshot

Clinic Manager selects applicable symptom in a list of symptoms when editing
    Try To Input Text    ${symptom_list}    Fever
    Wait Until Element Is Visible    ${symptom_option}
    Try To Click Element    ${symptom_option}
