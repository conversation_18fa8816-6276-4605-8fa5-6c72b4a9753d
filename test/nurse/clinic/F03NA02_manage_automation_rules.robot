*** Settings ***
Documentation       F03NA02 Noona administrator can manage automation rules
...                 Preconditions:
...                 - Noona administrator has logged in

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}management${/}admin_common.resource

Suite Setup         Login As Nurse Admin
Suite Teardown      Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na02    clinic-web    noona_admin


*** Variables ***
${enable-radiobutton}           //span[@class='form-label' and text()='Enabled']
${disable-radiobutton}          //span[@class='form-label' and text()='Disabled']
${turkish-checkbox-checked}     //*[@data-testid="enabled-checkbox-tr_TR" and contains(@class, 'mdc-checkbox-checked')]
${turkish-checkbox-label}       //*[@data-testid="enabled-checkbox-tr_TR"]
${save_message}                 save
${automation_rules}             //*[@id='clinic-metrics-link']
${clinic_settings}              //*[@id='clinic-settings-link']
${clinic_settings_save}         //button[@id='save']
${clinic_language_table}        //*[@data-testid='manage-clinic-languages-table']
${toast_message}                (//*[@role='alert' and contains(text(),'Clinic settings are updated')])
${rule_info}                    (//*[@class='form-field'])[3]
${dialogue_tr}                  //*[@id='tab-tr_TR-dialog']
${message_tr}                   //*[@id='tab-tr_TR-message']


*** Test Cases ***
Nurse admin can manage automation rules
    [Tags]    nms9-ver-189
    User navigates to "Automation rules" page
    List of all automation rules are displayed
    User selects a rule with automated message link edit
    Dialog opens which displays rule information
    User can disable rule in case of rule not working appropriately
    #    If rule related automated message contains a link, user can edit links for different languages.
    #    There must be a link for each language, or the rule cannot be saved
    Automated message and notification dialog text can be edited in all languages which are enabled for clinic
    User saves changes, dialog closes and user is directed to automation rules page
    Restore original state


*** Keywords ***
Login As Nurse Admin
    Set Libraries Order
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Add Turkish For Clinic

User navigates to "Automation rules" page
    Wait Until Keyword Succeeds    15    1    Try To Click Element    ${admin_clinic_menu}
    Wait Until Keyword Succeeds    15    1    Try To Click Element    ${automation_rules}

List of all automation rules are displayed
    Wait Until Element Is Enabled    //tbody/tr[2]
    ${rows}    Get Element Count    //tbody/tr
    Should Be True    ${rows} > 1

User selects a rule with automated message link edit
    ${count}    Get Element Count    //tbody/tr/td[3]
    FOR    ${INDEX}    IN RANGE    1    ${count}
        ${text}    Get Text    (//tbody/tr/td[3])[${INDEX}]
        IF    '${text}'=='Enabled'
            Try To Click Element    (//tbody/tr/td[3])[${INDEX}]
            ${selected_id}    Get Text    (//tbody/tr/td[3])[${INDEX}]/../td[1]
            Set Test Variable    ${selected_id}
            BREAK
        END
    END

Dialog opens which displays rule information
    Wait Until Element Is Visible    ${rule_info}

User can disable rule in case of rule not working appropriately
    Wait Until Element Is Visible    ${disable-radiobutton}
    Sleep    1
    Wait Until Keyword Succeeds    9    1    Try To Click Element    ${disable-radiobutton}
    Wait Until Element Is Enabled    ${save_message}

Automated message and notification dialog text can be edited in all languages which are enabled for clinic
    Wait Until Element Is Visible    ${dialogue_tr}
    Wait Until Element Is Visible    ${message_tr}

User saves changes, dialog closes and user is directed to automation rules page
    Wait Until Keyword Succeeds    9    1    Try To Click Element    ${save_message}
    Wait Until Page Contains    Changes saved
    Try To Click Banner Message
    List of all automation rules are displayed

Add Turkish For Clinic
    Navigate To Clinic Settings
    Wait Until Element Is Visible    ${turkish-checkbox-label}
    ${checked}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${turkish-checkbox-checked}
    ...    timeout=5
    IF    ${checked} == ${FALSE}    Check Turkish And Save
    User navigates to "Automation rules" page

Remove Turkish For Clinic
    Navigate To Clinic Settings
    Wait Until Element Is Visible    ${turkish-checkbox-label}
    Scroll Element Into View    ${turkish-checkbox-label}
    ${checked}    Run Keyword And Return Status
    ...    Wait Until Page Contains Element
    ...    ${turkish-checkbox-checked}
    ...    timeout=5
    IF    ${checked} == ${FALSE}    Check Turkish And Save

Navigate To Clinic Settings
    Wait Until Keyword Succeeds    45    1    Try To Click Element    ${admin_clinic_menu}
    Wait Until Keyword Succeeds    45    1    Try To Click Element    ${clinic_settings}
    Wait Until Element Is Visible    ${clinic_language_table}

Check Turkish And Save
    Wait Until Keyword Succeeds    20    1    Try To Click Element    ${turkish-checkbox-label}
    Wait Until Keyword Succeeds    9    1    Click Button    ${clinic_settings_save}
    Wait Until Page Contains    ${clinic_settings_are_updated_banner}
    Try To Click Banner Message

Restore original state
    [Documentation]    selected_id should be passed from kw "User selects a rule with automated message link edit"
    Wait Until Keyword Succeeds    30    1    Try To Click Element    //td[text()="${selected_id}"]
    Wait Until Keyword Succeeds    30    1    Try To Click Element    ${enable-radiobutton}
    Wait Until Keyword Succeeds    30    1    Try To Click Element    ${save_message}
    Try To Click Banner Message
    Remove Turkish For Clinic
