*** Settings ***
Documentation       F03N02 Nurse can add and edit message templates and set default text to message type
...                 Preconditions:
...                 - Nurse is logged in nurse <PERSON><PERSON>.
...                 - Contact clinic feature must be enabled in clinic settings.
...                 - Languages IW, FI, AR enabled for clinic

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource            ${EXECDIR}${/}resources/nurse/templates.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03n02    clinic-web


*** Variables ***
${confirm_delete_modal}                 confirm-modal
${language-tabs}                        //div[contains(@class, 'translations')]/div/div
${set_default_message}                  //*[@data-testid="default-topic-type-status"]//label
${title_field}                          //*[@data-testid="title"]
${content_field}                        //*[@data-testid="content-en_GB"]
${save_template_button}                 save-message-template
${template_modal}                       edit-message-template-modal
${confirmation_button}                  ok-confirm
${test_message_templates}               //td[contains(text(),"Test message template title")]
${first_test_message_template}          (//td[contains(text(),"Test message template title")])[1]
${delete_template_button}               //*[@data-testid="delete-message-template"]
${cancel_button}                        cancel-confirm
${en_gb_text}                           content-en_GB
${set_default_type}                     //*[@data-testid="defaultForTopicType"]
${set_default_type_input}               //*[@data-testid="defaultForTopicType"]//input
${set_default_type_first_option}        //*[@data-testid="option-0"]
${care_team_not_editable_checkbox}      //*[@data-testid="template-care-team-not-editable"]
${manager_email}                        <EMAIL>
${fi}                                   //*[@id='tab-fi_FI']
${it}                                   //*[@id='tab-it_IT']
${en}                                   //*[@id='tab-en_GB']
${pt}                                   //*[@id='tab-pt_PT']
${es}                                   //*[@id='tab-es_ES']
${fr}                                   //*[@id='tab-fr_FR']
${da}                                   //*[@id='tab-da_DK']
${nl}                                   //*[@id='tab-nl_NL']
${tr}                                   //*[@id='tab-tr_TR']
${de}                                   //*[@id='tab-de_DE']
${no}                                   //*[@id='tab-no_NO']
${sv}                                   //*[@id='tab-sv_FI']
${pl}                                   //*[@id='tab-pl_PL']
${default_text}                         Default text
${checkbox_checked_attribute}           checkbox-checked
${cancel_template_edit_btn}             //*[@id="cancel-message-template"]
${save_toast}                           //*[@id="toast-container"]//*[contains(text(), "Changes saved")]


*** Test Cases ***
Main success scenario - Template can be saved
    [Documentation]    This test case also includes Extension A - Edit template
    [Tags]    nms9-ver-173    nms9-ver-174
    # nms9-ver-173
    Login As Nurse
    Navigate to Message templates
    Click the "New message template" button
    Fill in message template information
    Languages which are enabled to clinic are displayed
    Clinic primary language is displayed separately
    Save and dialog closes
    # nms9-ver-174
    Select template from list
    Fill in message template information
    Save and dialog closes
    [Teardown]    Run Keywords    Delete template
    ...    AND    Close Browser

Extension C - Set default text for a message type
    [Documentation]    This test case also includes Extension B - Delete template
    [Tags]    nms9-ver-176    nms9-ver-175
    # nms9-ver-176
    Login As Nurse
    Navigate to Message templates
    Click the "New message template" button
    Fill in message template information
    Save and dialog closes
    Select non-default template
    Enter template text    foo
    Select "Set as default text for a message type"
    Select message type
    Save and dialog closes
    Select template from list
    Enter template text    bar
    Save and dialog closes
    "Default text" is displayed in selected message template "Type" column in list view
    # nms9-ver-175
    Select template from list
    Select delete
    Confirmation dialog is displayed
    Cancel deletion and return to template list screens
    [Teardown]    Run Keywords    Delete template
    ...    AND    Close Browser

Extension D — Lock template content
    [Tags]    nms9-ver-177    noona_admin
    Login As Nurse    clinic=${appointment_clinic}[name]    user_type=${USER_TYPE}[noona_admin]
    Navigate to Message templates
    Click the "New message template" button
    Fill in message template information
    Set content as not editable
    Save and dialog closes
    Lock icon is shown
    Admin can edit the template
    Close Browser
    Login As Nurse    ${appointment_clinic}[user_email]    clinic=${appointment_clinic}[name]
    Navigate to Message templates
    Lock icon is shown
    Can't edit the locked template
    Close Browser
    Login As Nurse    ${manager_email}    clinic=${appointment_clinic}[name]
    Navigate to Message templates
    Lock icon is shown
    Can't edit the locked template
    Close Browser
    Login As Nurse    clinic=${appointment_clinic}[name]    user_type=${USER_TYPE}[noona_admin]
    Navigate to Message templates
    [Teardown]    Run Keywords    Delete template
    ...    AND    Close Browser


*** Keywords ***
Click the "New message template" button
    Try To Click Element    ${new_template_button}

Fill in message template information
    ${time}    Get Time
    Set Test Variable    ${time}
    Try To Input Text    ${title_field}    Test message template title ${time}
    ${content}    Generate Random String
    Try To Input Text    ${content_field}    ${content}
    Try To Click Element    ${title_field}

Set content as not editable
    Try To Click Element    ${care_team_not_editable_checkbox}

Lock icon is shown
    Wait Until Page Contains Element
    ...    //*[contains(@id, "message-template")]//td[contains(text(), "${time}")]/..//*[@data-mat-icon-name="icon-lock"]

Admin can edit the template
    Try To Click Element    //*[contains(@id, "message-template")]//td[contains(text(), "${time}")]
    Wait Until Element Is Enabled    ${title_field}

Can't edit the locked template
    Try To Click Element    //*[contains(@id, "message-template")]//td[contains(text(), "${time}")]
    Wait Until Page Contains Element    ${title_field}
    Page Should Contain    Content editable by admin only
    Element Should Be Disabled    ${title_field}

Delete template
    Admin can edit the template
    Try To Click Element    ${delete_template_button}
    Try To Click Element    ${confirmation_button}
    Wait Until Page Contains    The message template has been deleted

Languages which are enabled to clinic are displayed
    Try For Equal Element Text    ${en}    EN
    Try For Equal Element Text    ${fi}    FI
    Try For Equal Element Text    ${it}    IT
    Try For Equal Element Text    ${pt}    PT
    Try For Equal Element Text    ${es}    ES
    Try For Equal Element Text    ${fr}    FR
    Try For Equal Element Text    ${nl}    NL
    Try For Equal Element Text    ${tr}    TR
    Try For Equal Element Text    ${de}    DE
    Try For Equal Element Text    ${no}    NO
    Try For Equal Element Text    ${sv}    SV
    Try For Equal Element Text    ${pl}    PL

Clinic primary language is displayed separately
    Try For Equal Element Text    (${language-tabs})[1]    EN

Save and dialog closes
    Try To Click Element    ${save_template_button}
    Sleep    1
    Run Keyword And Ignore Error    Click Element    ${confirmation_button}
    Wait For Element To Not Be Present    ${template_modal}
    Try To Click Element    ${save_toast}

Select template from list
    Wait Until Element Is Visible    ${first_test_message_template}
    Scroll Element Into View    ${first_test_message_template}
    Try To Click Element    ${first_test_message_template}

Select non-default template
    ${element_count}    Get Length    ${test_message_templates}
    FOR    ${INDEX}    IN RANGE    1    ${element_count}+1
        Wait Until Element Is Visible    (${test_message_templates})[${INDEX}]/following-sibling::td[2]
        ${text}    Get Text    (${test_message_templates})[${INDEX}]/following-sibling::td[2]
        IF    '${default_text}' not in '${text}'
            Try To Click Element    (${test_message_templates})[${INDEX}]
            BREAK
        END
    END

Select delete
    Try To Click Element    ${delete_template_button}

Confirmation dialog is displayed
    Wait For Element To Be Present    ${confirm_delete_modal}

Cancel deletion and return to template list screens
    Try To Click Element    ${cancel_button}
    Try To Click Element    ${cancel_template_edit_btn}
    Wait For Element To Not Be Present    ${confirm_delete_modal}

Select "Set as default text for a message type"
    ${attr}    Get Element Attribute    ${set_default_message}/../../..    class
    ${status}    Run Keyword And Return Status    Should Not Contain    ${attr}    ${checkbox_checked_attribute}
    Scroll Element Into View    ${save_template_button}
    IF    ${status}    Click Element    ${set_default_message}

Select message type
    Scroll Element Into View    ${save_template_button}
    Try To Click Element    ${set_default_type}
    Try To Input Text    ${set_default_type_input}    Question
    Try To Click Element    ${set_default_type_first_option}

Enter template text
    [Arguments]    ${text}
    Try To Input Text    ${content_field}    ${text}
    Try To Click Element    ${title_field}

"Default text" is displayed in selected message template "Type" column in list view
    ${first_template_text}    Set Variable    xpath=${first_test_message_template}/following-sibling::td[1]
    ${first_template_type}    Set Variable    xpath=${first_test_message_template}/following-sibling::td[2]
    Try For Equal Element Text    ${first_template_text}    bar
    Try For Equal Element Text    ${first_template_type}    Default text
