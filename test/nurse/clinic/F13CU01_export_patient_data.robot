*** Settings ***
Documentation       F13CU01 Clinic user can export patient data
...                 Preconditions:
...                 - User is logged in.

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}file_exports.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource

Suite Setup         Set Libraries Order
Test Setup          Prepare Chrome for Clinic Downloads And Login
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f13cu01    clinic-web


*** Variables ***
${download-wait}                1.5 min
${data_export_link}             //*[@data-testid="file-download"]
${copy_to_clipboard_button}     copy
${open_QOL_questionnaires}
...                             //*[@id="patient-messaging-content"]//div[contains(text(), "QUALITY OF LIFE QUESTIONNAIRE (15D©)")]/..//span[contains(text(), "Open")]


*** Test Cases ***
User Can Export Patient Data As PDF
    [Tags]    nms9-ver-280
    Search Patient By Identity Code    ${f13cu01_patient}[ssn]
    Choose General Information Tab
    User Selects Export Patient Data To PDF File
    IF    '${remote_url_exists}'=='False'    PDF Downloaded To User's Computer

Extension A - User Can Export Case Summary As PDF File
    [Tags]    nms9-ver-281
    Search Patient By Identity Code    ${f13cu01_patient}[ssn]
    Navigate To Patient Cases Tab
    Open Case Details From Patient Cases
    Select Export To PDF
    IF    '${remote_url_exists}'=='False'    PDF Downloaded To User's Computer

Extension B - User Can Export Case Summary As Text
    [Tags]    nms9-ver-282
    Search Patient By Identity Code    ${f13cu01_patient}[ssn]
    Navigate To Patient Cases Tab
    Open QOL Case Details From Patient Cases
    Select Copy To Clipboard
#    TODO: paste to another system.


*** Keywords ***
User Selects Export Patient Data To PDF File
    Wait Until Element Is Visible    ${data_export_link}
    Scroll Element Into View    ${data_export_link}
    Try To Click Element    ${data_export_link}
    Wait Until Page Contains    PDF report downloaded    35s

PDF Downloaded To User's Computer
    Wait Until Keyword Succeeds    ${download-wait}    3    Directory Should Exist    ${downloads-dir}
    Wait Until Keyword Succeeds    ${download-wait}    3    File Should Exist    ${downloads-dir}${/}*.pdf
    Diary Download Should Contain PDF Signature    ${downloads-dir}

Open Case Details From Patient Cases
    Try To Click Element    (${open_patient_cases})[1]

Open QOL Case Details From Patient Cases
    Try To Click Element    ${open_QOL_questionnaires}

Select Export To PDF
    Try To Click Element    ${download_pdf_button}
    Wait Until Page Contains    PDF report downloaded    30s

Select Copy To Clipboard
    Try To Click Element    ${clipboard_icon}
    Clipboard Content
    Try To Click Element    ${copy_to_clipboard_button}
    Wait Until Page Contains    Report copied to clipboard

Clipboard Content
    Wait Until Page Contains    ${f13cu01_patient}[ssn]
    Page Should Contain    ${f13cu01_patient}[name]
