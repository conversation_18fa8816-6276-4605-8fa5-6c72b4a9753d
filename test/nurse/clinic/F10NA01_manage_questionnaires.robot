*** Settings ***
Documentation       F10NA01 Noona admin can manage which questionnaires are enabled for a clinic
...                 - Noona admin user is logged in.

Resource            ${EXECDIR}/resources/nurse/login.resource
Resource            ${EXECDIR}/resources/nurse/templates.resource
Resource            ${EXECDIR}/resources/nurse/clinic_common.resource
Resource            ${EXECDIR}/resources/management/admin_common.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinical_settings.resource
Resource            ${EXECDIR}${/}resources/clinic_settings/clinical_settings.resource
Resource            ${EXECDIR}/resources/clinic_settings/clinic_information.resource

Suite Setup         Set Libraries Order
Test Setup          Run Keywords    Acquire Lock    UpdateClinic
...                     AND    Disable Questionnaires As Test Setup
Test Teardown       Run Keywords    Release Lock    UpdateClinic
...                     AND    Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f10na01    clinic-web


*** Variables ***
${clinic_settings_link}             clinic-settings-link
${basic_settings_tab}               base-settings
${clinical_settings_tab}            clinical-settings
${enable_maas_checkbox}             //*[@data-testid="enabled-checkbox-quality-of-life-topics-bounceMaas"]
${enable_hads_checkbox_span}
...                                 //*[@data-testid="enabled-checkbox-quality-of-life-topics-bounceHads"]/div[1]
${enable_hads_checkbox}             //*[@data-testid="enabled-checkbox-quality-of-life-topics-bounceHads"]
${enable_maas_score_checkbox}       //*[@data-testid="score-visible-checkbox-quality-of-life-topics-bounceMaas"]
${enable_hads_score_checkbox}       //*[@data-testid='score-visible-checkbox-quality-of-life-topics-bounceHads']
${enable_ipq_score_checkbox}        //*[@data-testid="score-visible-checkbox-quality-of-life-topics-ipq"]
${maas_checked}
...                                 (//*[@data-testid="enabled-checkbox-quality-of-life-topics-bounceMaas" and contains(@class, 'checkbox-checked')])
${maas_not_checked}
...                                 (//*[@data-testid="enabled-checkbox-quality-of-life-topics-bounceMaas" and not(contains(@class, 'checkbox-checked'))])
${hads_checked}
...                                 (//mat-checkbox[@data-testid="enabled-checkbox-quality-of-life-topics-bounceHads" and contains(@class, 'checkbox-checked')])
${hads_not_checked}
...                                 (//*[@data-testid="enabled-checkbox-quality-of-life-topics-bounceHads" and not(contains(@class, 'checkbox-checked'))])
${maas_score_checked}
...                                 (//*[@data-testid="score-visible-checkbox-quality-of-life-topics-bounceMaas" and contains(@class, 'checkbox-checked')])
${maas_score_not_checked}
...                                 (//*[@data-testid="score-visible-checkbox-quality-of-life-topics-bounceMaas" and not(contains(@class, 'checkbox-checked'))])
${hads_score_checked}
...                                 (//mat-checkbox[@data-testid="score-visible-checkbox-quality-of-life-topics-bounceHads" and contains(@class, 'checkbox-checked')])
${ipq_score_checked}
...                                 (//*[@data-testid="score-visible-checkbox-quality-of-life-topics-ipq" and contains(@class, 'checkbox-checked')])
${hads_score_not_checked}
...                                 (//mat-checkbox[@data-testid="score-visible-checkbox-quality-of-life-topics-bounceHads" and not(contains(@class, 'checkbox-checked'))])
${ipq_score_not_checked}
...                                 (//*[@data-testid="score-visible-checkbox-quality-of-life-topics-ipq" and not(contains(@class, 'checkbox-checked'))])
${save_button}                      save
${add_qol_dropdown_arrow}           (//noona-ng-select[@data-testid='questionnaire-aeq-type']//span)[1]


*** Test Cases ***
Enable Questionnaires
    [Tags]    nms9-ver-262
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    # to make sure that page is completely loaded before the next step
    Wait Until Element Is Visible
    ...    ${clinic_user_list_last_name_header}
    Wait Until Element Is Visible    ${clinic_menu}
    Wait Until Element Is Enabled    ${clinic_menu}
    Go To Clinical Settings
    Enable MAAS And HADS Questionnaires
    Save Settings
    Check Saved Settings
    Enable Score For IPQ    # questionnaire is always enabled for TIPI. Only score is disabled/enabled.
    Save Settings
    Sleep    3s    # give time for the reload after saving in clinical settings (reload is working as designed)
    Check Saved Settings Scoring Enabled
    Questionnaires Are No Longer Available In Schedule List
    # Questionnaires Are Removed From Scheule Templates If Used
    # Questionnaires Are Removed Patient's Schedules If Has Not Been Sent
    # Previously answered questionnaires results will stay visible
    Wait Until Element Is Visible    ${schedule_template_name_header}
    Go To Clinical Settings
    Disable Questionnaires And Scoring
    Check Saved Settings Questionnnaires And Scoring Disabled
    Navigate To Schedule Templates
    Get Current QOL Questionnaires
    List Should Not Contain Value    ${qol_questionnaires}    MAAS
    List Should Not Contain Value    ${qol_questionnaires}    HADS


*** Keywords ***
Go To Clinical Settings
    Wait Until Element Is Visible    ${admin_clinic_menu}
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    Wait Until Element Is Visible    ${clinical_settings_tab}
    Try To Click Element    ${clinical_settings_tab}    wait_in_seconds=5x

Enable MAAS And HADS Questionnaires
    Wait Until Element Is Visible    ${select_questionnaires_header}
    Scroll Element Into View    ${select_questionnaires_header}
    Wait Until Page Contains Element    ${enable_maas_checkbox}
    ${attr_hads}    Get Element Attribute    ${enable_hads_checkbox_span}    class
    IF    'mat-checkbox-checked' not in '${attr_hads}'
        Scroll Element Into View    ${enable_hads_checkbox_span}
        Try To Click Element    ${enable_hads_checkbox_span}
        ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${hads_checked}    timeout=5s
        IF    ${status}==${FALSE}
            Try To Click Element    ${enable_hads_checkbox_span}
            Wait Until Page Contains Element    ${hads_checked}    timeout=5s
        END
    END
    ${attr_maas}    Get Element Attribute    ${enable_maas_score_checkbox}    class
    IF    'mat-checkbox-checked' not in '${attr_maas}'
        Scroll Element Into View    ${enable_maas_checkbox}
        Try To Click Element    ${enable_maas_checkbox}
        Wait Until Page Contains Element    ${maas_checked}
    END

Check Saved Settings
    Wait Until Element Is Visible    ${clinic_settings_header}
    Wait Until Element Is Visible    ${select_questionnaires_header}
    Scroll Element Into View    ${select_questionnaires_header}
    Sleep    1    # give time for page reload ready from the "Save settings"-keyword
    Wait Until Element Is Visible    ${basic_settings_tab}    timeout=20s
    Try To Click Element    ${basic_settings_tab}    wait_in_seconds=5x
    Try To Click Element    ${clinical_settings_tab}    wait_in_seconds=5x
    Wait Until Element Is Visible    ${enable_maas_checkbox}
    Scroll Element Into View    ${enable_maas_checkbox}
    Sleep    1    # gives time to scroll before verifying the next element
    Wait Until Page Contains Element    ${maas_checked}
    Wait Until Page Contains Element    ${hads_checked}

Enable Score For IPQ
    ${attr_ipq_score}    Get Element Attribute    ${enable_ipq_score_checkbox}    class
    IF    'mat-checkbox-checked' not in '${attr_ipq_score}'
        Try To Click Element    ${enable_ipq_score_checkbox}
    END

Check Saved Settings Scoring Enabled
    Wait Until Element Is Visible    ${select_questionnaires_header}
    Scroll Element Into View    ${select_questionnaires_header}
    Wait Until Page Contains Element    ${enable_maas_checkbox}
    Wait Until Page Contains Element    ${maas_checked}
    Wait Until Page Contains Element    ${hads_checked}
    # checks that scoring is automatically enabled when questionnaires are enabled
    Wait Until Page Contains Element
    ...    ${maas_score_checked}
    # checks that scoring is automatically enabled when questionnaires are enabled
    Wait Until Page Contains Element
    ...    ${hads_score_checked}
    # checks that scoring can be enabled independently from questionnaires
    Wait Until Page Contains Element
    ...    ${ipq_score_checked}

Disable Questionnaires And Scoring
    Wait Until Element Is Visible    ${enable_maas_score_checkbox}
    ${attr_maas_score}    Get Element Attribute    ${enable_maas_score_checkbox}    class
    ${attr_maas}    Get Element Attribute    ${enable_maas_checkbox}    class
    ${attr_hads_score}    Get Element Attribute    ${enable_hads_score_checkbox}    class
    ${attr_hads}    Get Element Attribute    ${enable_hads_checkbox}    class
    ${attr_ipq_score}    Get Element Attribute    ${enable_ipq_score_checkbox}    class
    IF    'checkbox-checked' in '${attr_hads_score}'
        Sleep    1
        Scroll Element Into View    ${enable_hads_score_checkbox}
        Try To Click Element    ${enable_hads_score_checkbox}
        ${status}    Run Keyword And Return Status
        ...    Wait Until Page Contains Element
        ...    ${hads_score_not_checked}
        ...    timeout=5s
        IF    ${status}==${FALSE}
            Try To Click Element    ${enable_hads_score_checkbox}
            Wait Until Page Contains Element    ${hads_score_not_checked}    timeout=5s
        END
    END
    IF    'checkbox-checked' in '${attr_hads}'
        Try To Click Element    ${enable_hads_checkbox_span}
        ${status}    Run Keyword And Return Status
        ...    Wait Until Page Contains Element
        ...    ${hads_not_checked}
        ...    timeout=5s
        IF    ${status}==${FALSE}
            Try To Click Element    ${enable_hads_checkbox_span}
            Wait Until Page Contains Element    ${hads_not_checked}    timeout=5s
        END
    END
    IF    'checkbox-checked' in '${attr_ipq_score}'
        Try To Click Element    ${enable_ipq_score_checkbox}
    END
    IF    'checkbox-checked' in '${attr_maas_score}'
        Try To Click Element    ${enable_maas_score_checkbox}
    END
    IF    'checkbox-checked' in '${attr_maas}'
        Try To Click Element    ${enable_maas_checkbox}
    END
    IF    'checkbox-checked' in '${attr_maas_score}' or 'checkbox-checked' in '${attr_maas}' or 'checkbox-checked' in '${attr_hads_score}' or 'checkbox-checked' in '${attr_hads}' or 'checkbox-checked' in '${attr_ipq_score}'
        Save Settings
    END

Check Saved Settings Questionnnaires And Scoring Disabled
    Wait Until Element Is Visible    ${clinic_settings_header}
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${clinic_settings_header}
    Try To Click Element    ${basic_settings_tab}    wait_in_seconds=5x
    Try To Click Element    ${clinical_settings_tab}    wait_in_seconds=5x
    Wait Until Page Contains Element    ${hads_not_checked}
    Wait Until Page Contains Element    ${hads_score_not_checked}
    Wait Until Page Contains Element    ${ipq_score_not_checked}
    Wait Until Page Contains Element    ${maas_not_checked}
    Wait Until Page Contains Element    ${maas_score_not_checked}

Get Current QOL Questionnaires
    Wait Until Element Is Visible    ${schedule_templates_tab}
    Reload Page
    Wait Until Element Is Visible    ${schedule_templates_tab}
    Try To Click Element    ${schedule_templates_tab}
    Try To Click Element    ${add_schedule_template_button}    wait_in_seconds=5x
    Try To Click Element    ${add_qol_questionnaire_schedule}
    Try To Click Element    ${add_qol_questionnaire_schedule_dropdown}
    @{qol_questionnaires}    Create List
    @{qol_questionnaires_elements}    Get WebElements    ${questionnaires_dropdown_options}
    FOR    ${option}    IN    @{qol_questionnaires_elements}
        ${text}    Get Text    ${option}
        Append To List    ${qol_questionnaires}    ${text}
    END
    Set Test Variable    @{qol_questionnaires}

Disable Questionnaires As Test Setup
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Wait Until Element Is Visible
    ...    ${clinic_user_list_last_name_header}
    Wait Until Element Is Visible    ${admin_clinic_menu}
    Wait Until Element Is Enabled    ${admin_clinic_menu}
    Go To Clinical Settings
    Disable Questionnaires And Scoring
    Close Browser

Questionnaires Are No Longer Available In Schedule List
    Navigate To Schedule Templates
    Get Current QOL Questionnaires
    List Should Contain Value    ${qol_questionnaires}    MAAS
    List Should Contain Value    ${qol_questionnaires}    HADS
    Try To Click Element    ${add_qol_dropdown_arrow}    # click the dropdown to close
    Try To Click Element    ${cancel_schedule_template_modal}    # cancels the dropdown only
    Try To Click Element    ${discard_changes_button}

Questionnaires Are Removed From Scheule Templates If Used
    Try To Click Element    //td[contains(text(),"F10NA01 Template")]
    Wait Until Element Is Visible    ${cancel_schedule_template_modal}
    Page Should Not Contain    MAAS
    Page Should Not Contain    HADS
    Try To Click Element    ${cancel_schedule_template_modal}
