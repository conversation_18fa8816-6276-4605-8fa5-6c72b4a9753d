*** Settings ***
Documentation       F03N01 Nurse can send clinic announcements to patients
...                 Preconditions:
...                 - Nurse is logged in nurse <PERSON><PERSON>.
...                 [file:./test/patient/clinic/F03P01.robot|F03P01 Patient can view announcements from clinic]
...                 TODO:
...                 - Date after which the announcement is no longer displayed to patients
...                 - If the active module is closed the announcement stays in patients inbox until it expires
...                 - If announcement expires, the announcement disappears
...                 - Patients in selected care teams get SMS notification
...
...                 F03P01 Patient can view announcements from clinic
...                 Preconditions:
...                 - Nurse has sent an announcement patients in this patient's care team.
...                 NOTE: New message red indicator included in the test
...

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}announcements.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource

Suite Setup         Set Libraries Order
Test Setup          Set Application On Environment
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03n01    usecase-f03p01    patient-web    clinic-web


*** Test Cases ***
Nurse can send clinic announcements to patients
    [Documentation]    Also includes nms9-ver-172 - F03P01 Patient can view announcements from clinic
    [Tags]    nms9-ver-195    nms9-ver-172    native-web
    Set Variables For Test Suite F03N01
#    # nms9-ver-195
    Send Announcement To Patient
#    # nms9-ver-172
    View Announcement As Patient
    Verify Clinic Message Title Is Correct    Announcement ${today}
    Verify Clinic Message Date Is Correct    ${today}
    Verify Clinic Message Is Correct    This is a test announcement
    Close Clinic Message
    New Message Indicator Should Be Not Visible
    Close Browser
    IF    'native' not in '${ENVIRONMENT}'
        Patient Received Email About Announcement    ${patient_email}    ${automated_tests_clinic}[name]
    END
    [Teardown]    F03N01 Test Teardown
    # TODO: Patients in selected care teams get SMS notification

Expire Announcements
    [Tags]    manual
    # TODO: Date after which the announcement is no longer displayed to patients
    # TODO: If the active module is closed the announcement stays in patients inbox until it expires
    # TODO: If announcement expires, the announcement disappears


*** Keywords ***
Set Variables For Test Suite F03N01
    ${date}    Get Current Date
    ${tomorrow}    Add Time To Date    ${date}    1 day    result_format=%-d.%-m
    ${today}    Convert Date    ${date}    result_format=%d.%m.%Y
    ${day}    Convert Date    ${date}    result_format=%a
    ${day}    Convert To Upper Case    ${day}
    Set Test Variable    ${today}    ${day}${SPACE}${today}
    Set Test Variable    ${tomorrow}
    IF    'native' in '${ENVIRONMENT}'
        Set Test Variable    ${patient_email}    ${f03n01_patient_native}[email]
        Set Test Variable    ${care_team_name}    ${automated_tests_clinic}[f03n01_native_app_team]
        IF    'test' in '${ENVIRONMENT}'
            Set Test Variable    ${care_team_id}    ${automated_tests_clinic}[f03n01_native_app_id_test]
        ELSE
            Set Test Variable    ${care_team_id}    ${automated_tests_clinic}[f03n01_native_app_id_staging]
        END
    ELSE
        Set Test Variable    ${patient_email}    ${f03n01_patient}[email]
    END
    Set Test Variable    @{mailosaur_keys}    hjiveulb    sekTQ4ZSL1ZdgthT

F03N01 Test Teardown
    Open All Unread Messages And Close
    Delete All Announcement Emails

Delete All Announcement Emails
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

View Announcement As Patient
    Sleep    5
    Login As Patient    ${patient_email}
    Navigate To Clinic
    IF    'native' not in '${ENVIRONMENT}'
        New Message Indicator Is Correct    1
    END
    Select Latest Clinic Message

Open All Unread Messages And Close
    Set Application On Environment
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Read Unread Messages In Current Page
    Close All App Instances

Send Announcement To Patient
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse
        Wait Until Noona Loader Is Not Visible
        Input Announcement Details
        ...    Announcement ${today}
        ...    ${tomorrow}
        ...    ${automated_tests_clinic}[f03n01_care_team]
        Patient Count For Announcement Is Correct
        Post Announcement
        Care Team Selections Should Be Disabled
        Close Browser
    ELSE
        Generate Clinic Token
        ...    ${automated_tests_clinic}[default_manager]
        ...    ${DEFAULT_PASSWORD}
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
        Send Announcement To Patient Via API
        ...    careteam_name=${care_team_name}
        ...    careteam_id=${care_team_id}
        ...    title=Announcement ${today}
    END
