*** Settings ***
Documentation                           F13CU03-6 Clinic user can display reports - Symptom report
...                                     Precondition:
...                                     Clinic user is logged in.
...                                     Analytics dashboards and Reporting are enabled in the clinic settings.
...                                     Clinic user has access to In-App analytics, Reporting and Reporting data export.

Resource                                ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource                                ${EXECDIR}${/}resources${/}nurse${/}analytics${/}reporting_page.resource
Resource                                ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource

Suite Setup         Set Libraries Order
Test Setup          Run Keywords    Login As Nurse    email=${f13cu03-6_clinic}[user1_email]
...                     AND    Wait Until Noona Loader Is Not Visible
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags                              usecase-f13cu03-6    clinic-web    manual


*** Variables ***
&{symptom_filter_values}
...    date_range=default
...    treatment_module_type=Select all
...    care_team=Select all
...    age=default
...    gender=Select all
...    patient_group=Select all
...    symptom_type=Select all
...    patient_diagnosis=Select all
...    priority=Select all
...    care_team_member=Select all
...    reporter_role=Select all


*** Test Cases ***
Clinic user can display reports - Enrollment report
    [Tags]    nms9-ver-454
    Navigate To Analytics Page - Reporting
    Acknowledge Reporting Page Disclaimer If It Is Visible
    Clinic User Is Redirected To Reporting Screen
    Clinic User Can See A List Of Reports
    Clinic User Selects A Report They Want To View    Enrollment
    # Todo Tooltip text not getting extracted: Clinic user can see a tooltip explaining the purpose of the report    Enrollment
    # Clinic user can filter the data based on the selected report type
    Date Range Filter Is Set To 180 Days By Default
    Gender Filter By Default No Option Is Selected
    Care Team Filter By Default No Option Is Selected
    Patient Activation Status Filter By Default No Option Is Selected
    Current Status Filter By Default No Option Is Selected
    Age Filter By Default 0-125 Is Selected
    Free Text Field For Searching With Patient Name And Patient MRN Number. By Default, No Filter Is Selected
    # todo Clinic User Sets The Filters For The Enrollmemt Report And Presses Apply
    # todo The Report Shows The Following Information
    # todo User Can Sort With All Of The Result Columns
    # todo Clinic User Can Sort Up To 5 Columns Using The Multi Sort
    # todo Clinic User Can Scroll To The End Of The Report With Infinite Scrolling
    # todo take help for Harri Clinic User Can See When The Data Was Last Refreshed
    # todo Clinic User Can Download The Report As A CSV File
    Clinic User Can Press Reset And All Filtering Fields Are Re-set To The Default Values
    Clinic User Can Navigate To The List Of Reports And Choose A New Report From There    Symptom

Clinic user can display reports - Symptom report
    [Documentation]    Test verifies reporting filter's functionalities on Symptom reporting page (test step 1 to 16)
    [Tags]    nms9-ver-444    nms9-ver-444-1
    Navigate To Analytics Page - Reporting
    Acknowledge Reporting Page Disclaimer If It Is Visible
    Clinic User Is Redirected To Reporting Screen
    Clinic User Can See A List Of Reports
    Clinic User Selects A Report They Want To View    Symptom
    Date Range Filter Is Set To 180 Days By Default
##    Treatment Module Type. Possibility to select all, Select individual Treatment Module Types and to use search for finding the Treatment Module Type.
    Treatment Module Type Filter By Default No Option Is Selected
    Select A Reporting Filter Option    Treatment Module Type    Abdominal Radiotherapy Module
    Unselect A Reporting Filter Option    Abdominal Radiotherapy Module
    Search For A Treatment Module
##    Care Team. Possibility to select all, Select individual Care Teams and to use search for finding the Care Teams.
    Care Team Filter By Default No Option Is Selected
    Select A Reporting Filter Option    Care Team    f13cu03-6_careteam1
    Unselect A Reporting Filter Option    f13cu03-6_careteam1
    Select A Reporting Filter Option    Care Team    Select all
    Unselect A Reporting Filter Option    Select all
    Search For A Care Team
##    Age. By default 0-125 is selected.
    Age Filter By Default 0-125 Is Selected
##    Gender. By default no filter is selected. Possibility to select all or select an individual Gender.
    Gender Filter By Default No Option Is Selected
    Select A Reporting Filter Option    Gender    Female
    Unselect A Reporting Filter Option    Female
    Select A Reporting Filter Option    Gender    Male
    Unselect A Reporting Filter Option    Male
    Select A Reporting Filter Option    Gender    Select all
    Unselect A Reporting Filter Option    Select all
##    Patient Group. By default Select all is selected.
    # Patient Group Filter By Default No Option Is Selected >> waiting for confirmation which option is default (conflict in use case and implementation)
    Select A Reporting Filter Option    Patient Group    orange_group
    Unselect A Reporting Filter Option    orange_group
    Select A Reporting Filter Option    Patient Group    Select all
    Unselect A Reporting Filter Option    Select all
    Search For A Patient Group
##    Symptom Type. Possibility to select all, Select individual Symptom Type and to use search for finding the Symptom Type
    Symptom Type Filter By Default No Option Is Selected
    Select A Reporting Filter Option    Symptom type    Fever
    Unselect A Reporting Filter Option    Fever
    Select A Reporting Filter Option    Symptom type    Select all
    Unselect A Reporting Filter Option    Select all
    Search For A Symptom Type
##    Patient Diagnosis. Possibility to select all, Select individual Patient Diagnosis and to use search for finding the Patient Diagnosis.
    Patient Diagnosis Filter By Default No Option Is Selected
    Select A Reporting Filter Option    Patient Diagnosis    C18 Malignant neoplasm of colon
    Unselect A Reporting Filter Option    C18 Malignant neoplasm of colon
    Select A Reporting Filter Option    Patient Diagnosis    Select all
    Unselect A Reporting Filter Option    Select all
    Search For A Patient Diagnosis
##    Priority. Possibility to select all, Select individual Pritority and to use search for finding the Pritority.
    Priority Filter By Default No Option Is Selected
    Select A Reporting Filter Option    Priority    Critical
    Unselect A Reporting Filter Option    Critical
    Select A Reporting Filter Option    Priority    Select all
    Unselect A Reporting Filter Option    Select all
    Search For A Priority
##    Care Team Member. By default, Possibility to select all, Select individual Care Team Members and to use search for finding the Care Team Members (currently the clinic has one team member)
    Care Team Member Filter By Default No Option Is Selected
    Select A Reporting Filter Option    Care team member    f13cu03-6 user1
    Unselect A Reporting Filter Option    f13cu03-6 user1
    Select A Reporting Filter Option    Care team member    Select all
    Unselect A Reporting Filter Option    Select all
    Search For A Care Team Member
##    Reporter Role. Possibility to select all, Select individual Reporter role. By default no filter is selected.
    Reporter Role Filter By Default No Option Is Selected
    Select A Reporting Filter Option    Reporter role    Care giver
    Unselect A Reporting Filter Option    Care giver
    Select A Reporting Filter Option    Reporter role    Select all
    Unselect A Reporting Filter Option    Select all
    Clinic User Can See A Tooltip Explaining The Purpose Of The Report    Symptom

Clinic user can display reports - Symptom report - Filter results table
    [Documentation]    Test verifies reporting filter result table on Symptom reporting page (test step 17 to 35)
    [Tags]    nms9-ver-444    nms9-ver-444-2
    Navigate To Analytics Page - Reporting
    Acknowledge Reporting Page Disclaimer If It Is Visible
    Clinic User Is Redirected To Reporting Screen
    Clinic User Selects A Report They Want To View    Symptom
    Clinic User Sets The Filter For Symptom Report And Presses Apply
    Clinic User Can See Selected Report Filter Results
    The Symptom Report Shows The Following Information: 17 result columns
    The Report Shows: Patient Name
    The Report Shows: Patient MRN Number
    The Report Shows: Patient Creation Date
    The Report Shows: Patient Diagnosis
    The Report Shows: Patient Treatment Module(s)
    The Report Shows: Patient Care Team(s)
    The Report Shows: Patient Group(s)
    The Report Shows: Patient Zip Code
    The Report Shows: Patient Age
    The Report Shows: Patient Gender
    The Report Shows: Symptom reported
    The Report Shows: Symptom Date
    The Report Shows: Response date
    The Report Shows: Priority
    The Report Shows: Care team member
    The Report Shows: Reporter role
    The Report Shows: Reporter name

##    These following steps will be cover in nms9-ver-444-2
#    User can sort with all of the result columns
#    Clinic user can sort up to 5 columns using the Multi sort
#    Clinic user can scroll to the end of the report with infinite scrolling
#    Clinic User Can See When The Data Was Last Refreshed
#    Clinic User Can Download The Report As A CSV File
#    Clinic user can press Reset and all filtering fields are re-set to the default values
#    Clinic user can navigate to the list of reports and choose a new report from there


*** Keywords ***
Verify If Disclaimer Box Is Displayed And After Acknowledging Disclaimer It Is Closed
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${disclaimer_dialog}
    IF    ${status} == ${TRUE}
        Try To Click Element    ${acknowledge_button}
        Wait Until Element Is Not Visible    ${disclaimer_dialog}
    END

From The Dropdown Menu, Clinic User Clicks On Reporting
    Verify If Disclaimer Box Is Displayed And After Acknowledging Disclaimer It Is Closed
    # Todo: all users must acknowledge the disclaimer 1st time and every 90 days there after
    Try To Click Element    ${reporting_button}

Clinic User Can See A Tooltip Explaining The Purpose Of The Report
    [Arguments]    ${reporting_name}
    IF    '${reporting_name}' == 'Symptom'
        Mouse Over    //*[@id='information_s']
        Page Should Contain    ${symptom_tooltip_txt}
        Click Element    //*[@id='information_s']
        # TODO: try to get the tooltip text and verify that they are correct. We currently use capture page screenshot to see if the tooltip is visible on mouse over.
    ELSE IF    '${reporting_name}' == 'Enrollment'
        Mouse Over    //*[@id='information_s']
        Page Should Contain    ${enrollment_tooltip_txt}
        Click Element    //*[@id='information_s']
    END

# Default option of each reporting filter

Date Range Filter Is Set To 180 Days By Default
    ${todays_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y
    ${date_before_180days}    Subtract Time From Date
    ...    ${todays_date}
    ...    180 days
    ...    result_format=%d.%m.%Y
    ...    exclude_millis=yes
    ...    date_format=%d.%m.%Y
    ${date_from_date_filter}    Get Text    ${date_filter}
    ${first_date_from_date_filter}    Get Substring    ${date_from_date_filter}    0    10
    Should Be Equal    ${first_date_from_date_filter}    ${date_before_180days}

Gender Filter By Default No Option Is Selected
    ${selected_gender_value}    Get Text    ${gender_filter}
    Should Be Equal    ${selected_gender_value}    ${default_value_of_gender_filter}

Treatment Module Type Filter By Default No Option Is Selected
    ${treament_module_type_value}    Get Text    ${treament_module_type_filter}
    Should Be Equal    ${treament_module_type_value}    ${default_value_of_treament_module_type_filter}

Patient Group Filter By Default No Option Is Selected
    ${patient_group_filter_value}    Get Text    ${patient_group_filter}
    Should Be Equal    ${patient_group_filter_value}    ${default_value_of_patient_group_filter}

Symptom Type Filter By Default No Option Is Selected
    ${symptom_type_value}    Get Text    ${symptom_type_filter}
    Should Be Equal    ${symptom_type_value}    ${default_value_of_symptom_type_filter}

Care Team Filter By Default No Option Is Selected
    ${selected_care_team_value}    Get Text    ${care_team_filter}
    Should Be Equal    ${selected_care_team_value}    ${default_value_of_care_team_filter}

Patient Diagnosis Filter By Default No Option Is Selected
    ${patient_diagnosis_value}    Get Text    ${patient_diagnosis_filter}
    Should Be Equal    ${patient_diagnosis_value}    ${default_value_of_patient_diagnosis_filter}

Priority Filter By Default No Option Is Selected
    ${priority_filter_value}    Get Text    ${priority_filter}
    Should Be Equal    ${priority_filter_value}    ${default_value_of_priority_filter}

Care Team Member Filter By Default No Option Is Selected
    ${care_team_member_filter_value}    Get Text    ${care_team_member_filter}
    Should Be Equal    ${care_team_member_filter_value}    ${default_value_of_care_team_member_filter}

Reporter Role Filter By Default No Option Is Selected
    ${reporter_role_filter_value}    Get Text    ${reporter_role_filter}
    Should Be Equal    ${reporter_role_filter_value}    ${default_value_of_reporter_role_filter}

Patient Activation Status Filter By Default No Option Is Selected
    ${selected_patient_activation_status_value}    Get Text    ${patient_activation_status_filter}
    Should Be Equal
    ...    ${selected_patient_activation_status_value}
    ...    ${default_value_of_patient_activation_status_filter}

Current Status Filter By Default No Option Is Selected
    ${selected_current_status_value}    Get Text    ${current_status_filter}
    Should Be Equal    ${selected_current_status_value}    ${default_value_of_current_status_filter}

Age Filter By Default 0-125 Is Selected
    Try To Click Element    ${age_filter}
    Wait Until Element Is Visible    ${age_range_title}
    Wait Until Element Is Visible    ${age_range_min}
    Wait Until Element Is Visible    ${age_range_max}
    Wait Until Page Contains Element    ${age_range_min_default_value}
    Wait Until Page Contains Element    ${age_range_max_default_value}
    Try To Click Element    ${age_filter}

# Search funtionality in each reporting filter

Search For A Treatment Module
    Get All Options Under A Reporting Filter    Treatment Module Type
    ${random_treatment_module_type}    Evaluate    random.choice(${list_of_options})    random
    Search For A Option From A Reporting Filter    Treatment Module Type    ${random_treatment_module_type}

Search For A Care Team
    Get All Options Under A Reporting Filter    Care Team
    ${random_care_team}    Evaluate    random.choice(${list_of_options})    random
    Search For A Option From A Reporting Filter    Care Team    ${random_care_team}

Search For A Patient Group
    Get All Options Under A Reporting Filter    Patient Group
    ${random_patient_group}    Evaluate    random.choice(${list_of_options})    random
    Search For A Option From A Reporting Filter    Patient Group    ${random_patient_group}

Search For A Symptom Type
    Get All Options Under A Reporting Filter    Symptom type
    ${random_symptom_type}    Evaluate    random.choice(${list_of_options})    random
    Search For A Option From A Reporting Filter    Symptom type    ${random_symptom_type}

Search For A Patient Diagnosis
    Get All Options Under A Reporting Filter    Patient Diagnosis
    ${random_patient_diagnosis}    Evaluate    random.choice(${list_of_options})    random
    Search For A Option From A Reporting Filter    Patient Diagnosis    ${random_patient_diagnosis}

Search For A Priority
    Get All Options Under A Reporting Filter    Priority
    ${random_priority}    Evaluate    random.choice(${list_of_options})    random
    Search For A Option From A Reporting Filter    Priority    ${random_priority}

Search For A Care Team Member
    Get All Options Under A Reporting Filter    Care team member
    ${random_care_team_member}    Evaluate    random.choice(${list_of_options})    random
    Search For A Option From A Reporting Filter    Care team member    ${random_care_team_member}

Free Text Field For Searching With Patient Name And Patient MRN Number. By Default, No Filter Is Selected
    [Documentation]    searching with MRN and Name is covered in next step, here keyword name is kept to match with test case wordings
    Wait Until Element Is Visible    ${patient_name_search_box}
    Try To Click Element    ${patient_name_search_box}
    Wait Until Element Is Visible    ${patient_name_search_list_box}

Clinic User Can Press Reset And All Filtering Fields Are Re-set To The Default Values
    Try To Click Element    ${reset_button}
    Wait Until Noona Loader Is Not Visible
    # Todo If there are any results from a previous search they will persist on the screen until a new search is initiated by pressing Apply.
    Date Range Filter Is Set To 180 Days By Default
    Gender Filter By Default No Option Is Selected
    Care Team Filter By Default No Option Is Selected
    Patient Activation Status Filter By Default No Option Is Selected
    Current Status Filter By Default No Option Is Selected
    Age Filter By Default 0-125 Is Selected

Clinic User Can Navigate To The List Of Reports And Choose A New Report From There
    [Arguments]    ${reporting_name}
    Try To Click Element    ${reporting_burger_menu}
    Clinic User Selects A Report They Want To View    ${reporting_name}

# Clinic User Sets The Filters For The Enrollmemt Report And Presses Apply
    # Arguments]    @{filter_values}

Clinic User Sets The Filter For Symptom Report And Presses Apply
    # TODO: create a keyword to select different date range. For the time being, the filer always takes default value (180 days) in date range filter
    IF    '${symptom_filter_values}[date_range]' == 'default'
        Date Range Filter Is Set To 180 Days By Default
    END
    Select A Reporting Filter Option    Treatment Module Type    ${symptom_filter_values}[treatment_module_type]
    Select A Reporting Filter Option    Care Team    ${symptom_filter_values}[care_team]
    IF    '${symptom_filter_values}[age]' == 'default'
        Age Filter By Default 0-125 Is Selected
    END
    Select A Reporting Filter Option    Gender    ${symptom_filter_values}[gender]
    Select A Reporting Filter Option    Patient Group    ${symptom_filter_values}[patient_group]
    Select A Reporting Filter Option    Symptom type    ${symptom_filter_values}[symptom_type]
    Select A Reporting Filter Option    Patient Diagnosis    ${symptom_filter_values}[patient_diagnosis]
    Select A Reporting Filter Option    Priority    ${symptom_filter_values}[priority]
    Select A Reporting Filter Option    Care team member    ${symptom_filter_values}[care_team_member]
    Select A Reporting Filter Option    Reporter role    ${symptom_filter_values}[reporter_role]
    Try To Click Element    ${apply_button}
    Wait Until Noona Loader Is Not Visible

Clinic User Can See Selected Report Filter Results
    Wait Until Element Is Visible    ${noona_reporting_result_container}
    Wait Until Element Is Visible    ${noona_reporting_result_container_header}
    ${number_of_records_found}    Get Text    ${noona_reporting_result_container_header}/*/span[1]
    Log    Number of records found is ${number_of_records_found}
    ${number_of_records_found_int}    Convert To Integer    ${number_of_records_found}
    IF    ${number_of_records_found_int} <= 1
        #Element Text Should Be    ${noona_reporting_result_container_header}/*/span[2]    records found
        Page Should Contain    ${number_of_records_found} record found
    ELSE
        Page Should Contain    ${number_of_records_found} records found
    END

The ${report} Report Shows The Following Information: ${expected_number_of_columns} result columns
    ${number_of_results_columns}    Get All Symptom Reporting Result Columns Labels
    Should Be Equal    ${number_of_results_columns}    ${expected_number_of_columns}

The Report Shows: ${expected_result_column_name}
     ${expected_result_columnn}    Format String    ${noona_reporting_table_column_label_text_template}    ${expected_result_column_name}
     Page Should Contain Element    ${expected_result_columnn}

# The report shows the following information
#    Number of results
#    The date and time when the report was last generated (uses clinic time zone and date formatting)
#
# Information on fields based on the chosen filters and report
#    #Symptom report
#    Patient Name
#    Patient MRN Number
#    Patient Creation Date
#    Patient Diagnosis
#    Patient Treatment Module(s)
#    Patient Care Team(s)
#    Patient Group(s)
#    Patient Zip Code
#    Patient Age
#    Patient Gender
#    Symptom reported
#    Symptom Date
#    Response Date
#    Priority
#    Care team member
#
# User can sort with all of the result columns
#
# Clinic user can sort up to 5 columns using the Multi sort
#
# Clinic user can scroll to the end of the report with infinite scrolling
#
# Clinic user can see when the data was last refreshed
#    Todo How to verify exat time of refresh, take developers help?
#    Data is read from a data-mart
#    Data mart updates data from Noona every 12 hours
#    Last data update becomes visible at the top of the report
# Download button becomes visible only after there are results to be shown

# Download button becomes inactive while the CSV is fetched

# Clinic User Can Download The Report As A CSV File
    # Download button becomes visible only after there are results to be shown
#    Download button becomes inactive while the CSV is fetched
#
# Clinic user can press Reset and all filtering fields are re-set to the default values
#    If there are any results from a previous search they will persist on the screen until a new search is initiated by pressing Apply.
#

# Information on fields based on the chosen filters and report
#    #Enrollment report
#    Patient Name
#    Patient MRN Number
#    Patient Treatment Module
#    Patient Care Team(s)
#    Patient Zip Code
#    Activation Status
#    Current Status
#    Patient Group(s)
#    Status Date
#    Patient Diagnosis
#    Patient Age
#    Patient Gender
#    Patient Creation Date
#    Invitation Sent by User
