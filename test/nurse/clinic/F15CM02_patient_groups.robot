*** Settings ***
Documentation       F15CM02 Clinic manager can manage patient groups

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_group.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse    email=${automated_tests_clinic}[default_manager]
Test Teardown       Close browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15cm02    clinic-web


*** Variables ***
${multiple_clinics_group}       Multiple clinics


*** Test Cases ***
Main success scenario - Add And Remove Patient Group
    [Tags]    nms9-ver-329
    Navigate To Patient Groups
    Remove Category
    Patient Group Save Changes
    Multiple Clinics Is Not Displayed In Patient Group
    Add New Category
    Set Category Color
    Add Group To Category
    Add New Group
    Patient Group Save Changes
    Changes Are Saved
    Multiple Clinics Is Not Displayed In Patient Group

Extension A - Editing groups
    [Documentation]    Affects on patient which is tested in F15CU06
    [Tags]    nms9-ver-330
    Navigate To Patient Groups
    Remove Category
    Patient Group Save Changes
    Save Category And Groups
    Edit Group
    Patient Group Save Changes
    Changes Are Saved


*** Keywords ***
Save Category And Groups
    Add New Category
    Set Category Color
    Add Group To Category
    Add New Group
    Patient Group Save Changes
    Changes Are Saved

Add Group To Avoid Invalid Save
    Add New Group
    Patient Group Save Changes

Multiple Clinics Is Not Displayed In Patient Group
    Wait Until Element Is Visible    ${pg_save_button}
    Page Should Not Contain    ${multiple_clinics_group}
