*** Settings ***
Documentation       F03NA01 Noona administrator can edit clinic settings -> Integration

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}integration_settings.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinic_settings.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}medication_list.resource

Suite Setup         Run Keywords    Set Libraries Order    AND    Setup App Environment
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na01    clinic-web    noona_admin


*** Test Cases ***
Main success scenario - Integration - FHIR Integration Settings
    [Tags]    usecase-f03na01_1    nms9-ver-566    nms9-ver-566-1
    Login As Nurse    clinic=${test_clinic_setting_7}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of FHIR Integration Setting
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Disable FHIR Integration Settings
        Save Settings
        Enable FHIR Integration And Save The Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable FHIR Integration And Save The Settings
        ${current_status}    Run Keyword    Check Status Of FHIR Integration Setting    # check if update is successful
        Should Be Equal    ${current_status}    enabled
    END
    [Teardown]    Close Browser

Main Success Scenario - Integration - Clinic Integration Setting And Sub-Settings
    [Tags]    usecase-f03na01_1    nms9-ver-566    nms9-ver-566-2
    Login As Nurse    clinic=${test_clinic_setting_7}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Integration For Clinic Setting
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Select Clinic Settings Radio Button    ${patient_status_change_enabled_text}    Yes
        Input Required Fields Value For Patient Status Change Integration Settings
        Select Clinic Settings Radio Button    ${aria_site_id_mandatory_id_text}    Yes
        Select Clinic Settings Radio Button    ${mapping_ariaid1_text}    Medical record number
        Select Clinic Settings Radio Button    ${mrn_ssn_ins_integration_text}    Yes
        Select Clinic Settings Radio Button    ${aria_care_team_initialisation_text}    Yes
        Select Clinic Settings Radio Button    ${patientmessage_questionnaire_casesummery_text}    Yes
        Save Settings
        Select Clinic Settings Radio Button    ${patient_status_change_enabled_text}    No
        Select Clinic Settings Radio Button    ${aria_site_id_mandatory_id_text}    No
        Select Clinic Settings Radio Button    ${mapping_ariaid1_text}    SSN / Identity Code / INS number
        Select Clinic Settings Radio Button    ${mrn_ssn_ins_integration_text}    No
        Select Clinic Settings Radio Button    ${aria_care_team_initialisation_text}    No
        Select Clinic Settings Radio Button    ${patientmessage_questionnaire_casesummery_text}    No
        Save Settings
        Disable Integration For Clinic Settings
        Save Settings
        Enable Integration For Clinic And Save The Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable Integration For Clinic And Save The Settings
        Select Clinic Settings Radio Button    ${patient_status_change_enabled_text}    Yes
        Input Required Fields Value For Patient Status Change Integration Settings
        Select Clinic Settings Radio Button    ${aria_site_id_mandatory_id_text}    Yes
        Select Clinic Settings Radio Button    ${mapping_ariaid1_text}    Medical record number
        Select Clinic Settings Radio Button    ${mrn_ssn_ins_integration_text}    Yes
        Select Clinic Settings Radio Button    ${aria_care_team_initialisation_text}    Yes
        Select Clinic Settings Radio Button    ${patientmessage_questionnaire_casesummery_text}    Yes
        Save Settings
        Select Clinic Settings Radio Button    ${patient_status_change_enabled_text}    No
        Select Clinic Settings Radio Button    ${aria_site_id_mandatory_id_text}    No
        Select Clinic Settings Radio Button    ${mapping_ariaid1_text}    SSN / Identity Code / INS number
        Select Clinic Settings Radio Button    ${mrn_ssn_ins_integration_text}    No
        Select Clinic Settings Radio Button    ${aria_care_team_initialisation_text}    No
        Select Clinic Settings Radio Button    ${patientmessage_questionnaire_casesummery_text}    No
        Save Settings
        Disable Integration For Clinic Settings
        Save Settings
    END
    [Teardown]    Close Browser

Main Success Scenario - Integration - Treatment Units (Name, ID, Time Zone)
    [Tags]    usecase-f03na01_1    nms9-ver-566    nms9-ver-566-3
    Login As Nurse    clinic=${test_clinic_setting_7}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    Scroll Element Into View    ${tunit_header}
    Wait Until Element Is Visible    ${tunit_header}
    ${status}    Run Keyword And Return Status    Element Should Be Visible    ${remove_tunit_btn}
    IF    ${status} == True
        Try To CLick Element    ${remove_tunit_btn}
        Save Settings
        Wait Until Element Is Not Visible    ${remove_tunit_btn}
    END
    Try To CLick Element    ${add_tunit_btn}
    Wait Until Element Is Visible    ${added_tunit_list}
    ${name}    Get Value    ${tunit_name_field}
    ${id}    Get Value    ${tunit_id_field}
    ${time_zone}    Get Value    ${tunit_time_zone_field}
    Should Be Empty    ${name}
    Should Be Empty    ${id}
    Should Be Empty    ${time_zone}
    Input New Values To Treatment Unit Fields
    Save Settings
    Scroll Element Into View    ${edit_tunit}    # remove this line after debugging
    Capture Screenshot    # remove this line after debugging
    ${name}    Get Value    ${tunit_name_field}
    ${id}    Get Value    ${tunit_id_field}
    ${time_zone}    Get Value    ${tunit_time_zone_field}
    Should Be Equal    ${name}    ${tunit_name_value}
    Should Be Equal    ${id}    ${tunit_id_value}
    Should Be Equal    ${time_zone}    ${tunit_time_zone_value}
    Reload Page    # todo Remove this line after fixing NOONA-25747
    Wait Until Noona Loader Is Not Visible    # todo Remove this line after fixing NOONA-25747
    Sleep    2s    # todo Remove this line after fixing NOONA-25747
    Treatment Unit Fields Should Be Disbaled To Edit
    Try To CLick Element    ${edit_tunit}
    Treatment Unit Fields Should Be Enabled To Edit
    Input New1 Values To Treatment Unit Fields
    Save Settings
    ${name}    Get Value    ${tunit_name_field}
    ${id}    Get Value    ${tunit_id_field}
    ${time_zone}    Get Value    ${tunit_time_zone_field}
    Should Be Equal    ${name}    ${tunit_name_value1}
    Should Be Equal    ${id}    ${tunit_id_value1}
    Should Be Equal    ${time_zone}    ${tunit_time_zone_value1}
    Treatment Unit Fields Should Be Disbaled To Edit
    Page Should Contain Link    ${time_zone_doc_link}
    Wait Until Element Is Visible    ${time_zone_doc_link_element}
    Scroll Element Into View    ${time_zone_doc_link_element}
    Click Link    ${time_zone_doc_link_element}
    Switch Window    NEW
    Location Should Be    ${time_zone_doc_link_https}
    [Teardown]    Close Browser

Main Success Scenario - Integration - Is Data Lake Integration Enabled
    [Tags]    usecase-f03na01_1    nms9-ver-566    nms9-ver-566-4
    Login As Nurse    clinic=${test_clinic_setting_7}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Data Lake Integration
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Input Data Lake Account Name
        Input Data Lake Container Name
        Input Data Lake Encryption Key URL
        Input Data Lake Public Key
        Input Data Lake Shared Access Signature Token
        Save Settings
        Disable Data Lake Integration And Save Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable Data Lake Integration And Save Settings
        Input Data Lake Account Name
        Input Data Lake Container Name
        Input Data Lake Encryption Key URL
        Input Data Lake Public Key
        Input Data Lake Shared Access Signature Token
        Save Settings
        Remove Values From Data Lake Setting Fields
        Disable Data Lake Integration And Save Settings
    END
    [Teardown]    Close Browser

Main Success Scenario - Integration - Medical Records
    [Tags]    usecase-f03na01_1    nms9-ver-566    nms9-ver-566-5
    Login As Nurse    clinic=${test_clinic_setting_7}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Medical Records Be Visible For The Patient
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Steps For Integration - Medical Records Verification
    ELSE IF    '${current_status}' == 'disabled'
        Enable Medical Records Be Visible For The Patient
        Steps For Integration - Medical Records Verification
    END
    [Teardown]    Close Browser

Main Success Scenario - Integration - Laboratory Results
    [Tags]    usecase-f03na01_1    nms9-ver-566    nms9-ver-566-6
    Login As Nurse    clinic=${test_clinic_setting_7}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Laboratory Results Be Visible For The Patient
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Disable Laboratory Results Settings
        Save Settings
        Enable Laboratory Results Settings
        Save Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable Laboratory Results Settings
        Save Settings
        Disable Laboratory Results Settings
        Save Settings
    END
    [Teardown]    Close Browser

Main Success Scenario - Integration - Clinical Reports
    [Tags]    usecase-f03na01_1    nms9-ver-566    nms9-ver-566-7
    Login As Nurse    clinic=${test_clinic_setting_7}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Clinical Reports Be Visible For The Patient
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Disable Clinical Reports Settings
        Save Settings
        Enable Clinical Reports Settings
        Save Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable Clinical Reports Settings
        Save Settings
        Disable Clinical Reports Settings
        Save Settings
    END
    [Teardown]    Close Browser

Extension F - Clinic setting to manage Medication feature
    [Tags]    nms9-ver-404
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    ${current_status}    Run Keyword    Check Status Of Medication List Settings
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Disable Medication List Settings
        Save Settings
        Enable Medication List And Save The Settings
    ELSE IF    '${current_status}' == 'disabled'
        Enable Medication List And Save The Settings
        ${current_status}    Run Keyword    Check Status Of Medication List Settings    # check if update is successful
        Should Be Equal    ${current_status}    enabled
    END
    Login As Patient    ${f03na01_patient2}[email]
    Go To Library
    Check That Medication List Is Displayed For Patient
    Logout As Patient
    Close Browser
    [Teardown]    Extension F - Test teardown

Extension I - Enable Common FHIR Integration
    [Tags]    nms9-ver-480
    Extension I - Test Setup
    Select Clinic Settings Radio Button    ${fhir_integration_settings_enabled_text}    Yes
    Wait Until Page Contains Element    ${customer_tenant_id}
    Input Text    ${customer_tenant_id}    test${now}
    Enable Data Integrations    DiagnosticReports
    Enable Data Integrations    DocumentReferences
    Enable Data Integrations    MedicationStatements
    Enable Data Integrations    Quality of life questionnaires
    Enable Data Integrations    Symptoms
    Enable Data Integrations    Translations
    Save Settings
    Reload Page
    Verify If The Common FHIR Integration Are Saved
    Select Clinic Settings Radio Button    ${fhir_integration_settings_enabled_text}    No
    Save Settings
    Wait Until Page Contains    ${fhir_integration_settings_enabled_text}
    Page Should Not Contain    ${customer_tenant_id}
    Close Browser


*** Keywords ***
Enable Medication List And Save The Settings
    Enable Medication List Settings
    Save Settings

Extension F - Test teardown
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    Disable Medication List Settings
    Save Settings
    ${current_status}    Run Keyword    Check Status Of Medication List Settings    # check if update is successful
    Should Be Equal    ${current_status}    disabled
    Close All Browsers

Extension I - Test Setup
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    Select Clinic Settings Radio Button    ${fhir_integration_settings_enabled_text}    Yes
    Wait Until Element Is Visible    ${customer_tenant_id}
    Clear Element Text    ${customer_tenant_id}
    Disable Data Integrations    DiagnosticReports
    Disable Data Integrations    DocumentReferences
    Disable Data Integrations    MedicationStatements
    Disable Data Integrations    Quality of life questionnaires
    Disable Data Integrations    Symptoms
    Disable Data Integrations    Translations
    Select Clinic Settings Radio Button    ${fhir_integration_settings_enabled_text}    No
    Save Settings
    ${now}    Get Current Date    result_format=%d%Y%m
    Set Test Variable    ${now}

Verify If The Common FHIR Integration Are Saved
    Wait Until Element Is Visible    ${customer_tenant_id}
    ${tenant_id}    Get Value    ${customer_tenant_id}
    Should Be Equal    ${tenant_id}    test${now}
    Verify Data Integration Setting Status    DiagnosticReports    enabled
    Verify Data Integration Setting Status    DocumentReferences    enabled
    Verify Data Integration Setting Status    MedicationStatements    enabled
    Verify Data Integration Setting Status    Quality of life questionnaires    enabled
    Verify Data Integration Setting Status    Symptoms    enabled
    Verify Data Integration Setting Status    Translations    enabled

Attribute Should Exist For Element
    [Arguments]    ${element_locator}    ${attribute}
    ${value}    Get Element Attribute    ${element_locator}    ${attribute}
    Should Not Be Equal    ${value}    ${None}

Attribute Should Not Exist For Element
    [Arguments]    ${element_locator}    ${attribute}
    ${value}    Get Element Attribute    ${element_locator}    ${attribute}
    Should Be Equal    ${value}    ${None}

Treatment Unit Fields Should Be Disbaled To Edit
    Attribute Should Exist For Element    ${tunit_name_field}    ${disabled}
    Attribute Should Exist For Element    ${tunit_id_field}    ${disabled}
    Attribute Should Exist For Element    ${tunit_time_zone_field}    ${disabled}

Treatment Unit Fields Should Be Enabled To Edit
    Attribute Should Not Exist For Element    ${tunit_name_field}    ${disabled}
    Attribute Should Not Exist For Element    ${tunit_id_field}    ${disabled}
    Attribute Should Not Exist For Element    ${tunit_time_zone_field}    ${disabled}

Input New Values To Treatment Unit Fields
    Input Text    ${tunit_name_field}    ${tunit_name_value}
    Input Text    ${tunit_id_field}    ${tunit_id_value}
    Input Text    ${tunit_time_zone_field}    ${tunit_time_zone_value}

Input New1 Values To Treatment Unit Fields
    Input Text    ${tunit_name_field}    ${tunit_name_value1}
    Input Text    ${tunit_id_field}    ${tunit_id_value1}
    Input Text    ${tunit_time_zone_field}    ${tunit_time_zone_value1}

Steps For Integration - Medical Records Verification
    Select Method To Use In Fetching CCD    S3
    ${current_status}    Run Keyword    Check Status Of Can Launch DHIT Portal
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'disabled'
        Enable Patient Can Launch DHIT Portal From Noona (Cures Act 21st Century)
        Input DHIT URL
        Save Settings
    ELSE IF    '${current_status}' == 'enabled'
        Disable Patient Can Launch DHIT Portal From Noona (Cures Act 21st Century)
        Save Settings
        Verify That DHIT URL Field Is Not Visible
    END
    Select Method To Use In Fetching CCD    ARIA
    Verify That Is Clinic An MO Or RO Environment Option Is Visible
    Select Clinic Environment As Radiation Oncology
    Select Authentication Type    No Authentication
    Verify That API Key Field Is Visible
    Input API Key
    Save Settings
    ${current_status}    Run Keyword    Check Selected Value Of Clinic Environment Radiation Oncology
    Set Test Variable    ${current_status}
    IF    '${current_status}' == 'enabled'
        Select Clinic Environment As Medical Oncology
        Capture Screenshot
        Save Settings
    ELSE IF    '${current_status}' == 'disabled'
        Log    Already enabled
        Capture Screenshot
    END
    Select Authentication Type    Client Credentials
    Verify That Endpoint Url Field Is Visible
    Input Endpoint Url
    Save Settings
    Select Authentication Type    Basic Auth
    Verify That API Key Field Is Visible
    Verify That User Field Is Visible
    Verify That Endpoint Url Field Is Visible
    Input API Key
    Input Endpoint Url
    Input User
    Verify That Change Password Link Is Visible
    Click Change Password Link
    Password Field Should Be Visible
    Input Password Into Password Field
    Verify That Cancel Password Change Is Visible
    Save Settings
    #Uncomment following 3 steps after this defect is fixed:
    #NOONA-26079 After Cancelling password, Save is not Enabled blocking from saving changes
#    Try To Click On Cancel Password Change
#    Save Settings
#    Password Field Should Not Be Visible
#    Verify That Change Password Link Is Visible
    Disable Medical Records Be Visible For The Patient
    Save Settings
    Verify That Medical Records Be Visible For The Patient Is Disabled
