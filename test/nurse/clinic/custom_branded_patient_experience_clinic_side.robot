*** Settings ***
Documentation       F03NA06 Noona administrator can define a Custom branded patient experience
...
...                 Preconditions:
...                 - Access Clinic as Noona Admin
...                 - Navigate to Clinic Settings (under clinic link in main navigation)
...                 - Scroll to “Clinic experience for patients”
...                 - Enable “Custom branding”
...
...
...                 - Scenario 1: Define Clinic Shortname and Custom Url
...                 - Scenario 2: Define Custom Color Only
...                 - Sceanrio 3: Define Custom Logo Only
...                 - Scenario 4: Define Custom Service Name Only
...                 - Scenario 5: Define Custom Color, Logo, & Service Name
...                 - Scenario 6: Toggle Off Custom Branding
...

Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}co-branding_patient.resource
Resource            ${EXECDIR}${/}resources${/}common.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na06    noona_admin    clinic-web


*** Variables ***
${custom_branding_main_darker}                  88, 124, 88    # hex code #587c58
${custom_branding_main_color}                   103, 146, 103    # hex code #679267
${custom_branding_main_color_hex}               679267
${default_main_color_darker_landing_page}       117, 79, 155    # hex code #754f9b
${default_primary_color}                        136, 93, 179    # hex code #885db3
${default_primary_color_lighter_login_page}     173, 134, 205    # hex code #ad86cd
${default_primary_color_darker_login_page}      100, 50, 153    # hex code #643299
${clinic_short_name}                            CBATC
${too_dark_color_hex}                           270b1a
${too_light_color_hex}                          e8a9ca
${svg_logo}                                     valid-logo.svg


*** Test Cases ***
Noona administrator can define a Custom branded patient experience
    [Tags]    nms9-ver-334
    Login As Admin And Toggle Custom Branding    enable
    Check Login Page Custom Branding    enable
    Switch Browser    ${admin_browser_id}
    Update Clinic Custom Branding Settings    disable
    Save Basic Settings
    Check Login Page Default Branding    disable


*** Keywords ***
Check Login Page Custom Branding
    [Documentation]    darken_color is the color 1 level darker than the primary; used in landing page only
    [Arguments]    ${action}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${PATIENT_LOGIN_URL}?clinic=${clinic_short_name}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE
        Open Browser    ${PATIENT_LOGIN_URL}?clinic=${clinic_short_name}    ${BROWSER}
    END
    Accept All Cookies If Visible
    Wait Until Page Contains    Log in to ${custom_branding_automated_test}[name]
    ${browser_ids}    Get Browser Ids
    ${admin_browser_id}    Get From List    ${browser_ids}    0
    Set Test Variable    ${admin_browser_id}
    Sleep    2    # takes seconds to show custom branding after redirect
    Verify Css Property Color    ${landing_page_button_color}    ${custom_branding_main_color}    background-color
    Verify Css Property Color    ${landing_page_background}    ${custom_branding_main_darker}    background-color
    Verify Css Property Color    ${privacy_statement_link}    ${custom_branding_main_color}    color
    Verify Css Property Color    ${about_link_login_page}    ${custom_branding_main_color}    color
    Wait Until Element Is Visible    ${landing_page_button_color}
    Try To Click Element    ${landing_page_button_color}
    Sleep    2    # takes seconds to show custom branding after redirect
    Wait Until Page Contains    Log in to ${custom_branding_automated_test}[name]
    Verify Css Property Color    ${oidc_login_button_id}    ${custom_branding_main_color}    background-image
    Verify Css Property Color    ${show_password_button}    ${custom_branding_main_color}    color
    Verify Css Property Color    ${oidc_problems_logging_in}    ${custom_branding_main_color}    color
    Close Browser

Check Login Page Default Branding
    [Documentation]    darken_color is the color 1 level darker than the primary; used in landing page only
    [Arguments]    ${action}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE
        Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    END
    Accept All Cookies If Visible
    Wait Until Page Contains    Log in to Noona
    Sleep    2    # takes seconds to show custom branding after redirect
    Verify Css Property Color    ${landing_page_button_color}    ${default_primary_color}    border-color
    Verify Css Property Color
    ...    ${landing_page_background}
    ...    ${default_main_color_darker_landing_page}
    ...    background-color
    Verify Css Property Color    ${privacy_statement_link}    ${default_primary_color}    color
    Verify Css Property Color    ${about_link_login_page}    ${default_primary_color}    color
    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${landing_page_login_button}
    Try To Click Element    ${landing_page_button_color}
    Sleep    2    # takes seconds to show custom branding after redirect
    Wait Until Page Contains    Log in to Noona
    Verify Css Property Color
    ...    ${oidc_login_button_id}
    ...    ${default_primary_color_lighter_login_page}
    ...    background-image
    Verify Css Property Color
    ...    ${oidc_login_button_id}
    ...    ${default_primary_color_darker_login_page}
    ...    background-image
    Verify Css Property Color    ${show_password_button}    ${default_primary_color}    color
    Verify Css Property Color    ${oidc_problems_logging_in}    ${default_primary_color}    color
    Close Browser

Login As Admin And Toggle Custom Branding
    [Arguments]    ${action}
    Login As Nurse    clinic=${custom_branding_automated_test}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Basic Settings Tab
    Wait Until Page Contains    Clinic experience for patients
    Update Clinic Custom Branding Settings    ${action}
    Check If Color Is Too Light Or Dark
    Try To Input Text    ${custom_color_field}    ${custom_branding_main_color_hex}
    Wait Until Page Does Not Contain    The color cannot be applied. Reason: the color is too dark.    timeout=3s
    Wait Until Page Does Not Contain    The color cannot be applied. Reason: the color is too light.    timeout=3s
    Upload Logo With SVG Format    ${svg_logo}
    Input Clinic Specific Name    ${custom_branding_automated_test}[name]'s    ${custom_branding_automated_test}[name]
    Save Basic Settings

Check If Color Is Too Light Or Dark
    Try To Input Text    ${custom_color_field}    ${too_dark_color_hex}
    Wait Until Page Contains    The color cannot be applied. Reason: the color is too dark.    timeout=3s
    Try To Input Text    ${custom_color_field}    ${too_light_color_hex}
    Wait Until Page Contains    The color cannot be applied. Reason: the color is too light.    timeout=3s

Check Class Login Page Color Theme
    [Arguments]    ${color}    ${clinic_name}    ${action}
    Wait Until Page Contains    Welcome to ${clinic_name}    timeout=3
    Input Text    ${email_textbox}    ${CLINIC_PATIENT}[email]
    Input Password    ${password_textbox}    ${DEFAULT_PASSWORD}
    Sleep    2    # takes seconds to show custom branding after redirect
    ${button_element}    Set Variable If    '${action}'=='enable'    background-color    border-bottom-color
    Verify Css Property Color    ${login_button}    ${color}    ${button_element}
    Verify Css Property Color    ${problems_logging_in_btn_color}    ${color}    color
    Verify Css Property Color    ${dont_have_account_link}    ${color}    color
    Verify Css Property Color    ${privacy_statement_link}    ${color}    color
    Verify Css Property Color    ${about_link_login_page}    ${color}    color
