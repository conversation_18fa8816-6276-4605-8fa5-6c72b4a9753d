*** Settings ***
Documentation       F02N11 Nurse can view patient statistics
...                 Preconditions:
...                 - Patient status feature is enabled in clinic settings.
...                 - Patient with CCD: <EMAIL>
...                 - VDT date: 27.11.2020 (if this date is not in the date range
...                 - in clinic analytics, test will fail. Need to complete F13P02

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n11    clinic-web


*** Test Cases ***
# TODO: Complete F13P02 Patient can view, download and transmit medical records
Admin Can View Clinic Analytics
    [Tags]    nms9-ver-160-1    nms9-ver-160    noona_admin
    Login As Nurse    clinic=${automated_tests_clinic}[name]    user_type=${USER_TYPE}[noona_admin]
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Clinic Analytics Page Is Opened

Manager Can View Clinic Analytics
    [Tags]    nms9-ver-160-2    nms9-ver-160
    Login As Nurse    ${automated_tests_clinic}[default_manager]    ${automated_tests_clinic}[name]
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Clinic Analytics Page Is Opened

Nurse Can View Clinic Analytics
    [Tags]    nms9-ver-160-3    nms9-ver-160
    Login As Nurse    ${automated_tests_clinic}[default_user]    ${automated_tests_clinic}[name]
    Wait Until Page Does Not Contain Element    ${loader}    15s
    Clinic Analytics Page Is Opened


*** Keywords ***
Clinic Analytics Page Is Opened
    Try To Click Element    ${clinic_menu}
    Wait Until Page Contains Element    patient-status-analytics
    Try To Click Element    ${patient_status_analytics}
    Wait Until Page Contains    Patient portal usage
