*** Settings ***
Documentation       F15NA02 Noona admin can manage case escalation settings
...                 Preconditions:
...                 - Noona admin is logged in

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinical_settings.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15na02    clinic-web    noona_admin


*** Test Cases ***
Main success scenario - Admin Can Manage Case Escalation Settings
    [Documentation]    This test case alternately enables and disables the setting each day.
    ...    Whatever the initial config is, the setting will still be updated
    [Tags]    nms9-ver-305
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Case Management Tab
    Wait Until Page Contains Element    ${case_escalation_enabled_yes}
    Scroll Element Into View    ${case_escalation_enabled_yes}
    ${attr}    Get Element Attribute    ${case_escalation_enabled_yes}    class
    IF    'mat-mdc-radio-checked' not in '${attr}'    #if case escalation is disabled
        Enable Case Escalation
        Enable Case Delay Reason
    ELSE
        Disable Case Delay Reason
        Disable Case Escalation
    END
    Save Settings
