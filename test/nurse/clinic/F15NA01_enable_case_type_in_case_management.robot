*** Settings ***
Documentation       F15NA01 Noona admin can manage case types and case base priorities

Resource            ${EXECDIR}/resources/nurse/login.resource
Resource            ${EXECDIR}/resources/management/admin_common.resource
Resource            ${EXECDIR}/resources/clinic_settings/case_management.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinic_settings.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
Test Teardown       Close browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15na01    clinic-web    noona_admin


*** Test Cases ***
Enabling disabled case type should be successful
    [Tags]    nms9-ver-296
    Open "Clinic settings" And Select Case Management Tab
    Disable case type and update base priority
    Enable case type and update base priority to previous
    Save and verify
    Verify If Settings Are Updated Correctly


*** Keywords ***
Navigate to Clinic settings > Case management tab
    ${clinic settings tab}=    Set variable    xpath://h2[text()="Clinic settings"]
    ${patient details in menu}=    Set variable    xpath://h4[text()="Patient details"]
    Wait until keyword succeeds    3x    1s    Click link    ${admin_clinic_menu}
    Wait until keyword succeeds    3x    1s    Click link    clinic-settings-link
    Wait until page contains element    ${clinic settings tab}
    Wait until keyword succeeds    10x    1s    Click element    case-management
    Wait until page contains element    ${patient details in menu}

Disable case type and update base priority
    Wait Until Element Is Visible    (${first_case_type}//div[@class='fieldContainer'])[2]
    ${case_type_name}=    Get Text    (${first_case_type}//div[@class='fieldContainer'])[2]
    ${case_priority}=    Get Text    ${first_case_type}//noona-ng-select/div
    ${first checked case type checkbox}=    Set variable    ${first_case_type}//mat-checkbox
    ${checked case}=    Get element attribute    ${first checked case type checkbox}    class
    IF    "checkbox-checked" in "${checked case}"
        Try To Click Element    ${first_case_type}//mat-checkbox    # disables the case type
        Input Text
        ...    //span[contains(text(),"${case_type_name}")]/ancestor::formly-group/formly-field[3]//noona-ng-select//input
        ...    Low
        Press Keys
        ...    //span[contains(text(),"${case_type_name}")]/ancestor::formly-group/formly-field[3]//noona-ng-select//input
        ...    TAB
        Save and verify
    END
    Set Test Variable    ${case_type_name}
    Set Test Variable    ${case_priority}

Enable case type and update base priority to previous
    Reload Page
    Wait Until Element Is Visible    (${first_case_type}//div[@class='fieldContainer'])[2]
    ${first_case_type_name}=    Get Text    (${first_case_type}//div[@class='fieldContainer'])[2]
    Should Be Equal    ${first_case_type_name}    ${case_type_name}
    # enables the case type
    Try To Click Element
    ...    //span[contains(text(),"${case_type_name}")]/ancestor::formly-group[1]/formly-field[1]//mat-checkbox/div[1]
    Input Text
    ...    //span[contains(text(),"${case_type_name}")]/ancestor::formly-group[1]/formly-field[3]//noona-ng-select//input
    ...    ${case_priority}
    Press Keys
    ...    //span[contains(text(),"${case_type_name}")]/ancestor::formly-group[1]/formly-field[3]//noona-ng-select//input
    ...    TAB

Save and verify
    ${toast message}=    Set variable    css:.toast-message
    Try To Click Element    save
    Wait until element is visible    ${toast message}
    Element should contain    ${toast message}    Clinic settings are updated.
    Try To Click Banner Message

Verify If Settings Are Updated Correctly
    Reload Page
    Wait Until Element Is Visible    (${first_case_type}//div[@class='fieldContainer'])[2]
    ${new_first_case_type_name}=    Get Text    (${first_case_type}//div[@class='fieldContainer'])[2]
    ${new_first_case_priority}=    Get Text    ${first_case_type}//noona-ng-select/div
    Should Be Equal    ${new_first_case_type_name}    ${case_type_name}
    Should Be Equal    ${new_first_case_priority}    ${case_priority}
