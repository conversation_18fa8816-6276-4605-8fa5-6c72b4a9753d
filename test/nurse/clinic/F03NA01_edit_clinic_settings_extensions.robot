*** Settings ***
Documentation       F03NA01 Noona administrator can edit clinic settings

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}clinical_settings.resource
Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na01    clinic-web    noona_admin


*** Test Cases ***
Extension A - Managing Clinic Modules
    [Tags]    nms9-ver-186
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Wait Until Element Is Visible
    ...    ${clinic_user_list_last_name_header}
    Wait Until Element Is Visible    ${admin_clinic_menu}
    Wait Until Element Is Enabled    ${admin_clinic_menu}
    Open "Clinic settings" And Select Clinical Settings Tab
    Enable Urologic Surgery
    Save Settings
    # to make sure page is loaded before the next step
    Wait Until Element Is Visible
    ...    ${pharmacotherapy_details_yes}
    Settings Saved Module Enabled
    Disable Urologic Surgery
    Save Settings
    Settings Saved Module Disabled

Extension B - Time zone setting
    [Tags]    nms9-ver-187
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Basic Settings Tab
    Edit Time Zone Setting    America/New_York
    Save Basic Settings
    Check Saved Time Zone Setting    America/New_York
    Edit Time Zone Setting    Africa/Abidjan
    Save Settings

Extension C - Default text modified
    [Tags]    nms9-ver-188
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Clinical Settings Tab
    Edit Customised Clinical Contents
    Save Settings
    Check Customised Clinical Contents
