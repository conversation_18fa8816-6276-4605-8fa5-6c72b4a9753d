*** Settings ***
Documentation       F07CU02 Clinic user can receive automated notifications
...                 Preconditions:
...                 - Notifications are enabled in clinic settings
...                 - Notifications are enabled in clinic user's profile

Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07cu02    clinic-web


*** Variables ***
${email_subject}                Noona consultation request
${text_body}                    Consultation request to ${care_team1} / ${automated_tests_clinic}[f07cu02_name]
${care_team1}                   Care Team 1
${message_txt}                  Test Notification
${api_key}                      0wxOl5zBNcuCFNtA
${server_id}                    ogdizleh


*** Test Cases ***
Main success scenario - Clinic User Can Receive Automated Notifications
    [Tags]    nms9-ver-259
    Set Email And Delete Previous Messages    ${server_id}    ${api_key}
    Login As Patient    ${f07cu02_patient3}[email]
    Navigate To Clinic
    Ask About Other Symptom
    Select A Case As A Nurse
    Consult A Care Team    ${care_team1}    ${automated_tests_clinic}[f07cu02_name]    ${message_txt}
    ${link}    User Received An Email About A New Message
    ...    ${automated_tests_clinic}[f07cu02_user]
    ...    ${email_subject}
    ...    ${text_body}
    ...    check_link=${NURSE_URL}
    Go To    ${link}
    Login As Nurse    ${automated_tests_clinic}[f07cu02_user]
    Wait Until Page Contains Element    ${patient_cards}


*** Keywords ***
Select A Case As A Nurse
    Login As Nurse
    Search Patient By Identity Code    ${f07cu02_patient3}[ssn]
    Select First Open Case From List
