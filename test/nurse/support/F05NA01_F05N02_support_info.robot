*** Settings ***
Documentation       *Use cases:*
...
...                 [F05N02]: Clinic user can display support information
...
...                 [F05NA01]: Noona administrator can manage clinic support information content
...
...                 *Preconditions:*
...
...                 [F05N02]: User has logged in.
...
...                 [F05NA01]: User has logged in management panel.
...
...                 *Main success scenario:*
...
...                 [F05N02]:
...
...                 - User navigates to Support.
...
...                 - User views clinic support material.
...
...                 [F05NA01]:
...
...                 - User navigates to Clinic support.
...
...                 - User modifies support page content in markdown syntax.
...
...                 - User can select Preview to see how markdown renders when content is published.
...
...                 - User selects Save.
...
...                 - Updated content is displayed to clinic users.
...                 - Test cases can't be executed parallel.

Resource            ${EXECDIR}${/}resources${/}nurse${/}support_info.resource

Suite Setup         Set Libraries Order
Suite Teardown      Reset Support Page And Close Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f05na01    usecase-f05n02    management


*** Test Cases ***
Check Support Information
    [Documentation]    Admin sets support information to be shown to nurse and manager.
    [Tags]    nms9-ver-212    nms9-ver-213-1    nms9-ver-213
    [Setup]    Change Support Page Content    input_text=${information_text}
    Preview Support Information    expected_text=${displayed_text}
    Close Browser
    Check Support Information As Manager    expected_text=${displayed_text}
    Close Browser
    Check Support Information As Nurse    expected_text=${displayed_text}
    Close Browser

Check Bulleted List With Formatted Text
    [Documentation]    Support information contains a bulleted list with formatted (italic) and non-formatted text.
    [Tags]    nms9-ver-213-2    nms9-ver-213
    [Setup]    Change Support Page Content    input_text=${bulleted_list}
    Preview Bulleted Support Information
    Close Browser
    Check Bulleted List    nurse
    Close Browser
    Check Bulleted List    manager
    Close Browser

Fill Support Information To The Limit
    [Documentation]    Type 8192 (= 4 * 2048) characters.
    [Tags]    nms9-ver-213-3    nms9-ver-213
    [Setup]    Run Keyword And Warn On Failure    Change Support Page Content    input_text=${alphabets[:4] * 2048}
    Run Keyword And Warn On Failure    Preview Support Information    expected_text=${alphabets[:4] * 2048}
    Run Keyword And Warn On Failure    Close Browser
    Run Keyword And Warn On Failure    Check Support Information As Nurse    expected_text=${alphabets[:4] * 2048}
    Run Keyword And Warn On Failure    Close Browser
    Run Keyword And Warn On Failure    Check Support Information As Manager    expected_text=${alphabets[:4] * 2048}
    Run Keyword And Warn On Failure    Close Browser

Empty Support Information
    [Documentation]    Support link content is empty if support info is empty.
    [Tags]    nms9-ver-213-4    nms9-ver-213
    [Setup]    Run Keyword And Warn On Failure    Change Support Page Content    input_text=${EMPTY}
    Run Keyword And Warn On Failure    Preview Support Information    expected_text=${EMPTY}
    Run Keyword And Warn On Failure    Close Browser
    Run Keyword And Warn On Failure    Check Support Information As Nurse    expected_text=${EMPTY}
    Run Keyword And Warn On Failure    Close Browser

Support Information Chars Over Limit
    [Documentation]    Type 8193 (= 3 * 2731) characters ie. 1 over the maximum number of characters.
    [Tags]    nms9-ver-213-5    nms9-ver-213
    Fill Support Page Content Over Limit    input_text=${alphabets[:3] * 2731}
    Element Should Be Disabled    id:save
    Page Should Contain Element    class:error-pop-up
    Page Should Contain    You have written 1 characters more than fits in the form.
    Close Browser
