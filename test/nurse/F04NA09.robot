*** Settings ***
Documentation       F04NA09 Noona administrator can manage study platform access to clinic data
...                 Preconditions:
...                 - <PERSON><PERSON> Admin is logged in to Noona Clinic (in a specific Clinic)
...                 - At least one study site request has been sent to a clinic from the study platform

Force Tags          usecase-f04na09    manual


*** Test Cases ***
Main success scenario
    <PERSON><PERSON> Admin navigates to the Study sites tab
    Noona Admin can see the list of all study sites requests:
    Study name
    Clinic name
    Site name
    name of user who sent the request
    date when the request was sent
    "Generate token link" if the study site is not authenticated yet, otherwise a "Site authenticated" mention
    <PERSON>ona Admin click on a Generate token link
    a token is generated for this request
    the Study site token dialog is opened
    Noona Admin can close the dialog to cancel the action
    Noona Admin can see the token
    Noona Admin clicks on the Copy Token button
    The Token is copied to the clipboard
    The dialog is closed
    A confirmation toast message is displayed at the top and fades away after some time
    Noona Admin can generate another token if needed by clicking on the Generate Token link again for the same request
