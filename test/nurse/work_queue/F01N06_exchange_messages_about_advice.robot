*** Settings ***
Documentation       Nurse can exchange messages with patients related to clinic symptom advice
...                 Preconditions:
...                 - Contact clinic feature must be enabled in clinic settings.
...                 - Nurse has contacted patient, F01N01 Nurse can process symptoms waiting for response from Nurse.
...                 Limitations:
...                 - SMS notification is not yet testable
...                 - Language version is not yet testable
...                 - Feedback rating will be tested as an alternate scenario (not yet created)

Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n06    clinic-web    work-queue


*** Test Cases ***
Patient Gets Notication By Email
    [Documentation]    Also includes Extension A - Reply to patient message
    [Tags]    nms9-ver-59    nms9-ver-60
    Ask About Symptoms As Patient    ${f01n06_patient}[email]
    Close Browser
    Login As Nurse
    Search Patient By Identity Code    ${f01n06_patient}[ssn]
    Wait Until Page Contains    Other symptom
    Set Email And Delete Previous Messages    uidvkbw1    RkUVtpFA002PoSq
    Select Symptom And Contact Patient With    Question
    Patient Received An Email Notification    ${f01n06_patient}[email]
    Close All Browsers
    # TODO: Patient gets notification by SMS
    # Extension A
    Login As Patient    ${f01n06_patient}[email]
    Select Latest Clinic Message
    Reply To Clinic Message    Test response to clinic for f01n06.
    Close Browser
    Login As Nurse
    Search Patient By Identity Code    ${f01n06_patient}[ssn]
    Wait Until Page Contains    Other symptom
    Set Email And Delete Previous Messages    uidvkbw1    RkUVtpFA002PoSq
    Select Symptom And Contact Patient With    Question
    Patient Received An Email Notification    ${f01n06_patient}[email]
    # TODO: Patient gets notification by SMS

Extension B - Use message template
    [Tags]    nms9-ver-61
    Ask About Symptoms As Patient    ${f01n06_patient}[email]
    Close Browser
    Login As Nurse
    Search Patient By Identity Code    ${f01n06_patient}[ssn]
    Wait Until Page Contains    Other symptom
    Set Email And Delete Previous Messages    uidvkbw1    RkUVtpFA002PoSq
    Select Symptom And Contact Patient With    Question    message_template=V F01N06 Test Template
    Wait Until Page Contains    Information saved
    Patient Received An Email Notification    ${f01n06_patient}[email]
    # TODO: If language version is missing, template is disabled
    Select First Open Case From List
    Latest Message/Note Is Added In The Case    Test template content. Do not delete.

Extension C - Patient feedback
    [Tags]    nms9-ver-62
    Ask About Symptoms As Patient    ${f01n06_patient_extc}[email]
    Login As Nurse
    Search Patient By Identity Code    ${f01n06_patient_extc}[ssn]
    Set Email And Delete Previous Messages    uidvkbw1    RkUVtpFA002PoSq
    Select Symptom And Contact Patient With    Instructions
    Patient Received An Email Notification    ${f01n06_patient_extc}[email]
    Close Browser
    Patient Has Selected If Instructions Were Sufficient    No    Information is insufficient.
    Close Browser
    Login As Nurse
    Search Patient By Identity Code    ${f01n06_patient_extc}[ssn]
    Latest Message/Note Is Added In The Case    Information is insufficient.
    # TODO: If clinic has enabled patient feedback rating, nurse can see patients instruction rating as a message in discussion chain


*** Keywords ***
Ask About Symptoms As Patient
    [Arguments]    ${email}
    Login As Patient    ${email}
    Navigate to Clinic
    Ask About Other Symptom

Patient Has Selected If Instructions Were Sufficient
    [Arguments]    ${option}    ${message}
    Login As Patient    ${f01n06_patient_extc}[email]
    Select Latest Clinic Message
    Respond If The Info Sufficient As Patient    ${option}    ${message}

Patient Received An Email Notification
    [Arguments]    ${patient}
    Patient Received An Email About A New Message
    ...    ${patient}
    ...    New message from your care team at ${automated_tests_clinic}[name]
    ...    Your care team at ${automated_tests_clinic}[name] sent you a message
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
