*** Settings ***
Documentation       F01N01 Nurse can process symptoms or questions waiting for response from Nurse
...                 Preconditions: Contact clinic feature must be enabled in clinic settings.
...                 - Nurse changes and verifies the work queue view.
...                 - User can select to view the work queue in as patient cards or as a list. Selected value is remembered.
...                 - User changes care team member. Selected value is remembered if user changes work queue tab.
...                 - Contact Patient With Question. Sent response is displayed as last in discussion chain and as unread.
...                 - TODO: Check the status with instructions and invitations. Needs fix to issue explained in test case documentation.
...                 - TODO: Email notification.
...                 - Extra: Read the response using patient and check that status changes at the clinic from "Unread".

Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}timeline.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_users.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n01    clinic-web    work-queue


*** Variables ***
${first_patient_card}           workqueue-item-identity-code-0
${template_contact_message}     Test template content. Do not delete.
${patient_language}             English
${language_selector}            //select[contains(@ng-model, "ctrl.messageTemplate.language")]
${care_team_option}             //*[contains(@data-testid,"option")]//p[contains(.,"f01p01s18 care team")]/ancestor::*[contains(@class,'mdc-checkbox')]


*** Test Cases ***
Nurse Can See Only The Patients That Belong To The Care Team(s) They Are A Member
    [Tags]    nms9-ver-54-1    nms9-ver-54
    Create A New Patient Under F01p01s18 Care Team
    Create New Case For F01p01s18 Patient
    Noona Admin Edits Clinic User's Care Team    remove
    Clinic User Is Not A Member Of A Care Team
    Noona Admin Edits Clinic User's Care Team    add
    Clinic User Is Now A Member Of A Newly Added Care Team
    Noona Admin Edits Clinic User's Care Team    remove
    Clinic User Is Not A Member Of A Care Team
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Select Work Queue View
    [Documentation]    User can select to view the work queue in as patient cards or as a list
    ...    and the selection is default when the user accesses the work queue the next time.
    [Tags]    nms9-ver-54-2    nms9-ver-54
    Login As Nurse
    Select Care Team Filter In Work Queue    Care Team 1
    Select Case Type Filter In Work Queue    Select all
    Change And Verify Work Queue View
    Close Browser

Select Care Team Member Patients
    [Documentation]    User selects care team member at Open cases, clicks Reports tab, goes back
    ...    and verifies that selected care team member is still selected.
    [Tags]    nms9-ver-54-3    nms9-ver-54
    Login As Nurse
    Change And Verify Selected Care Team Member
    Close Browser

Contact Patient With Question
    [Tags]    nms9-ver-54-4    nms9-ver-54
    Login As Nurse
    Create Case For Patient
    Select Symptom And Contact Patient With    Question
    Sent Response Is Displayed As Last In Discussion Chain And Is Unread    Question
    Close Browser

Contact Patient With Instructions
    [Tags]    nms9-ver-54-5    nms9-ver-54
    Login As Nurse
    Create Case For Patient
    Select Symptom And Contact Patient With    Instructions
    Close Browser

Contact Patient With Invitation For A Physical Examination
    [Tags]    nms9-ver-54-6    nms9-ver-54
    Login As Nurse
    Create Case For Patient
    Select Symptom And Contact Patient With    Invitation for a physical examination
    Close Browser

Contact Patient With Invitation For Clinical Tests
    [Tags]    nms9-ver-54-7    nms9-ver-54
    Login As Nurse
    Create Case For Patient
    Select Symptom And Contact Patient With    Invitation for clinical tests
    Close Browser

Extension A - Patient sent multiple entries from diary
    [Tags]    nms9-ver-55
    Add An Activated Patient Under Default Clinic    f01n01-exta     module=${ACUTE_LEUKEMIA_AND_MDS}
    Login As Patient    ${patient_email}
    Add Symptom Diary    Other symptom
    Add Symptom Diary    Fatigue and weakness
    Close Browser
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Timeline Tab
    Symptom Intensity Value Is Displayed    Other symptom    Severe
    Symptom Intensity Value Is Displayed    Fatigue and weakness    Mild
    Capture Screenshot    # to check if current date is highlighted
    Verify Symptom Date    Other symptom
    Verify Symptom Date    Fatigue and weakness
    Multiple Symptom Entries Are Displayed Under Each Other    Fatigue and weakness    Other symptom
    Open Patient Cases Tab
    Select First Open Case From List
    Contact Patient Per Case    Instructions    message_template=none
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Nurse can refresh work queue
    [Tags]    nms9-ver-56
    Login As Nurse
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01n01_care_team]
    Select Case Type Filter In Work Queue    Select all
    Remove Selected Primary Providers
    Refresh Work Queue
    Wait Until Page Contains Element    ${first_patient_card}
    Close Browser

Extension C - Patient didn't find the instructions sufficient
    [Documentation]     kept workflow to follow UI instead of API as this maybe one of the only instances TA uses UI for sending instructions
    ...                 nms9-ver-58 sends instructions to patient via api
    [Tags]    nms9-ver-57
    Add An Activated Patient Under Default Clinic
    ...    f01n01-extc
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N01}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    ${current_date}    Get Current Date
    Contact Patient    Instructions-${current_date}    Instructions
    Close Browser
    Login As Patient    ${patient_email}
    Navigate to Clinic
    Select Latest Clinic Message
    Respond If The Info Sufficient As Patient    No    Additional question to Instructions-${current_date}
    Close Browser
    Login As Nurse
    Select Case Type Filter In Work Queue    Instructions
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01n01_care_team]
    Remove Selected Primary Providers
    Patient Card Is Displayed In Current Work Queue    ${family_name}
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension D — Patient found the instructions sufficient
    [Tags]    nms9-ver-58
    Add An Activated Patient Under Default Clinic
    ...    f01n01-extd
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N01}
    Send Contact Patient Request    11
    Login As Patient    ${patient_email}
    Navigate to Clinic
    Select Latest Clinic Message    Title of contact message
    Respond If The Info Sufficient As Patient    Yes
    Close Browser
    Login As Nurse
    Select Case Type Filter In Work Queue    Instructions
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01n01_care_team]
    Patient Card Is Not Displayed In Current Work Queue    ${first_name}
    Search Patient By Identity Code    ${patient_ssn}
    Open Patient Cases Tab
    Case Status Is Correct    Instructions    Closed
    Close Browser
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

*** Keywords ***
Contact Patient And Select Template
    [Arguments]    ${contact_type}    ${message_template}
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible    ${contact_patient_button}    timeout=5s
    ${contact_patient_button}    Set Variable If    ${status}==True    ${contact_patient_button}
    ...    ${contact_patient_button_opened}
    Wait Until Page Contains Element    ${contact_patient_button}
    Try To Click Element    ${contact_patient_button}
    Wait Until Page Contains Element    ${contact_type_list}
    Sleep    1s
    Select From List By Label    ${contact_type_list}    ${contact_type}
    Wait Until Page Contains Element    ${contact_message_field}
    ${random_string}    Generate Random String    70
    Set Test Variable    ${random_string}
    IF    '${message_template}'=='none'
        Input Text    ${contact_message_field}    ${random_string}
    ELSE
        Select Message Template To Contact Patient    ${message_template}
    END

Message Contains Selected Template Message
    ${contact_message}    Get Value    ${contact_message_field}
    Should Contain    ${template_contact_message}    ${contact_message}

Add Random Text
    ${random_string}    Generate Random String    70
    Set Test Variable    ${random_string}
    Input Text    ${contact_message_field}    ${random_string}    clear=False
    ${contact_message}    Get Value    ${contact_message_field}
    Should Contain    ${contact_message}    ${template_contact_message}${random_string}

Default Language Is Chosen
    ${selected_language}    Get Selected List Label    ${language_selector}
    Should Contain    ${selected_language}    ${patient_language}

Send Messsage
    Try To Click Element    ${send_contact_button}
    Wait Until Page Contains Element    ${patient-messaging-content}

Create Case For Patient
    Set Case Details    Other
    Open A New Case    ${f01n01_patient_1}[ssn]    save

Set Case Details
    [Arguments]    ${case_type}
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=Care Team 1
    ...    assigned_to=Clinic User    case_description=Test case description    case_priority=Medium
    ...    case_origin=Patient Initiated Call    case_status=New    case_notes=Test case notes
    ...    case_outcome=Provider Consulted
    Set Test Variable    &{case_details}

Add Symptom Diary
    [Arguments]    ${symptom}
    Click Add Menu Button
    Add Symptom Entry
    Add A Symptom    ${symptom}
    Create A New Symptom Entry
    Complete Other Symptom Questionnaire
    IF    '${symptom}'=='Other symptom'
        Select Answer To Question    How would you rate the severity of your symptom?    Severe
    END
    Click Next Button
    Save Symptom Report
    Click View Your Diary
    IF    '${symptom}'=='Other symptom'    Click Emergency Symptom Ok Button

Create A New Patient Under F01p01s18 Care Team
    Add An Activated Patient Under Default Clinic    f01p01s18-patient    module=${CHEMO_18_SYMPTOMS}
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01P01S18}

Create New Case For F01p01s18 Patient
    Login As Nurse    
    Set New Case Details For Patient    Symptom Management
    Open A New Case    ${patient_ssn}    save
    Wait Until Page Contains    Case created successfully
    Try to Click Banner Message
    Close Browser

Set New Case Details For Patient
    [Arguments]    ${case_type}
    Set Test Variable    @{critical_patient_concerns}    Chest Pain
    &{case_details}    Create Dictionary
    ...    case_type=${case_type}
    ...    care_team=${automated_tests_clinic}[f01p01s18_care_team]
    ...    case_description=Test case description
    ...    case_priority=Critical
    ...    case_origin=Patient Initiated Call
    ...    case_status=New
    Set Test Variable    &{case_details}

Noona Admin Edits Clinic User's Care Team
    [Arguments]    ${action}
    Login As Nurse    user_type=${USER_TYPE}[noona_admin]
    Navigate To Clinic Users
    Select User    ${automated_tests_clinic}[single_wq_user_email]
    Wait Until Element Is Visible    ${phone_nbr_box}
    Add Or Remove F01P01S18 Care Team For User    ${action}
    Close Browser

Add Or Remove F01P01S18 Care Team For User
    [Arguments]    ${action}
    Wait Until Page Does Not Contain Element    ${loader}    5s
    Scroll Element Into View    ${care_team_option}
    ${attr}    Get Element Attribute    ${care_team_option}    class
    # Handles "remove" action
    IF    'checkbox-checked' in '${attr}' and '${action}' == 'remove'
        Try To Click Element    ${care_team_option}
        Save User Information
        Wait Until Page Contains    Updated
        Try To Click Banner Message
        Wait Until Page Does Not Contain    Updated
    ELSE IF    'checkbox-checked' not in '${attr}' and '${action}' == 'remove'
        Log    Care team already in the desired state.
        Try To Click Element    ${cancel_btn}
        # Handles "add" action
    ELSE IF    'checkbox-checked' not in '${attr}' and '${action}' == 'add'
        Try To Click Element    ${care_team_option}
        Save User Information
        Wait Until Page Contains    Updated
        Try To Click Banner Message
        Wait Until Page Does Not Contain    Updated
    ELSE IF    'checkbox-checked' in '${attr}' and '${action}' == 'add'
        Log    Care team already in the desired state.
        Try To Click Element    ${cancel_btn}
    END

Clinic User Is Not A Member Of A Care Team
    Login As Nurse    ${automated_tests_clinic}[single_wq_user_email]
    Try To Click Element    ${care_team_filter}
    Sleep    1s
    Text Should Not Be In The Page    ${automated_tests_clinic}[f01p01s18_care_team]
    Go To Patient Reports
    Wait Until Page Does Not Contain Element    ${loader}    5s
    Try To Click Element    ${care_team_filter}
    Sleep    1s
    Text Should Not Be In The Page    ${automated_tests_clinic}[f01p01s18_care_team]
    Close Browser

Clinic User Is Now A Member Of A Newly Added Care Team
    Login As Nurse    ${automated_tests_clinic}[single_wq_user_email]
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01p01s18_care_team]
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Open First Workqueue Item
    Wait Until Page Contains    ${case_assigned_text}
    Try To Click Banner Message
    Wait Until Page Does Not Contain    ${case_assigned_text}
    Return To Work Queue
    Wait Until Page Does Not Contain Element    ${loader}    5s
    Wait Until Page Contains Element    ${care_team_filter}
    Go To Patient Reports
    Wait Until Page Does Not Contain Element    ${loader}    5s
    Try To Click Element    ${care_team_filter}
    Sleep    1s
    Text Should Be In The Page    ${automated_tests_clinic}[f01p01s18_care_team]
    Close Browser
