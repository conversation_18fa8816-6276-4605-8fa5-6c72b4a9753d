*** Settings ***
Documentation       Nurse can exchange messages with patients related to open questions
...                 Preconditions:
...                 - Contact clinic feature must be enabled in clinic settings.
...                 - Nurse has contacted patient, F01N01 Nurse can process symptoms waiting for response from Nurse.
...                 NOTE: Test cases under this use should be run in sequence

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n07    clinic-web    work-queue


*** Test Cases ***
Reply To Nurse Message Main Scenario and Reply To Patient Message-Ext.A
    [Documentation]    Combine manin scenario and extention A
    [Tags]    nms9-ver-63    nms9-ver-64
    [Setup]    Set Email And Delete Previous Messages     ${f01n07_email_keys}[0]    ${f01n07_email_keys}[1]
    Add An Activated Patient Under Default Clinic     f01n07    mailosaur=${mailosaur_keys}[0]
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Send Open Questions To Patient    F01N07 Question Topic    Question    F01N07 sample message for patient.
    Latest Message/Note Is Added In The Case    F01N07 sample message for patient.
    Sleep    5    # gives more time for the message from first tc to be sent to patient
    Login As Patient      ${patient_email}
    Select Latest Clinic Message
    Reply To Clinic Message    Test response to clinic for f01n07.
    Login As Nurse
    Search Patient By Identity Code       ${patient_ssn}
    Open Patient Cases Tab
    Select Symptom And Contact Patient With    Question
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${automated_tests_clinic}[name]
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}