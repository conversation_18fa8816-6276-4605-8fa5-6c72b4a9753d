*** Settings ***
Documentation       F01N08 Nurse can list patients whose requests has been handled.
...                 Preconditions: A nurse has sent a message, a questionnaire to a patient or contacted patient (without patient contacting the clinic first).
...                 - Show handled patients from the last seven days and 365.
...                 - Input invalid dates, search and see that pages don't crash.
...                 - Change care team and search.
...                 - Change end date and search.
...                 - View Closed Case from search results.

Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_management.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n08    clinic-web    work-queue

*** Test Cases ***
Main Success Scenario - Nurse can list patients whose requests have been handled
    [Tags]    nms9-ver-65
    [Setup]    Add An Activated Patient Under Default Clinic    f01n08-main    subscriber_id=${AUTOMATED_TESTS_SUBS_ID_F01N08}
    Send Contact Patient Request    11
    Login As Nurse    ${automated_tests_clinic}[f01n08_manager]
    Clinic User Can See Only The Patients That Belong To Their Care Teams
    Click List View Button
    Patients Are Displayed In List View
    Click Card View Button
    ${todays_date}    Get Current Date    result_format=%d.%m.%Y
    Group Date Is Correct    ${todays_date}
    Nurse Can Change Date By Selecting Another Date
    Noona Does Not Remember The Data Range Selection
    Close Browser
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main Success Scenario - Work Queue Filter - Cases you have handled - Consultation Request
    [Tags]    nms9-ver-66-1    nms9-ver-66
    [Setup]    Add An Activated Patient Under Default Clinic    f01n08-wq1    subscriber_id=${AUTOMATED_TESTS_SUBS_ID_F01N08}
    Login As Patient Via API    ${patient_email}    ${AUTOMATED_TESTS_CLINIC_ID}
    Ask About Other Issues As Patient Via API    Information about side effects    Test content
    Generate Clinic Token
        ...    ${automated_tests_clinic}[f01n08_manager]
        ...    ${DEFAULT_PASSWORD}
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Get First Patient Case via API    ${patient_id}
    Consult A Care Team Via API    ${first_case_id}    Consulting care team 2 ${today}    ${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_2}
    Generate Clinic Token
        ...    ${automated_tests_clinic}[default_manager]
        ...    ${DEFAULT_PASSWORD}
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Get First Patient Case via API    ${patient_id}
    Reply To Consulation Via API    ${first_case_id}    Replying to consultation of f01n08 ${today}
    Generate Clinic Token
        ...    ${automated_tests_clinic}[f01n08_manager]
        ...    ${DEFAULT_PASSWORD}
        ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Close first case Via API    ${first_case_id}
    Login As Nurse    email=${automated_tests_clinic}[f01n08_manager]
    Go To Closed Cases Tab
    Select Care Team Filter In Closed Cases    Cases you have handled
    Show Closed Cases Today
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Select Care Team Filter In Closed Cases    My teams' patients
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Select Care Team Filter In Closed Cases    f01n08 care team
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Select Care Team Filter In Closed Cases    f01n08 manager
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Wait Until Page Contains    Consulting care team 2 ${today}
    Page Should Contain    Replying to consultation of f01n08 ${today}
    Close Browser
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - No messages
    [Tags]    nms9-ver-67
    Login As Nurse
    Go To Closed Cases Tab
    Select Care Team Filter In Closed Cases    No closed cases care team
    Wait Until Page Contains    No patients in the queue
    Close Browser

*** Keywords ***
Case Assigned To Correct Care Team
    [Arguments]    ${care_team}
    Wait Until Element Is Visible    ${care_team_column_values}
    ${count}    Get Element Count    ${care_team_column_values}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${text}    Get Text    (${care_team_column_values})[${INDEX}]
        Should Be Equal    ${text}    ${care_team}
    END

Case Assigned To Correct Nurse
    [Arguments]    ${care_person}
    Wait Until Element Is Visible    ${care_person_column_values}
    ${count}    Get Element Count    ${care_person_column_values}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${text}    Get Text    (${care_person_column_values})[${INDEX}]
        Should Contain Any    ${text}    ${care_person}    Assign to me
    END

Tab Counter Shows More Than Zero
    ${case_count}    Get Text    ${tab_counter}
    Should Not Contain    ${case_count}    (0)


Clinic User Can See Only The Patients That Belong To Their Care Teams
    Go To Closed Cases Tab
    Try To Click Element    ${care_team_filter_legacy}
    Wait Until Element Is Visible    (${closed_cases_care_teams})[1]
    ${count}    Get Element Count    ${closed_cases_care_teams}
    ${count}    Convert To String    ${count}
    Should Be Equal    ${count}    7    #only 1 care team is visible and the rest are existing values
    ${text}    Get Text    (${closed_cases_care_teams})[4]
    Should Be Equal    ${text}    f01n08 care team

Nurse Can Change Date By Selecting Another Date
    ${date}    Get Current Date
    Closed Cases Date Range Has The Default Last 7 Days Value
    Show Closed Cases From The Last    365 days
    Sleep    1    #needed sleep because execute javascript kw does not wait
    ${current_start_date}    Execute Javascript    ${closed_cases_start_date_value_js}
    ${current_end_date}    Execute Javascript    ${closed_cases_end_date_value_js}
    ${todays_date}    Convert Date    ${date}    result_format=%d.%m.%Y
    ${365_days_before}    Subtract Time From Date    ${date}    365 days    result_format=%d.%m.%Y
    Should Be Equal    ${current_start_date}    ${365_days_before}
    Should Be Equal    ${current_end_date}    ${todays_date}

Noona Does Not Remember The Data Range Selection
    Navigate To Patient Page
    Wait Until Location Contains    ${CREATE_PATIENT_ENDPOINT}
    Go To Work Queue
    Go To Closed Cases Tab
    Closed Cases Date Range Has The Default Last 7 Days Value

Closed Cases Date Range Has The Default Last 7 Days Value
    ${date}    Get Current Date
    ${todays_date}    Convert Date    ${date}    result_format=%d.%m.%Y
    ${7_days_before}    Subtract Time From Date    ${date}    7 days    result_format=%d.%m.%Y
    Wait Until Element Is Visible    ${closed_cases_date_range_label}
    Sleep    1    #needed sleep because execute javascript kw does not wait
    ${current_start_date}    Execute Javascript    ${closed_cases_start_date_value_js}
    ${current_end_date}    Execute Javascript    ${closed_cases_end_date_value_js}
    Should Be Equal    ${current_start_date}    ${7_days_before}
    Should Be Equal    ${current_end_date}    ${todays_date}
