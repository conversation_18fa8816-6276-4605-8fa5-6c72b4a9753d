*** Settings ***
Documentation       F01N10 Nurse can list patients whose symptom report
...                 has triggered an alert or a self care rule and validate the triggered rule
...                 Preconditions:
...                 - Patient has sent symptom entry to clinic which fills the criteria of an emergency, semi-emergency,
...                 urgent or mild level self care rule.

Resource            ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}modules.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n10    clinic-web    manual


*** Variables ***
@{PATIENTS LIST FOR CURRENT USER}       has made consultation request
...                                     has received consultation request
...                                     has received consultation reply
...                                     has assigned herself/himself
...                                     is responsible care person (in main module)

@{PATIENTS LIST FOR SELECTED USER}      has made consultation request
...                                     has received consultation request
...                                     has received consultation reply
...                                     has assigned herself/himself
...                                     is responsible care person (in main module)
...                                     Note: All care people of current users care teams are listed.
${first_case_initials}                  //*[@id="workqueue-0"]//span/div
${open_case_available}                  //*[@id="workqueue-item-identity-code-0"]


*** Test Cases ***
Nurse can validate automated rules
    [Documentation]    Feature removed. NOONA-13841 Remove robot cases for validate automated answers and automated answers work queue
    [Setup]    Close Open Cases    N05070070
    Patient Add Severe Symptom To Diary
    Login As Nurse
    Go To Automated Answers
    Wait Until Keyword Succeeds    20s    1s    Select Care Team Filter In Work Queue    f01n10 care team
    Select Patient Card    James Hines
    Wait Until Page Contains    OTHERSYMPTOM01
    Page Should Contain    The adverse effect the patient is experiencing, is severe.
    Validate Automated Answer    correct    Correct validation test
    Wait Until Page Contains    OTHERSYMPTOM01: The automated message has been validated as correct.
    Page Should Contain    Correct validation test
    Return To Patients
    Sleep    1
    Page Should Not Contain    James Hines

Work queue filter
    [Documentation]    Feature removed. NOONA-13841 Remove robot cases for validate automated answers and automated answers work queue
    Login As Nurse
    Go To Automated Answers
    Wait Until Keyword Succeeds    25s    1s    Select Care Team Filter In Work Queue    My teams' patients
    Wait Until Page Contains Element    ${open_case_available}
    Wait Until Keyword Succeeds    25s    1s    Select Care Team Filter In Work Queue    My patients
    Nurse Initials Is Displayed In The First Patient Card    CU
    ${number_of_cases_value}    Get Number Of Patients
    Wait Until Keyword Succeeds    25s    1s    Select Care Team Filter In Work Queue    Care Team 1
    Wait Until Keyword Succeeds
    ...    10s
    ...    0.2s
    ...    Number Of Automated Answers Should Be Different
    ...    ${number_of_cases_value}
    Wait Until Keyword Succeeds    25s    1s    Select Care Team Filter In Work Queue    Lock User
    Wait Until Keyword Succeeds    10s    1s    Nurse Initials Is Displayed In The First Patient Card    LU

Extension A - Patient symptom has triggered multiple same level rules
    [Documentation]    Feature removed. NOONA-13841 Remove robot cases for validate automated answers and automated answers work queue
    Add Multiple Same Level Rules    <EMAIL>
    Login As Nurse
    Go To Automated Answers
    Wait Until Keyword Succeeds    30s    1s    Select Care Team Filter In Work Queue    f01n10 care team
    Select Patient Card    Grace Simon
    Wait Until Page Contains    SWOLLENARM02HighFever
    Wait Until Page Contains    SWOLLENARM03UpperLimbErysipelasEmergency
    Validate Automated Answer    correct    Correct validation test    rule=SWOLLENARM02HighFever
    Wait Until Page Contains    SWOLLENARM02: The automated message has been validated as correct.
    Page Should Contain    Correct validation test
    Validate Automated Answer
    ...    incorrect
    ...    Incorrect validation test
    ...    rule=SWOLLENARM03UpperLimbErysipelasEmergency
    Wait Until Page Contains    SWOLLENARM03: Automated message validated as incorrect.
    Page Should Contain    Incorrect validation test
    Return To Patients
    Sleep    1
    Page Should Not Contain    Grace Simon


*** Keywords ***
Patient Add Severe Symptom To Diary
    Login As Patient    <EMAIL>
    Click Add Menu Button
    Add Symptom Entry
    Sleep    1
    questionnaires.Select Symptom From List    Other symptom
    Complete Other Symptom Questionnaire
    Select Answer To Question    How would you rate the severity of your symptom?    Severe
    Click Next Button
    Save Symptom Report
    Emergency Priority Symptom Is Displayed

Get Number Of Patients
    ${automated_answers_value}    Get Text    ${automated_answers_tab}
    RETURN    ${automated_answers_value}

Number Of Automated Answers Should Be Different
    [Arguments]    ${number_of_cases_value}
    ${number_of_cases_value_2}    Get Number Of Patients
    Should Not Contain    ${number_of_cases_value}    ${number_of_cases_value_2}

Nurse Initials Is Displayed In The First Patient Card
    [Arguments]    ${initials}
    Wait Until Element Is Visible    (${patient_cards})[1]
    Scroll Element Into View    (${patient_cards})[1]
    Sleep    1
    ${text}    Get Text    ${first_case_initials}
    Should Be Equal    ${initials}    ${text}
