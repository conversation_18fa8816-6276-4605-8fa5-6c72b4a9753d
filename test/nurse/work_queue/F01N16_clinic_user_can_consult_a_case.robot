*** Settings ***
Documentation       F01N16 Clinic user can consult a patient case
...                 Preconditions:
...                 - Patient has contacted clinic about a symptom, answered AEQ,
...                 made a symptom entry that triggers emergency or semi-emergency rule or
...                 replied a discussion a nurse has initiated.
...                 - Consulting is enabled in clinic settings.
...                 This makes New cases-work queue visible.
...                 *Note:* Messaging need not to be enabled in clinic settings.
...                 NOTE:    # TODO: NOONA-10663 Clinic - Consult A Care Person - Care person searchable dropdown doesn't work properly
...                 Test clinic: TA Clinic Automated_tests

Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n16    clinic-web    work-queue


*** Test Cases ***
Main Success Scenario - Clinic user can consult a patient case
    [Documentation]    Also includes Extension A - Consulting again
    [Tags]    nms9-ver-92    nms9-ver-93
    # nms9-ver-92
    Main Success Scenario Setup
    Select A Case As A Nurse
    Consult A Care Team    Care Team 3    Third Tester    Test consultation
    Case Is Unassigned    ${OTHER_SYMPTOM}
    Patient Card Is Displayed In The Care Team's Work Queue    Care Team 3    #case appears in consulted team's wq
    Close Browser
    Other Nurse Can Reply To Consultation
    Close Browser
    # nms9-ver-93
    Select A Case As A Nurse
    Consult A Care Team    Care Team 4    Fourth Tester    Test consulting again
    Case Is Unassigned    ${OTHER_SYMPTOM}
    Patient Card Is Displayed In The Care Team's Work Queue    Care Team 5    #case appears in consulted team's wq
    Close Browser
    Login As Nurse    ${automated_tests_clinic}[f01n16_nurse_3]
    Patient Card Is Displayed In The Care Team's Work Queue    Care Team 4    #case appears in consulted team's wq
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Cancelling consultation
    [Documentation]    Pre-condition: The clinician team's patient case has been consulted to another clinician in the same care team.
    ...    Clinician cannot cancel or forward consultation to another consul outside of the clician's care team
    [Tags]    nms9-ver-94
    Add An Activated Patient Under Default Clinic    f01n16-extb    subscriber_id=${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_5}
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Ask About Other Symptom
    Close Browser
    Select A Case As A Nurse
    Consult A Care Team    Care Team 4    Fourth Tester    Test cancelling consultation
    Wait Until Page Does Not Contain    Consultation sent
    Case Status Is Correct    ${OTHER_SYMPTOM}    Consultation In Progress
    Cancel Consultation
    Wait Until Noona Loader Is Not Visible
    Case Status Is Correct    ${OTHER_SYMPTOM}   In progress
    Go Back To Care Team's Work Queue    Care Team 5
    Select Case Type Filter In Work Queue    Select all
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Close Browser
    Login As Nurse    ${automated_tests_clinic}[f01n16_nurse_3]
    Wait Until Noona Loader Is Not Visible
    Remove All Care Team Filter
    Select My Patients In Work Queue
    Wait Until Page Does Not Contain    ${first_name}${SPACE}${family_name}    timeout=10s
    #TODO: Create a sub-test which verifies that no cancelling consultation option is available if the case has been sent to a consul in different care team than the clinician's care team
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

*** Keywords ***
Select A Case As A Nurse
    Login As Nurse    ${automated_tests_clinic}[f01n16_nurse_1]
    Search Patient By Identity Code    ${patient_ssn}
    Select First Open Case From List

Go Back To Care Team's Work Queue
    [Arguments]    ${care_team}
    Go To Work Queue
    Sleep    2
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${care_team}

Main Success Scenario Setup
    Add An Activated Patient Under Default Clinic    f01n16-main    subscriber_id=${AUTOMATED_TESTS_SUBS_ID_CARE_TEAM_5}
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Ask About Other Symptom
    Close Browser

Patient Card Is Displayed In The Care Team's Work Queue
    [Arguments]    ${care_team}
    Go Back To Care Team's Work Queue    ${care_team}
    Select Case Type Filter In Work Queue    Select all
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}

Other Nurse Can Reply To Consultation
    Login As Nurse    ${automated_tests_clinic}[f01n16_nurse_2]
    Select Case Type Filter In Work Queue    Select all
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    Care Team 3
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Case Status Is Correct    ${OTHER_SYMPTOM}    Consultation In Progress
    Reply To Consultation    Test reply to consultation
    Wait Until Page Contains    A reply to consultation was sent
    Reload Page
    Case Status Is Correct    ${OTHER_SYMPTOM}    Consultation answered
    Case Assignee Is Correct    ${OTHER_SYMPTOM}    Lima Tester