*** Settings ***
Documentation       Using case note templates in cases
...                 - Checks that case note templates can be used with patients
...                 - Can be under F15CU01 Clinic user can create, process and edit a case
...                 - Note: These tcs have their own cases in JAMA but not existing on confluence

Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_note.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse
Test Timeout        ${TEST_TIMEOUT}

Force Tags          clinic-web    work-queue


*** Test Cases ***
Main success scenario I - Add Note Template When Creating A Case
    [Tags]    nms9-ver-338
    Add An Activated Patient Under Default Clinic    f15cu01-case-note-1
    Set Case Details    Medication Instructions
    Open A New Case    ${patient_ssn}    continue    case_note_template=Yes
    Click Save And Exit Case Button
    Wait Until Page Contains    Case created successfully
    Verify Case Note Text
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension A - Edit Case Note
    [Tags]    nms9-ver-339
    Add An Activated Patient Under Default Clinic    f15cu01-case-note-a
    Set Case Details    Other
    Open A New Case    ${patient_ssn}    save
    Wait Until Page Contains    Case created successfully
    Process The Case
    Click Edit Case Button
    Click Continue Case Button
    Use Case Note Template
    Click Save And Exit Case Button
    Wait Until Page Contains    The patient case was successfully updated
    Verify Case Note Text
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main Success Scenario II — Add Note Template In Patient Note
    [Tags]    nms9-ver-340-1    nms9-ver-340
    Add An Activated Patient Under Default Clinic    f15cu01-case-note-2
    Search Patient By Identity Code    ${patient_ssn}
    Add Note With Template
    Verify Patient Note With Template
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main Success Scenario III — Add Note Template In Case
    [Tags]    nms9-ver-340-2    nms9-ver-340
    Add An Activated Patient Under Default Clinic    f15cu01-case-note-3
    Set Case Details    Medication Instructions
    Open A New Case    ${patient_ssn}    save
    Create Case Note With Template
    Verify Case Note With Template In A Case
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Set Case Details
    [Arguments]    ${case_type}
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=Care Team 1
    ...    assigned_to=Clinic User    case_description=Test case description    case_priority=Medium
    ...    case_origin=Patient Initiated Call    case_status=New    case_notes=${EMPTY}
    ...    case_outcome=Provider Consulted
    Set Test Variable    &{case_details}
