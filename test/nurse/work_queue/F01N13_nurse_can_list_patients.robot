*** Settings ***
Documentation       F01N13 Nurse can list patients to whom questionnaire has been scheduled
...                 Preconditions:
...                 - Nurse has scheduled adverse effect questionnaire to a patient.

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource
Resource            ${EXECDIR}${/}resources/nurse/announcements.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n13    clinic-web    work-queue


*** Variables ***
${f01n13_care_team}             nms9-ver-84-defect
${case_outcome}                 Provider Consulted
${complete_qol_button}          //button[@data-testid='open-extra-action']
${aeq_questionnaire}            aeq
${qol_questionnaire}            qol
${complete}                     Complete
${provider}                     Doctor, Dr Fine.
${doctor}                       Dr Doctor


*** Test Cases ***
Main success scenario - Nurse can list patients to whom questionnaire has been scheduled
    [Tags]    nms9-ver-2    long-tc    long-tc-4
    [Setup]    Create Patients And Clean Care Team As Test Setup
    Send Symptom Questionnaires And Answer As Patients
    Login As Nurse
    Wait Until Element Is Not Visible    ${noona-loader}
    Go To Patient Reports
    Unhandled Tab Is Selected By Default
    Remove Selected Primary Providers
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01n13_care_team_main]
    Work Queue Is Grouped By Date    3
    Set Questionnaires Dates
    Latest Date Is Displayed In Patient Reports    ${after_2_days}
    Patient Reports Can Be Filtered By Date    Tomorrow
    Work Queue Is Grouped By Date    1
    Latest Date Is Displayed In Patient Reports    ${tomorrow_date}
    Patient Reports Can Be Filtered By Date    Today
    Work Queue Is Grouped By Date    1
    Latest Date Is Displayed In Patient Reports    ${current_date}
    Patient Reports Can Be Filtered By Date    ${after_2_days}
    Work Queue Is Grouped By Date    1
    Latest Date Is Displayed In Patient Reports    ${after_2_days}
    Navigate To Announcements
    Go To Work Queue
    Go To Patient Reports
    Care Team Is Selected    ${automated_tests_clinic}[f01n13_care_team_main]
    Select Patient Report Unhandled Filter
    Wait Until Element Is Visible    ${noona-loader}
    Wait Until Element Is Not Visible    ${noona-loader}
    Patient Card's Color Is Correct    no_symptoms    index=0
    Patient Card's Color Is Correct    critical    index=1
    Patient Card's Color Is Correct    medium    questionnaire=yes    index=2
    Select Non Completed Tab
    Wait Until Element Is Visible    ${noona-loader}
    Wait Until Element Is Not Visible    ${noona-loader}
    Patient Card's Color Is Correct    unanswered
    Select Patient Report Unhandled Filter
    Click List View Button
    Patients Are Displayed In List View
    Close Browser
    [Teardown]    Delete All Patients As Teardown

Main success scenario - Nurse can list patients to whom questionnaire has been scheduled - Sorting
    [Tags]    manual
    User can select to view the Patient reports as patient cards or as a list.
    List view can be sorted with the following data:
    Priority, by default cases are first listed in priority order
    Patient name
    ID/SSN
    Patient Group
    Case type
    Date answered
    Status
    Care team
    Primary provider (to show, needs to be added in clinic configuration)
    Assignee
    Nurse can filter patient list according to the Display patients filter values.
    Select patient whose adverse effect report user wants to display.

Work Queue Filter
    [Tags]    nms9-ver-84-1    nms9-ver-84
    Login As Nurse
    Select Case Type Filter In Work Queue    Select all
    Remove All Care Team Filter
    Remove Selected Primary Providers
    Select My Patients In Work Queue
    Wait Until Keyword Succeeds    10s    1s    Nurse Initials Is Displayed In The First Patient Card    CU
    ${number_of_cases_value}    Get Number Of Patients
    Unselect My Patients In Filter In Work Queue
    Select Care Team Filter In Work Queue    Care Team 1
    Wait Until Keyword Succeeds    10s    1s    Number Of Cases Should Be Different    ${number_of_cases_value}
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    Work Queue Test
    Wait Until Keyword Succeeds    10s    1s    Nurse Initials Is Displayed In The First Patient Card    WQT
    Close Browser

Work Queue - Provider Filter
    [Documentation]    Precondition: In clinic setting >> Case Management >> Primary Provider checkbox
    ...    should be checked and settings saved
    [Tags]    nms9-ver-84-2    nms9-ver-84
    Generate Random Patient Data
    Create An Activated Patient Via API    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}    ${AUTOMATED_TESTS_SUB_ID_F01N13}    with_provider=default
    Send Symptom Questionnaire Via Api To Patient    ${baseline_quest}
    Newly Created Provider Patient Answers Questionnaire
    Check Provider Filter For Patient Reports - Unhandled Tab
    Close Patient Case In Work Queue
    Close Browser
    Login As Nurse
    Create New Case For Provider Patient    Symptom Management
    Check Primary Provider Filter For Open Cases
    Search Patient By Identity Code    ${patient_ssn}
    Add Questionnaire To Schedule    ${BASELINE_QUESTIONNAIRE}
    Check Provider Filter For Patient Reports - Non completed And Today
    [Teardown]    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

MRN Column Is Displayed On The List View
    [Documentation]    Precondition: In the clinic setting, MRN is set as primary identifier
    ...    TODO: Value check
    [Tags]    nms9-ver-84-3    nms9-ver-84        defect
    Login As Nurse    ${appointment_clinic}[user_email]
    Create New Case    Chemo / Tx Questions
    Go To Work Queue
    Select Case Type Filter In Work Queue    Select all
    Select Care Team Filter In Work Queue    nms9-ver-84-defect
    Remove Patient Group Filters In Work Queue
    Click Card View Button
    Wait For Element To Not Be Present    ${mrn_column}
    Click List View Button
    Wait For Element To Be Present    ${mrn_column}
    Go To Patient Reports
    Wait For Element To Be Present    ${mrn_column}
    Go To Closed Cases Tab
    Click Card View Button
    Show Closed Cases From The Last    30 days
    Click List View Button
    Wait For Element To Be Present    ${mrn_column}
    Close Browser


*** Keywords ***
Get Number Of Patients
    ${open_cases_value}    Get Text    ${open_cases_tab}
    RETURN    ${open_cases_value}

Number Of Cases Should Be Different
    [Arguments]    ${number_of_cases_value}
    ${number_of_cases_value_2}    Get Number Of Patients
    Should Not Contain    ${number_of_cases_value}    ${number_of_cases_value_2}

Nurse Initials Is Displayed In The First Patient Card
    [Arguments]    ${initials}
    Wait Until Element Is Visible    (${patient_cards})[1]
    Scroll Element Into View    (${patient_cards})[1]
    ${text}    Get Text    ${first_case_initials}
    Should Be Equal    ${initials}    ${text}

Create New Case
    [Arguments]    ${case_type}
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=${f01n13_care_team}
    ...    case_description=Test case description    case_priority=Medium
    ...    case_origin=Patient Initiated Call    case_status=New
    Set Test Variable    &{case_details}
    Open A New Case    ${f01n13_patient}[ssn]    save
    Close Case With Outcome    ${case_outcome}

Find Non-Expired Questionnaire And Mark As Expired
    [Arguments]    ${questionnaire}    ${ssn}
    ${expire_button}    Set Variable If    '${questionnaire}'=='aeq'    ${mark_aeq_as_expired_button}
    ...    ${mark_qol_as_expired_button}
    Login As Nurse
    Search Patient By Identity Code    ${ssn}
    Navigate To Questionnaires Tab
    ${passed}    Run Keyword And Return Status    Wait Until Page Contains    ${complete}    timeout=5s
    IF    ${passed}    Mark All Questionnaires As Expired    ${questionnaire}
    Close Browser

Mark All Questionnaires As Expired
    [Arguments]    ${questionnaire_type}
    ${count}    Get Element Count    ${complete_qol_button}
    FOR    ${INDEX}    IN RANGE    0    ${count}
        Mark Questionnaire As Expired    ${questionnaire_type}
    END

Create New Case For Provider Patient
    [Arguments]    ${case_type}
    Set Test Variable    @{critical_patient_concerns}    Chest Pain
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=${automated_tests_clinic}[f01n13_care_team]
    ...    case_description=Test case description    case_priority=High
    ...    case_origin=Patient Initiated Call
    Set Test Variable    &{case_details}
    Open A New Case    ${patient_ssn}    save
    Try To Click Banner Message

Check Primary Provider Filter For Open Cases
    Return To Work Queue
    Click Card View Button
    Remove All Care Team Filter
    Remove Case Type Filters In Work Queue
    Remove Selected Primary Providers
    Wait Until Keyword Succeeds    9s    0.3s    Remove Patient Group Filters In Work Queue
    Select Primary Provider    ${provider}
    Click List View Button
    Wait For Element To Be Present    ${primary_provider_column}
    Page Should Contain    ${first_name}${SPACE}${family_name}
    Click Card View Button
    Wait For Element To Not Be Present    ${primary_provider_column}

Close Patient Case In Work Queue
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Close Case

Check Provider Filter For Patient Reports - Non completed And Today
    Go To Work Queue
    Go To Patient Reports
    Remove All Care Team Filter
    Remove Questionnaire Filter In Work Queue
    Remove Selected Primary Providers
    Wait Until Keyword Succeeds    9s    0.3s    Remove Patient Group Filters In Patient Reports Work Queue
    Select Primary Provider    ${provider}
    Page Should Not Contain    ${first_name}${SPACE}${family_name}
    Select Non Completed Tab
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Select Today Tab
    Patient Card Is Displayed In Current Work Queue    ${first_name}${SPACE}${family_name}
    Click List View Button
    Wait For Element To Be Present    ${primary_provider_column}
    Page Should Contain    ${first_name}${SPACE}${family_name}
    Click Card View Button
    Wait For Element To Not Be Present    ${primary_provider_column}
    Select Patient Report Unhandled Filter
    Remove Selected Primary Providers
    Close Browser

Newly Created Provider Patient Answers Questionnaire
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Select Answer Questionnaire
    Select Yes For Symptoms    Other symptom
    Click Element    ${aeq_questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    Evaluate Previous Symptom    previous_symptom=new_entry
    questionnaires.Check And Write To Text Area
    Select Answer To Question    When did you have this symptom?    Today
    questionnaires.Select Specific Answer To Question    How would you rate the severity of your symptom?    Severe
    ...    questionnaire=tnonc distress
    questionnaires.Select Specific Answer To Question    Have you used any medication to alleviate your symptoms?    No
    ...    questionnaire=tnonc distress
    Click Element    ${aeq_questionnaire_next_button}
    Send Symptom Questionnaire To Clinic
    Wait Until Page Contains    ${emergency_symptom_instructions_text_2-1}
    Wait Until Page Contains    ${emergency_symptom_instructions_text_2-2}
    Click Emergency Symptom Ok Button
    Close Browser

Check Provider Filter For Patient Reports - Unhandled Tab
    Login As Nurse
    Go To Patient Reports
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    ${automated_tests_clinic}[f01n13_care_team]
    Select Primary Provider    ${provider}
    Remove Patient Group Filters In Current Work Queue
    Select Questionnaire Type In Work Queue    Symptom questionnaire
    Patient Card Is Displayed In Current Work Queue    ${first_name}
    Click List View Button
    Wait For Element To Be Present    ${primary_provider_column}
    Page Should Contain    ${first_name}
    Click Card View Button
    Wait For Element To Not Be Present    ${primary_provider_column}

Send Symptom Questionnaire To Patient
    [Arguments]    ${patient_id}    ${questionnaire}    ${review_date}    ${send_date}
    Generate Clinic Token
    ...    ${automated_tests_clinic}[default_user]
    ...    ${DEFAULT_PASSWORD}
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    Set Test Variable    ${patient_id}
    Send Symptom Questionnaire Via Api To Patient
    ...    ${questionnaire}
    ...    review_date=${review_date}
    ...    send_date=${send_date}

Send Symptom Questionnaires And Answer As Patients
    Send Symptom Questionnaire To Patient
    ...    ${patient_id1}
    ...    ${baseline_quest}
    ...    ${current_date_scheduled}
    ...    ${current_date_scheduled}
    Send Symptom Questionnaire To Patient
    ...    ${patient_id2}
    ...    ${treatment_visit_quest}
    ...    ${tomorrow_date_scheduled}
    ...    ${current_date_scheduled}
    Send Symptom Questionnaire To Patient
    ...    ${patient_id3}
    ...    ${clinic_appointment_quest}
    ...    ${after_2_days_scheduled}
    ...    ${current_date_scheduled}
    Close Browser
    Login As Patient    ${patient_email1}
    Answer Latest Symptom Questionnaire With Other Symptom    new_entry    Mild
    Close Browser
    Login As Patient    ${patient_email2}
    Answer Latest Symptom Questionnaire With Other Symptom    new_entry    Severe
    Close Browser
    Login As Patient    ${patient_email3}
    Answer Latest Symptom Questionnaire With No Symptoms
    Close Browser

Create Patients And Clean Care Team As Test Setup
    Add An Activated Patient Under Default Clinic    f01n13-1    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N13_MAIN}
    Set Test Variable    ${patient_id1}    ${patient_id}
    Set Test Variable    ${patient_email1}    ${patient_email}
    Add An Activated Patient Under Default Clinic    f01n13-2    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N13_MAIN}
    Set Test Variable    ${patient_id2}    ${patient_id}
    Set Test Variable    ${patient_email2}    ${patient_email}
    Add An Activated Patient Under Default Clinic    f01n13-3    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F01N13_MAIN}
    Set Test Variable    ${patient_id3}    ${patient_id}
    Set Test Variable    ${patient_email3}    ${patient_email}
    Set Questionnaires Dates    date_format=%Y-%m-%d    date_format_scheduled=%Y-%m-%d
    Login As Nurse
    Close Open Cases Per Care Team
    ...    ${automated_tests_clinic}[f01n13_care_team_main]
    ...    tab=Patient Reports
    ...    questionnaire_type=Symptom questionnaire
    Close Browser

Delete All Patients As Teardown
    Remove Patient As Test Teardown
    ...    ${patient_email1}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    Remove Patient As Test Teardown
    ...    ${patient_email2}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
    Remove Patient As Test Teardown
    ...    ${patient_email3}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}
