*** Settings ***
Documentation       F15CU02 Clinic user can add note to a case
...                 Preconditions:
...                 - User is logged in.
...                 - Case is created for a patient.

Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource

Suite Setup         Run Keywords    Set Libraries Order
...                     AND    Close Open Cases    643643
Test Setup          Login As Nurse
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15cu02    clinic-web


*** Test Cases ***
Create a case note
    [Tags]    nms9-ver-315
    Set Case Details    Referrals
    Open A New Case    ${f15cu02_patient}[ssn]    save
    Create Case Note    Test Topic    Test case note
    Wait Until Page Contains    Note added
    Create Case Note    Test Topic 2    Latest case note
    Wait Until Page Contains    Note added

Extension A - Edit case note
    [Tags]    nms9-ver-316
    Set Case Details    Pre-Test Instructions
    Open A New Case    ${f15cu02_patient}[ssn]    save
    Create Case Note    Test Topic    Test case note
    Edit Case Note    Edited Note Topic    Edited case note content
    Wait Until Page Contains    Note updated

Extension B - Set a case reminder
    [Tags]    nms9-ver-317
    Set Case Details    Paperwork    care_team=f15cu02 care team
    Open A New Case    ${f15cu02_patient}[ssn]    save
    Reminder Date For Tomorrow
    Create Case Note    Test Topic    Test case note    reminder=${tomorrow}
    Check If Case Reminder Is Unavailable
    No Case Is Displayed For Patient


*** Keywords ***
Set Case Details
    [Arguments]    ${case_type}    ${care_team}=Care Team 1
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=${care_team}
    ...    assigned_to=Clinic User    case_description=Test case description    case_priority=Medium
    ...    case_origin=Patient Initiated Call    case_status=New    case_notes=Test case notes
    ...    case_outcome=Provider Consulted
    Set Test Variable    &{case_details}

No Case Is Displayed For Patient
    Return To Patients
    Go To Work Queue
    Sleep    1
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    f15cu02 care team
    Sleep    1
    Page Should Not Contain    f15cu02 usecase

Reminder Date For Tomorrow
    ${date}    Get Current Date
    ${tomorrow}    Add Time To Date    ${date}    1 day    result_format=%d.%m.%Y
    Set Test Variable    ${tomorrow}
