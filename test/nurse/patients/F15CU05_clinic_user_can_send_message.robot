*** Settings ***
Documentation       F15CU05 Clinic user can send a message to patient related to a case
...                 Preconditions:
...                 - Case management is enabled for clinic.
...                 - Case is created for an active patient account.

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource

Suite Setup         Run Keywords    Set Libraries Order    AND    Login As Nurse
Suite Teardown      Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15cu05    clinic-web


*** Variables ***
${patients-search}                  id:input-search-ssn-search
${case1-title}                      (//div[contains(@class, 'case-title')])[1]
${msg-unread-element}               css:.patient-unread-message
${unread-text}                      unread
${case-subtitle-element4}           (//div[contains(@class,'subtitle')])[4]
${patient-clinic-button}            navigation-clinic-link
${patient-top-msg}                  (//div[@class='icons-area-outer'])[1]
${contact_patient_button_top}       //*[@data-testid="contact-patient-btn"]


*** Test Cases ***
Clinic user can send a message to patient related to a case
    [Tags]    nms9-ver-322
    Create New Case    Appointment Questions
    ${current_date}    Get Current Date
    Contact Patient Per Case    Instructions    message_template=none    message=Contacted patient ${current_date}
    Message is sent to patient and it is displayed in the case
    Until the patient opens the message, message is indicated to be unread


*** Keywords ***
Open case details from patient cases
    Wait Until Keyword Succeeds    9    1    Click Element    ${patients-search}
    Wait Until Keyword Succeeds    9    3    Press Keys    ${patients-search}    ${CLINIC_PATIENT_2.ssn}    RETURN
    Wait Until Location Contains    handle-patient
    Wait Until Page Contains    ${CLINIC_PATIENT_2.ssn}
    Element Text Should Be    id:patient-name    ${CLINIC_PATIENT_2.first_name} ${CLINIC_PATIENT_2.last_name}
    Select First Open Case From List

Select Contact the patient
    Wait Until Element Is Visible    ${case1-title}
    Wait Until Keyword Succeeds    9    1    Click Button    ${contact_patient_button_top}

Select Send
    Wait Until Keyword Succeeds    9    1    Click Button    id:send-contact

Message is sent to patient and it is displayed in the case
    Reload Page
    Wait Until Page Contains    ${random_string}

Until the patient opens the message, message is indicated to be unread
    Wait Until Element Is Visible    ${msg-unread-element}
    Wait Until Element Contains    ${msg-unread-element}    ${unread-text}
    Patient Reads New Message
    Nurse Vierifies Message Read

Patient Reads New Message
    Login As Patient    email=${f15cu05_patient}[email]
    Try To Click Element    ${patient-clinic-button}
    Try To Click Element    ${patient-top-msg}
    Wait Until Page Contains    ${random_string}

Nurse Vierifies Message Read
    Switch Browser    1
    Reload Page
    Wait Until Element Is Visible    ${case-subtitle-element4}
    Wait Until Page Contains    Message read by patient
    Element Should Not Be Visible    ${msg-unread-element}

Create New Case
    [Arguments]    ${case_type}
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=Care Team 1
    ...    assigned_to=Clinic User    case_description=Test case description    case_priority=Medium
    ...    case_origin=Patient Initiated Call    case_status=New    case_notes=Test case notes
    ...    case_outcome=Provider Consulted
    Set Test Variable    &{case_details}
    Open A New Case    ${f15cu05_patient}[ssn]    save
