*** Settings ***
Documentation       F02N09 Nurse can manage patient treatment modules
...                 - The nurse is logged in to Noona.
...                 - A patient exists in the system.
...                 - At least one questionnaire template is created for clinic.

Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n09    clinic-web


*** Variables ***
${template}                         Auto test template
${template_treatment_module}        Acute Leukemia and MDS
${template_care_team}               Care Team 1
${template_clinic_user}             Clinic User
${template_questionnaire}           Baseline questionnaire
${module}                           Immuno-oncological pharmacotherapy
${schedule_content}                 Treatment visit
${patient_module}                   Bone radiotherapy
${patient_careteam}                 Care Team 2
${multi_ques_treatment_module}      Kidney cancer symptoms
${multi_aeq_qol_template}           Multiple AEQ QOL Template
${ques_name_displayed}              //*[@data-testid="inquiry-aeq-type"]
@{RANDOM_QUESTIONNAIRES}            Baseline questionnaire
...                                 Treatment visit
...                                 Clinic appointment
...                                 Follow-up
...                                 Status check
...                                 WHO-5
...                                 SOC-13
...                                 ACT Patient Survey


*** Test Cases ***
Add And Remove Module With A Schedule Template
    [Tags]    nms9-ver-153    nms9-ver-154
    Add An Activated Patient Under Default Clinic
    ...    withTemplate
    ...    module=${CHEMO_18_SYMPTOMS}
    Search Patient By Identity Code    ${patient_ssn}
    Add Treatment Module    True    ${template}    True
    Verify Template Content And Save
    ...    ${template_treatment_module}
    ...    ${template_care_team}
    ...    ${template_clinic_user}
    ...    ${template_questionnaire}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B — Close treatment module
    [Tags]    nms9-ver-155
    Add An Activated Patient Under Default Clinic
    ...    closeModule
    ...    module=${CHEMO_18_SYMPTOMS}
    Search Patient By Identity Code    ${patient_ssn}
    Add Treatment Module    True    ${template}    True
    Verify Template Content And Save
    ...    ${template_treatment_module}
    ...    ${template_care_team}
    ...    ${template_clinic_user}
    ...    ${template_questionnaire}
    Close And Verify Module Moved To Closed    ${template_treatment_module}    ${current_date}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension C — Add module without a schedule template
    [Tags]    nms9-ver-156-1    nms9-ver-156
    Add An Activated Patient Under Default Clinic
    ...    withoutTemplate
    ...    module=${BONE_RADIOTHERAPY}
    Search Patient By Identity Code    ${patient_ssn}
    Add Treatment Module    False    ${module}    True    ${template_care_team}    ${schedule_content}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Nurse Can Add Or Remove Care Team To Patient Module
    [Documentation]    tc purpose is to check that Reports and Messages checkboxes are
    ...    enabled and clickable when another team is added for patient
    [Tags]    nms9-ver-156-2    nms9-ver-15    nms9-ver-15-2    defect
    Add An Activated Patient Under Default Clinic
    ...    defect-156-2
    ...    module=${BONE_RADIOTHERAPY}
    Search Patient By Identity Code    ${patient_ssn}
    Add A Second Care Team To Patient Main Module
    ...    ${patient_module}
    ...    ${template_care_team}
    Page Should Contain    ${template_care_team}
    Page Should Contain    ${patient_careteam}
    Remove Second Care Team From Patient Main Module    Bone radiotherapy
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension D — Move the rest review and send dates X days forward / backward
    [Documentation]    "2 questionnaires" schedule template should be set to be sent 1 day after treatment date
    [Tags]    nms9-ver-157-1    nms9-ver-157
    Add An Activated Patient Under Default Clinic
    ...    f02n09-extd
    ...    module=${CHEMO_11}
    Search Patient By Identity Code    ${patient_ssn}
    Edit Module    ${CHEMO_11}[name]
    Use Template For Scheduled Content    2 Questionnaires
    ${today}    Get Current Date
    ${scheduled_date}    Add Time To Date    ${today}    7 days    result_format=%-d.%-m.%Y
    ${forward_one_day}    Add Time To Date    ${today}    9 days    result_format=%d.%m.%Y
    ${backward_one_day}    Add Time To Date    ${today}    7 days    result_format=%d.%m.%Y
    Select Current Date For Scheduled Template    ${scheduled_date}
    Try To Click Element    ${scheduled_template_apply_button}
    Move Review Date    ${forward_one_day}
    Checkbox Appears After Moving Date    forward
    Scroll Element Into View    ${move_the_review_and_send_dates_checkbox}
    Try To Click Element    ${move_the_review_and_send_dates_checkbox}
    Questionnaires Review Dates Are Moved    forward
    Try To Click Element    ${move_the_review_and_send_dates_checkbox}    # unclicks checkbox to reset
    # should backward one day since template sends questionnaire one day after treatment date
    Move Review Date
    ...    ${backward_one_day}
    Checkbox Appears After Moving Date    backward
    Try To Click Element    ${move_the_review_and_send_dates_checkbox}
    Questionnaires Review Dates Are Moved    backward
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved
    Try To Click Banner Message
    Questionnaire Treatment Review Date Is Correct    ${BASELINE_QUESTIONNAIRE}    ${backward_date}
    Questionnaire Sending Date Is Correct    ${BASELINE_QUESTIONNAIRE}    ${backward_date}
    Questionnaire Treatment Review Date Is Correct    ${QUALITY_OF_LIFE_QUESTIONNAIRE}    ${original_back_send_date}
    Questionnaire Sending Date Is Correct    ${QUALITY_OF_LIFE_QUESTIONNAIRE}    ${original_back_send_date}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extention D - Nurse Can See And Edit Module Template Questionnaires
    [Documentation]    This tc was added due to a defect NOONA-24230
    ...    New Module template i.e 'Multiple AEQ QOL Template' was added to clinic for this tc
    [Tags]    nms9-ver-157-2    nms9-ver-157    defect
    Add An Activated Patient Under Default Clinic
    ...    f02n09-d
    ...    module=${CHEMO_18_SYMPTOMS}
    Search Patient By Identity Code    ${patient_ssn}
    Add Treatment Module    True    ${multi_aeq_qol_template}    True
    Verify Multi-Questionnaire Template Content Before Editing
    Nurse Edits Module Template Questionnaires And Clicks Save
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension E — Schedule module start and end
    [Tags]    nms9-ver-158
    Add An Activated Patient Under Default Clinic
    ...    moduleStart
    ...    module=${CHEMO_18_SYMPTOMS}
    Search Patient By Identity Code    ${patient_ssn}
    Add Treatment Module    True    ${template}    False
    Verify Template Content And Save
    ...    ${template_treatment_module}
    ...    ${template_care_team}
    ...    ${template_clinic_user}
    ...    ${template_questionnaire}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Checkbox Appears After Moving Date
    [Arguments]    ${move_date}
    ${move}    Set Variable If    '${move_date}'=='forward'    forward
    ...    '${move_date}'=='backward'    back
    Wait Until Page Contains    Move the remaining review and send dates ${move}
    Wait Until Page Contains    one day

Questionnaires Review Dates Are Moved
    [Arguments]    ${move_date}
    Wait Until Element Is Visible    ${2_questionnaires_sending_date_2}
    ${treatment_date1}    Get Value    ${2_questionnaires_treatment_date_1}
    ${treatment_date2}    Get Value    ${2_questionnaires_treatment_date_2}
    ${sending_date1}    Get Value    ${2_questionnaires_sending_date_1}
    ${sending_date2}    Get Value    ${2_questionnaires_sending_date_2}
    ${today}    Get Current Date
    # send date when move backward
    ${original_back_send_date}    Add Time To Date
    ...    ${today}
    ...    5 days
    ...    result_format=${date_format}
    # original send date
    ${original_send_date}    Add Time To Date
    ...    ${today}
    ...    6 days
    ...    result_format=${date_format}
    # send date when move forward
    ${original_fwd_send_date}    Add Time To Date
    ...    ${today}
    ...    7 days
    ...    result_format=${date_format}
    # treatment/review date when move backward
    ${backward_date}    Add Time To Date
    ...    ${today}
    ...    7 days
    ...    result_format=${date_format}
    # original treatment send date after applying template
    ${original_date}    Add Time To Date
    ...    ${today}
    ...    8 days
    ...    result_format=${date_format}
    # treatment/review date when move forward
    ${forward_date}    Add Time To Date
    ...    ${today}
    ...    9 days
    ...    result_format=${date_format}
    Set Test Variable    ${backward_date}
    Set Test Variable    ${original_back_send_date}
    Set Test Variable    ${original_date}
    Set Test Variable    ${original_send_date}
    IF    '${move_date}'=='forward'
        Should Be Equal    ${treatment_date1}    ${forward_date}
        Should Be Equal    ${treatment_date2}    ${forward_date}
        Should Be Equal    ${sending_date1}    ${original_fwd_send_date}
        Should Be Equal    ${sending_date2}    ${original_fwd_send_date}
    END
    IF    '${move_date}'=='backward'
        Should Be Equal    ${treatment_date1}    ${backward_date}
        Should Be Equal    ${treatment_date2}    ${backward_date}
        Should Be Equal    ${sending_date1}    ${original_back_send_date}
        Should Be Equal    ${sending_date2}    ${original_back_send_date}
    END

Verify Multi-Questionnaire Template Content Before Editing
    Wait Until Page Contains Element    ${treatment_module_selector}
    ${selected_module}    Get Text    ${treatment_module_selector}
    Scroll Element Into View    ${add_treatment_module_cancel_button}
    ${selected_care_team}    Get Text    ${responsible_care_team_dropdown}
    Wait Until Element Is Visible    ${responsible_nurse_caredown}
    ${selected_nurse}    Get Text    ${responsible_nurse_caredown}
    Should Be Equal    ${selected_module}    ${multi_ques_treatment_module}
    Should Be Equal    ${selected_care_team}    ${template_care_team}
    Should Be Equal    ${selected_nurse}    ${template_clinic_user}
    Page Should Contain Element    ${ques_name_displayed}//div[normalize-space(text())="${BASELINE_QUESTIONNAIRE}"]
    Page Should Contain Element    ${ques_name_displayed}//div[normalize-space(text())="${TREATMENT_VISIT}"]
    Page Should Contain Element    ${ques_name_displayed}//div[normalize-space(text())="${CLINIC_APPOINTMENT}"]
    Page Should Contain Element    ${ques_name_displayed}//div[normalize-space(text())="${FOLLOW_UP}"]
    Page Should Contain Element    ${ques_name_displayed}//div[normalize-space(text())="${STATUS_CHECK}"]
    Page Should Contain Element
    ...    ${ques_name_displayed}//div[normalize-space(text())="${QUALITY_OF_LIFE_QUESTIONNAIRE}"]
    Page Should Contain Element    ${ques_name_displayed}//div[normalize-space(text())="${PATIENTS_COME_FIRST}"]
    Page Should Contain Element    ${ques_name_displayed}//div[normalize-space(text())="${COMPASS}"]

Nurse Edits Module Template Questionnaires And Clicks Save
    [Documentation]    This kw tries to edit a randomly selected questionnaire before adding module to patient
    ...    This is checked because of defect NOONA-24230
    ...    There are only five symptom questionnaires and lots of qol's, so i just used three qols in the list
    ...    to locator of a scheduled quest in the module are differentiated by an index between 0 - 7
    ...    added a step to generate the index to help randomly to select a specific questionnaire for test run
    ...    logged the generated index to help during debugging
    ${random_number}    Evaluate    random.randint(0, 7)    modules=random
    Log    ${random_number}
    ${random_ques_field}    Set Variable
    ...    //*[@id="symptom-inquiry_${random_number}"]/formly-group/formly-field/formly-group/formly-field[1]/formly-wrapper-field
    FOR    ${questionnaire}    IN    @{RANDOM_QUESTIONNAIRES}
        Edit Questionnaire In Randomly Selected Field    ${random_ques_field}    ${questionnaire}
    END
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module added
    Try To Click Banner Message

Edit Questionnaire In Randomly Selected Field
    [Documentation]    Tries to edit the questionnaire in the dynamically selected field with the current questionnaire from the list.
    [Arguments]    ${random_ques_field}    ${questionnaire}
    Try To Click Element    ${random_ques_field}    wait_in_seconds=9x
    Try To Input Text    ${random_ques_field}//input    ${questionnaire}
    Text Should Not Be In The Page    No results
    Click Questionnaire From List    ${questionnaire}
