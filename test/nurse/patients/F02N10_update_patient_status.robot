*** Settings ***
Documentation       F02N10 Nurse can update patient status
...                 - Patient status feature is enabled in clinic settings.

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse    email=<EMAIL>
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n10    clinic-web


*** Variables ***
${general_information_tab}              tab-general-information
${patient_cases_tab}                    tab-patient-messaging
${my_health_status_dropdown}            my-health-status
${family_resource_dropdown}             family-resource-guide
${education_module_value_1}             //input[contains(@value, "educationModel1")]/..
${myhealth_status_active_value}         active
${myhealth_status_offered_value}        offered
${family_resource_sent_value}           sentByClinic
${family_resource_declined_value}       declined
${save_button}                          save-patient
${myhealth_status}                      //div[contains(text(), "myHealth status")]/following::div[1]
${family_resource_status}               //div[contains(text(), "Family resource guide")]/following::div[1]
${completed_education_modules}          //div[contains(text(), "Completed education modules")]/following::div[1]


*** Test Cases ***
Nurse Can Update Patient Status
    [Tags]    nms9-ver-159
    Search Patient By Identity Code    ***********
    Go To General Information
    Set Patient Status Values
    Save Status
    Go To Patient Cases Tab
    Check Patient Status From Background Information
    Go To General Information
    Change Patient Status Values
    Save Status
    Go To Patient Cases Tab
    Check Changed Patient Status From Background Information


*** Keywords ***
Login To Clinic And Create New Patient
    Login As Nurse    email=<EMAIL>
    Update Patient Data With Random Data
    Nurse.Patients.Create Patient    &{patient_dict}

Save Status
    Try To Click Element    ${save_button}
    Wait Until Page Contains    Patient updated
    Try To Click Banner Message

Set Patient Status Values
    Try To Select By Value    ${my_health_status_dropdown}    ${myhealth_status_active_value}
    Try To Select By Value    ${family_resource_dropdown}    ${family_resource_sent_value}
    Try To Click Element    ${education_module_value_1}

Go To General Information
    Try To Click Element    ${general_information_tab}

Go To Patient Cases Tab
    Try To Click Element    ${patient_cases_tab}

Check Patient Status From Background Information
    Wait Until Page Contains Element    ${myhealth_status}
    ${status}    Run Keyword And Return Status
    ...    Element Should Contain
    ...    ${completed_education_modules}
    ...    Education module 1
    IF    ${status}==${FALSE}
        Go To General Information
        Set Patient Status Values
        Save Status
        Go To Patient Cases Tab
        Wait Until Page Contains Element    ${myhealth_status}
        Element Should Contain    ${myhealth_status}    Active
        Element Should Contain    ${family_resource_status}    Sent by clinic
        Element Should Contain    ${completed_education_modules}    Education module 1
    ELSE
        Element Should Contain    ${myhealth_status}    Active
        Element Should Contain    ${family_resource_status}    Sent by clinic
    END

Change Patient Status Values
    Try To Select By Value    ${my_health_status_dropdown}    ${myhealth_status_offered_value}
    Try To Select By Value    ${family_resource_dropdown}    ${family_resource_declined_value}
    Try To Click Element    ${education_module_value_1}

Check Changed Patient Status From Background Information
    Wait Until Page Contains Element    ${myhealth_status}
    Element Should Contain    ${myhealth_status}    Offered
    Element Should Contain    ${family_resource_status}    Declined
    Element Should Contain    ${completed_education_modules}    No information provided
