*** Settings ***
Documentation       F03N04 Nurse can exchange messages with a patient in a discussion the nurse initiated
...                 Preconditions:
...                 - Contact clinic feature must be enabled in clinic settings.
...                 Note:
...                 - Test case 1 and 2 should be run in sequence

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_education.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Setup          Set Email And Date
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03n04    clinic-web


*** Variables ***
${message_date_format}                              %d%m%Y%H%M
${message_template_content}                         This is testing template for automation
${message_template_with_docs_content}               Template with patient education documents
${added_text_to_template}                           Added Text To Template
${message_content_before_adding_locked_template}    Message content before adding locked template
${locked_message_template_content}
...                                                 This template has been locked by the admin and should be uneditable.
${echocardiogram_document_name}                     Echocardiogram (Echo)
${trastuzumab-dkst_document_name}                   Trastuzumab-dkst (Ogivri)
${unread_message}                                   Message unread by patient
${unread_envelope}                                  //*[contains(@class,'patient-unread-message__container')]//*[contains(@class,'patient-unread-message__icon')]
${read_message}                                     Message read by patient
${read_envelope}                                    //*[contains(@class,'patient-read-message__container')]//*[contains(@class,'patient-read-message__icon')]


*** Test Cases ***
Main success scenario - Nurse initiates a discussion with a patient
    [Documentation]    Also includes: Extension A - View message or document status
    [Tags]    nms9-ver-179-1    nms9-ver-179    nms9-ver-366
    Add An Activated Patient Under Default Clinic
    ...    f03n04-1
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}
    # nms9-ver-179
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Contact Patient    test f03n04 ${today}    Question
    Try To Click Banner Message
    Select First Open Case From List
    Wait Until Element Contains    ${patient_message_title}    test f03n04 ${today}
    Wait Until Page Contains    ${random_string}
    # nms9-ver-366 (next 2 lines)
    Wait Until Element Is Visible    ${message_unread_text_element}
    Wait Until Element Is Visible    ${message_unread_envelop_icon}
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${automated_tests_clinic}[name]
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    Close Browser
    # nms9-ver-366
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Close Clinic Message
    Close Browser
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Select First Open Case From List
    Wait Until Element Is Visible    ${message_read_text_element}
    Wait Until Element Is Visible    ${message_read_envelop_icon}
    Element Should Not Be Visible    ${message_unread_text_element}
    Element Should Not Be Visible    ${message_unread_envelop_icon}
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main success scenario - Nurse initiates a discussion with a patient - With Message Template
    [Tags]    nms9-ver-179-2    nms9-ver-179
    Add An Activated Patient Under Default Clinic
    ...    f03n04-2
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Contact Patient    test f03n04 ${today}    Question    message_template=Testing template
    Try To Click Banner Message
    Sleep    1
    Select First Open Case From List
    Wait Until Element Contains    ${patient_message_title}    test f03n04 ${today}
    Wait Until Page Contains    ${message_template_content}
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${automated_tests_clinic}[name]
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main success scenario - Nurse initiates a discussion with a patient - Message Template With Patient Education Documents
    [Tags]    nms9-ver-179-3    nms9-ver-179
    Add An Activated Patient Under Default Clinic
    ...    f03n04-3
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Contact Patient With Patient Education Documents In Message Template
    ...    test f03n04 ${today}
    ...    Question
    ...    F03N04 Template With Docs
    Input Text    ${contact_message_field}    ${added_text_to_template}    clear=${FALSE}
    Wait Until Element Contains    ${number_of_documents_dropdown_contact_patient}    Documents (15)
    Click Add Documents
    Document Is Selected If Already Attached In The Message
    Error Message Is Displayed When Document Limit Is Reached
    Try To Click Element    ${document_search_cancel_button}
    Try To Click Element    ${send_contact_button}
    Wait Until Page Does Not Contain Element    ${send_contact_button}
    Wait Until Page Contains Element    ${patient-messaging-content}
    Try To Click Banner Message
    Select First Open Case From List
    Wait Until Element Contains    ${patient_message_title}    test f03n04 ${today}
    Wait Until Page Contains    ${message_template_with_docs_content}
    Wait Until Element Contains    ${number_of_documents_dropdown_message_thread}    Documents (15)
    Wait Until Page Contains    ${added_text_to_template}
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New documents from your care team at ${automated_tests_clinic}[name]
    ...    Please log in to read the message and see the documents.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main success scenario - Nurse initiates a discussion with a patient - Locked Message Template With Docs
    [Tags]    nms9-ver-179-4    nms9-ver-179
    Add An Activated Patient Under Default Clinic
    ...    f03n04-4
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Try To Click Element    ${non_case_related_contact_patient_button}
    Try To Input Text    ${contact_message_field}    ${message_content_before_adding_locked_template}
    Contact Patient With Patient Education Documents In Message Template
    ...    test f03n04 ${today}
    ...    Question
    ...    F03N04 Locked Template
    Contact Patient Text Area Is Uneditable
    Page Should Not Contain    ${message_content_before_adding_locked_template}
    ${val}    Get Value    ${contact_message_field}
    Should Be Equal    ${val}    ${locked_message_template_content}
    Try To Click Element    ${send_contact_button}
    Wait Until Page Does Not Contain Element    ${send_contact_button}
    Select First Open Case From List
    Wait Until Element Contains    ${patient_message_title}    test f03n04 ${today}
    Wait Until Element Contains    ${number_of_documents_dropdown_message_thread}    Documents (2)
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New documents from your care team at ${automated_tests_clinic}[name]
    ...    Please log in to read the message and see the documents.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main success scenario - Nurse initiates a discussion with a patient - Remove Patient Education Documents
    [Tags]    nms9-ver-179-5    nms9-ver-179
    Add An Activated Patient Under Default Clinic
    ...    f03n04-5
    ...    mailosaur=${mailosaur_keys}[0]
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Contact Patient With Patient Education Documents In Message Template
    ...    test f03n04 ${today}
    ...    Question
    ...    F03N04 Template With Docs
    Wait Until Element Contains    ${number_of_documents_dropdown_contact_patient}    Documents (15)
    Click Add Documents
    Remove Attached Document From Search Modal    ${echocardiogram_document_name}
    Save Education Document Attachment
    Wait Until Page Does Not Contain    ${echocardiogram_document_name}
    Remove Attached Document From Contact Patient Window    ${trastuzumab-dkst_document_name}
    Try To Click Element    ${send_contact_button}
    Wait Until Page Does Not Contain Element    ${send_contact_button}
    Select First Open Case From List
    Try To Click Element    ${number_of_documents_dropdown_contact_patient}
    Wait Until Page Does Not Contain    ${echocardiogram_document_name}
    Wait Until Page Does Not Contain    ${trastuzumab-dkst_document_name}
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New documents from your care team at ${automated_tests_clinic}[name]
    ...    Please log in to read the message and see the documents.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Reply patient's message in a discussion Clinic user initiated
    [Tags]    nms9-ver-180
    Close Open Cases Per Care Team    f03n04 care team
    Close All Browsers
    Add An Activated Patient Under Default Clinic
    ...    f03n04-6
    ...    mailosaur=${mailosaur_keys}[0]
    ...    subscriber_id=${AUTOMATED_TESTS_SUB_ID_F03N04}
    ...    module=${CHEMO_18_SYMPTOMS}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Contact Patient    test f03n04 ${today}    Question
    Try To Click Banner Message
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message    test f03n04 ${today}
    Reply To Clinic Message    Answer to question. F03N04.
    Login As Nurse
    Select Case Type Filter In Work Queue    Select all
    Unselect My Patients In Filter In Work Queue
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    f03n04 care team
    Remove Selected Primary Providers
    Remove Patient Group Filters In Work Queue
    Wait Until Keyword Succeeds    10s    1s    Patient Card's Color Is Correct    medium
    Select Patient Card    ${first_name}${SPACE}${family_name}
    Contact Patient Per Case    Instructions    message_template=none
    Wait Until Page Contains    ${unread_message}
    Wait Until Page Contains Element    ${unread_envelope}
    Patient Received An Email About A New Message
    ...    ${patient_email}
    ...    New message from your care team at ${automated_tests_clinic}[name]
    ...    Please log in to read the message.
    ...    check_link=${PATIENT_URL_IN_MESSAGE}/patient/
    Login As Patient    ${patient_email}
    Navigate To Clinic
    Select Latest Clinic Message
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Select Latest Message And Check Icon Plus Text
    Close All App Instances
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Inactive patient cannot be contacted
    [Tags]    nms9-ver-181
    Login As Nurse
    Search Patient By Identity Code    ${f03n04_patient_3}[ssn]
    Wait Until Page Contains    The patient has not yet activated the account
    Contact Patient Button Is Disabled


*** Keywords ***
Set Email And Date
    ${today}    Get Current Date    result_format=${message_date_format}
    Set Test Variable    ${today}
    Set Test Variable    @{mailosaur_keys}    3yzqsw2e    1JckMqfiHwhqRzpX
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]

Error Message Is Displayed When Document Limit Is Reached
    Search Document Articles    Head
    Select Education Document By Title     and Neck Cancer: The Basics
    Wait Until Page Contains    ${maximum_attachment_limit_error_message}

Document Is Selected If Already Attached In The Message
    Search Document Articles    Heart
    ${element}    Format String    ${selected_document_table_data}    ${echocardiogram_document_name}
    Wait Until Element Is Visible    ${element}
    ${attr}    Get Element Attribute    ${element}    class
    Should Contain    ${attr}    document-selected
    Wait Until Element Contains    ${attachment_list_in_search_document_modal}    ${echocardiogram_document_name}

Select Latest Message And Check Icon Plus Text
    Select First Case from Patient Cases Tab
    Wait Until Page Contains    ${read_message}
    Wait Until Page Contains Element    ${read_envelope}
