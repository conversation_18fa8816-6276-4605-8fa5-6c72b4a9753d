*** Settings ***
Documentation       F10N02 Nurse can fill a questionnaire on behalf of the patient

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f10n02    clinic-web


*** Variables ***
${urgent_rule_name}     OTHERSYMPTOM04


*** Test Cases ***
Main - Clinic user can fill a questionnaire on behalf of the patient - QOL
    [Documentation]    Checks this functionality by using a QOL questionnaire; HADS
    [Tags]    nms9-ver-260-1    nms9-ver-260
    Add An Activated Patient Under Default Clinic    f10n02
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Add Questionnaire To Schedule    HADS
    compare_questionnaires.Complete Questionnaire    HADS
    compare_questionnaires.Save Questionnaire    HADS
    Questionnaire Filled By Clinic User    HADS
    Close All App Instances
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Main - Clinic user can fill a questionnaire on behalf of the patient - Symptom Questionnaire
    [Documentation]    Checks this functionality by using a Symptom questionnaire; Baseline Questionnaire
    ...    This testcase simulates filling a symptom for Urgent rule
    ...    TODO: Semi-emergency and Emergency rule
    [Tags]    nms9-ver-260-2    nms9-ver-260
    Add An Activated Patient Under Default Clinic    f10n02    module=${UROLOGIC_SURGERY}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Add Questionnaire To Schedule    ${BASELINE_QUESTIONNAIRE}
    Nurse Fills In Baseline Questionnaire With Severe Symptom
    ...    Baseline questionnaire
    ...    Other symptom
    Save Symptom Questionnaire    Baseline questionnaire
    Questionnaire Filled By Clinic User    Baseline questionnaire
    Close Browser
    # nurse logs in again after answering questionnaire to re-test a defect we had in the past
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Go To Patient Cases Tab
    Select Case From Patient Cases Tab    Other symptom
    Nurse sees the name and the description of an urgent rule as a part of the message chain
    Close All App Instances
    [Teardown]    Run Keywords    Close All App Instances    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Nurse Fills In Baseline Questionnaire With Severe Symptom
    [Arguments]    ${questionnaire_type}    ${symptom}    ${next_of_kin}=no
    ${complete_button}    Set Variable
    ...    (//td[text()="${questionnaire_type}"]/following-sibling::td[4]//button[1])[last()]
    Wait Until Page Contains Element    xpath=${complete_button}    60s
    Scroll Element Into View    (//td[text()="${questionnaire_type}"])[last()]
    Try To Click Element    ${complete_button}
    Wait Until Page Contains Element    ${questionnaire_next_button}
    compare_questionnaires.Select Symptom From List    ${symptom}
    Click Element    ${questionnaire_next_button}
    Changes In Gen State Of Health Is Displayed
    questionnaires.Check And Write To Text Area
    Select Answer To Question    When did you have this symptom?    Today
    questionnaires.Select Specific Answer To Question    How would you rate the severity of your symptom?    Severe
    ...    questionnaire=tnonc distress
    questionnaires.Select Specific Answer To Question    Have you used any medication to alleviate your symptoms?    No
    ...    questionnaire=tnonc distress
    Click Element    ${aeq_questionnaire_next_button}
    IF    '${next_of_kin}'=='yes'
        Click Element    ${info_entered_by_caregiver}
    END

Go To Patient Cases Tab
    Try To Click Element    ${patient_cases_tab}

Nurse sees the name and the description of an urgent rule as a part of the message chain
    Wait Until Page Contains    ${urgent_rule_name}
