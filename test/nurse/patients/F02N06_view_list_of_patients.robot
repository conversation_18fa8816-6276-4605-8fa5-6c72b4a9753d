*** Settings ***
Documentation       F02N06 Nurse can view list of patients
...                 Preconditions:
...                 - You are logged into Noona as a nurse.
...                 - At least one patient exists in the system.

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}list_of_patients_tab.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}manage_users.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}nurse_profile.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n06    clinic-web


*** Variables ***
${patient_contact_info_header}      //div[contains(@class, 'patient-contact-info')]
${care_team_5}                      Care Team 5
${care_team_1}                      Care Team 1
${f02n06_care_team}                 f02n06 care team
${f02n06_nurse}                     <EMAIL>
${no_patients_found_text}           No patients found
${1_more_text}                      1 more..
${5_more_text}                      5 more..
${handle_patient_url}               /handle-patient/
${patient_list_url}                 /manage-patients/patient-list
${patient_information_text}         Patient Information


*** Test Cases ***
Nurse can view list of patients - Main
    [Tags]    nms9-ver-146
    Login As Nurse
    Wait Until Noona Loader Is Not Visible
    Navigate To Patient Page
    Go To List Of Patients Tab
    Wait Until Noona Loader Is Not Visible
    Filter Patients By Care Team    ${f02n06_care_team}
    Noona Remembers Filter Selection
    Noona Remembers Filter Selection After Logout and Login
    ${first_ssn}    Get Text    (${list_of_patient_rows})[1]/td[1]
    Nurse Can Click One Of The Patient Rows
    Nurse Is Redirected To General Information Tab
    Element Should Contain    ${patient_contact_info_header}    ${first_ssn}
    Return To Patients
    Nurse Is Redirected To List Of Patients Tab
    Close Browser

View patients of selected teams or own patients - ext. A
    [Tags]    nms9-ver-147
    Login As Nurse    ${f02n06_nurse}
    Wait Until Noona Loader Is Not Visible
    Navigate To Patient Page
    Go To List Of Patients Tab
    Get SSN List
    Nurse's Care Teams Are Selected By Default
    Nurse Can Select And Unselect Care Teams
    Set Test Variable    ${ssn_list_before_filter_update}    ${ssn_list}
    Nurse Can View Other Team's Patients
    Table Is Updated With The Care Team Selection
    Close Browser

View patients with selected status - ext. B
    [Tags]    nms9-ver-148
    Login As Nurse    ${f02n06_nurse}
    Wait Until Noona Loader Is Not Visible
    Navigate To Patient Page
    Go To List Of Patients Tab
    Get Selected Status
    Should Be Equal    ${selected_status_list}[0]    ${username_locked_status}
    Should Be Equal    ${selected_status_list}[1]    ${5_more_text}
    Try To Click Element    ${patient_list_status_dropdown}
    Only Account Statuses Are Selected
    All Account Status Are Unselected When Additional Status Is Selected
    Only One Additional Status Can Be Selected At A Time
    Clicking Select all Selects Only All Account Statuses
    Clicking Remove all Unselects All Statuses
    Click Option From Patient List Filter    ${no_scheduled_questionnaires_status}
    Try To Click Element    ${patient_list_status_dropdown}
    Other Statuses Are Not Displayed
    Only Username Active And User Locked Are Displayed
    Close Browser


*** Keywords ***
Noona Remembers Filter Selection
    ${count_before_navigation}    Get Element Count    ${list_of_patient_rows}
    Go To Work Queue
    Wait Until Noona Loader Is Not Visible
    Navigate To Patient Page
    Go To List Of Patients Tab
    Wait Until Element Is Visible    ${selected_care_teams}
    Wait Until Page Does Not Contain    ${no_patients_found_text}
    Sleep    1
    ${selected}    Get Text    ${selected_care_teams}
    ${count_after_navigation}    Get Element Count    ${list_of_patient_rows}
    Should Be Equal    ${selected}    ${f02n06_care_team}
    Should Be Equal    ${count_before_navigation}    ${count_after_navigation}

Noona Remembers Filter Selection After Logout and Login
    ${count_before_logout}    Get Element Count    ${list_of_patient_rows}
    ${care_team_before_logout}    Get Text    ${selected_care_teams}
    Clinic User Clicks Log Out
    Wait Until Page Contains    ${welcome_to_noona_text}
    Login To Noona    nurse    ${automated_tests_clinic}[default_user]    False
    Keep Me Logged In    Yes
    Navigate To Patient Page
    Go To List Of Patients Tab
    Sleep    1
    ${count_after_logout}    Get Element Count    ${list_of_patient_rows}
    ${care_team_after_logout}    Get Text    ${selected_care_teams}
    Should Be Equal    ${count_before_logout}    ${count_after_logout}
    Should Be Equal    ${care_team_before_logout}    ${care_team_after_logout}

Nurse's Care Teams Are Selected By Default
    [Documentation]    Care Team 5 and f02n06 care teams are assigned to the current user
    Wait Until Element Is Visible    ${selected_care_teams}
    Get Selected Care Teams
    Should Be Equal    ${selected_care_team_list}[0]    ${care_team_5}
    Should Be Equal    ${selected_care_team_list}[1]    ${1_more_text}
    Try To Click Element    ${patient_list_care_team_dropdown}
    List Of Patients Filter Is Selected    ${care_team_5}
    List Of Patients Filter Is Selected    ${f02n06_care_team}

Nurse Can Select And Unselect Care Teams
    Click Option From Patient List Filter    ${care_team_5}
    List Of Patients Filter Is Unselected    ${care_team_5}
    Click Option From Patient List Filter    ${care_team_1}
    List Of Patients Filter Is Selected    ${care_team_1}

Nurse Can View Other Team's Patients
    Try To Click Element    ${patient_list_care_team_dropdown}
    Wait Until Page Contains    ${view_patients_outside_noona_text}
    Wait Until Element Is Enabled    ${view_other_team_also_button}
    Try To Click Element    ${view_other_team_also_button}

Table Is Updated With The Care Team Selection
    Get Selected Care Teams
    Should Be Equal    ${selected_care_team_list}[0]    ${f02n06_care_team}
    Should Be Equal    ${selected_care_team_list}[1]    ${1_more_text}
    Sleep    1
    Wait Until Noona Loader Is Not Visible
    Get SSN List
    Set Test Variable    ${ssn_list_update_filter_update}    ${ssn_list}
    Should Not Be Equal    ${ssn_list_before_filter_update}    ${ssn_list_update_filter_update}
    Try To Click Element    ${patient_list_care_team_dropdown}
    List Of Patients Filter Is Selected    ${f02n06_care_team}
    List Of Patients Filter Is Selected    ${care_team_1}

Nurse Can Click One Of The Patient Rows
    Try To Click Element    (${list_of_patient_rows})[1]

Nurse Is Redirected To General Information Tab
    Wait Until Location Contains    ${handle_patient_url}    timeout=5s
    Wait Until Page Contains    ${patient_information_text}    # Verifies that patient is in General Information tab

Nurse Is Redirected To List Of Patients Tab
    Wait Until Location Contains    ${patient_list_url}
    Wait Until Element Is Visible    ${selected_care_teams}
    ${care_team_after_logout}    Get Text    ${selected_care_teams}
    Should Be Equal    ${care_team_after_logout}    ${f02n06_care_team}

Only Account Statuses Are Selected
    Sleep    1
    @{account_status_list}    Create List
    ...    ${username_locked_status}
    ...    ${login_reminder_sent_status}
    ...    ${username_active_status}
    ...    ${username_sent_status}
    ...    ${locked_by_user_status}
    ...    ${email_delivery_problems_status}
    @{additional_status_list}    Create List    ${proxy_patient_status}    ${no_scheduled_questionnaires_status}
    ${count}    Get Length    ${account_status_list}
    FOR    ${item}    IN    @{account_status_list}
        List Of Patients Filter Is Selected    ${item}
    END
    FOR    ${item}    IN    @{additional_status_list}
        List Of Patients Filter Is Unselected    ${item}
    END

All Account Statuses Are Unselected
    @{account_status_list}    Create List
    ...    ${username_locked_status}
    ...    ${login_reminder_sent_status}
    ...    ${username_active_status}
    ...    ${username_sent_status}
    ...    ${locked_by_user_status}
    ...    ${email_delivery_problems_status}
    ${count}    Get Length    ${account_status_list}
    FOR    ${item}    IN    @{account_status_list}
        List Of Patients Filter Is Unselected    ${item}
    END

All Account Status Are Unselected When Additional Status Is Selected
    Filter Patients By Status    ${proxy_patient_status}
    Try To Click Element    ${patient_list_status_dropdown}
    All Account Statuses Are Unselected
    List Of Patients Filter Is Selected    ${proxy_patient_status}
    Get Selected Status
    Should Be Equal    ${selected_status_list}[0]    ${proxy_patient_status}

Only One Additional Status Can Be Selected At A Time
    Click Option From Patient List Filter    ${no_scheduled_questionnaires_status}
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    ${patient_list_status_dropdown}    wait_in_seconds=5x
    Wait Until Element Is Visible    ${noona-loader}
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    ${patient_list_status_dropdown}    wait_in_seconds=7x
    Wait Until Noona Loader Is Not Visible
    List Of Patients Filter Is Selected    ${no_scheduled_questionnaires_status}
    List Of Patients Filter Is Unselected    ${proxy_patient_status}

Clicking Select all Selects Only All Account Statuses
    Try To Click Element    ${select_all_filters_option}
    Try To Click Element    ${patient_list_status_dropdown}
    Try To Click Element    ${patient_list_status_dropdown}
    Only Account Statuses Are Selected

Clicking Remove all Unselects All Statuses
    Try To Click Element    ${remove_all_selections_option}
    Try To Click Element    ${patient_list_status_dropdown}
    Try To Click Element    ${patient_list_status_dropdown}
    All Account Statuses Are Unselected
    List Of Patients Filter Is Unselected    ${no_scheduled_questionnaires_status}
    List Of Patients Filter Is Unselected    ${proxy_patient_status}

Other Statuses Are Not Displayed
    Wait Until Page Does Not Contain    ${username_sent_status}
    Wait Until Page Does Not Contain    ${login_reminder_sent_status}
    Wait Until Page Does Not Contain    ${locked_by_user_status}
    Wait Until Page Does Not Contain    ${email_delivery_problems_status}

Only Username Active And User Locked Are Displayed
    Wait Until Page Contains    ${username_active_status}
    Wait Until Page Contains    ${username_locked_status}
