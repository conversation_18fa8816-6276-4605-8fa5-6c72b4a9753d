*** Settings ***
Documentation       F15CU03 Clinic user can categorize a closed case
...                 Preconditions:
...                 - Clinic user is logged in
...                 - The "Case outcome" setting is enabled in the Case management settings of the clinic

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15cu03    clinic-web


*** Variables ***
${case_date_elements}       //div[contains(@class, 'patient-case-item-bar')]
${case_outcome}             Provider Consulted
${delay_reason}             High Call Volume
${close_case_button}        //*[@data-testid='close-case-button']
${case_outcome_detail}      //div[text()='Case outcome']/../..
${delay_reason_label}       //div[text()='Reason for delay']/../..
${f01n13_care_team}         nms9-ver-84-defect


*** Test Cases ***
Main - Clinic User Can Close A Case With Case Outcome
    [Tags]    nms9-ver-318
    Login As Nurse    ${appointment_clinic}[user_email]
    Set Case Details    ${case_type_paperwork}
    Open A New Case    ${f01n13_patient}[ssn]    save
    Close Case With Outcome    ${case_outcome_left_message}
    Case Is Not Expanded
    Case Status Is Correct    ${case_type_paperwork}    ${case_status_closed}
    Select Case From List    ${case_type_paperwork}    ${case_status_closed}
    Case Outcome Is Displayed In Case Details    ${case_outcome_left_message}

Extension A - Clinic User Gives A Reason For Delayed Case Handling
    [Documentation]    [file:F15NA02.robot|F15NA02 Noona admin can manage case escalation].
    ...    "Case outcome" setting should be enabled in the Case management settings of the clinic
    ...    The tc patient should always have 'open cases' that has been created earlier. This is to
    ...    ensure that when script tries to close the first open case in wq 'Reason For delay'
    ...    question is asked
    ...    This case uses TA clinic Appointment clinic - this is the clinic that has the clinic settings
    [Tags]    nms9-ver-319
    [Setup]    Create A High Priority Case
    Login As Nurse    ${appointment_clinic}[user_email]
    Select Case Type Filter In Work Queue    Symptom Management
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    nms9-ver-84-defect
    Maximize Browser Window
    Open First Workqueue Item And Click Close
    Select Case Outcome    ${case_outcome}
    Select Reason For Delay    ${delay_reason}
    Try To Click Element    ${close_case_modal}
    Case Outcome And Reason For Delay Are Displayed In Details


*** Keywords ***
Set Case Details
    [Arguments]    ${case_type}
    Set Test Variable    @{critical_patient_concerns}    Chest Pain
    &{case_details}    Create Dictionary    case_type=${case_type}    care_team=${f01n13_care_team}
    ...    assigned_to=New Bug    case_description=Test case description
    ...    acute_symptom=${critical_patient_concerns}    case_priority=High
    ...    case_origin=Patient Initiated Call    case_status=New    case_notes=Test case notes
    ...    case_outcome=Provider Consulted
    Set Test Variable    &{case_details}

Open First Workqueue Item And Click Close
    Open First Workqueue Item
    Get Case Date
    Try To Click Element    ${close_case_button}

Case Outcome And Reason For Delay Are Displayed In Details
    Try To Click Element
    ...    ${case_date_elements}//div[contains(text(), "Symptom Management")]/following-sibling::div//span[contains(text(), "${case_date}")]
    Wait Until Element Is Visible    ${case_outcome_detail}
    Element Should Contain    ${case_outcome_detail}    ${case_outcome}
    Element Should Contain    ${delay_reason_label}    ${delay_reason}

Create A High Priority Case
    [Documentation]    Creates case that escalates faster for testing of Extension A
    Login As Nurse    ${appointment_clinic}[user_email]
    Set Case Details    ${case_type_symptom_management}
    Open A New Case    ${f01n13_patient}[ssn]    save
    Wait Until Page Contains    ${case_created_successfully_banner}
    Close Browser
