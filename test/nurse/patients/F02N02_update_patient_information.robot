*** Settings ***
Documentation       F02N02 Nurse can update patient information
...                 Preconditions:
...                 - Nurse is logged in to Noona nurse section.
...                 - Patient exists in the system.
...                 - Open case exists in the clinic work queue.
...                 Note: This test case uses a specific patient.
...                 Please don't modify the patient for other purposes.

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources/nurse/general_information.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n02    clinic-web


*** Variables ***
${label-treatment-module-saved}     Treatment module saved
${label-patient-updated}            Patient updated
${add-nurse-link}                   //*[@data-testid="module-responsible-users-add-button"]
${nurse-dropdown}                   //*[@data-testid="responsible-nurse"]
${update_care_team_dropdown}        //*[@data-testid="responsible-unit"]/descendant::input
${care_team_name}                   f02n02 care team
${update_to_care_team}              Care Team 2
${save_patient_button}              save-patient
${email_field}                      //*[@data-testid='emailAddress']
${original_nurse}                   f02n02 nurse
${line_of_treatment_link}           lineOfTreatment
${edit_info}                        //*[@data-testid='edit-patient-button']
${patient_acct_email_field}         //*[@data-testid='patientAccountEmail']
${patient_acct_phone_number}        //*[@data-testid='patientAccountPhoneNumber']
${temp_phone_number}                +********0000
${main_phone_number}                +********
${edit_info_save_button}            //*[@data-testid='send-button']
${email_holder}                     //div[@id="patient-messaging-content"]//div[2]//mat-card/div[3]/div[2]
${phone_holder}                     //*[@id="patient-messaging-content"]//mat-card/div[4]/div[2]


*** Test Cases ***
Main - Nurse Can Update Patient Information
    [Tags]    nms9-ver-142
    Add An Activated Patient Under Default Clinic    f02n02-main    module=${IMMUNOLOGICAL_PHARMACOTHERAPY}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Choose General Information Tab
    Modify patient general information
    Save changes
    General information save confirmation is displayed
    Restore patient general information
    Save changes
    General information save confirmation is displayed
    Navigate to treatment modules tab and add module information
    Save changes module specificly
    Treatment module save confirmation is displayed
    Restore module information
    Save changes module specificly
    Return To Work Queue
    Search Patient By Identity Code    ${patient_ssn}
    Choose General Information Tab
    Modify Patient Details With Edit Info
    Save And Close Edit Info Modal
    Verify Info Was Updated Then Restore Original Data
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

*** Keywords ***
Modify patient general information
    Wait Until Element Is Enabled    ${email_field}
    ${field-email}    Get Value    ${email_field}
    Set Test Variable    ${test-email}    ${field-email}
    ${temp_email_first_part}    Generate Random String    chars=[LOWER]
    Set Test Variable    ${temp_email_first_part}
    Input Text    ${email_field}    ${temp_email_first_part}@CAPSLO.ch
    Wait Until Keyword Succeeds
    ...    3
    ...    1
    ...    Textfield Value Should Be
    ...    ${email_field}
    ...    ${temp_email_first_part}@capslo.ch
    ${text}    Get Value    ${email_field}
    Should Be Lower Case    ${text}

Restore patient general information
    Wait Until Keyword Succeeds
    ...    3
    ...    1
    ...    Textfield Value Should Be
    ...    ${email_field}
    ...    ${temp_email_first_part}@capslo.ch
    Wait Until Element Is Enabled    ${email_field}
    Input Text    ${email_field}    ${test-email}

Save changes
    Wait Until Element Is Enabled    ${save_patient_button}
    Try To Click Element    ${save_patient_button}

General information save confirmation is displayed
    Wait Until Element Is Visible    //div[@aria-label='${label-patient-updated}']
    Wait Until Element Is Not Visible    //div[@aria-label='${label-patient-updated}']

Navigate to treatment modules tab and add module information
    Edit Module    Immuno-oncological pharmacotherapy
    Try To Click Element    ${update_care_team_dropdown}/../..
    Try To Input Text    ${update_care_team_dropdown}    ${update_to_care_team}
    Press Keys    ${update_care_team_dropdown}    ENTER
    Wait Until Keyword Succeeds    9    1    Scroll Element Into View    ${line_of_treatment_link}
    Try To Click Element    ${add-nurse-link}
    Try To Click Element    ${nurse-dropdown}/descendant::input
    Input Text    ${nurse-dropdown}/descendant::input    Clinic user
    Press Keys    ${nurse-dropdown}/descendant::input    ENTER

Restore module information
    Edit Module    ${IMMUNO_ONCO_PHARMACOTHERAPY}[name]
    Wait Until Element Is Visible    ${add-nurse-link}
    Try To Click Element    ${nurse-dropdown}/descendant::input
    Input Text    ${nurse-dropdown}/descendant::input    ${original_nurse}
    Press Keys    ${nurse-dropdown}/descendant::input    ENTER
    Try To Click Element    ${update_care_team_dropdown}/../..
    Try To Input Text    ${update_care_team_dropdown}    ${care_team_name}
    Press Keys    ${update_care_team_dropdown}    ENTER

Save changes module specificly
    Try To Click Element    //*[@data-testid="save-treatment-module"]

Treatment module save confirmation is displayed
    Wait Until Element Is Visible    //div[@aria-label='${label-treatment-module-saved}']
    Wait Until Element Is Not Visible    //div[@aria-label='${label-treatment-module-saved}']

Modify Patient Details With Edit Info
    Wait Until Element Is Enabled    ${edit_info}
    Try To Click Element    ${edit_info}
    Wait Until Element Is Enabled    ${patient_acct_email_field}
    Wait Until Element Is Enabled    ${patient_acct_phone_number}
    Clear Element Text    ${patient_acct_email_field}
    ${temp_email_first_part}    Generate Random String    10    chars=[LOWER]
    Set Test Variable    ${temp_email_first_part}
    Input Text    ${patient_acct_email_field}    ${temp_email_first_part}@noona.fi
    Clear Element Text    ${patient_acct_phone_number}
    Input Text    ${patient_acct_phone_number}    ${temp_phone_number}

Save And Close Edit Info Modal
    Wait Until Element Is Enabled    ${edit_info_save_button}
    Try To Click Element    ${edit_info_save_button}
    Wait Until Page Contains    ${account_updated_banner_message}
    Try To Click Banner Message
    Wait Until Page Does Not Contain    ${account_updated_banner_message}
    Wait Until Page Does Not Contain Element    ${loader}    5s

Verify Info Was Updated Then Restore Original Data
    Wait Until Element Is Enabled    ${edit_info}
    Element Should Contain    ${email_holder}    ${temp_email_first_part}@noona.fi
    Element Should Contain    ${phone_holder}    ${temp_phone_number}
    Try To Click Element    ${edit_info}
    Wait Until Element Is Enabled    ${patient_acct_email_field}
    Clear Element Text    ${patient_acct_email_field}
    Input Text    ${patient_acct_email_field}    ${patient_email}
    Clear Element Text    ${patient_acct_phone_number}
    Input Text    ${patient_acct_phone_number}    ${main_phone_number}
    Save And Close Edit Info Modal
    Wait Until Page Does Not Contain    ${account_updated_banner_message}
    Page Should Not Contain    ${temp_email_first_part}@noona.fi
    Page Should Not Contain    ${temp_phone_number}
