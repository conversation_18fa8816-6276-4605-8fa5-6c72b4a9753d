*** Settings ***
Documentation       F02N01 Nurse can register new patient

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}general_information.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}list_of_patients_tab.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource

Suite Setup         Set Libraries Order
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n01    clinic-web


*** Variables ***
${all_patients_filtered}            //*[@id="manage-patients"]//span[text()='All patients']
${care_team_filtered}               //*[@id="manage-patients"]//span[text()='{}']
${care_team_filter}                 //div[@title='{}']
${all_patients_filter}              //label[text()='All patients']/../..
${clear_filter_element}             //span[contains(@class, 'clear-integration-filter-button')]
${candidates_care_team_dropdown}    (//*[@id='candidates__careTeam--filter']//span[contains(@class, 'arrow')])[1]
${tunit}                            666
${care_team}                        Appointment Care Team
${patient_first_name}               Testy Middle
${new_login_email}                  //div[text()="Login email"]/following-sibling::div
${tunit}                            666
${dob}                              1983-03-05
${invitation}                       //*[@data-testid="invite-patient-action"]
${invalid_xter_fname_error}         (//*[@class="error-message"]//formly-validation-message[contains(., "Following characters are not allowed: ?, !")])[1]
${invalid_xter_lname_error}         (//*[@class="error-message"]//formly-validation-message[contains(., "Following characters are not allowed: ?, !")])[2]


*** Test Cases ***
Main Success Scenario - Nurse can register new patient
    [Tags]    nms9-ver-137-1    nms9-ver-137
    Login As Nurse
    Set New Patient Data With Random Data
    Create New Patient With Robot
    Change Patient Password    ${DEFAULT_PASSWORD}
    Wait Until Element Is Visible    ${new_login_email}
    ${new_patient_email}    Get Text    ${new_login_email}
    Close Browser
    Login As Patient    ${new_patient_email}
    Verify Patient Login Is Successful

Main Success Scenario - Nurse can register new SPMC patient
    [Tags]    nms9-ver-137-2    nms9-ver-137
    Login As Nurse    ${spmc_clinic_a}[manager_email]
    Set New Patient Data With Random Data
    Set To Dictionary
    ...    ${patient_dict}
    ...    email
    ...    ${patient_dict}[first name].${patient_dict}[last name]+<EMAIL>
    ...    care team=Care Team 1
    Create New Patient With Robot
    Change Patient Password    ${DEFAULT_PASSWORD}
    Wait Until Element Is Visible    ${new_login_email}
    ${new_patient_email}    Get Text    ${new_login_email}
    Close Browser
    Login As Nurse    ${spmc_clinic_b}[manager_email]
    Create New Multiclinic Patient With Robot    ${spmc_clinic_a}[name]
    Try To Click Banner Message
    Location Should Contain    /modules
    Choose General Information Tab
    Other Clinic Details Are Visible In General Info Tab    ${spmc_clinic_a}[name]    Username active
    ...    ${patient_dict}[first name]${SPACE}${patient_dict}[last name]    ${patient_dict}[date of birth]
    ...    ${patient_dict}[mobile number]

Extension A - Create Patient With Questionnaire Template
    [Tags]    nms9-ver-138
    Create New Patient With Template
    Change Patient Password    ${DEFAULT_PASSWORD}
    Wait Until Element Is Visible    ${new_login_email}
    ${new_patient_email}    Get Text    ${new_login_email}
    Verify Scheduled Questionnaire
    Close Browser
    Login As Patient    ${new_patient_email}
    Verify Patient Login Is Successful

Extension B - Verify patient details and treatment module and send invite to patient
    [Tags]    nms9-ver-139
    Send Candidate Request    ${APPOINTMENT_USER_TOKEN}    ${tunit}
    Login As Nurse    ${appointment_clinic}[user_email]
    Navigate To Candidates Tab
    Wait Until Page Does Not Contain Element    ${loader}    20s
    Remove All Care Team In Candidate Tab
    List Of Patients On Candidates Tab Is Displayed
    Select Patient And Click Invite
    Fill The Missing Information And Click Invite Patient

Extension C — Search for EHR patient and send user account
    [Tags]    nms9-ver-140-1    nms9-ver-140
    Send Candidate Request    ${APPOINTMENT_USER_TOKEN}    ${tunit}
    Login As Nurse    ${appointment_clinic}[user_email]
    Search Patient By Identity Code    ${patient_ssn}
    Wait Until Page Contains    Patient created by the EHR
    Try To Click Element    ${send_user_account_button}
    Fill The Missing Information And Click Invite Patient

Extension C — Candidate Tab To Stick Selected Care Teams
    [Tags]    nms9-ver-140-2    defect    nms9-ver-140
    Login As Nurse    ${appointment_clinic}[user_email]
    Navigate To Candidates Tab
    Select Care Team In Candidate Tab    ${appointment_clinic}[appointment_care_team]
    All Care Team Patients Are Filtered    ${appointment_clinic}[appointment_care_team]
    Go To List Of Patients Tab And Return
    All Care Team Patients Are Filtered    ${appointment_clinic}[appointment_care_team]
    Try To Click Element    ${clear_filter_element}
    All Patients Are Filtered
    Go To List Of Patients Tab And Return
    All Patients Are Filtered

Extension C — Check That Error Message Shows Up When Invited/Candidate Patients Info Contains Invalid Characters
    [Documentation]    tc was added due to NOONA-21974 , NOONA-22035 and NOONA-21834
    ...    Note 1: note: Noona allows special characters to be used with emails
    ...    Note 2: Delete Patient Account kw is updated to handle NOONA-22713
    ...    Added a condition that if confirmation message about Patient deletion did not show up after the deletion,
    ...    script checks if patient is still searchable. Therefore, this will confirm if patient is deleted or not.
    [Tags]    nms9-ver-140-3    defect    nms9-ver-140
    Send Candidate Request With Invalid Name Characters
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${tunit}
    Verify That Error Message Is Displayed - General Information Page
    Try To Click Element    ${back_to_button}
    Verify That Error Message Is Displayed - Invitation Modal
    Correct Candidate Information And Send Invite
    [Teardown]    Extension C - Test Teardown

Extension D — Send Invitation To EHR Patient Existing In Another Via Clinic General Information
    [Documentation]    Note: birtdate and email should match to create a proxy patient then manually send the invite
    ...    If not matching, test will fail and proxy patient with random email will be created
    [Tags]    nms9-ver-141    spmc    api
    ##Create patient in Clinic A
    Add An Activated Patient Under Pendo Clinic    f02n01
    ##Send patient invite in Clinic B - proxy patient
    Create Proxy Via FHIR API EMR Integration
    Login As Nurse    ${appointment_clinic}[user_email]
    Sleep    1
    Search Patient By Identity Code    ${patient_ssn}
    Wait Until Page Contains    Patient created by the EHR
    Try To Click Element    ${send_user_account_button}
    Fill The Missing Information And Click Invite Patient    input_spmc_email=${patient_email}

##TODO: Extension D — Send Invitation To EHR Patient Existing In Another Via Candidates Tab


*** Keywords ***
All Patients Are Filtered
    Wait Until Element Is Visible    ${all_patients_filtered}

All Care Team Patients Are Filtered
    [Arguments]    ${care_team}
    ${care_team}    Format String    ${care_team_filtered}    ${care_team}
    Wait Until Element Is Visible    ${care_team}

List Of Patients On Candidates Tab Is Displayed
    # to make sure that page is completely loaded before the next step
    Wait Until Element Is Visible
    ...    ${candidates_table}
    Wait Until Element Is Visible    ${invitation}
    Wait Until Element Is Enabled    ${invitation}

Select Care Team In Candidate Tab
    [Arguments]    ${care_team}
    Wait Until Element Is Visible    ${candidates_care_team_dropdown}
    Wait Until Element Is Enabled    ${candidates_care_team_dropdown}
    Wait Until Page Does Not Contain Element    ${loader}
    Try To Click Element    ${candidates_care_team_dropdown}
    Try To Click Element    ${remove_all_selections_option}
    ${care_team_name}    Format String    ${care_team_filter}    ${care_team}
    Try To Click Element    ${care_team_name}
    Try To Click Element    ${candidates_care_team_dropdown}
    Wait Until Element Is Not Visible    ${all_patients_filter}

Remove All Care Team In Candidate Tab
    Wait Until Element Is Visible    ${candidates_care_team_dropdown}
    Wait Until Element Is Enabled    ${candidates_care_team_dropdown}
    Try To Click Element    ${candidates_care_team_dropdown}
    Try To Click Element    ${remove_all_selections_option}
    Try To Click Element    ${candidates_care_team_dropdown}
    Wait Until Page Contains    All patients

Go To List Of Patients Tab And Return
    Wait Until Element Is Visible    ${patient_list_tab}
    Wait Until Element Is Enabled    ${patient_list_tab}
    Try To Click Element    ${patient_list_tab}
    Wait Until Page Does Not Contain Element    ${loader}
    Wait Until Page Does Not Contain    No patients found
    Try To Click Element    ${candidates_tab}

Select Patient And Click Invite
    Wait Until Page Contains Element
    ...    //*[contains(text(), '${patient_mrn}')]/../*[@data-testid="invite-patient-action"]
    Page Should Contain Element    //*[contains(text(), '${patient_mrn}')]/../td[1]
    Page Should Contain Element    //*[contains(text(), '${family_name}')]/../td[2]
    Page Should Contain Element    //*[contains(text(), '${patient_first_name}')]/../td[3]
    ${family_name_lower}    Convert To Lower Case    ${family_name}
    Page Should Contain Element    //*[contains(text(), '${patient_email}')]/../td[4]
    Try To Click Element    //*[contains(text(), '${patient_mrn}')]/../*[@data-testid="invite-patient-action"]

Fill The Missing Information And Click Invite Patient
    [Arguments]    ${input_spmc_email}=no
    IF    '${input_spmc_email}'!='no'
        Set Window Size    ${WINDOW_WIDTH}    1000    #needed for script to see the multiclinic pop up contents
        Try To Input Text    ${email_field}    ${input_spmc_email}
        Wait Until Page Contains    Email already used by another clinic. Verify that this is the same patient. Accounts will be linked (1):
        Element Should Contain    ${multi_clinic_pop_container}//span[@class='bold clinic']   ${pendo_test_clinic}[name]
    END
    Fill In ICD, Module And Care Team With Default Values
    Try To Click Element    ${send_invite_button}
    IF    '${input_spmc_email}'=='no'
        Page should not contain    Email is already in use
    END
    Wait Until Page Contains    Invitation sent
    Try To Click Banner Message

Create Proxy Via FHIR API EMR Integration
    ${external_id}    Generate Random String    8
    Set Test Variable    ${external_id}
    ${body_json}    Invite Patient As Candidate With Specific Patient Details
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${tunit}

Verify That Error Message Is Displayed - General Information Page
    Login As Nurse    ${appointment_clinic}[user_email]
    Search Patient By Identity Code    ${patient_ssn}
    Patient Status Is Correct    Patient created by the EHR
    Wait Until Element Is Visible    ${invalid_xter_fname_error}    5s
    Wait Until Element Is Visible    ${invalid_xter_lname_error}    5s

Verify That Error Message Is Displayed - Invitation Modal
    Navigate To Candidates Tab
    Wait Until Noona Loader Is Not Visible
    Remove All Care Team In Candidate Tab
    List Of Patients On Candidates Tab Is Displayed
    Try To Click Element
    ...    //*[contains(text(), '${patient_mrn}')]/../*[@data-testid="invite-patient-action"]
    Wait Until Page Does Not Contain Element    ${loader}    5s
    Wait Until Page Contains Element    ${email_field}
    Wait Until Element Is Visible    ${invalid_xter_fname_error}    5s
    Wait Until Element Is Visible    ${invalid_xter_lname_error}    5s

Correct Candidate Information And Send Invite
    Clear Element Text    ${fname_field}
    Try To Input Text    ${fname_field}    ${first_name}
    Clear Element Text    ${lname_field}
    Try To Input Text    ${lname_field}    ${family_name}
    Clear Element Text    ${email_field}
    Try To Input Text    ${email_field}    ${patient_email}
    Fill The Missing Information And Click Invite Patient
    Close Browser

Extension C - Test Teardown
    Generate Clinic Token
    ...    ${appointment_clinic}[manager_email]
    ...    ${DEFAULT_PASSWORD}
    ...    ${APPOINTMENT_CLINIC_ID}
    Send Change User Password Request
    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${appointment_clinic}[manager_email]
    ...    ${APPOINTMENT_CLINIC_ID}
    ...    ${APPOINTMENT_USER_TOKEN}
