*** Settings ***
Documentation       F02N03 Nurse can lock and unlock patient login
...                 - Nurse is logged in to Noona nurse section.
...                 - Patient exists in system

Resource            ${EXECDIR}${/}resources${/}nurse${/}general_information.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_common.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n03    clinic-web


*** Test Cases ***
Nurse Can Lock Patient Login
    [Tags]    nms9-ver-143-1    nms9-ver-143
    Login As Nurse
    Search Patient By Identity Code    ${f02n03_patient}[ssn]
    Click "Lock" Or "Unlock" Button To Allow / Disallow Patient Login    lock
    Close Browser
    Verify Patient Login Locked    ${f02n03_patient}[email]
    Close Browser

Nurse Can Unlock Patient Login
    [Tags]    nms9-ver-143-2    nms9-ver-143
    Login As Nurse
    Search Patient By Identity Code    ${f02n03_patient}[ssn]
    Click "Lock" Or "Unlock" Button To Allow / Disallow Patient Login    unlock
    Close Browser
    Verify Patient Login Unlocked    ${f02n03_patient}[email]
    Close Browser

Nurse Can Lock SPMC Patient
    [Tags]    nms9-ver-143-3    nms9-ver-143
    Reset Patient Status    ${f02n03_patient2}[ssn]    unlocked
    Click "Lock" Or "Unlock" Button To Allow / Disallow Patient Login    lock
    Patient Status Is Correct    Locked by user, SPMC Manager
    Wait Until Page Contains
    ...    Your clinic is locked for this patient, but they can still access their other clinics in Noona.
    Other Clinic Patient Status Is Correct    Username active
    Reload Page
    Other Clinic Patient Status Is Correct    Username active
    Close Browser
    Login As Patient    ${f02n03_patient2}[email]
    Try To Click More Button
    Wait Until Page Contains    ${spmc_clinic_b}[name]
    Page Should Not Contain Element    ${go_to_another_clinic_button}
    Close Browser

Nurse Can Unlock SPMC Patient
    [Tags]    nms9-ver-143-4    nms9-ver-143
    Reset Patient Status    ${f02n03_patient2}[ssn]    locked
    Click "Lock" Or "Unlock" Button To Allow / Disallow Patient Login    unlock
    Patient Status Is Correct    Username active
    Wait Until Page Does Not Contain
    ...    Your clinic is locked for this patient, but they can still access their other clinics in Noona.
    Other Clinic Patient Status Is Correct    Username active
    Reload Page
    Other Clinic Patient Status Is Correct    Username active
    Close Browser
    Login As Patient    ${f02n03_patient2}[email]
    Wait Until Element Is Visible    ${spmc_clinic_toggle_header}
    Try To Click More Button
    Wait Until Page Contains    ${spmc_clinic_a}[name]
    Page Should Contain Element    ${go_to_another_clinic_button}
    Close Browser

Unlock Patient That Reached Max Password Attempts
    [Tags]    nms9-ver-143-5    nms9-ver-143
    Reset Patient Status    ${f02n03_spmc_patient}[ssn]    unlocked
    Close Browser
    Go To Patient Login Page
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Type Wrong Password For 6 Times    ${f02n03_spmc_patient}[email]
    Patient Is Locked    ${f02n03_spmc_patient}[email]
    Login As Nurse    ${spmc_clinic_a}[manager_email]
    Search Patient By Identity Code    ${f02n03_spmc_patient}[ssn]
    Choose General Information Tab
    Wait Until Page Contains    The patient account has been locked due to too many failed password attempts.
    Patient Status Is Correct    Username locked
    Other Clinic Patient Status Is Correct    Username locked
    Close All Browsers
    Login As Nurse    ${spmc_clinic_b}[manager_email]
    Search Patient By Identity Code    ${f02n03_spmc_patient}[ssn]
    Choose General Information Tab
    Wait Until Page Contains    The patient account has been locked due to too many failed password attempts.
    Patient Status Is Correct    Username locked
    Other Clinic Patient Status Is Correct    Username locked
    Click "Lock" Or "Unlock" Button To Allow / Disallow Patient Login    unlock
    Wait Until Page Contains    Patient unlocked
    Patient Status Is Correct    Username active
    Other Clinic Patient Status Is Correct    Username active
    Close Browser
    Login As Nurse    ${spmc_clinic_a}[manager_email]
    Search Patient By Identity Code    ${f02n03_spmc_patient}[ssn]
    Choose General Information Tab
    Patient Status Is Correct    Username active
    Other Clinic Patient Status Is Correct    Username active
    Close Browser


*** Keywords ***
Reset Patient Status
    [Arguments]    ${ssn}    ${expected_status}
    Login As Nurse    ${spmc_clinic_a}[manager_email]
    Search Patient By Identity Code    ${ssn}
    Choose General Information Tab
    Wait Until Element Is Visible    ${send_new_login_link}
    ${patient_is_locked}    Run Keyword And Return Status    Page Should Contain Element    ${unlock_button}
    ${patient_is_unlocked}    Run Keyword And Return Status    Page Should Contain Element    ${lock_button}
    IF    ${patient_is_locked} and '${expected_status}'=='unlocked'
        Try To Click Element    ${unlock_button}
        Wait Until Page Contains    Patient unlocked
        Try To Click Banner Message
    ELSE IF    ${patient_is_unlocked} and '${expected_status}'=='locked'
        Try To Click Element    ${lock_button}
        Wait Until Page Contains    Patient locked
        Try To Click Banner Message
    END

Go To Patient Login Page
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE
        Open Browser    ${PATIENT_LOGIN_URL}    ${BROWSER}
    END
