*** Settings ***
Documentation       F10CU05 Clinic user can mark if a patient declined to answer to a questionnaire
...                 Preconditions:
...                 - Quality of life questionnaire is scheduled for a patient

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f10cu05    clinic-web


*** Test Cases ***
Nurse Can Mark Questionnaire Patient Declined
    [Tags]    nms9-ver-271
    [Setup]    Decline All Unanswered Questionnaires
    Add Questionnaire To Schedule    Quality of life (15D)
    Decline Questionnaire For Patient
    Questionnaire Status Is Correct    Quality of life (15D)    PATIENT DECLINED
    Go To Work Queue
    Go To Patient Reports
    Select Today Tab
    Select Care Team Filter In Work Queue    f10cu05 care team
    Remove Questionnaire Filter In Work Queue
    Remove Patient Group Filters In Patient Reports Work Queue
    Patient Card Is Displayed In Current Work Queue    ${f10cu05_patient}[name]
    Patient's Card Status Is Correct    Patient declined


*** Keywords ***
Decline All Unanswered Questionnaires
    Login As Nurse
    Search Patient By Identity Code    ${f10cu05_patient}[ssn]
    Navigate To Questionnaires Tab
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${questionnaire_options_button}\[last()]
    ...    timeout=5s
    IF    ${status}    Decline Questionnaires Per Row

Decline Questionnaires Per Row
    ${count}    Get Element Count    ${questionnaire_options_button}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${status}    Run Keyword And Return Status
        ...    Wait Until Page Contains Element
        ...    ${questionnaire_options_button}\[last()]
        IF    ${status}    Decline Questionnaire For Patient
    END
