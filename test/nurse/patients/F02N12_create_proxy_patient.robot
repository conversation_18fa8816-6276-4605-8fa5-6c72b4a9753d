*** Settings ***
Documentation       F02N12 Nurse can create a proxy patient account

Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}general_information.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n012    clinic-web


*** Variables ***
${invite_patient_button}        id:open-invite-patient
${id_code_field}                id:ssn
${email_address_field}          //*[@data-testid="emailAddress"]
${spmca_ehr_token_test}         VsVcoe071iCtg1GoXwsA6g==
${spmca_ehr_token_staging}      XwrVesE6qYsqrVlhvR68RQ==
${spmca_tunit}                  unitTestA
${care_team_1_option}           //*[@title='Care Team 1']


*** Test Cases ***
Create New Proxy Patient And Check Status
    [Documentation]    Test case shoud NOT be refactored into creating proxy patient via api as with UI is the purpose of this test case
    [Tags]    nms9-ver-161
    Login As Nurse
    Set New Patient Data With Random Data
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${patients_page_header}
    Create New Patient With Robot    proxy=True
    Verify Patient Status Is Proxy

Extension A - Change existing patient account to proxy patient account
    [Tags]    nms9-ver-162
    Login As Nurse
    Set New Patient Data With Random Data
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${patients_page_header}
    Create New Patient With Robot
    Change Patient Status To Proxy And Verify
    Close Browser
    If existing patient is a Candidate and their email is in use on another clinic: Invitation dialog will open
    Try To Click Element    ${send_invite_button}
    Wait Until Page Contains    ${patient_updated_text}
    Wait Until Element Does Not Contain    ${User_status}    ${patient_created_by_ehr_status}
    Verify Patient Status Is Proxy
    Other Clinic Details Are Visible In General Info Tab    ${spmc_clinic_b}[name]    ${username_sent_status}
    ...    ${patient_dict}[first name]${SPACE}${family_name}    ${patient_dict}[date of birth]
    ...    ${patient_dict}[mobile number]

Extension B - Send account invitation to proxy patient
    [Tags]    nms9-ver-163
    Login As Nurse
    Set New Patient Data With Random Data
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${patients_page_header}
    Create New Patient With Robot    proxy=True
    Try To Click Banner Message
    Verify Patient Status Is Proxy
    Send User Account    input_phone=yes
    Try To Click Banner Message
    Change Patient Password    ${DEFAULT_PASSWORD}
    Wait Until Page Does Not Contain    Password changed
    Wait Until Element Is Visible    ${email_address_field}
    ${new_patient_email}    Get Value    ${email_address_field}
    Login As Patient    ${new_patient_email}
    Verify Patient Login Is Successful

Extension C - Proxy patient declines account invitation
    [Tags]    nms9-ver-164
    Login As Nurse
    Set New Patient Data With Random Data
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${patients_page_header}
    Create New Patient With Robot    proxy=True
    Verify Patient Status Is Proxy
    Patient Declined Account Invite And Send User Account Button Is Enabled


*** Keywords ***
If existing patient is a Candidate and their email is in use on another clinic: Invitation dialog will open
    ${spmca_ehr_token}    Set Variable If    '${ENVIRONMENT}'=='staging'    ${spmca_ehr_token_staging}
    ...    '${ENVIRONMENT}'=='test'    ${spmca_ehr_token_test}
    Send Candidate Request    ${spmca_ehr_token}    ${spmca_tunit}
    Login As Nurse    ${spmc_clinic_b}[manager_email]
    Set New Patient Data With Random Data
    Set To Dictionary
    ...    ${patient_dict}
    ...    email=${patient_email}
    ...    last name=${family_name}
    ...    ssn=${patient_ssn}
    Create New Patient With Robot
    Close Browser
    Login As Nurse    ${spmc_clinic_a}[manager_email]
    Search Patient By Identity Code    ${patient_ssn}
    Wait Until Element Is Visible    ${change_to_proxy_button}
    Try To Click Element    ${change_to_proxy_button}
    Wait Until Page Contains    ${spmc_patient_text}
    Wait Until Page Contains Element    ${send_invitation_icd_dropdown}
    Set Focus To Element    ${send_invitation_icd_dropdown}
    Try To Click Element    ${send_invitation_icd_dropdown}
    Try To Click Element    ${send_user_account_icd_first_option}
    Scroll Element Into View    ${treatment_module_dropdown}
    Try To Click Element    ${treatment_module_dropdown}
    Try To Click Element    ${send_user_account_first_treatment_module}
    Try To Click Element    ${care_team_link}
    Try To Click Element    ${send_user_care_team_dropdown}
    Try To Click Element    ${care_team_1_option}
