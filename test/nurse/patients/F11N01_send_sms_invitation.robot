*** Settings ***
Documentation       F11N01 Nurse can send SMS invitations
...                 Preconditions:
...                 - Nurse is logged in to Noona nurse section.
...                 - Finnish ID code validation is enabled by clinic admin

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}list_of_patients_tab.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}general_information.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}sms_invitation_tab.resource
Library             ${EXECDIR}${/}resources${/}libraries${/}id-gen.py

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f11n01    clinic-web


*** Variables ***
${tunit}                        666
${birth_date}                   1983-03-05
${first_patient_sms_status}     //*[@id='patient0']/td[7]
${first_patient_mrn}            //*[@id='patient0']/td[1]
${announcement_care_team}       Announcement


*** Test Cases ***
Main Success Scenario - Nurse can send SMS invitations
    [Documentation]    Also includes: Extension A - Nurse can set email for SMS invited patient
    [Tags]    nms9-ver-39    nms9-ver-341    patient-2fa    sms
    [Setup]    Setup Email And Lock Other Patients
    # nms9-ver-341
    Login As Nurse    ${appointment_clinic}[user_email]
    Invite Patient Via SMS Invitation
    ...    ${APPOINTMENT_USER_TOKEN}
    ...    ${tunit}
    ...    ${birth_date}
    ...    phone_number=${mailosaur_number}
    Navigate To Patient Page
    Navigate To SMS-invitations
    Select SMS Invitation Care Team    Appointment Care Team
    Select SMS Invitation Status    Username sent
    Invitation Status Is Correct    Username sent
    Select SMS Invitation Status    Select all
    Wait Until SMS Invite Status Is Correct
    Patient Received SMS Invitation To Use Noona    ${appointment_clinic}[name]
    # nms9-ver-39
    Edit Email And Send User Account
    Nurse Can Send Questionnaire
    [Teardown]    Close All Browsers


*** Keywords ***
Edit Email And Send User Account
    Try To Click Element    (//*[@id='${edit_sms-invitation_link}'])[1]
    Wait Until Location Contains    handle-patient
    Try To Input Text    ${email_address_field}    ${family_name}@${mailosaur_keys}[0].mailosaur.net
    Try To Click Element    ${save_patient_button}
    Wait Until Page Contains    ${patient_updated_text}
    Try To Click Banner Message
    Wait Until Element Is Enabled    ${send_user_account_button}
    Try To Click Element    ${send_user_account_button}
    Wait Until Page Contains    ${patient_updated_text}
    Try To Click Banner Message

Nurse Can Send Questionnaire
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Add Questionnaire To Schedule    Baseline questionnaire
    Patient Received Invitation To Answer AEQ
    ...    ${family_name}@${mailosaur_keys}[0].mailosaur.net
    ...    ${appointment_clinic}[name]

Wait Until SMS Invite Status Is Correct
    FOR    ${INDEX}    IN RANGE    0    15
        Wait Until Element Is Visible    ${first_patient_sms_status}
        ${status}    Get Text    ${first_patient_sms_status}
        ${mrn}    Get Text    ${first_patient_mrn}
        IF    '${status}'=='Invitation sent' and '${mrn}'=='${patient_mrn}'
            BREAK
        ELSE IF    '${status}'!='Invitation sent' and '${INDEX}'!='14'
            Reload Page
        ELSE IF    '${status}'!='Invitation sent' and '${INDEX}'=='14'
            Fail    Invitation is not sent within seconds!
        END
    END

Setup Email And Lock Other Patients
    Set Test Variable    @{mailosaur_keys}    hq7uzdww    7GMYxWnlKj9GHFDB
    Delete All Messages In Server    ${mailosaur_keys}[0]    ${mailosaur_keys}[1]
    Delete All SMS From Mailosaur
    Lock Patients To Exclude From Announcement    ${appointment_clinic}[user_email]    ${announcement_care_team}
