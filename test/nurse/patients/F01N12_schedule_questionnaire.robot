*** Settings ***
Documentation       [F01N12] Nurse can schedule a questionnaire or message to be sent to a patient
...                 Test:
...                 - Adds a new scheduled questionnaire for a random patient
...                 - Patient completes and checks the summary (text and number inputs) of questionnaire
...                 - Saves and checks the status of questionnaire
...                 - Checks saved summary (text and number inputs)

Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f01n12    clinic-web


*** Variables ***
${last_scheduled_content}       (//*[starts-with(@data-testid, 'symptom-inquiry_')])[last()]
${template}                     Auto test template
${template_questionnaire}       Baseline questionnaire


*** Test Cases ***
Create And Complete IPQ Questionnaire
    [Tags]    nms9-ver-78
    Add An Activated Patient Under Default Clinic    f01n12-main
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Add Questionnaire To Schedule    IPQ
    Login As Patient    email=${patient_email}
    questionnaires.Complete Questionnaire    IPQ
    questionnaires.Save Questionnaire    IPQ
    questionnaires.Check Saved Questionnaire    IPQ
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


Extension A - Remove Scheduled Questionnaire
    [Tags]    nms9-ver-82
    Add An Activated Patient Under Default Clinic    f01n12-exta
    Login As Nurse
    Schedule Questionnaire
    Questionnaire Status Is Correct    IPQ    SCHEDULED
    Remove Questionnaire
    Scheduled Questionnaire Is Removed    IPQ
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}

Extension B - Schedule More Content Using A Schedule Template
    [Tags]    nms9-ver-83
    Add An Activated Patient Under Default Clinic    f01n12-extb    module=${ACUTE_LEUKEMIA_AND_MDS}
    Login As Nurse
    Search Patient By Identity Code    ${patient_ssn}
    Edit Module    ${ACUTE_LEUKEMIA_AND_MDS}[name]
    Use Template And Apply
    Template Added To Scheduled Content
    Save Module
    Remove Questionnaire
    [Teardown]    Run Keywords    Close Browser    AND    Remove Patient As Test Teardown
    ...    ${patient_email}
    ...    ${automated_tests_clinic}[default_manager]
    ...    ${AUTOMATED_TESTS_CLINIC_ID}
    ...    ${AUTOMATED_TESTS_EHR_TOKEN}


*** Keywords ***
Schedule Questionnaire
    Search Patient By Identity Code    ${patient_ssn}
    Schedule Questionnaire For Tomorrow    IPQ
    Try To Click Banner Message

Use Template And Apply
    Use Template For Scheduled Content    ${template}
    ${date}    Get Current Date    result_format=%d.%m.%Y
    Select Current Date For Scheduled Template    ${date}
    Try To Click Element    ${scheduled_template_apply_button}

Template Added To Scheduled Content
    Wait Until Page Contains Element    ${last_scheduled_content}
    Element Should Contain    ${last_scheduled_content}    ${template_questionnaire}
    Page Should Contain Element    ${last_scheduled_content}/following-sibling::div/div[text()=' Scheduled ']

Save Module
    Try To Click Element    ${save_treatment_module_button}
    Wait Until Page Contains    Treatment module saved
    Try To Click Banner Message
