*** Settings ***
Documentation       Define a Custom branded patient experience - Clinic side
...
...                 Preconditions:
...                 - Access Clinic as Noona Admin
...                 - Navigate to Clinic Settings (under clinic link in main navigation)
...                 - Scroll to “Clinic experience for patients”
...                 - Enable “Custom branding”
...
...                 Test Data:
...                 - Clinic User 4: TA clinic Custom_Branding
...
...                 - Scenario 1: Define Clinic Shortname and Custom Url
...                 - Scenario 2: Define Custom Color Only
...                 - Sceanrio 3: Define Custom Logo Only
...                 - Scenario 4: Define Custom Service Name Only
...                 - Scenario 5: Define Custom Color, Logo, & Service Name
...                 - Scenario 6: Toggle Off Custom Branding
...

Resource            ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}co-branding_patient.resource

Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          manual    patient-web


*** Variables ***
${svg_logo}                 valid-logo.svg
${jpg_photo}                file_example_JPG_100kB.jpg
${patient_email_cbath}      <EMAIL>
${patient_email_cbatt}      <EMAIL>
${patient_email_cbatp}      <EMAIL>
${patient_email_cbatcc}     <EMAIL>


*** Test Cases ***
Noona Admin Defines A Brand Color In Clinic Settings
    [Documentation]    NOONA-14986    Custom branding to patient login page
    Define Clinic Shortname And Custom Url    ${custom_branding_helsinki}[name]
    Noona Admin Edits Custom Branding In Basic Settings    ${custom_branding_helsinki}[name]
    Clear Custom Color
    Admin Can Preview Derived Color Before Saving    40a82d
    Admin Can Preview Derived Color Before Saving    666699
    Admin Can Preview Derived Color Before Saving    ad271d
    Admin Can Preview Derived Color Before Saving    AAAAAA
    Admin Can Preview Derived Color Before Saving    40a82d
    Clear Uploaded Logo
    Select Custom Service Name    yes
    Select Custom Service Name    no
    Save Basic Settings And Close Browser
    Custom Branding Is Visible To Patient    ${patient_email_cbath}    custom_color    default_logo    default_noona

Noona Admin Defines A Logo In Clinic Settings
    [Documentation]    NOONA-14986    Custom branding to patient login page
    Define Clinic Shortname And Custom Url    ${custom_branding_tampere}[name]
    Noona Admin Edits Custom Branding In Basic Settings    ${custom_branding_tampere}[name]
    Clear Custom Color
    Upload Logo With SVG Format    ${svg_logo}
    Upload Logo With Non-svg Format    ${jpg_photo}
    Upload Logo With SVG Format    ${svg_logo}
    Select Custom Service Name    no
    Save Basic Settings And Close Browser
    Custom Branding Is Visible To Patient    ${patient_email_cbatt}    default_color    custom_logo    default_noona

Noona Admin Defines Custom Service Name
    [Documentation]    NOONA-14986    Custom branding to patient login page
    Define Clinic Shortname And Custom Url    ${custom_branding_porvoo}[name]
    Noona Admin Edits Custom Branding In Basic Settings    ${custom_branding_porvoo}[name]
    Clear Uploaded Logo
    Select Custom Service Name    yes
    Input Clinic Specific Name    Automated Custom Clinic's    Automated Custom Clinic
    Save Basic Settings And Close Browser
    Custom Branding Is Visible To Patient
    ...    ${patient_email_cbatp}
    ...    default_color
    ...    default_logo
    ...    custom_clinic_name

Noona Admin Defines Custom Branding
    [Documentation]    NOONA-14986    Custom branding to patient login page
    Define Clinic Shortname And Custom Url    TA clinic Custom_Branding
    Noona Admin Edits Custom Branding In Basic Settings    TA clinic Custom_Branding
    Admin Can Preview Derived Color Before Saving    40a82d
    Upload Logo With SVG Format    ${svg_logo}
    Select Custom Service Name    yes
    Input Clinic Specific Name    Test Custom Clinic's    Test Custom Clinic
    Save Basic Settings And Close Browser
    Custom Branding Is Visible To Patient
    ...    ${patient_email_cbatcc}
    ...    custom_color
    ...    custom_logo
    ...    custom_clinic_name

Noona Admin Toggles Off Custom Branding
    [Documentation]    NOONA-14986    Custom branding to patient login page
    Define Clinic Shortname And Custom Url    TA clinic Automated_tests
    Noona Admin Edits Custom Branding In Basic Settings    TA clinic Automated_tests
    Toggle Off Custom Branding
    Save Basic Settings And Close Browser
    Custom Branding Is Visible To Patient    ${patient_email_cbatcc}    default_color    default_logo    default_noona


*** Keywords ***
Noona Admin Edits Custom Branding In Basic Settings
    [Arguments]    ${clinic_name}
    Login As Nurse    clinic=${clinic_name}    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Basic Settings Tab
    Noona Administrator Edits Clinic Experience For Patients
