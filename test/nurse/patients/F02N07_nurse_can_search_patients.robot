*** Settings ***
Documentation       F02N07 Nurse can search patients
...                 Preconditions:
...                 - You are logged into Noona as a clinic user.
...                 - At least one patient exists in Noona.
...                 - All patients in search results have activated their accounts
...                 - Patient info activated for clinic: Last name, First name, Date of birth, ID/SSN

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_management.resource
Resource            ${EXECDIR}${/}resources/nurse/patient_cases.resource

Suite Setup         Run Keywords    Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f02n07    clinic-web


*** Test Cases ***
Main Success Scenario - Nurse Can Search Patients
    [Documentation]    Also includes
    ...    Extension A — User can access a patient directly from the search results dropdown list
    ...    Extension B - User can display the entire list of search results and access a patient in this list
    [Tags]    nms9-ver-149    nms9-ver-150    nms9-ver-151
    # nms9-ver-149
    Login As Nurse
    Search Field Place Holder Is Correct
    Input Text    ${search_a_patient_field}    Patientf02n07-MRN
    Search Dropdown Result Rows Are Correct    1
    Input Text    ${search_a_patient_field}    Patientf02n07-SSN
    Search Dropdown Result Rows Are Correct    2
    Input Text    ${search_a_patient_field}    Patientf02n07
    Search Dropdown Result Rows Are Correct    5
    Search Suggestion Footer Is Correct    6
    Search Patient By Identity Code    ${f02n07_patient_3}[ssn]
    Wait Until Location Contains    /patient-messaging
    Patient Cases Tab Is Selected And Open
    # nms9-ver-150
    Return To Work Queue
    Input Text    ${search_a_patient_field}    Patientf02n07
    Try To Click Element    ${search_result_list_rows}\[2]
    Wait Until Location Contains    /patient-messaging
    Patient Cases Tab Is Selected And Open
    Wait Until Element Is Visible    ${patient_name_header}
    IF    'test' in '${ENVIRONMENT}'
        Element Should Contain    ${patient_info_header}    ${f02n07_patient_2}[ssn]
        Element Should Contain    ${patient_info_header}    ${f02n07_patient_2}[name]
    ELSE
        Element Should Contain    ${patient_info_header}    ${f02n07_patient_6}[ssn]
        Element Should Contain    ${patient_info_header}    ${f02n07_patient_6}[name]
    END
    Return To Work Queue
    Wait Until Location Contains    /new-messages
    # nms9-ver-151
    Navigate To Patient Page
    Wait Until Element Is Visible    ${search_a_patient_field}
    Try To Input Text    ${search_a_patient_field}    Patientf02n07
    Try To Click Element    ${search_result_suggestion_footer}
    Wait Until Element Is Visible    ${search_result_page_header}
    Search Field Is Empty
    Try To Click Element    ${search_results_last_name_column header}
    Search Results Are In Correct Order    ascending    ${search_results_last_names}
    Try To Click Element    ${search_results_last_name_column header}
    Search Results Are In Correct Order    descending    ${search_results_last_names}
    Select A Patient From Search Result    ${f02n07_patient_1}[ssn]
    Wait Until Location Contains    /patient-messaging
    Patient Cases Tab Is Selected And Open
    Wait Until Element Is Visible    ${patient_name_header}
    Element Should Contain    ${patient_info_header}    ${f02n07_patient_1}[ssn]
    Element Should Contain    ${patient_info_header}    ${f02n07_patient_1}[name]
    Try To Click Element    ${return_to_search}
    Wait Until Element Is Visible    ${search_result_page_header}
    ${patient1}    Format String    ${search_result_ssn}    ${f02n07_patient_1}[ssn]
    Wait Until Element Is Visible    ${patient1}
    ${patient2}    Format String    ${search_result_ssn}    ${f02n07_patient_6}[ssn]
    Wait Until Element Is Visible    ${patient2}


*** Keywords ***
Search Field Place Holder Is Correct
    Wait Until Element Is Visible    ${search_a_patient_field}
    ${attr}    Get Element Attribute    ${search_a_patient_field}    placeholder
    Should Be Equal    ${attr}    Search a patient

Select A Patient From Search Result
    [Arguments]    ${ssn}
    Wait Until Element Is Visible    ${search_result_page_header}
    ${patient}    Format String    ${search_result_ssn}    ${ssn}
    Try To Click Element    (${patient})[1]

Search Field Is Empty
    Wait Until Element Is Visible    ${search_a_patient_field}
    ${text}    Get Value    ${search_a_patient_field}
    Should Be Equal    ${text}    ${None}

Get Search Results In List
    [Arguments]    ${column_elements}
    Wait Until Element Is Visible    ${column_elements}\[1]
    @{result_list}    Create List
    @{result_elements}    Get WebElements    ${column_elements}
    FOR    ${result_element}    IN    @{result_elements}
        ${text}    Get Text    ${result_element}
        Append To List    ${result_list}    ${text}
    END
    Set Test Variable    @{result_list}

Search Results Are In Correct Order
    [Arguments]    ${order}    ${column}
    Sleep    2s    # wait for sorting
    @{last_name_list}    Get Search Results In List    ${column}
    @{original_list}    Copy List    ${last_name_list}
    IF    '${order}'=='ascending'
        Sort List    ${last_name_list}
    ELSE
        Reverse List    ${last_name_list}
    END
    Lists Should Be Equal    ${last_name_list}    ${original_list}
