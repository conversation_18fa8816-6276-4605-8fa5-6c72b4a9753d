*** Settings ***
Documentation       F15CU04 Clinic user can see a log of changes done to the case.
...                 Preconditions:
...                 - Clinic user is logged in

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Nurse
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f15cu04    clinic-web


*** Variables ***
${first_workqueue_utem}         workqueue-item-identity-code-0
${change_log_button}
...                             //*[contains(@class, 'mat-expanded')]//div[starts-with(@class, 'case-changelog')]/div[1]
${change_log_area_titles}
...                             //*[contains(@class, 'mat-expanded')]//div[contains(text(), 'Change log')]/../../../div[2]/div[1]
${change_log_area_actions}
...                             //*[contains(@class, 'mat-expanded')]//div[contains(text(), 'Change log')]/../../../div[2]/div[2]
${date_format}                  %d.%m.%Y
${clinic_user_text}             Clinic User
${action}                       Added a case note


*** Test Cases ***
User Can See Case Logs
    # Note: Patient needs to be assigned to clinic user in questionnaires tab
    [Tags]    nms9-ver-320
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    Care Team 1
    Select Case Type Filter In Work Queue    Select all
    Remove Selected Primary Providers
    Open The First Case From Work Queue
    Open Change Log
    Log Contains Actions

Extension A — The list of changes in the case log is updated
    # Note: Patient needs to be assigned to clinic user in questionnaires tab
    [Documentation]
    ...    | =Action= | =Text in log= | =Notes= |
    ...    | New case created | Created case | - |
    ...    | Editing and adding new content to case | Added [label name] | When content is added for the first time, only the label is displayed |
    ...    | Added symptom | Added [symptom name] | Display added symptom name |
    ...    | Edited symptom | Edited [symptom name] | Display edited symptom name |
    ...    | Removed symptom | Removed [symptom name] | Display removed symptom name |
    ...    | Added medication refill content | Added Medication information | Display content name |
    ...    | Edited medication refill content | Edited Medication information | Display content name |
    ...    | Removing existing content (checkbox) | Removed [removed value], [removed value] from [label name] | Display removed values |
    ...    | Adding values to existing content (checkbox) | Added [added value], [added value]from [label name] | Display added values |
    ...    | Editing existing content (dropdown, radio button) | Edited [label name] from [old value]to [new value] | Display old and new value |
    ...    | Consultation request sent | Sent consultation request to [recipient] | Display recipient name |
    ...    | Consultation responded | Replied to consultation request from [recipient] | Display recipient name |
    ...    | Consultation request cancelled | Consultation request cancelled | - |
    ...    | Added case note | Added case note | - |
    ...    | Edited case note | Edited case note | - |
    ...    | Closed case | Closed case | - |
    ...    | Content archived to EMR via integration | Content archived to EMR | - |
    ...    | Case exported as PDF | Content exported as PDF | - |
    ...    | Case exported as text | Content exported as text | - |
    [Tags]    nms9-ver-321
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    Care Team 1
    Select Case Type Filter In Work Queue    Select all
    Remove Selected Primary Providers
    Open The First Case From Work Queue
    Add Case Note
    Open Change Log
    Log Is Updated


*** Keywords ***
Open The First Case From Work Queue
    Try To Click Element    ${first_workqueue_utem}

Open Change Log
    Wait Until Page Contains Element    ${change_log_button}
    Try To Click Element    ${change_log_button}

Log Contains Actions
    Wait Until Page Contains Element    ${change_log_area_titles}
    Try For Element Should Contain    ${change_log_area_titles}    User
    Element Should Contain    ${change_log_area_titles}    Action
    Element Should Contain    ${change_log_area_titles}    Time
    Element Should Contain    ${change_log_area_actions}    Case created

Add Case Note
    ${case_note_topic}    Generate Random String
    ${case_note_text}    Generate Random String
    Set Test Variable    ${case_note_topic}
    Set Test Variable    ${case_note_text}
    Create Case Note    ${case_note_topic}    ${case_note_text}

Log Is Updated
    Wait Until Page Does Not Contain    Note added
    ${current_date}    Get Current Date    result_format=${date_format}
    Set Test Variable    ${current_date}
    Wait Until Page Contains Element
    ...    (//div[contains(@class, 'log-contents')]//div[contains(text(), '${current_date}')]/..)[last()]
    Element Should Contain
    ...    (//div[contains(@class, 'log-contents')]//div[contains(text(), '${current_date}')]/..)[last()]
    ...    ${clinic_user_text}
    Element Should Contain
    ...    (//div[contains(@class, 'log-contents')]//div[contains(text(), '${current_date}')]/..)[last()]
    ...    ${action}
