*** Settings ***
Documentation       F12SY05 System can schedule patient questionnaires based on information from EMR
...                 Preconditions:
...                 - Integration is enabled for the clinic.
...                 Test:
...                 - Sent questionnaire is visible to patient and nurse
...                 - Canceled questionnaire is not visible for patient and nurse
...                 - Settings via UI are properly saved

Resource            ${EXECDIR}${/}resources${/}patient${/}appointment.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources/test_data/API_test_data.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f12sy05    patient-web


*** Variables ***
###test data 1
${appointment_reason}                       test2
${location}                                 666
${appointment_tunit_name_JA}                <PERSON>'s Angels
${appointment_reason_text}                  Visit Dr<PERSON> for a cure
${questionnaire_timezone_EEST}              EEST    # EEST when daylight saving time, EET if not dst
###test data 2
${appointment_reason_noona-17459}           testr
${treatment_unit_id}                        555
${appointment_tunit_name_Paris}             Paris HUS
# need to comvert timezone name from clinic integration settings Time zone location (Europe/Paris)
${questionnaire_timezone_CEST}              CEST    # CEST when daylight saving time, CET if not dst
${appointment_name_visible_for_patient}     MA
${tnonc_distress_questionnaire}             Tennessee Oncology Distress Screening


*** Test Cases ***
Send Appointment With Questionnaire To Patient
    [Tags]    nms9-ver-275-1    nms9-ver-275
    Add An Activated Patient Under Appointment Clinic    f12sy05-1
    Set Appointment Date In The Future    11 days
    Send Appointments Via FHIR API    ${location}    ${appointment_reason}    ${patient_id}
    Login As Patient    ${patient_email}
    Wait Until Page Contains    ${upcoming_events_header_text}
    Check Questionnaire From The Timeline
    ...    2 days
    ...    ${appointment_reason_text}
    ...    ${questionnaire_timezone_EEST}
    ...    ${appointment_tunit_name_JA}
    ...    ${baseline_questionnaire}
    Login As Nurse    ${appointment_clinic}[user_email]    clinic=${appointment_clinic}[name]
    Check Scheduled Questionnaire From The Clinic Page    ${patient_ssn}    ${baseline_questionnaire}
    [Teardown]    Appointment Teardown    ${appointment_id}    ${patient_mrn}

Remove Appointment With Questionnaire
    [Tags]    nms9-ver-275-2    nms9-ver-275
    Add An Activated Patient Under Appointment Clinic    f12sy05-2
    Set Appointment Date In The Future    35 days
    Send Appointments Via FHIR API    ${location}    ${appointment_reason}    ${patient_id}
    Login As Patient    ${patient_email}
    Wait Until Page Contains    ${upcoming_events_header_text}
    Check Questionnaire From The Timeline
    ...    2 days
    ...    ${appointment_reason_text}
    ...    ${questionnaire_timezone_EEST}
    ...    ${appointment_tunit_name_JA}
    ...    ${baseline_questionnaire}
    Login As Nurse    ${appointment_clinic}[user_email]    ${appointment_clinic}[name]
    Check Scheduled Questionnaire From The Clinic Page
    ...    ${patient_ssn}
    ...    ${baseline_questionnaire}
    Appointment Teardown    ${appointment_id}    ${patient_mrn}
    Login As Patient    ${patient_email}
    Questionnaire Removed From The Timeline    ${questionnaire_timezone_EEST}
    Login As Nurse    ${appointment_clinic}[user_email]    clinic=${appointment_clinic}[name]
    Questionnaire Removed From The Clinic Pages    ${patient_ssn}
    [Teardown]    Close All Browsers

Check Automated Questionnaire Scheduling UI Settings
    [Tags]    nms9-ver-275-3    nms9-ver-275    noona_admin
    Login As Nurse    clinic=${test_clinic_setting_1}[name]    user_type=${USER_TYPE}[noona_admin]
    Go To Integration Settings
    Add And Save A New Schedule
    Verify Schedule Is Saved
    [Teardown]    Close All Browsers

Cancelled TNONC Distress Questionnaires Should Not Be Displayed In Patient's Diary
    [Documentation]    This is a corner case scenrio reported in production
    [Tags]    defect    nms9-ver-275-4    nms9-ver-275
    Add An Activated Patient Under Appointment Clinic    f12sy05-3
    Set Appointment Date In The Future    11 days
    Send Appointments Via FHIR API    ${treatment_unit_id}    ${appointment_reason_noona-17459}    ${patient_id}
    Login As Patient    ${patient_email}
    Wait Until Page Contains    ${upcoming_events_header_text}
    Check Questionnaire Appointment From The Diary Timeline
    ...    0 days
    ...    ${appointment_name_visible_for_patient}
    ...    ${questionnaire_timezone_CEST}
    ...    ${appointment_tunit_name_Paris}
    Close Browser
    Login As Nurse    ${appointment_clinic}[user_email]    clinic=${appointment_clinic}[name]
    Check Scheduled Questionnaire From The Clinic Page    ${patient_ssn}    ${tnonc_distress_questionnaire}
    Close Browser
    Appointment Teardown    ${appointment_id}    ${patient_mrn}
    Login As Patient    ${patient_email}
    Questionnaire Appointment Removed From The Diary Timeline    ${questionnaire_timezone_CEST}
    Close Browser
    Login As Nurse    ${appointment_clinic}[user_email]    clinic=${appointment_clinic}[name]
    Questionnaire Removed From The Clinic Pages    ${patient_ssn}
    Close Browser

Remove Failed Appointment In UI
    [Documentation]    This is a helper to remove failed appointemnts
    ...    add previous appointment_id from report and patient mrn below
    [Tags]    manual    remove_appointment
    Appointment Teardown    ${appointment_id}    ${patient_mrn}
