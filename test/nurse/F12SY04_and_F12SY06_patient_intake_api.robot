*** Settings ***
Documentation       F12SY04 System can manage patient accounts based on information from EMR.
...                 F12SY06 System can manage patient's care teams based on information from EMR
...                 Preconditions:
...                 - Integration is enabled for the clinic.
...                 - Integration user exists.
...                 - Treatment unit exists.
...                 Test:
...                 - Create new patient and compare sent and received patient details.
...                 - Can't add user without treatment unit.
...                 - Can update patient details.

Resource            ${EXECDIR}${/}resources${/}common_setup.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags
...    usecase-f12sy04
...    usecase-f12sy06
...    nms9-ver-273
...    nms9-ver-274
...    nms9-ver-276
...    api
...    clinic-web


*** Variables ***
${tunit}            666
${birth_date}       1983-03-05


*** Test Cases ***
Create New Patient Via FHIR API
    ${body_json}    Invite Patient Via SMS Invitation    ${APPOINTMENT_USER_TOKEN}    ${tunit}    ${birth_date}
    Get Patient Details
    Compare Data    ${body_json}

Can't Add New Patient Without Valid Treatment Unit ID
    [Tags]    nms9-ver-274-4
    Invite Patient Via SMS Invitation With Invalid Treatment Unit    ${APPOINTMENT_USER_TOKEN}

Update Patient Details Via FHIR API
    ${body_json}    Invite Patient Via SMS Invitation    ${APPOINTMENT_USER_TOKEN}    ${tunit}    ${birth_date}
    Get Patient Details
    Compare Data    ${body_json}
    ${body_json_updated}    Update Details    ${tunit}
    Get Patient Details
    Compare Data    ${body_json_updated}

Main success scenario
    [Tags]    manual    deprecating
    Noona receives patient account messages from EMR
    Patient account is created by filling in following information based on the integration message
    Patient name
    If patient has middle name initial, it is included in the First name field in Noona
    SSN / EMR
    Phone numbers
    Max 3 phone numbers
    Email
    Address
    Clinic site
    Primary provider
    Default treatment module is applied to every patient account
