*** Settings ***
Documentation       F07CU04 Clinic User Can Select Cookie Preferences

Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}cookies.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}nurse_profile.resource

Suite Setup         Set Libraries Order
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f07cu04    clinic-cookies    clinic-web


*** Test Cases ***
Main Success Scenario - Clinic User Can Select Cookie Preferences
    [Documentation]    This tc also checks the full cookie policy content
    ...    and the initial cookie settings text displayed on login page
    ...    Both checks are done in all languages
    [Tags]    nms9-ver-462
    Clinic User Lands On Noona With A New Browser Session
    Cookie Settings Modal Is Displayed
    Clinic User Can Click The Cookie Policy Link Within The Settings To Review The Policy Details
    Clinic User Selects Their Cookie Preferences
    The Cookie Settings Modal Is Dismissed
    Clinic User Can Proceed With Login
    [Teardown]    Close Browser

Extension A - Checking Your Cookie Setting Selection After Login
    [Documentation]    This tc checks that full cookie policy is displayed to user from help menu
    ...    tc also checks that the clinic user can change their cookie preference after login
    [Tags]    nms9-ver-463
    Clinic User Selects Cookie Policy From Help Menu After Login
    Cookie Policy Modal Is Shown
    Clinic User Can Review And Change The Consent Previously Selected
    The Cookie Settings Modal Is Dismissed For User
    [Teardown]    Close Browser


*** Keywords ***
Clinic User Lands On Noona With A New Browser Session
    Open Url    ${NURSE_LOGIN_URL}    en

Cookie Settings Modal Is Displayed
    Check That Cookie Settings Are Displayed On Clinic Landing Page

Clinic User Can Click The Cookie Policy Link Within The Settings To Review The Policy Details
    Set Locale List
    FOR    ${locale}    IN    @{locales_list}
        Open Url    ${NURSE_LOGIN_URL}    ${locale}
        Verify Cookie Settings Modal Content    ${locale}
        Verify Clinic Cookie Policy Content From Cookie Settings Modal    ${locale}
        Close Browser
        Sleep    1s
    END

Clinic User Selects Their Cookie Preferences
    Open Url    ${NURSE_LOGIN_URL}    en
    Check That Cookie Settings Are Displayed On Clinic Landing Page
    Clinic User Selects Necessary Cookies
    Verify Necessary Cookies Selection In Clinic Browser Session
    Verify Necessary Cookies Selection In Cookie Policy From Clinic Landing Page
    Close Browser
    Open Url    ${NURSE_LOGIN_URL}    en
    Check That Cookie Settings Are Displayed On Clinic Landing Page
    Clinic User Selects All Cookies
    Verify All Cookies Selection In Clinic Browser Session
    Verify All Cookies Selection In Cookie Policy From Clinic Landing Page
    Close Browser

The Cookie Settings Modal Is Dismissed
    Open Url    ${NURSE_LOGIN_URL}    en
    Check That Cookie Settings Are Displayed On Clinic Landing Page
    Try To Click Element    ${accept_all_cookies_for_clinic}
    Wait Until Element Is Not Visible    ${clinic_cookies_image}
    Wait Until Element Is Not Visible    ${accept_necessary_cookies_for_clinic}
    Wait Until Element Is Not Visible    ${accept_all_cookies_for_clinic}

Clinic User Can Proceed With Login
    Login As Nurse From Landing Page    ${automated_tests_clinic}[default_user]
    Wait until keyword succeeds    3x    1s    Keep Me Logged In    Yes

Clinic User Selects Cookie Policy From Help Menu After Login
    Login As Nurse
    Clinic User Clicks Cookie Policy From Help Menu

Cookie Policy Modal Is Shown
    Wait Until Page Contains Element    ${change_consent_title_clinic}
    Scroll Element Into View    ${accept_all_cookies_for_clinic}
    Wait Until Element Is Visible    ${accept_all_cookies_for_clinic}
    Wait Until Element Is Visible    ${accept_all_cookies_for_clinic}/mat-icon
    Verify Cookie Policy Shown To Clinic User

Clinic User Can Review And Change The Consent Previously Selected
    Verify All Cookies Selection In Clinic Browser Session
    Should contain    ${cookies}    ${clinic_preferred_lang}
    Clinic User Selects Necessary Cookies
    Wait Until Element Is Not Visible    ${accept_necessary_cookies_for_clinic}
    Wait Until Element Is Not Visible    ${accept_all_cookies_for_clinic}
    Verify Necessary Cookies Selection In Clinic Browser Session
    Clinic User Clicks Cookie Policy From Help Menu
    Wait Until Element Is Visible    ${accept_necessary_cookies_for_clinic}/mat-icon

The Cookie Settings Modal Is Dismissed For User
    Clinic User Selects Necessary Cookies
    Wait Until Element Is Not Visible    ${accept_necessary_cookies_for_clinic}
    Wait Until Element Is Not Visible    ${accept_all_cookies_for_clinic}

Verify Cookie Policy Shown To Clinic User
    ${actual_clinic_cookie_policy_text}    Get Text    ${cookie_setting_section}
    Log    ${actual_clinic_cookie_policy_text}
    ${clinic_cookie_policy_content}    Get File
    ...    ${EXECDIR}${/}data${/}cookie_policy_content${/}clinic_cookies_data${/}clinic_cookie_policy_en_GB.txt
    Log    ${clinic_cookie_policy_content}
    ${are_equal}    Run Keyword And Return Status
    ...    Should Be Equal As Strings
    ...    ${actual_clinic_cookie_policy_text}
    ...    ${clinic_cookie_policy_content}
    IF    '${are_equal}' == 'False'
        Fail    The content of the clinic cookie policy from cookie setting modal does not match.
    END
