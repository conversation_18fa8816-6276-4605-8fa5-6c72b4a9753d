*** Settings ***
Documentation       F07CU03 Clinic user can single sign-on with external system user id

Force Tags          usecase-f07cu03    manual


*** Test Cases ***
Main success scenario
    Activate single sign-on (in case of Uranus SSO)
    Navigate to profile settings
    Input external system user id in profile information (user id of the system which single sign-on is linked)
    Save settings
    Navigate to Noona url while being logged in in the system which is used for single sign-on
    Continue to use Noona
