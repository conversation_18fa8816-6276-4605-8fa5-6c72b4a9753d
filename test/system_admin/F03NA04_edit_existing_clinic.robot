*** Settings ***
Documentation       F03NA04 Noona administrator can edit existing clinic

Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Admin
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na04    management


*** Variables ***
${clinic_name}                      piru2
${clinic_table}                     clinic-
${short_name_input}                 short-name
${save_button}                      save
${clinic_enabled_radio_button}      //label[contains(@for,'clinic-enabled-true')]
${clinic_disabled_radio_button}     //label[contains(@for,'clinic-enabled-false')]
${unity_cust_account_no}            //input[contains(@id,'unityCustomerAccountNumber')]
${unity_cust_account_no_txt}        unityid
${asset_id}                         //input[contains(@id,'varianAssetId')]
${asset_id_txt}                     assetid
${enabled}                          Enabled
${disabled}                         Disabled


*** Test Cases ***
Noona Admin Can Edit Clinic
    [Tags]    nms9-ver-191
    User Selects Existing Clinic    ${clinic_name}
    User Edits Some Input Fields
    User Selects Save
    User Checks Updated Details    ${clinic_name}

Extension A - Disabling a clinic
    [Tags]    nms9-ver-192
    User Selects Existing Clinic    ${clinic_name}
    User Selects Status To    ${disabled}
    User Selects Save
    User Checks Clinic Status    ${clinic_name}    ${disabled}
    User Selects Existing Clinic    ${clinic_name}
    User Selects Status To    ${enabled}
    User Selects Save
    User Checks Clinic Status    ${clinic_name}    ${enabled}

Extension B - Mapping Clinic Data To CSM Health Dashboard In Unity
    [Tags]    nms9-ver-193
    User Selects Existing Clinic    ${clinic_name}
    Try To Input Text    ${unity_cust_account_no}    ${unity_cust_account_no_txt}
    Try To Input Text    ${asset_id}    ${asset_id_txt}
    User Selects Save
    User Selects Existing Clinic    ${clinic_name}
    Clear Element Text    ${unity_cust_account_no}
    Clear Element Text    ${asset_id}
    User Selects Save

*** Keywords ***
User Selects Existing Clinic
    [Arguments]    ${clinic_name}
    Wait Until Page Contains Element    ${clinic_table}
    Try To Click Element    //td[contains(@class,'clinic-name') and contains(text(),'${clinic_name}')]

User Edits Some Input Fields
    ${random_string}    Generate Random String
    Set Test Variable    ${random_string}
    Wait Until Page Contains Element    ${short_name_input}
    Try To Input Text    ${short_name_input}    ${random_string}

User Selects Save
    Try To Click Element    ${save_button}
    Wait Until Page Contains    Clinic updated successfully

User Checks Updated Details
    [Arguments]    ${clinic_name}
    Reload Page
    Wait Until Page Contains Element    ${clinic_table}
    Try To Click Element    //td[contains(@class,'clinic-name') and contains(text(),'${clinic_name}')]
    Wait Until Page Contains Element    ${short_name_input}
    Page Should Contain    ${random_string}

User Selects Status To
    [Arguments]    ${status}
    Wait Until Page Contains Element    ${clinic_disabled_radio_button}
    IF    '${status}'=='Disabled'
        Try To Click Element    ${clinic_disabled_radio_button}
    END
    IF    '${status}'=='Enabled'
        Try To Click Element    ${clinic_enabled_radio_button}
    END

User Checks Clinic Status
    [Arguments]    ${clinic_name}    ${status}
    Reload Page
    Wait Until Page Contains Element
    ...    //td[contains(@class,'clinic-name') and contains(text(),'${clinic_name}')]/../td[contains(@class,'clinic-status')]
    Element Should Contain
    ...    //td[contains(@class,'clinic-name') and contains(text(),'${clinic_name}')]/../td[contains(@class,'clinic-status')]
    ...    ${status}
