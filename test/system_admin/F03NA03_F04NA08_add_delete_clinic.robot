*** Settings ***
Documentation       F03NA03 Noona administrator can create new clinic
...                 F04NA08 Noona administrator can delete clinic
...                 - User has enabled 2-factor-authentication.
...                 - Noona administrator has logged in to /management

Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}management${/}noona_management_site.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na03    usecase-f04na08    management


*** Variables ***
${delete_clinic_button}             //*[@id="delete-clinic"]
${confirm_button}                   //*[@id="ok-confirm"]
${admin_sms_code_first_input}       (//*[@id="sms-password"]/../div)[1]
${admin_password_input}             //*[@id='password']
${admin_password_input_text_button}    //button[@type='submit']//div[text()='Next']


*** Test Cases ***
Disallow Spaces Or Other Special Characters In Clinic Shortname
    [Documentation]    Also includes
    ...    F07U01 User can login - Main Success Scenario - Admin Can Login With 2FA
    # TODO: invalid characters check doesn't work properly with random, there can be same char twice but pages show it only once.
    [Tags]    nms9-ver-190    nms9-ver-241
    # nms9-ver-241
    Login As Admin With 2FA
    Wait Until Page Contains Element    ${mgmt_clinic_table}    10s
    # nms9-ver-190
    User Selects Add New Clinic
    Verify Clinic Shortname Field
    [Teardown]    Run Keywords    Close All Browsers    AND    Delete All SMS From Mailosaur

Noona Admin Can Add New Clinic And Delete Clinic
    [Tags]    nms9-ver-190    nms9-ver-199    sms
    Login As Admin with 2FA
    User Selects Add New Clinic
    User Fills In Clinic Information
    Admin User Selects Save
    User Checks Clinic Status
    # nms9-ver-199
    Select Created TA Clinic
    Click Delete And Confirm
    Input Verification Code
    Wait Until Page Contains    Clinic deletion requested successfully
    [Teardown]    Run Keywords    Close All Browsers    AND    Delete All SMS From Mailosaur


*** Keywords ***
Click Delete And Confirm
    Delete All SMS From Mailosaur
    Try To Click Element    ${delete_clinic_button}
    Wait Until Page Contains    Are you sure you want to delete the clinic?
    Try To Click Element    ${confirm_button}

Input Verification Code
    Get Noona Admin SMS From Mailosaur
    Try To Click Element     ${admin_sms_code_first_input}
    Press Keys    None       ${get_sms_code}
