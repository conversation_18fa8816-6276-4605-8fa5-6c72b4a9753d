*** Settings ***
Documentation       F03NA05 Noona administrator can resolve patient's clinic

Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Admin
Test Teardown       Close All Browsers
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f03na05    management


*** Variables ***
${patient_phone_no}         +358123456789
${find_patients_link}       find-patient-link
${patient_search_input}     //div[contains(@class,"inputs-area")]//label/../input
${submit_button}            //button[contains(@type,"submit")]
${patient_clinic}           TA clinic Automated_tests
${found_clinic}             //div[contains(@class,"found-clinic")]
${clinic_table}             clinic-


*** Test Cases ***
Administrator Can Resolve Patient's Clinic
    [Tags]    nms9-ver-194
    Navigate To Find patients
    Enter Patient ID
    Select Search
    Patient's Clinic Is Displayed On The Right
    Enter Patient Phone Number
    Select Search
    Patient's Clinic And ID Is Displayed On The Right


*** Keywords ***
Navigate To Find patients
    Wait Until Page Contains Element    ${clinic_table}
    Try To Click Element    ${find_patients_link}

Enter Patient ID
    IF    '${ENVIRONMENT}'=='test'
        Set Test Variable    ${patient_id}    ${f03na05_patient}[id_test]
    ELSE
        Set Test Variable    ${patient_id}    ${f03na05_patient}[id_staging]
    END
    Try To Input Text    ${patient_search_input}    ${patient_id}

Select Search
    Try To Click Element    ${submit_button}

Patient's Clinic Is Displayed On The Right
    Wait Until Element Contains    ${found_clinic}    ${patient_clinic}

Enter Patient Phone Number
    Try To Input Text    ${patient_search_input}    ${patient_phone_no}

Patient's Clinic And ID Is Displayed On The Right
    Wait Until Element Contains    ${found_clinic}    ${patient_clinic}
    Wait Until Element Contains    ${found_clinic}    ${patient_id}
