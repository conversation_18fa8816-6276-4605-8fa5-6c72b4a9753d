*** Settings ***
Documentation       F04NA07_1 Noona administrator can edit a Noona administrator (Admin OIDC)

Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}common.resource
Resource            ${EXECDIR}${/}resources${/}management${/}manage_admins.resource

Suite Setup         Set Libraries Order
Test Setup          Login As Admin
Test Teardown       Close Browser
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f04na07_1    management    noona_admin


*** Variables ***
${admin_list_first_row}     user-0
${prefix}                   aaa
${admin_number}             +3580000


*** Test Cases ***
Main success scenario - Noona Administrator Can Edit A Noona Administrator (Admin OIDC)
    [Tags]    nms9-ver-522
    Select Manage Admins In Top Navigation
    Select A User
    Updating User Information Can Be Cancelled Using Cancel Buttton
    Edit Any Of The Following User Data
    Updated Information Can Be Saved Using Save Button


*** Keywords ***
Select Manage Admins In Top Navigation
    Wait Until Element Is Visible    ${manage_admins_link}
    Try To Click Element    ${manage_admins_link}
    Wait Until Element Is Not Visible    ${noona-loader}    timeout=30s

Select A User
    Wait Until Noona Loader Is Not Visible    timeout=40s
    Wait Until Element Is Visible    ${admin_list_first_row}    timeout=40s
    Try To Click Element    ${admin_list_first_row}
    Wait Until Page Contains Element    ${first_name_input}

Updating User Information Can Be Cancelled Using Cancel Buttton
    Wait Until Page Contains Element    ${cancel_button}
    Try To Click Element    ${cancel_button}
    Wait Until Page Contains Element    ${admin_list_first_row}

Edit Any Of The Following User Data
    Select A User
    Clear Text From All Input Fields
    Generate Random Admin Data
    Enter Generated Random Admin Data

Clear Text From All Input Fields
    Try To Click Element    ${first_name_input}
    Clear Element Text    ${first_name_input}
    Try To Click Element    ${first_name_input}
    Clear Element Text    ${last_name_input}
    Try To Click Element    ${email_input}
    Clear Element Text    ${email_input}
    Try To Click Element    ${phone_number_input}
    Clear Element Text    ${phone_number_input}

Generate Random Admin Data
    [Documentation]    I used {prefix}==AAA to ensure that the first admin on list is always the one edited
    ${f_name}    Generate Random String    10    [LOWER][NUMBERS]
    ${first_name}    Catenate    ${prefix}${f_name}
    Log    ${first_name}
    Set Test Variable    ${first_name}
    ${l_name}    Generate Random String    10    [LOWER][NUMBERS]
    ${last_name}    Catenate    ${prefix}${l_name}
    Log    ${last_name}
    Set Test Variable    ${last_name}
    ${admin_email}    Catenate    ${first_name}.${last_name}@noona.fi
    Set Test Variable    ${admin_email}
    Log    ${admin_email}

Enter Generated Random Admin Data
    Try To Input Text    ${first_name_input}    ${first_name}
    Try To Input Text    ${last_name_input}    ${last_name}
    Try To Input Text    ${email_input}    ${admin_email}
    Try To Input Text    ${phone_number_input}    ${admin_number}

Updated Information Can Be Saved Using Save Button
    Wait Until Element Is Enabled    ${save_button}
    Try To Click Element    ${save_button}
    Wait Until Noona Loader Is Not Visible
    Wait Until Page Contains    Updated
    Try To Click Banner Message
    Confirm Updated Information

Confirm Updated Information
    Wait Until Page Contains Element    ${admin_list_first_row}
    Try To Click Element    ${admin_list_first_row}
    Wait Until Element Is Visible    ${first_name_input}
    Text Should Be In The Page    ${first_name}
    Text Should Be In The Page    ${last_name}
    Text Should Be In The Page    ${admin_email}
