*** Settings ***
Documentation       F05SP01 Support person can exchange anonymous messages with patient about feedback
...                 Preconditions:
...                 - Patient has sent feedback to support and allowed support to contact the patient back
...                 - [file:./test/patient/more/F05P01.robot|F05P01 Patient has to be able to send anonymous feedback to support]
...                 - Support person is logged in admin console
...                 NOTE: This was updated to manual to exclude from the day run as this feature is already removed from requirements

Resource            ${EXECDIR}${/}resources${/}management${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource

Suite Setup         Run Keywords    Set Libraries Order    AND    Login As Admin
Suite Teardown      Close All Browsers
Test Teardown       Go To Management Frontpage
Test Timeout        ${TEST_TIMEOUT}

Force Tags          usecase-f05sp01    management    manual


*** Variables ***
${support-icon}     css:.support-icon
${feedback-row}     //div[contains(@class,'feedback-thread')]
${msg-chain}        //p[@class='content']


*** Test Cases ***
Support person can exchange anonymous messages with patient about feedback
    [Tags]    nms9-ver-220    manual
    Support person navigates to feedback page
    Support person selects and opens a feedback message received by a patient
    Support person enters reply message
    Support person sends reply message
    Sent reply message is displayed last in reply message chain
    Feedback message is marked as read

Support person closes case without reply - ext. A
    [Tags]    nms9-ver-221    manual
    Support person navigates to feedback page
    Support person selects and opens a feedback message received by a patient
    Feedback message is marked as read


*** Keywords ***
Go To Management Frontpage
    ${url}    Get Location
    @{base}    Split String    ${url}    \#
    Go To    ${base}[0]

Support person navigates to feedback page
    Wait Until Keyword Succeeds    9    1    Click Element    ${support-icon}

Support person selects and opens a feedback message received by a patient
    Wait Until Keyword Succeeds    9    1    Click Element    (${feedback-row})[3]

Support person enters reply message
    ${now}    Get Current Date    exclude_millis=True
    Set Test Variable    ${test-feedback-answer}    ${now} robot feedback answer
    Wait Until Keyword Succeeds    9    1    Input Text    feedback-input    ${test-feedback-answer}

Support person sends reply message
    Wait Until Keyword Succeeds    9    1    Click Element    css:.send

Sent reply message is displayed last in reply message chain
    Wait Until Element Contains    (${msg-chain})[1]    ${test-feedback-answer}

Feedback message is marked as read
    ${class}    Get Element Attribute    (${feedback-row})[3]    class
    Should Not Contain    ${class}    new

Support person navigates to Support in top navigation
    Support person navigates to feedback page

Patient's message and possible earlier conversations with this patient are displayed
    Try To Click Element    (${feedback-row})[3]
    Wait Until Element Contains    (${msg-chain})[1]    robot
    Try To Click Element    (${feedback-row})[3]
    Wait Until Element Contains    (${msg-chain})[2]    robot

Support person types a message and clicks Send
    Support person enters reply message
    Support person sends reply message
    Sent reply message is displayed last in reply message chain
