*** Settings ***
Documentation       Login tests for clinic and patient

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers

Force Tags          login_tests


*** Variables ***
${edit_users_column}    clinic
${work_queue}           new-messages
${diary_icon}           //h1[@id="header-page-title" and text()="Diary "]


*** Test Cases ***
Clinic Admin Login
    [Tags]    cli    noona_admin
    Login as Nurse    clinic=${automated_tests_clinic}[name]   user_type=${USER_TYPE}[noona_admin]
    Wait Until Page Contains Element    ${edit_users_column}

Clinic Nurse Login
    [Tags]    login_tests_master
    Login as Nurse    email=${automated_tests_clinic}[default_user]    clinic=${automated_tests_clinic}[name]
    Wait Until Page Contains Element    ${work_queue}

Clinic Manager Login
    Login as Nurse    email=${automated_tests_clinic}[default_manager]    clinic=${automated_tests_clinic}[name]
    Wait Until Page Contains Element    ${work_queue}

Patient Login
    [Tags]    login_tests_master
    Login As Patient    email=${CLINIC_PATIENT}[email]
    Wait Until Page Contains Element    ${diary_icon}
