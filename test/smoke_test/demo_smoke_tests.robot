*** Settings ***
Documentation       Smoke tests for clinic and patient of Demo site.
...                 To verify that data gets wiped and inserted successfully
...                 To verify that Demo site is working correctly after wipe and insertion of data

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers

Force Tags          demo_smoke_test


*** Variables ***
${clinic_user_demo}                 <EMAIL>
${work_queue}                       new-messages
${clinic_demo}                      Smoke Test
${patient_1_full_name}              <PERSON>
${patient_2_full_name}              Jon <PERSON>
${patient_search}                   //*[contains(@id, "input-search")]
${patient_table}                    //*[@id="table-noona-template"]
${enter_patient}                    //*[@id="table-noona-template"]//*[text()="{}"]
${patient_name_header}              //*[@id="patient-name"]/span[text()="{}"]
${diary}                            Diary
${library_button}                   //*[@id="navigation-library-link"]
${approve}                          //*[@for="approved-checkbox"]
${patient_consent}                  Terms of use and privacy statement
${next}                             //*[@class="ds-button__label" and text()="Next"]
${keep_me_logged_in_yes_patient}    //input[@id='remember-me-yes']/following-sibling::label


*** Test Cases ***
Verify That First Patient Is Inserted And Can Be Searched
    Setup For Login As Nurse
    Search Patient By Name And Validate    ${patient_1_full_name}

Verify That Last Patient Is Inserted And Can Be Searched
    Setup For Login As Nurse
    Search Patient By Name And Validate    ${patient_2_full_name}

Verify That CCD For First Patient Is Available
    Login To Noona Demo    patient    ${CLINIC_PATIENT_DEMO_1}[email]
    Accept Consent If Patient Loging First Time Otherwise Verify Patient Home Page Is Displayed
    # Todo: Replace with Go To Library keyword in below line once duplicate keyword is removed
    # Todo: ...    from either library.resource or shared_add_menu.resource
    Try To Click Element    ${library_button}
    Click Medical Records Intro
    Select All Records
    Click View Patient Records
    Display First CCD Record
    Verify Todays Date Is Present
    Click CCD Record
    Verify Todays Date Is Present
    Close CCD Record Modal
    Close CCD Record List Modal

Verify That CCD For Last Patient Is Available
    Login To Noona Demo    patient    ${CLINIC_PATIENT_DEMO_2}[email]
    Accept Consent If Patient Loging First Time Otherwise Verify Patient Home Page Is Displayed
    # Todo: Replace with Go To Library keyword in below line once duplicate keyword is removed
    # Todo: ...    from either library.resource or shared_add_menu.resource
    Try To Click Element    ${library_button}
    Click Medical Records Intro
    Select All Records
    Click View Patient Records
    Display First CCD Record
    Verify Todays Date Is Present
    Click CCD Record
    Verify Todays Date Is Present
    Close CCD Record Modal
    Close CCD Record List Modal


*** Keywords ***
Setup For Login As Nurse
    Login To Noona Demo    nurse    email=${clinic_user_demo}
    Wait Until Page Contains Element    ${work_queue}

Search Patient By Name And Validate
    [Arguments]    ${search_text}
    Try To Input Text    ${patient_search}    ${search_text}
    Press Keys    None    RETURN
    Wait Until Page Contains Element    ${patient_table}
    ${split_text}    Split String    ${search_text}
    ${patient_entry}    Evaluate    '${enter_patient}'.format('${split_text[1]}')
    Try To Click Element    ${patient_entry}
    ${patient_header}    Evaluate    '${patient_name_header}'.format('${search_text}')
    Wait Until Page Contains Element    ${patient_header}

Verify Todays Date Is Present
    ${date}    Get Time    day,month,year
    Wait Until Page Contains    ${date[2]}.${date[1]}.${date[0]}

Accept Consent If Patient Loging First Time Otherwise Verify Patient Home Page Is Displayed
    Verify Keep Me Logged-in Is Successful
    ${status}    Run Keyword And Return Status    Wait Until Page Contains    ${patient_consent}
    IF    ${status}
        Approve Consent-1
        Approve Consent-2
        Verify That Patient Is Logged-in Successfully
    ELSE
        Verify That Patient Is Logged-in Successfully
    END

Approve Consent-1
    Wait Until Page Contains Element    ${approve}
    Click Element    ${approve}
    Click Element    ${next}

Approve Consent-2
    Sleep    1s
    Wait Until Page Contains Element    ${approve}
    Click Element    ${approve}
    Click Element    ${next}

Verify Keep Me Logged-in Is Successful
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${keep_me_logged_in_yes_patient}
    IF    ${status}
        Try To Click Element    ${keep_me_logged_in_yes_patient}
        Try To Click Element    ${keep_me_logged_in_next}
    END

Verify That Patient Is Logged-in Successfully
    Wait Until Page Contains    ${diary}    15s
