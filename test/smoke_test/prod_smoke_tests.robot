*** Settings ***
Documentation       Production smoke tests for admin, clinic and patient
...                 Quality Assurance Test Checklist
...                 Please ensure that the following tests are conducted for all environments (US, CA, EU, AU):

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}nurse_profile.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}analytics${/}reporting_page.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource


Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers

Force Tags          prod_smoke_tests


*** Variables ***
${work_queue}                                                       new-messages
${diary_text}                                                       This is your diary
${test_patient_ssn}                                                 040624-1
${test_clinic_user_fullname}                                        Operations Engineer
#staging patient ssn: 260500A649R
#staging clinic user full name:  Lisa Lynt
${test_patient_fullname}                                            Generic Tester
${admin_landing_page_login}                                         //*[@id="open-login"]
${analytics_link_option}                                            //*[@id="provider-analytics-link"]
${reporting_link_option}                                            //*[@id="reporting-link"]
${clinic_user_logout_link}                                          //*[@id="logout-link"]
${reporting_symptoms_analytic_button}                               //*[@id="mat-menu-panel-0"]//span[contains(text(), "Symptoms")]
${reporting_enrollment_analytic_button}                             //*[@id="mat-menu-panel-0"]//span[contains(text(), "Enrollment")]
${reporting_case_management_analytic_button}                        //*[@id="mat-menu-panel-0"]//span[contains(text(), "Case Management")]
${reporting_clinic_intiated_messages_symptoms_analytic_button}      //*[@id="mat-menu-panel-0"]//span[contains(text(), "Clinic Initiated Messages")]
${reporting_filter_apply_button}                                    //*[@class="noona-reporting-filters__actions"]//button[1]
${microsoft_signin_page_url}                                        https://login.microsoftonline.com/


*** Test Cases ***
Verify Admin Login Page Is Accessible
    [Documentation]    Verify that the admin login page is displayed correctly.
    [Tags]    prod_smoke_tests    prod_admin
    Open Noona Admin PROD    ${MANAGEMENT_LOGIN_URL}

Patient Login And Report A Symptom
    [Documentation]    Test patient has treatment module: Chemotherapy 18 symptoms
    ...    Verify the patient login process.
    ...    Test the capability to report symptoms.
    ...    Confirm that the logout function is operational.
    [Tags]    prod_smoke_tests    prod_patient
    Login To Noona PROD    patient    email=${PATIENT_USERNAME}    password=${PATIENT_PASSWORD}
    Wait Until Page Contains    ${diary_text}
    Add Nausea Or Vomiting Symptom To Diary
    Logout As Patient
    Patient Is Logged Out

Verify Clinic User's Functionality
    [Documentation]    Verify functionality of the clinic login page, search and logout.
    [Tags]    prod_smoke_tests    prod_clinician
    Login To Noona PROD    nurse    email=${NURSE_USERNAME}    password=${NURSE_PASSWORD}
    Wait Until Work Queue Is Fully Loaded
    Search Patient By Identity Code    ${test_patient_ssn}
    Return To Work Queue
    Logout As Nurse
    Clinic User Is Logged Out

Verify Work Queue Functionality
    [Documentation]    Verify that clinic user can select a patient case from the care team work queue, close case
    [Tags]    prod_smoke_tests    prod_work_queue
    Login To Noona PROD    patient    email=${PATIENT_USERNAME}    password=${PATIENT_PASSWORD}
    Wait Until Page Contains    ${diary_text}
    Navigate to Clinic
    Ask About Other Symptom
    Close Browser
    Login To Noona PROD    nurse    email=${NURSE_USERNAME}    password=${NURSE_PASSWORD}
    Wait Until Work Queue Is Fully Loaded
    Clinic User Selects And Processes The Patient Case From Work Queue
    Clinic User Returns To Workqueue Page
    The Test Patient's Case Is No Longer Visible On The Clinic User's Work Queue
    Close Browser

Clinic User Can Open And See Noona Analytics Reporting Page
    [Documentation]    Verify functionality of a clinic's analytics page
    [Tags]    prod_smoke_tests    prod_analytics
    Login To Noona PROD    nurse    email=${NURSE_USERNAME}    password=${NURSE_PASSWORD}
    Clinic User Opens Analytic Reporting Page
    Clinic User Can Filter Enrollment Analytics Data


*** Keywords ***
Open Noona Admin PROD
    [Arguments]    ${MANAGEMENT_LOGIN_URL}    ${open_browser}=True
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${MANAGEMENT_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE IF    ${open_browser}
        Open Browser    ${MANAGEMENT_LOGIN_URL}    ${BROWSER}
    END
    Reload Page
    Wait Until Page Contains Element    ${admin_landing_page_login}    timeout=30s
    Wait Until Element Is Visible    ${admin_landing_page_login}
    Click Element    ${admin_landing_page_login}
    # We keep this condition until OIDC migration is completely done to all Noona's customers.
    ${page_location}    Get Location
    Location Should Contain    ${microsoft_signin_page_url}
    Wait Until Element Is Visible    ${ms_sso_email_textbox}

Login To Noona PROD
   [Arguments]    ${user}    ${email}    ${password}=${EMPTY}    ${open_browser}=True
    ${url}    Set Variable If
    ...    '${user}'=='patient'
    ...    ${PATIENT_LOGIN_URL}
    ...    '${user}'=='nurse'
    ...    ${NURSE_LOGIN_URL}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists} and ${open_browser}
        Open Browser    ${url}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE IF    ${open_browser}
        Open Browser    ${url}    ${BROWSER}
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Page Should Not Contain    503 Service Temporarily Unavailable
    IF    '${user}'=='patient'
        Accept All Cookies If Visible
        Wait Until Location Contains    /patient/#/sign-in
        Wait Until Noona Loader Is Not Visible
        Login As Patient From OIDC Login Page   ${email}    ${password}
    ELSE IF    '${user}' == 'nurse'
        Accept All Cookies If Visible For Clinic
        Login As Nurse PROD    ${email}    ${password}
        IF    '${ENVIRONMENT}'=='prod-us' or '${ENVIRONMENT}'=='prod-ca'
            RETURN
        ELSE IF    '${ENVIRONMENT}'=='prod-eu' or '${ENVIRONMENT}'=='staging'
            Keep Me Logged In    Yes
        END
    END

Login As Nurse PROD
    [Arguments]    ${email}    ${password}
    Wait Until Element Is Visible    ${email_textbox}
    Try To Input Text    ${email_textbox}    ${email}
    Input Password    ${password_textbox}    ${password}
    Wait Until Element Is Enabled    ${login_button}
    Try To Click Element    ${login_button}

Clinic User Returns To Workqueue Page
    Wait Until Page Contains Element    ${back_to_button}
    Click Element    ${back_to_button}
    Wait Until Noona Loader Is Not Visible

Clinic User Opens Analytic Reporting Page
    Wait Until Page Contains Element    ${analytics_link_option}
    Try To Click Element    ${analytics_link_option}
    Wait Until Page Contains Element    ${reporting_link_option}
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    ${reporting_link_option}
    Wait Until Location Contains    nurse#/reporting
    Acknowledge Reporting Page Disclaimer If It Is Visible
    Wait Until Noona Loader Is Not Visible

Clinic User Can Filter Enrollment Analytics Data
    Wait Until Page Contains Element    ${reporting_enrollment_analytic_button}    30s
    Try To Click Element    ${reporting_enrollment_analytic_button}
    Wait Until Page Contains Element    ${reporting_filter_apply_button}    30s
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    ${reporting_filter_apply_button}
    Wait Until Noona Loader Is Not Visible
    Element Text Should Be    //*[@class="noona-reporting-header__title"]    Enrollment

Wait Until Work Queue Is Fully Loaded
    Wait Until Location Contains    new-messages
    Wait Until Page Contains Element    ${work_queue}
    Wait Until Element Is Visible    ${work_queue}
    Wait Until Noona Loader Is Not Visible

The Test Patient's Case Is No Longer Visible On The Clinic User's Work Queue
    Wait Until Work Queue Is Fully Loaded
    Wait Until Page Does Not Contain    ${test_patient_fullname}

Clinic User Selects And Processes The Patient Case From Work Queue
    Remove All Care Team Filter
    IF    '${ENVIRONMENT}'=='prod-us'
        Set Test Variable    ${care_team_name}    Test Team
    ELSE IF    '${ENVIRONMENT}'=='prod-ca'
        Set Test Variable    ${care_team_name}    test care team
    ELSE IF    '${ENVIRONMENT}'=='prod-eu'
        Set Test Variable    ${care_team_name}    Test Care team
    ELSE IF    '${ENVIRONMENT}'=='staging'
        Set Test Variable    ${care_team_name}    Ace Team
        Set Test Variable    ${test_clinic_user_fullname}    Lisa Lynt
    END
    Select Care Team Filter In Work Queue    ${care_team_name}
    Select Patient Card    ${test_patient_fullname}
    Case Is Assigned To Correct User    ${OTHER_SYMPTOM}    ${test_clinic_user_fullname}
    IF    '${ENVIRONMENT}'=='prod-us' or '${ENVIRONMENT}'=='staging'
        # Case outcome is enabled in PROD US and Staging
        Close Case With Outcome    Provider Consulted
    ELSE
        Close Case
    END

Patient Page Is Opened With Patient Cases As Default Tab
    Wait Until Page Contains     //*[text()=" Patient cases "]
