*** Settings ***
Documentation       Login tests for clinic and patient of Demo site

Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource            ${EXECDIR}${/}resources${/}shared_login.resource

Suite Setup         Set Libraries Order
Test Teardown       Close All Browsers

Force Tags          demo_smoke_test    demo_login_tests


*** Variables ***
${work_queue}                       new-messages
${diary}                            Diary
${clinic_manager_demo}              <EMAIL>
${clinic_demo}                      Smoke Test
${approve}                          //*[@for="approved-checkbox"]
${patient_consent}                  Terms of use and privacy statement
${next}                             //*[@class="ds-button__label" and text()="Next"]
${keep_me_logged_in_yes_patient}    //input[@id='remember-me-yes']/following-sibling::label


*** Test Cases ***

Clinic Nurse Login
    Login To Noona Demo    nurse    email=${clinic_manager_demo}
    Wait Until Page Contains Element    ${work_queue}

Patient Login
    Login To Noona Demo    patient    ${CLINIC_PATIENT_DEMO_2}[email]
    Accept Consent If Patient Loging First Time Otherwise Verify Patient Home Page Is Displayed


*** Keywords ***
Accept Consent If Patient Loging First Time Otherwise Verify Patient Home Page Is Displayed
    Verify Keep Me Logged-in Is Successful
    ${status}    Run Keyword And Return Status    Wait Until Page Contains    ${patient_consent}
    IF    ${status}
        Approve Consent-1
        Approve Consent-2
        Verify That Patient Is Logged-in Successfully
    ELSE
        Verify That Patient Is Logged-in Successfully
    END

Approve Consent-1
    Wait Until Page Contains Element    ${approve}
    Click Element    ${approve}
    Click Element    ${next}

Approve Consent-2
    Sleep    1s
    Wait Until Page Contains Element    ${approve}
    Click Element    ${approve}
    Click Element    ${next}

Verify Keep Me Logged-in Is Successful
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${keep_me_logged_in_yes_patient}
    IF    ${status}
        Try To Click Element    ${keep_me_logged_in_yes_patient}
        Try To Click Element    ${keep_me_logged_in_next}
    END

Verify That Patient Is Logged-in Successfully
    Wait Until Page Contains    ${diary}    15s
