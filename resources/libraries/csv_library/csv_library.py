"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
import csv
import re
from robot.api.deco import keyword
from robot.libraries.BuiltIn import BuiltIn, logger


class CsvLibrary(object):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(CsvLibrary, self).__init__()

    @keyword
    def get_column_values(self, file_name, separator=";"):
        """Return values of a column from CSV file

        Note: white spaces are removed and non-breaking
        \xa0 is replaced with one space.

        :param file_name: path to the CSV file
        :param separator: delimiter
        :param skip_header: skip header row
        :return: list containing column values
        """
        logger.info("opening file: {0}".format(file_name))
        language_code = BuiltIn().get_variable_value("${LANGUAGE}")
        with open(file_name, "r") as file:
            reader = csv.DictReader(file, delimiter=separator)
            next(reader)
            column_values = []
            for row in reader:
                if row[language_code]:
                    row_val = re.sub(" +", " ", row[language_code].strip())
                    row_val = row_val.replace('""', '"')
                    row_val = row_val.replace("<u>", "")
                    row_val = row_val.replace("</u>", "")
                    row_val = row_val.replace("NULL", "")
                    row_val = row_val.replace("\t", " ")
                    row_val = row_val.replace("<center>", "")
                    row_val = row_val.replace("</center>", "")
                    row_val = row_val.replace("<br />", "")
                    row_val = row_val.replace("<b>", "")
                    row_val = row_val.replace("</b>", "")
                    column_values.append(row_val)
            return column_values

    @keyword
    def get_clinic_title(self, file_name, separator=";"):
        """Return the first name of the QoL
        in the form to be able to access the form from clinic UI

        Note: white spaces are removed and non-breaking
        \xa0 is replaced with one space.

        :param file_name: path to the CSV file
        :param separator: delimiter
        :return: clinic title for QoL
        """
        logger.info("opening file: {0}".format(file_name))
        language_code = BuiltIn().get_variable_value("${LANGUAGE}")
        with open(file_name, "r") as file:
            reader = csv.DictReader(file, delimiter=separator)
            return next(reader)[language_code]