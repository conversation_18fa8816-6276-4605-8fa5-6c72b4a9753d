import os, glob, zipfile


def diary_download_should_contain_pdf_signature(dir):
    for filepath in glob.glob(os.path.join(dir, '*.pdf')):
        with open(filepath, 'rb') as file:
            if '%PDF-' not in str(file.read())[:9]:
                raise Exception('\n\n-- File \'%s\' does not have a PDF file signature' % filepath)


def symptoms_download_should_contain_string(dir, symptom_str):
    for filepath in glob.glob(os.path.join(dir, '*.zip')):
        with zipfile.ZipFile(filepath) as zip_ref:
            zip_ref.extractall(dir)

    for filepath in glob.glob(os.path.join(dir, '**/*' + symptom_str + '*.csv'), recursive=True):
        with open(filepath) as file:
            content = file.read()
            if symptom_str not in content or ',' not in content:
                raise Exception(
                    '\n\n-- CSV file \'%s\' does not contain string \'%s\' or comma' % (filepath, symptom_str))


def medical_records_download_should_contain_html(dir, medical_str, ccd_download_zip_password=None):
    for filepath in glob.glob(os.path.join(dir, '*.zip')):
        with zipfile.ZipFile(filepath) as zip_ref:
            zip_ref.extractall(pwd=bytes(ccd_download_zip_password, 'utf-8'))

    for filepath in glob.glob(os.path.join(dir, '**/*' + medical_str + '*.html'), recursive=True):
        with open(filepath) as file:
            content = file.read()
            if medical_str not in content or ',' not in content:
                raise Exception(
                    '\n\n-- HTML file \'%s\' does not contain string \'%s\' or comma' % (filepath, medical_str))
