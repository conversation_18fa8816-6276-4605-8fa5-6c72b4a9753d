"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""

import imaplib
import email
from robot.api.deco import keyword

@keyword
def get_attachments(index):
    files=[]
    mail = imaplib.IMAP4_SSL('imap.mail.yahoo.com')
    mail.login('<EMAIL>', 'mxyupxdpxtnzwnmr')
    mail.select('inbox')

    result, data = mail.uid('fetch',index, '(RFC822)')
    m = email.message_from_string(data[0][1])
    if m.get_content_maintype() == 'multipart':
        for part in m.walk():
            #logger.console(part)

        #find the attachment part
            if part.get_content_maintype() == 'multipart': continue
            if part.get('Content-Disposition') is None: continue

        #save the attachment in the program directory
            filename = part.get_filename()
            files.append(filename)
            fp = open(filename, 'wb')
            fp.write(part.get_payload(decode=True))
            fp.close()
    return files
