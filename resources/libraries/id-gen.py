from random import randint

def get_random_finnish_id(real = False):
    t = {0:'0', 1:'1', 2:'2', 3:'3', 4:'4', 5:'5', 6:'6', 7:'7', 8:'8', 9:'9', 10:'A',
    11:'B', 12:'C', 13:'D', 14:'E', 15:'F', 16:'H', 17:'J', 18:'K', 19:'L', 20:'M',
    21:'N', 22:'P', 23:'R', 24:'S', 25:'T', 26:'U', 27:'V', 28:'W', 29:'X', 30:'Y' }
    dd = format(randint(1,28), '02d')
    mm = format(randint(1,12), '02d')
    yy = str(randint(22,99))
    if real:
        nnn = format(randint(2,899), '03d')
    else:
        nnn = str(randint(900,999))
    if int(nnn) % 2 == 0:
        gender = 'female'
    else:
        gender = 'male'
    return dd + mm + yy + '-' + nnn + t[int(dd+mm+yy+nnn)%31], gender
