"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.api.deco import keyword


class ModuleReader(object):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()

    @keyword
    def get_symptoms(self, file_name):
        """Return symptoms from TXT file

        :param file_name: path to the TXT file
        :return: list of symptoms
        """
        symptoms = []
        with open(file_name) as file:
            for symptom in file:
                if "<b>." in symptom:
                    result = symptom.split(".")[1].rstrip("\n")
                    symptoms.append(result)
        return symptoms

    @keyword
    def get_questions(self, file_name, symptom):
        """Return questions and answers of specified symptom

        :param file_name: path to the TXT file
        :param symptom: name of the symptom
        :return: list of questions and answers
        """
        questions_found = False
        questions_section = []
        with open(file_name) as file:
            for line in file.readlines():
                if "<b>." + symptom in line:
                    questions_found = True
                    result = line.split(".")[1]
                    questions_section.append(result.rstrip("\n"))
                    continue
                if questions_found:
                    if line.startswith(("----", "END OF MODULE")):
                        questions_found = False
                    else:
                        questions_section.append(line.rstrip("\n"))
        return questions_section
