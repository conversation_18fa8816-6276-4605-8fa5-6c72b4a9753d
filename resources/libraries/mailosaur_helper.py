"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""

from mailosaur import MailosaurClient
from mailosaur.models import SearchCriteria
from robot.api.deco import keyword
from datetime import datetime, timedelta


# Available in the API tab of a server
# email_api_key = "ZcrxxFqY0oyUX42d"          [ Noona Automated Email 3 ]
# email_server_id = "ujbmenad"                [ Noona Automated Email 3 ]
# sms_api_key = "ZBzuprq4roLdfwNg"            [ Noona Automated SMS ]
# sms_server_id = "tiazhwes"                  [ Noona Automated SMS ]


@keyword
def get_email_message(email_server_id, email_api_key, email, timeout=120):
    email_mailosaur = MailosaurClient(email_api_key)
    timeout = timeout * 1500
    criteria = SearchCriteria()
    criteria.sent_to = email
    message = email_mailosaur.messages.get(email_server_id, criteria, timeout=timeout)
    return message.text.body


def get_html_body(email_server_id, email_api_key, email):
    email_mailosaur = MailosaurClient(email_api_key)
    criteria = SearchCriteria()
    criteria.sent_to = email
    message = email_mailosaur.messages.get(email_server_id, criteria)
    return message.html.body


def get_link_in_email(email_server_id, email_api_key, email):
    email_mailosaur = MailosaurClient(email_api_key)
    criteria = SearchCriteria()
    criteria.sent_to = email
    message = email_mailosaur.messages.get(email_server_id, criteria)
    first_link = message.html.links[0]
    return first_link.href


def get_email_subject(email_server_id, email_api_key, email):
    email_mailosaur = MailosaurClient(email_api_key)
    criteria = SearchCriteria()
    criteria.sent_to = email
    message = email_mailosaur.messages.get(email_server_id, criteria)
    return message.subject


def get_message_id(server_id, server_api_key, message):
    mailosaur = MailosaurClient(server_api_key)
    last_hour = datetime.today() - timedelta(hours=1)
    criteria = SearchCriteria()
    criteria.sent_to = message
    message = mailosaur.messages.get(server_id, criteria, received_after=last_hour)
    return message.id

def delete_a_message_in_server(server_api_key, message_id):
    mailosaur = MailosaurClient(server_api_key)
    mailosaur.messages.delete(message_id)
    print(f'Message {message_id} has been deleted successfully')


def delete_all_messages_in_server(email_server_id, email_api_key):
    email_mailosaur = MailosaurClient(email_api_key)
    email_mailosaur.messages.delete_all(email_server_id)


def get_sms_message(sms_server_id, sms_api_key, number):
    sms_mailosaur = MailosaurClient(sms_api_key)
    criteria = SearchCriteria()
    criteria.sent_to = number
    message = sms_mailosaur.messages.get(sms_server_id, criteria)
    return message.text.body


def get_link_in_sms(sms_server_id, sms_api_key, number):
    sms_mailosaur = MailosaurClient(sms_api_key)
    last_hour = datetime.today() - timedelta(hours=1)
    criteria = SearchCriteria()
    criteria.sent_to = number
    message = sms_mailosaur.messages.get(sms_server_id, criteria, received_after=last_hour)
    returned_link = message.text.links[0]
    return returned_link.href
