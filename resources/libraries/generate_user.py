import datetime
import json
import requests
import yaml


class GenerateUser(object):
    def __init__(self, host, port):
        self._host = host
        self._port = port
        self._conn = NoonaConnection(self._host, self._port)

    def login_to_management(self, user, password, clinic):
        self._conn.login_as_management(user, password, clinic)

    def login_to_clinic(self, user, password, clinic):
        self._conn.login_as_clinic(user, password, clinic)

    def login_to_patient(self, user, password, clinic):
        self._conn.login_as_patient(user, password, clinic)

    def populate_data(self, file_path):
        data = self._read_file(file_path)
        endpoint = "{}://{}:{}{}".format(
            "http",
            self._host,
            self._port,
            "/api/patient",
        )
        self._bindings = {}
        treatment_bindings = self._get_treatments(endpoint)
        self._bindings["treatment"] = treatment_bindings
        if "care_teams" in data.keys():
            care_team_bindings = self._push_care_teams(data["care_teams"], endpoint)
            self._bindings["care_teams"] = care_team_bindings
        if "users" in data.keys():
            user_bindings = self._push_users(data["users"], endpoint)
            self._bindings["users"] = user_bindings

    def _read_file(self, file_path):
        with open(file_path, 'r') as yaml_file:
            return yaml.safe_load(yaml_file)

    def _get_treatments(self, endpoint):
        bindings = {}
        treatments = self._conn.request_as_clinic("getClinicTreatmentModules", [])
        for treatment in treatments:
            treatment_key = treatment["treatmentModuleType"]
            bindings[treatment_key] = treatment

        return bindings

    def _push_care_teams(self, care_teams, endpoint):
        bindings = {}
        for care_team in care_teams:
            care_team_result = self._conn.request_as_clinic("addCareTeam", [{"name": care_team}])
            bindings[care_team] = care_team_result

        return bindings

    def _push_users(self, users, endpoint):
        bindings = {}
        for user in users:
            for user_key in user.keys():
                user_data = user[user_key]
                translated_data = self._translate_user_data(user_data)
                user_id = self._conn.request_as_clinic("addPatient", translated_data)
                bindings[user_key] = user_id

        return bindings

    def _translate_user_data(self, user_data):
        user_information = {
            "birthDate": user_data["birth_date"],
            "emailAddress": user_data["email"],
            "firstName": user_data["first_name"],
            "gender": user_data["gender"],
            "identityCode": user_data["ssn"],
            "lastName": user_data["last_name"],
            "phoneNumber": user_data["phone"],
            "phoneNumberType1": user_data["phone_type"],
        }

        treatment = self._bindings["treatment"][user_data["module"]]
        care_team = self._bindings["care_teams"][user_data["subscriptions"]]

        user_parameters = [user_information]
        user_parameters.append(user_data["locale_id"])
        user_parameters.append(user_data["icd_10_code"])
        user_parameters.append(treatment)
        user_parameters.append(None)
        user_parameters.append(None)
        user_parameters.append([care_team])

        return user_parameters


class NoonaConnection(object):
    def __init__(self, host, port):
        self._host = host
        self._port = port
        self._management_login = None
        self._clinic_login = None
        self._patient_login = None

        endpoint = "{}://{}:{}{}".format(
            "http",
            self._host,
            self._port,
            "/api",
        )
        self._management_endpoint = "{}{}".format(
            endpoint,
            "/management",
        )
        self._clinic_endpoint = "{}{}".format(
            endpoint,
            "/clinic",
        )
        self._patient_endpoint = "{}{}".format(
            endpoint,
            "/patient",
        )

    def login_as_management(self, user, password, clinic):
        self._management_login = self._login(user, password, self._management_endpoint, clinic)

    def login_as_clinic(self, user, password, clinic):
        self._clinic_login = self._login(user, password, self._clinic_endpoint, clinic)

    def login_as_patient(self, user, password, clinic):
        self._patient_login = self._login(user, password, self._patient_endpoint, clinic)

    def _login(self, user, password, endpoint, desired_clinic='TA clinic Default_Hospital'):
        params = [{
            "account": user,
            "password": password,
        }]
        self.send_request(self._clinic_endpoint, 'verifyLoginCredentials', params)
        clinics = self.send_request(self._clinic_endpoint, 'getUserClinics', params)
        clinic = next(clinic for clinic in clinics if clinic.get('name', '') == desired_clinic)
        clinic_id = clinic.get('clinicId', '')
        params = [{
            'clinicId': clinic_id,
            'account': user,
            'rememberMe': None,
            'password': password,
        }]
        login_details = self.send_request(self._clinic_endpoint, 'login', params)
        login_token = login_details.get('loginToken', '')
        user_id = login_details.get('userId', '')
        return {
            'login_token': login_token,
            'user_id': user_id,
        }

    def request_as_management(self, method, params):
        return self.send_request(self._management_endpoint, method, params, self._management_login)

    def request_as_clinic(self, method, params):
        return self.send_request(self._clinic_endpoint, method, params, self._clinic_login)

    def request_as_patient(self, method, params):
        return self.send_request(self._patient_endpoint, method, params, self._patient_login)

    def send_request(self, endpoint, method, params, login_details=None, get_json_result=True):
        request_payload = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params,
            "id": 1,
        }
        headers = {}
        if login_details:
            headers = {
                'X-LOGIN-TOKEN': login_details.get('login_token', '')
            }
        print(str('=================='))
        def convert_dates(node):
            if isinstance(node, datetime.date):
                return str(node)

        response = requests.post(
            endpoint,
            data=(json.dumps(request_payload, default=convert_dates) + '\n'),
            headers=headers
        )
        return_data = response.text
        if get_json_result:
            try:
                return_data = json.loads(return_data).get('result', [])
            except:
                pass
        print(str(method))
        print(str((json.dumps(request_payload, default=convert_dates) + '\n')))
        print(str(return_data))
        print(str('=================='))
        return return_data
