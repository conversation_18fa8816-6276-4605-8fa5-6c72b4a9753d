# MODULE QUESTION RETRIEVER
# BEFORE USE PLEASE CHANGE/CREATE FILE PATHS ACCORDING TO YOUR SPECIFIC FILE PATHS
# CREATES FILES FOR EACH MODULE AND PRINTS QUESTIONS IN THE MODULE PER SYMPTOM FORM
# CHANGE 'LANGUAGE_COLUMN' VALUE FOR DIFFERENT LANGUAGES

# TODO: GI surgery, urination.csv bugaa
# TODO: Pain pointerin tarkat lokaatiot esille pdfään.
# TODO: Rash on outo lomake.
# TODO: Immunologic treatments, rash.csv
# TODO: <PERSON><PERSON> te<PERSON>, joku indikaattori siitä, tai kir<PERSON><PERSON><PERSON><PERSON><PERSON>in piirto pdfään.
# TODO: K<PERSON>y moduuleja läpi ja jos ne on kunnossa, push
# TODO: ENTÄ JOS MODUULIN NIMI ON SUBSET JOSTAIN PIDEMMÄSTÄ? SILLOIN 'IN' EI TOIMI!
# TODO: INDIKAATIO SUKUPUOLESTA.
# TODO: INDIKAATIO BASELINE VAI DIARY?

import os
import csv
from configparser import ConfigParser
import stringcase
import re
import os.path
from os import path


# STATICS
# STATIC COLUMN NUMBERS IN CSV
SHOW_CONDITIONS = 9
STATIC_CONDITIONS = 10
#  FINNISH_COLUMN = 11
#  ENGLISH_COLUMN = 12, FOR EXAMPLE.
ENUMERATION_KEY = 1
FIELD_TYPE = 0
ID_COLUMN = 4
TYPE_COLUMN = 3
SEPARATOR_DUPLICATES = "--------------------------------------------------------------------------------"
SEPARATOR = "------------------------------------------------------------------------------------------------------------------------"
SEPARATOR_START = "START FOR SECTION "
SEPARATOR_END = "END OF MODULE"
HEADER = "header:"
QUESTION = "question:"
ANSWER_OPTION = "answer option:"
painbodytext = "(A clickable body image is shown in this part of the symptom form for selecting symptomatic areas.)"
# (A clickable body image is shown in this part of the symptom form for selecting symptomatic areaaaas.)
# "(Klikattava vartalokuva näytetään tässä oireellisten alueiden valintaa varten.)"
actual_occurrences = 0  # FOR CHECKING PURPOSES
ln_simple = False
sex_ind = False
baseline_ind = False
html_spaces = "•<i>"
html_spaces_end = "</i>"

# FILE PATHS --> CHANGE IF NEEDED
FILE_PATH_FOR_MODULES = "/Users/<USER>/noona/noona-content/src/main/resources/forms/csv/cancer/"
FILE_PATH_FOR_MODULES_TWO = "/Users/<USER>/noona/noona-content/src/main/resources/forms/csv/noona/cancer/"
FILE_PATH_FOR_SYMPTOM_FORMS_TWO = "/Users/<USER>/noona/noona-content/src/main/resources/forms/csv/noona/cancer/forms/"
FILE_PATH_FOR_SYMPTOM_FORMS = "/Users/<USER>/noona/noona-content/src/main/resources/forms/csv/cancer/forms/"
FILE_PATH_FOR_OUTPUT = "/Users/<USER>/PycharmProjects/tools/Output/"
FILE_PATH_FOR_SYMPTOM_NAMES_PROPERTIES_FI = "/Users/<USER>/noona/noona-common/src/main/resources/symptom_names_fi_FI.properties"
FILE_PATH_FOR_SYMPTOM_NAMES_PROPERTIES = "/Users/<USER>/noona/noona-common/src/main/resources/symptom_names.properties"
FILE_PATH_FOR_SYMPTOM_NAMES_PROPERTIES_SV = "/Users/<USER>/noona/noona-common/src/main/resources/symptom_names.properties_fi_SV.properties"
FILE_PATH_MESSAGES = "/Users/<USER>/noona/noona-common/src/main/resources/messages.properties"
forwarding_module_key = "nurse.managePatients.treatmentModule."
text_on_sex_male = "Visible if the patient is male."
text_on_sex_female = "Visible if the patient is female."
baseline_ind_text = " (Visible if the symptom is part of a questionnaire sent by the clinic.)"
type_post_treatment_is = " PLACEHOLDER NOT IN YET"
type_post_treatment_is_not = " (PLACEHOLDER NOT IN YET"

def delete(path):
    for the_file in os.listdir(path):
        file_path = os.path.join(path, the_file)
        try:
            if os.path.isfile(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(e)


def get_symptom_name_prop(FILE_PATH_FOR_SYMPTOM_NAME, symptom):
    from py.checkAutomated import rpl_unicode
    forwarding_form_key = "fe.formNames."
    skip = False
    global displayed_symptom_name

    if 'messages' in FILE_PATH_FOR_SYMPTOM_NAME:
        forwarding_form_key = ""
        symptom = symptom.replace("=", "")
        skip = True
    else:
        forwarding_form_key = forwarding_form_key

    if FILE_PATH_MESSAGES.endswith("messages.properties"):
        wanted_encoding = 'ISO-8859-1'
    else:
        wanted_encoding = 'utf-8'

    with open(FILE_PATH_FOR_SYMPTOM_NAME, encoding=wanted_encoding) as f:
        file_cont = '[dummy_section]\n' + f.read()
    config_parser = ConfigParser(strict=False)
    config_parser.read_string(file_cont)
    if skip is False:
        symptom = symptom.replace(" ", "_").replace(",", "").lower()
        symptom = stringcase.camelcase(symptom)

    displayed_symptom_name = config_parser.get('dummy_section', forwarding_form_key + symptom)
    try:
        if displayed_symptom_name != "":
            displayed_symptom_name = rpl_unicode(displayed_symptom_name)
            return displayed_symptom_name
        if displayed_module_name == "":
            print("Found empty translation for key " + (forwarding_form_key + symptom) + ".")
            displayed_symptom_name = ("?" + (forwarding_form_key + symptom) + "?")
        return displayed_symptom_name
    except:
        print("Couldn't retrieve symptom name for key " + (
                    forwarding_form_key + symptom) + " from " + FILE_PATH_FOR_SYMPTOM_NAME + ".")
        displayed_symptom_name = (forwarding_form_key + file)
        return displayed_symptom_name


def get_ln():
    global LANGUAGE_COLUMN, ln_one, ln_two, ln_locations, ln_duplicates,\
        ln_asked, ln_simple, locale, FILE_PATH_MESSAGES, bolder, bolder_end, baseline_ind

    # Duplikaattia...

    locales = ["", "_fi_FI",
               "_sv_FI",
               "_da_DK",
               "_no_NO",
               "_de_DE",
               "_fr_FR",
               "_es_ES",
               "_it_IT",
               "_pt_PT",
               "_ar_IL",
               "_iw_IL",
               "_nl_NL",
               "_tr_TR"]

    bolder = "<b>"
    bolder_end = "</b>"
    determine = input("What language? (11 for Finnish, 12 for English and 13 for Swedish.) ")

    if determine == "11":
        locale = locales[1]
        text_on_sex_male = "Näkyvissä, jos potilas on mies."
        text_on_sex_female = "Näkyvissä, jos potilas on nainen."
        baseline_ind = "Näkyvissä, jos oire on osa klinikalta lähetettyä kyselyä."
    elif determine == "13":
        locale = locales[2]
    elif determine == "17":
        locale = locales[6]
    else:
        locale = locales[0]

    get_indication_on_baseline = input("Indicate baseline and form diff between questionnaires? (y/n)? ")
    if get_indication_on_baseline.lower().strip(" ") == "y":
        baseline_ind = True

    '''
    use_simplified = input("Use simplified projection (y/n)? ")
    if use_simplified.lower().strip(" ") == "y":
        ln_simple = True
    '''
    ln_simple = True
    '''
    get_indication_on_sex = input("Indicate what sex the patient has? (y/n)? ")
    if get_indication_on_sex.lower().strip(" ") == "y":
        sex_ind = True
    '''
    LANGUAGE_COLUMN = int(determine)

    # DETERMINE FINNISH OR ENGLISH
    if LANGUAGE_COLUMN == 11 or LANGUAGE_COLUMN == 13:
        ln_one = "Oirelomakkeessa "
        ln_two = " potilaalle näytetään:"
        ln_locations = "Määrittele kivun sijainti: "
        ln_duplicates = "Seuraavat esitetään käyttäjälle vähintään kahteen kertaan: "
        ln_asked = " kysytään oireissa "
    else:
        ln_one = "In the symptom form concerning "
        ln_two = " the patient is shown the following content: "
        ln_locations = "Pain locations in the body (Pain pointer):"
        ln_duplicates = "The following are straight duplicates in this module: "
        ln_asked = " is asked in symptom forms "

    print("Parsing...")


    FILE_PATH_MESSAGES = FILE_PATH_MESSAGES.replace(".properties", locale + ".properties")

    return LANGUAGE_COLUMN, ln_one, ln_two, ln_locations, ln_duplicates, ln_asked, ln_simple, locale, FILE_PATH_MESSAGES


def determine_path():
    global FILE_PATH_FOR_MODULES, FILE_PATH_FOR_MODULES_TWO, FILE_PATH_FOR_SYMPTOM_FORMS, FILE_PATH_FOR_SYMPTOM_FORMS_TWO

    if path.exists(FILE_PATH_FOR_SYMPTOM_FORMS) == False:
        FILE_PATH_FOR_MODULES = "/Users/<USER>/noona/noona-content/src/main/resources/forms/csv/noona/cancer/"
        FILE_PATH_FOR_SYMPTOM_FORMS = "/Users/<USER>/noona/noona-content/src/main/resources/forms/csv/noona/cancer/forms/"
        return FILE_PATH_FOR_MODULES, FILE_PATH_FOR_SYMPTOM_FORMS

    if os.path.isdir(FILE_PATH_FOR_SYMPTOM_FORMS_TWO) and \
            len(os.listdir(FILE_PATH_FOR_SYMPTOM_FORMS_TWO)) \
            >= len(os.listdir(FILE_PATH_FOR_SYMPTOM_FORMS)):
        FILE_PATH_FOR_SYMPTOM_FORMS = FILE_PATH_FOR_SYMPTOM_FORMS_TWO
    else:
        FILE_PATH_FOR_SYMPTOM_FORMS = FILE_PATH_FOR_SYMPTOM_FORMS

    if os.path.isdir(FILE_PATH_FOR_MODULES_TWO) and \
            len(os.listdir(FILE_PATH_FOR_MODULES_TWO)) \
            >= len(os.listdir(FILE_PATH_FOR_MODULES)):
        FILE_PATH_FOR_MODULES = FILE_PATH_FOR_MODULES_TWO
    else:
        FILE_PATH_FOR_MODULES = FILE_PATH_FOR_MODULES

    return FILE_PATH_FOR_MODULES, FILE_PATH_FOR_SYMPTOM_FORMS


def get_module_symptoms(file):
    global module_symptoms, actual_occurrences, FILE_PATH_FOR_MODULES, FILE_PATH_FOR_OUTPUT, f2
    # CLEAR OUT LIST FOR NEXT MODULE
    module_symptoms = []
    if os.path.exists(FILE_PATH_FOR_OUTPUT + file) is False:
        f2 = open(FILE_PATH_FOR_OUTPUT + file, "w+")
    FILE_PATH_FOR_MODULES = determine_path()[0]
    # OPEN MODULE SPECIFICATION FILE AND SEE LISTED SYMPTOM FORMS
    if (FILE_PATH_FOR_MODULES + file).endswith(".txt"):
        with open(FILE_PATH_FOR_MODULES + file) as f:
            for line in f:
                if '=' in line or line == "":
                    dummy = True
                else:
                    if line == "eating problems ":  # HARDCODED WORKAROUND
                        line = "eating problems"  # HARDCODED WORKAROUND
                    else:
                        line = line.replace("\n", "").replace(" ", "_")
                        if line.endswith("_"):
                            line = line[:-1]
                    module_symptoms.append(line)  # APPEND SYMPTOM FORMS INTO A LIST
                    module_symptoms = list(filter(None, module_symptoms))  # REMOVE BLANKS
            actual_occurrences += len(
                module_symptoms)  # FOR CHECKING PURPOSES --> WE HAVE ALL THE SYMPTOM OCCURRENCES IN THE MODULES

        return actual_occurrences, module_symptoms


def eval_show(show_condition, ids, enums, misses, missed_enums, questions, mock):
    show_condition = show_condition.lower()
    operands = [" contains any ",
                " over ",
                " is null "]

    parts = []
    conditions = []
    hits = 0
    bothHaveToBeTrue = False
    eitherHaveToBeTrue = False
    not_break = False

    # TODO: KERRO MITÄ PITÄÄ PAINAA, ETTÄ TULEE ESIIN.

    if " and " in show_condition:
        parts = show_condition.split(" and ")
        for part in parts:
            for operand in operands:
                if operand.strip() in part:
                    hits += 1
        if hits == len(parts):  # Eli jos on "oikea" && tilanne ehdon kanssa.
            bothHaveToBeTrue = True
            for p in parts:
                change = p.replace("is null", "' in misses").replace("contains any", "hörönplöö").replace("over",
                                                                                                          "hörönplöö")
                if "' in misses" in change:
                    change = ('"' + change[:change.find("' in misses")].strip() + '" in misses')
                i = parts.index(p)
                parts.remove(p)
                if change.find("hörönplöö") != -1:
                    changeid = p[0:change.find("hörönplöö")].strip()
                    foundEnumsFromCond = change[change.find("hörönplöö"):].replace("hörönplöö", "").strip().split(",")
                    change = 'questions.get("' + changeid + '")'
                    foundEnumsFromDict = eval(change)
                    lenChange = 'len(questions.get("' + changeid + '"))'
                    if foundEnumsFromDict is None:  # Avainta ei löydy.
                        foundEnumsFromDict = []
                    change = "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)"
                if change == "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)":
                    parts.insert(i, str(('"' + str(eval(change)) + '"')))
                else:
                    parts.insert(i, change)

                parts[:] = ['1 > 2' if x == '"False"' else x for x in parts]
                parts[:] = ['1 == 1' if x == '"True"' else x for x in parts]

            if bothHaveToBeTrue:
                for k in parts:
                    not_break = eval(k)
                    if not_break is False:
                        return False
                    else:
                        continue
                if parts[parts.index(k)] == parts[-1] and not_break:
                    return True

    elif " or " in show_condition:
        parts = show_condition.split(" or ")
        for part in parts:
            for operand in operands:
                if operand.strip() in part:
                    hits += 1
        if hits == len(parts):  # Eli jos on "oikea" OR tilanne ehdon kanssa.
            eitherHaveToBeTrue = True
            for p in parts:
                change = p.replace("is null", "' in misses").replace("contains any", "hörönplöö").replace("over",
                                                                                                          "hörönplöö")
                if "' in misses" in change:
                    change = ('"' + change[:change.find("' in misses")].strip() + '" in misses')
                i = parts.index(p)
                parts.remove(p)
                if change.find("hörönplöö") != -1:
                    changeid = p[0:change.find("hörönplöö")].strip()
                    foundEnumsFromCond = change[change.find("hörönplöö"):].replace("hörönplöö", "").strip().split(",")
                    change = 'questions.get("' + changeid + '")'
                    foundEnumsFromDict = eval(change)
                    lenChange = 'len(questions.get("' + changeid + '"))'
                    if foundEnumsFromDict is None:  # Avainta ei löydy.
                        foundEnumsFromDict = []
                    change = "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)"
                if change == "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)":
                    parts.insert(i, str(('"' + str(eval(change)) + '"')))
                else:
                    parts.insert(i, change)

                parts[:] = ['1 > 2' if x == '"False"' else x for x in parts]
                parts[:] = ['1 == 1' if x == '"True"' else x for x in parts]

            if eitherHaveToBeTrue:
                for k in parts:
                    try:
                        not_break = eval(k)
                        if not_break:
                            return True
                        else:
                            continue
                    except:
                        continue
                if parts[parts.index(k)] == parts[-1] and not_break is False:
                    return False

    if bothHaveToBeTrue is False or eitherHaveToBeTrue is False:
        change = show_condition.replace("is null", "' in misses").replace("contains any", "hörönplöö").replace("over",
                                                                                                               "hörönplöö")
        if "' in misses" in change:
            change = ('"' + change[:change.find("' in misses")].strip() + '" not in questions')
            return eval(change)
        if change.find("hörönplöö") != -1:
            changeid = show_condition[0:change.find("hörönplöö")].strip()
            foundEnumsFromCond = change[change.find("hörönplöö"):].replace("hörönplöö", "").strip().split(", ")
            change = 'questions.get("' + changeid + '")'
            foundEnumsFromDict = eval(change)
            lenChange = 'len(questions.get("' + changeid + '"))'
            if foundEnumsFromDict is None:  # Avainta ei löydy.
                return False
            pekka = len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond)))
            sari = len(foundEnumsFromDict)
            change = "len(list(dict.fromkeys(foundEnumsFromDict + foundEnumsFromCond))) < len(foundEnumsFromDict + foundEnumsFromCond)"
            kirjoitetaan = eval(change)
            return kirjoitetaan


def get_this_pain_pointer_enums(file, questions, thisid):
    data = csv.reader(file, delimiter=";")
    for i in data:
        questions.setdefault(thisid, []).append(i[ENUMERATION_KEY].lower().strip())
        if not ''.join(i).strip() or i[ID_COLUMN].lower().strip() != thisid and i[ID_COLUMN].strip() != "":
            return questions

def get_module_handle_file_and_file_match(file, symptom_name):
    file_match = file.replace("active-treatment", "").replace("recovery_", "").replace("-", " ").replace(".txt", "")
    if file_match.startswith("recovery"):
        file_match = file_match[len("recovery"):]
    if file_match.startswith("_"):
        file_match = file_match[len("_"):]
        file_match = file_match.replace("_", " ").lower()
    # handle_file = exact file name with directory for symptom form file name
    handle_file = (FILE_PATH_FOR_SYMPTOM_FORMS + symptom_name + ".csv")  # FULL FILE PATH
    return handle_file, file_match

def get_calendar_element():
    calendar_question = get_symptom_name_prop(FILE_PATH_MESSAGES, "patient.wizard.symptom.date").strip()
    calendar_first = get_symptom_name_prop(FILE_PATH_MESSAGES, "patient.wizard.symptomDateOption.symptomToday").strip()
    calendar_second = get_symptom_name_prop(FILE_PATH_MESSAGES, "patient.wizard.symptomDateOption.symptomConstant").strip()
    calendar_third = get_symptom_name_prop(FILE_PATH_MESSAGES, "patient.wizard.symptomDateOption.symptomOtherDates").strip()

    return calendar_question, calendar_first, calendar_second, calendar_third

def write_calendar_element(calendar_question, calendar_first, calendar_second, calendar_third):
    f2.write("\n" + calendar_question + "\n")
    f2.write(html_spaces + calendar_first + html_spaces_end + "\n")
    f2.write(html_spaces + calendar_second + html_spaces_end + "\n")
    f2.write(html_spaces + calendar_third + html_spaces_end + "\n")

def instantiate_file_crawl_number(symptom_name, numbers):
    if symptom_name != "":
        symptom_name = symptom_name.replace("_", " ").capitalize() + ","
        numbers += 1  # AMOUNT OF ACTUAL ITERATIONS SHOULD EQUAL WITH actual_occurrences
    return numbers, symptom_name

def write_symptom_headers(numbers, ln_simple, add, symptom_name):
    if numbers >= 1 and ln_simple:
        add += 1
        f2.write("\n" + SEPARATOR
                 + "\n" + bolder + str(add) + ". " + get_symptom_name_prop(
            (FILE_PATH_FOR_SYMPTOM_NAMES_PROPERTIES.replace(".properties", (locale)) +
             ".properties"), symptom_name) + bolder_end + "\n" + "\n")
    else:
        f2.write("\n" + SEPARATOR
                 + "\n" + ln_one + symptom_name + ln_two + "\n" + "\n")
    return numbers, add, symptom_name

def symptom_is_question_or_other_and_to_be_shown(file_match, row):
    rs = row[STATIC_CONDITIONS]
    # or "form is not questionnaire" in row[STATIC_CONDITIONS].strip():
    if file_match in rs.lower() and row[FIELD_TYPE] != "" \
            and rs.lstrip().lower().startswith("treatment module contains any") \
    or rs.strip() == "" and row[FIELD_TYPE].strip() != "" or row[
        FIELD_TYPE].strip() != "" and rs.lstrip().lower().startswith("gender") \
    or row[FIELD_TYPE].strip() != "" and \
            rs.strip() == "form is full" or (file_match) in rs.lower() \
            and rs.strip() == "form is full" \
            or "form is not questionnaire" in rs and "treatment module contains any" not in rs.strip()\
            or "form is questionnaire" in rs:
            return True # IF IS POSSIBLY AN INCLUDED QUESTION

def determine_new_helper_line(row_index):
    global additional_new_line
    if row_index > 0:
        additional_new_line = "\n"
    else:
        additional_new_line = ""

def determine_quest_type_info(row):
    if "form is full" in row[STATIC_CONDITIONS]:
        row[LANGUAGE_COLUMN] += baseline_ind_text
        '''
    if "form is questionnaire" in row[STATIC_CONDITIONS]:
        row[LANGUAGE_COLUMN] += "(Visible if the symptom is part of a questionnaire sent by the clinic.)"
    if "form is not questionnaire" in row[STATIC_CONDITIONS]:
        row[LANGUAGE_COLUMN] += "(Visible if the symptom is part of a questionnaire sent by the clinic.)"
        '''

def get_questions():
    # GLOBAL VARIABLES
    global skip, file, text, FILE_PATH_FOR_SYMPTOM_FORMS, FILE_PATH_FOR_MODULES, questions_shown, shown_enums, not_shown_questions, questions

    #  LANGUAGE CHECK
    get_ln()

    #  DETERMINE PATH, MIGHT BE OBSOLETE
    FILE_PATH_FOR_MODULES = determine_path()[0]

    FILE_PATH_FOR_SYMPTOM_FORMS = determine_path()[1]

    # Wizard-element sections
    calendar_question, calendar_first, calendar_second, calendar_third = get_calendar_element()

    # ID:S FOR MISSES AS A LIST
    # QUESTIONS THAT ARE BEING SHOWN PER SYMPTOM --> USED FOR TOGGLING WHAT ANSWER OPTIONS WE SHOW
    questions_shown = []
    # DICTIONARY FOR CHECKING IF STRAIGHT DUPLICATES ARE FOUND
    all_questions = {}

    # DELETE FILES FROM OUTPUT
    delete(FILE_PATH_FOR_OUTPUT)

    numbers = 0  # FOR CHECKING PURPOSES
    errors = False  # FOR CHECKING PURPOSES
    add = 0  # FOR PDF
    wizard = False # FOR DESCRIPTION TEXTS

    # "The" Script
    for file in os.listdir(FILE_PATH_FOR_MODULES):

        # IN EACH ITERATION GET MODULE'S SYMPTOMS
        get_module_symptoms(file)

        # file = the module name, symptom_name = symptom form file name
        for symptom_name in module_symptoms:

            handle_file, file_match = get_module_handle_file_and_file_match(file, symptom_name)

            try:

                with open(handle_file) as handle_file_open:

                    row_index = 0
                    numbers, symptom_name = instantiate_file_crawl_number(symptom_name, numbers)
                    questions_shown = []  # EMPTY THE LIST AFTER EVERY FILE ITERATION
                    questions = {}
                    shown_enums = []  # EMPTY THE LIST AFTER EVERY FILE ITERATION
                    not_shown_questions = []
                    missed_enums = []
                    went = False
                    numbers, add, symptom_name = write_symptom_headers(numbers, ln_simple, add, symptom_name)
                    handler = csv.reader(handle_file_open, delimiter=";")  # CSV READER

                    ###############################
                    ####START PARSING FILE HERE####
                    ###############################

                    for row in handler:  # START PARSING OF CSV! TÄMÄ ON KOPIOITUNA ALHAALLA REFAKTOROI TÄHÄN ASTI!
                        # GET QUESTIONS IN SYMPTOM AND IN MODULE

                        if baseline_ind:
                            determine_quest_type_info(row)
                        determine_new_helper_line(row_index)
                        if wizard and row[LANGUAGE_COLUMN].strip() != "" and row[FIELD_TYPE].strip() == "": # hoida tää pois
                            f2.write(row[LANGUAGE_COLUMN].strip() + "\n")
                            wizard = False
                            continue
                        if row[FIELD_TYPE].strip().lower() == "form end" \
                                or row[FIELD_TYPE].strip().lower() == "form begin": # hoida tää pois
                            continue
                        if symptom_is_question_or_other_and_to_be_shown(file_match, row):
                            if row[FIELD_TYPE].lower() == "date range":
                                write_calendar_element(calendar_question, calendar_first, calendar_second, calendar_third)
                                questions.setdefault(row[ID_COLUMN].strip(), [])
                                all_questions.setdefault(calendar_question, []).append(symptom_name.replace(",", ""))  # ADD QUESTIONS TO DICTIONARY
                                continue
                            if row[FIELD_TYPE].strip().lower() == "wizard section":
                                wizard = True
                                continue
                            if row[LANGUAGE_COLUMN].strip() == "" and row[FIELD_TYPE].strip().lower() == "pain pointer":
                                f2.write(additional_new_line + painbodytext + "\n")  # PLACEHOLDER
                                get_this_pain_pointer_enums(handle_file_open, questions, row[ID_COLUMN].strip()) # hoida tää pois
                            elif row[LANGUAGE_COLUMN] != "":
                                if row[SHOW_CONDITIONS].strip() != "":
                                    if eval_show(row[SHOW_CONDITIONS].strip(), questions_shown, shown_enums,
                                                 not_shown_questions, missed_enums, questions, row[LANGUAGE_COLUMN]):
                                        this_question = row[ID_COLUMN].lower().strip()
                                        questions.setdefault(this_question, [])
                                        f2.write(additional_new_line + row[LANGUAGE_COLUMN].replace('""', '"').strip() + "\n")
                                        if row[FIELD_TYPE].lower().strip() == "pain pointer":
                                            f2.write(additional_new_line + painbodytext + "\n")  # PLACEHOLDER
                                            get_this_pain_pointer_enums(handle_file_open, questions,
                                                                        row[ID_COLUMN].lower().strip())
                                        all_questions.setdefault(str(row[LANGUAGE_COLUMN]).strip(), []).append(
                                            symptom_name.replace(",", ""))  # ADD QUESTIONS TO DICTIONARY
                                        questions_shown.append(row[ID_COLUMN].lower())
                                        if row[ENUMERATION_KEY].lower().strip() == "boolean":
                                            questions.setdefault(this_question, []).append("true")
                                            questions.setdefault(this_question, []).append("false")
                                            shown_enums.append("true")
                                            shown_enums.append("false")
                                        went = True
                                    else:
                                        continue
                                elif row[SHOW_CONDITIONS].strip() == "":
                                    this_question = row[ID_COLUMN].lower().strip()
                                    questions.setdefault(this_question, [])
                                    if row[FIELD_TYPE].lower() == "pain pointer":
                                        f2.write(additional_new_line + painbodytext + "\n")  # PLACEHOLDER
                                        get_this_pain_pointer_enums(handle_file_open, questions,
                                                                    row[ID_COLUMN].lower().strip())
                                    f2.write(additional_new_line + row[LANGUAGE_COLUMN].replace('""', '"').strip() + "\n")
                                    all_questions.setdefault(str(row[LANGUAGE_COLUMN]).strip(), []).append(
                                        symptom_name.replace(",", ""))  # ADD QUESTIONS TO DICTIONARY
                                    questions_shown.append(row[ID_COLUMN].lower())
                                    if row[ENUMERATION_KEY].lower().strip() == "boolean":
                                        questions.setdefault(this_question, []).append("true")
                                        questions.setdefault(this_question, []).append("false")
                                        shown_enums.append("true")
                                        shown_enums.append("false")
                                    went = True

                        elif went and row[LANGUAGE_COLUMN] != "" or went and row[
                            ENUMERATION_KEY] != "" and LANGUAGE_COLUMN == ID_COLUMN:
                            if (file_match) in row[STATIC_CONDITIONS].lower() and row[
                                STATIC_CONDITIONS].strip() != "" and \
                                    row[STATIC_CONDITIONS].lstrip().lower().startswith(
                                        "treatment module contains any") or row[STATIC_CONDITIONS].strip() == "" \
                                    or row[STATIC_CONDITIONS].lstrip().lower().startswith(
                                "gender") and "treatment module contains any" not in row[STATIC_CONDITIONS].lower() \
                                    or row[STATIC_CONDITIONS].lstrip().lower().startswith("gender") and \
                                    (file_match) in row[STATIC_CONDITIONS].lower():  # TODO: Or show condition
                                if row[LANGUAGE_COLUMN].strip() != "" or row[ENUMERATION_KEY] != "":
                                    if LANGUAGE_COLUMN != ID_COLUMN:
                                        f2.write(html_spaces + row[LANGUAGE_COLUMN]
                                                 .replace('""', '"').strip() + html_spaces_end + "\n")
                                    shown_enums.append(row[ENUMERATION_KEY].lower().strip())
                                    if this_question is not None:
                                        questions.setdefault(this_question, []).append(
                                            row[ENUMERATION_KEY].lower().strip())
                            else:
                                if row[FIELD_TYPE].strip() != "":
                                    went = False
                                missed_enums.append(row[ENUMERATION_KEY].lower().strip())
                                continue
                            went = True
                        else:
                            if row[FIELD_TYPE].strip() != "" and row[LANGUAGE_COLUMN].strip() != "" and row[
                                ID_COLUMN].strip().lower() != "id":
                                not_shown_questions.append(row[ID_COLUMN].strip().lower())
                            went = False
                        row_index += 1
                        ###############################
                        ##DONE WITH PARSING FILE HERE##
                        ###############################

            except Exception as e:  # TÄHÄN ASTI KOPIOITU! REFAKTOROI TÄHÄN ASTI!
                print(str(
                    e).capitalize())  # IF CAN'T OPEN OR HANDLE A SYMPTOM FORM FILE, ALTHOUGH TOO BIG OF A SCOPE FOR TRY!
                if symptom_name != "":
                    print("I had problems with either opening or handling " + handle_file + ".")
                    # CATCH INCORRECT SYMPTOM FORM NAMES (BUT ALSO OTHER...).
                    errors = True  # FOR CHECKING PURPOSES IN THE END OF SCRIPT
                continue

        # FIND OUT IF WE HAVE DUPLICATES AND WRITE THEM
        text = False
        for key in all_questions:
            if len(all_questions[key]) > 1:  # DUPLICATE KEYS GET MORE VALUES
                text = True

        if text:
            f2.write("\n" + SEPARATOR_END + "\n" + SEPARATOR_DUPLICATES +
                     "\n" + ln_duplicates + "\n" +
                     SEPARATOR_DUPLICATES + "\n")
            for key in all_questions:
                if len(all_questions[key]) > 1:
                    f2.write("\n" + "'" + str(key).strip() + "'" + ln_asked +
                             str(all_questions[key]).replace("[", "").replace("]", "").replace("'", "") + ".")

        # CLEAR OUT FOR NEXT MODULE
        all_questions = {}

        # CLOSE WRITABLE FILE
        add = 0
        f2.close()

    # DELETE FORMS DIRECTORY FROM OUTPUT
    try:
        os.remove(FILE_PATH_FOR_OUTPUT + "forms")
    except:
        print("Forms directory not found.")

    # INFORM USER
    if text:
        print("Duplicates found and they are written to each module .txt file.")

    #  SLEDGEHAMMER CHECK THAT WE HAVE THE INFO WE NEED
    if errors == False and actual_occurrences == numbers:
        print("Was able to handle " + str(numbers) + " occurrences of symptom form files in modules and there are " +
              str(
                  actual_occurrences) + " occurrences of symptom files in the module spec files in total." + "\n" + "We should have all the info we need in the 'Output' folder as long as the spec files are correct and design of the forms is not altered.")
    else:
        print("\n" + "I have " + str(
            actual_occurrences) + " occurrences of symptom forms (that would need to be opened from module spec files), " + "and I opened (a) symptom form(s) " + str(
            numbers) + " time(s).")
        print("Errors occurred. Check prints above for further information." + "\n" +
              "If no errors are printed your module spec files might be incorrect or form files may be missing or the design of the forms altered.")


if __name__ == "__main__":
    get_questions()
