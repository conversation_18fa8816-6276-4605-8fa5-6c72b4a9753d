"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""

from robot.libraries.BuiltIn import BuiltIn
from robot.api.deco import keyword


@keyword
def get_selenium_browser_log():
    """ Return browser console log

    Useful tool to trace browser issues.
    Recommended to use with robot keyword "run keyword if test failed" in teardown.
    :return: browser console log
    """
    selib = BuiltIn().get_library_instance("SeleniumLibrary")
    return selib.driver.get_log("browser")

def remove_duplicated_char_in_string(input):
    """ Return a string after removing the duplication

    Ultilising set() function to convert a string (an iterable) to a new string with distinct characters.
    Order of the characters are not important.
    A helpful helper to sanitize string before giving to "Input Text"-keyword
    """
    s = set(input)

    return "".join(s)
