*** Settings ***
Library         SeleniumLibrary    timeout=${SYS_VAR_PAGE_TIMEOUT}    run_on_failure=common.Capture Screenshot
Library         FakerLibrary
Library         Collections
Library         DateTime
Library         String
Library         OperatingSystem
Library         ImapLibrary2
Library         ${EXECDIR}${/}resources${/}libraries${/}mailosaur_helper.py
Library         csv_library.csv_library.CsvLibrary    WITH NAME    CsvLibrary
Library         modules.module_reader.ModuleReader    WITH NAME    ModuleReader
Library         noona.login.login.Login    WITH NAME    Login
Library         noona.navigation.navigation.Navigation    WITH NAME    Navigation
Library         noona.nurse.patients.patients.Patients    WITH NAME    Nurse.Patients
Library         noona.nurse.patients.patient_details.patient_cases.patient_case.patient_case.PatientCase    WITH NAME    Nurse.PatientCase
Library         noona.nurse.patients.patient_details.questionnaires.questionnaire_inquiry.questionnaire_inquiry.QuestionnaireInquiry    WITH NAME    Nurse.QuestionnaireInquiry
Library         noona.nurse.patients.patient_details.questionnaires.questionnaires.Questionnaires    WITH NAME    Nurse.Questionnaires
Library         noona.nurse.patients.patient_details.general_info.personal_data_update.PersonalDataUpdate    WITH NAME    Nurse.PersonalDataUpdate
Library         noona.nurse.patients.patient_details.patient_education.patient_education_navigation.PatientEducationNavigation    WITH NAME    Nurse.PatientEducation
Library         noona.patient.clinic.clinic_menu.ClinicMenu    WITH NAME    Nurse.ClinicMenu
Library         RequestsLibrary
Library         JSONLibrary
Library         AppiumLibrary    run_on_failure=common.Capture Screenshot
Library         pabot.PabotLib
Resource        ${EXECDIR}${/}resources${/}try_keywords.resource
Resource        ${EXECDIR}${/}resources${/}common.resource
Variables       ${EXECDIR}${/}data${/}vars.yaml
