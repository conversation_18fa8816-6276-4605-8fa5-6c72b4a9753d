*** Settings ***
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Library     ${EXECDIR}${/}resources${/}libraries${/}verify_downloads.py


*** Keywords ***
Prepare Chrome for Clinic Downloads And Login
    [Arguments]    ${user}=Nurse    ${email}=${automated_tests_clinic}[default_user]    ${clinic}=${automated_tests_clinic}[name]    ${remember_login}=Yes
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    ${path}    Join Path    ${TEMPDIR}    autotest-downloads    robot-${now}
    Set Suite Variable    ${downloads-dir}    ${path}
    Create Directory    ${downloads-dir}
    ${chrome-options}    Evaluate    sys.modules['selenium.webdriver'].ChromeOptions()    sys, selenium.webdriver
    ${disabled}    Create List    Chrome PDF Viewer
    ${prefs}    Create Dictionary
    ...    download.default_directory=${downloads-dir}
    ...    plugins.plugins_disabled=${disabled}
    Call Method    ${chrome-options}    add_experimental_option    prefs    ${prefs}
    IF    '${BROWSER}'=='headlesschrome'
        Call Method    ${chrome-options}    add_argument    headless
    END
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    Set Suite Variable    ${remote_url_exists}
    IF    ${remote_url_exists}
        Open Browser
        ...    ${NURSE_LOGIN_URL}
        ...    browser=chrome
        ...    remote_url=${REMOTE_URL}
        ...    options=${chrome-options}
    ELSE
        Open Browser    ${NURSE_LOGIN_URL}    browser=chrome    options=${chrome-options}
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    IF    '${user}'!='admin'
        Accept All Cookies If Visible For Clinic
        Wait until keyword succeeds    9x    1s    Select Language    ${language_value}
    END
    Wait until keyword succeeds    9x    1s    Input Text    ${email_textbox}    ${email}
    Input Password    ${password_textbox}    ${DEFAULT_PASSWORD}
    Click Element    ${login_button}
    ${clin-check}    ${value}    Run Keyword And Ignore Error
    ...    Wait Until Element Is Visible    clinic    timeout=3s
    IF    '${clin-check}'=='PASS'
        Wait until keyword succeeds    3x    1s    Choose Clinic    ${clinic}
    END
    IF    '${remember_login}'!='none'
        Wait until keyword succeeds    3x    1s    Keep Me Logged In    ${remember_login}
    END
