*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource


*** Variables ***
${admin_page_nav_exports_link}          //a[@id='data-export-link']
${clinic_table}                         clinic-
${short_name_field}                     short-name
${short_name_input_characters}          Short-Name
${short_name_input_space}               short name
${short_name_input}                     SHRTNM
${short_name_error_message}             //*[@id="error-for-manage-clinic-form-short-name"]//span[@class="error"]
${short_name_error_text}                Following characters are not allowed:
${clinic_name_input}                    name
${save_new_clinic_button}               //*[@id="save"]
${clinic_enabled_radio_button}          //label[contains(@for,'clinic-enabled-true')]
${clinic_disabled_radio_button}         //label[contains(@for,'clinic-enabled-false')]
${add_new_clinic_button}                add-clinic
${country_code_dropdown}                (//b[contains(@class,'caret')])[1]
${timezone_dropdown}                    (//b[contains(@class,'caret')])[2]
${country_code_input}                   //input[contains(@input-type,'countryCode')]
${timezone_input}                       //input[contains(@input-type,'timeZone')]
${pcsn_input}                           pcsn
${clinic_uses_tenantid_label}           //label[contains(., 'This clinic uses Customer Tenant ID assigned by Varian *')]
${clinic_uses_tenantid_input_true}      //label[@for='usesTenantId-true']
${clinic_uses_tenantid_input_false}     //label[@for="usesTenantId-no"]
${tenant_id_input}                      //*[@id='tenantId']
${tenant_name_input}                    variantenantname
${wipe_data_disabled_button}            //label[contains(@for,'wipe-data-false')]
${list_option}                          //li[contains(@class,'option')]
${mgmt_clinic_table}                    //*[@id="clinic-"]
${clinic_created_confirmation_text}     Clinic created successfully



*** Keywords ***
Get Clinic Short Name
    [Arguments]    ${clinic_name}
    Login As Admin
    Sleep    5s
    Wait Until Page Contains    ${clinic_name}
    Sleep    3s
    ${clinic_shortname}    Get Text    //td[text()="${clinic_name}"]/following-sibling::td[1]
    Scroll Element Into View    //td[text()="${clinic_name}"]/following-sibling::td[1]
    Set Suite Variable    ${clinic_shortname}
    Close Browser

User Selects Add New Clinic
    Wait Until Page Contains Element    ${clinic_table}
    Try To Click Element    ${add_new_clinic_button}

User Fills In Clinic Information
    ${random_string_clinic_name}    Generate Random String
    Set Test Variable    ${clinic_name}    TA clinic ${random_string_clinic_name}
    ${short_name}    Generate Random String
    Set Test Variable    ${short_name}
    ${pcsn_code}    Generate Random String
    Set Test Variable    ${pcsn_code}
    ${tenant_id}    Generate Random String
    Set Test Variable    ${tenant_id}
    ${tenant_name}    Generate Random String
    Set Test Variable    ${tenant_name}
    Wait For Element To Be Present    ${short_name_field}
    Try To Input Text    ${short_name_field}    ${short_name}
    Input Text    ${clinic_name_input}    ${clinic_name}
    Click Element    ${country_code_dropdown}
    Try To Click Element    ${list_option}
    Click Element    ${timezone_dropdown}
    Try To Click Element    ${list_option}
    Input Text    ${pcsn_input}    ${pcsn_code}
    # The next 2 steps are added to disable clinic OIDC as the field is currently always "Yes". For now, as clinic OIDC not yet ready, whenever new clinic is created, it should be non-OIDC clinic.
    Wait Until Page Contains Element   ${clinic_uses_tenantid_label}
    Wait Until Page Contains Element    ${clinic_uses_tenantid_input_true}
    Try To Click Element    ${clinic_uses_tenantid_input_false}
    Wait Until Element Is Not Visible    ${tenant_id_input}
#    Coment the below lines for now as it's blocking DC
#    Input Text    ${tenant_id_input}    ${tenant_id}
#    Input Text    ${tenant_name_input}    ${tenant_name}
    Click Element    ${wipe_data_disabled_button}

Verify Clinic Shortname Field
    ${lowercase}    Generate A Distinctive Random String    7    [LOWER]
    Set Test Variable    ${lowercase}
    ${uppercase}    Generate A Distinctive Random String    7    [UPPER]
    Set Test Variable    ${uppercase}
    ${numbers}    Generate A Distinctive Random String    7    [NUMBERS]
    Set Test Variable    ${numbers}
    ${alphanumeric_1}    Generate A Distinctive Random String    7    [NUMBERS][LETTERS]
    Set Test Variable    ${alphanumeric_1}
    ${alphanumeric_2}   Generate A Distinctive Random String    7    [LETTERS][NUMBERS]
    Set Test Variable    ${alphanumeric_2}
    ${non_default_char_numbers}    Generate A Distinctive Random String    7    \%\=\}\$\+\^\~\*\ä\ö\#\Ň\Ƣ[NUMBERS]
    Set Test Variable    ${non_default_char_numbers}
    ${non_default_char_letters}    Generate A Distinctive Random String    7    \%\=\}\$\+\^\~\*\ä\ö\#\Ň\Ƣ[LETTERS]
    Set Test Variable    ${non_default_char_letters}
    ${special_char}    Set Variable    ~!@#
    Set Test Variable    ${special_char}
    ${utf-8}    Generate A Distinctive Random String    7    漢字øØ¢©½¾Ññ
    Set Test Variable    ${utf-8}
    Wait For Element To Be Present    ${short_name_field}
    Try To Input Text    ${short_name_field}    ${lowercase}
    Page Should Not Contain Element    ${short_name_error_message}
    Try To Input Text    ${short_name_field}    ${uppercase}
    Page Should Not Contain Element    ${short_name_error_message}
    Try To Input Text    ${short_name_field}    ${numbers}
    Page Should Not Contain Element    ${short_name_error_message}
    Try To Input Text    ${short_name_field}    ${alphanumeric_1}
    Page Should Not Contain Element   ${short_name_error_message}
    Try To Input Text    ${short_name_field}    ${alphanumeric_2}
    Page Should Not Contain Element    ${short_name_error_message}
    Try To Input Text    ${short_name_field}    ${non_default_char_numbers}
    Verify Removal Of Letters Or Numbers    ${non_default_char_numbers}
    Try To Input Text    ${short_name_field}    ${non_default_char_letters}
    Verify Removal Of Letters Or Numbers    ${non_default_char_letters}
    Try To Input Text    ${short_name_field}    ${special_char}
    Run Keyword And Ignore Error    Wait Until Error Message Contains Invalid Characters    ${special_char}
    Try To Input Text    ${short_name_field}    ${utf-8}
    Run Keyword And Ignore Error    Wait Until Error Message Contains Invalid Characters    ${utf-8}

Wait Until Error Message Contains Invalid Characters
    [Arguments]    ${text}
    Wait Until Page Contains Element    ${short_name_error_message}
    Wait Until Element Is Visible    ${short_name_error_message}
    ${error_message_text}    Get Text    ${short_name_error_message}
    ${removed_letters_or_numbers_message}    Remove String Using Regexp
    ...    ${error_message_text}
    ...    [Following characters are not allowed: ,]
    Element Should Contain    ${short_name_error_message}    ${short_name_error_text}
    Should Contain    ${text}    ${removed_letters_or_numbers_message}

Verify Removal Of Letters Or Numbers
    [Arguments]    ${text_input}
    ${removed_letters_or_numbers}    Remove String Using Regexp    ${text_input}    [a-zA-Z0-9]
    IF    '${removed_letters_or_numbers}'!='${EMPTY}'
        Wait Until Error Message Contains Invalid Characters    ${removed_letters_or_numbers}
    ELSE
        Page Should Not Contain    ${short_name_error_message}
    END

User Checks Clinic Details
    [Arguments]    ${clinic_name}
    Wait Until Page Contains Element    ${clinic_table}
    Try To Click Element    //td[contains(@class,'clinic-name') and contains(text(),'${clinic_name}')]
    Wait Until Page Contains Element    ${short_name_input}
    Page Should Contain    ${random_string}

Admin User Selects Save
    Try To Click Element    ${save_new_clinic_button}
    Wait Until Page Contains    ${clinic_created_confirmation_text}    timeout=120s

User Checks Clinic Status
    Reload Page
    Wait Until Page Contains Element
    ...    //td[contains(@class,'clinic-name') and contains(text(),'${clinic_name}')]/../td[contains(@class,'clinic-status')]
    Element Should Contain
    ...    //td[contains(@class,'clinic-name') and contains(text(),'${clinic_name}')]/../td[contains(@class,'clinic-status')]
    ...    Enabled

Select Created TA Clinic
    Wait Until Page Contains Element    ${clinic_table}
    Try To Click Element    //td[contains(@class,'clinic-name') and contains(text(),'${clinic_name}')]

Admin User Navigates To Exports Tab
    Try To Click Element    ${admin_page_nav_exports_link}
