

*** Variables ***
${data_export_requests_header_text}                 //h2[contains(text(),'Data export requests')]
${data_export_requests_table}                       //noona-data-export-request-table
${data_export_request_table_header_row}             //tr[contains(@class, data-export-table)]
${data_export_approval_modal}                       //*[@id="data-export-approval-modal"]
${data_export_approval_detail_data}                 (//*[@class="request-detail"])
${data_export_detail_title_status}                  //h5[text()="Status"]
${data_export_detail_title_date_and_time}           //h5[text()="Date and time"]
${data_export_detail_title_clinic}                  //h5[text()="Clinic"]
${data_export_detail_title_requested_by}            //h5[text()="Requested by"]
${data_export_detail_title_description}             //h5[text()="Description"]
${data_export_approval_options}                     //div[@class="request-options"]
${data_export_request_option_item}                  //*[@class="request-options__list-item"]
${data_export_approval_modal_close_button}          //*[@class="modal-actions"]//button[text()="Close"]
${data_export_approval_modal_approve_button}        //*[@class="modal-actions"]//button[text()=" Approve "]
${data_export_approval_modal_info_text1}            Please check the details of the request before approving it.
${data_export_approval_modal_info_text2}            Once approved, the person who requested the export will receive a SMS notification.
${approve_data_export_request_button}               //button[text()=' Approve ']
${data_export_confirmation_header_text}             //h3[contains(text(),'Are you sure?')]
${data_export_confirmation_texts}                   //p[contains(text(),'Are you sure you want to approve this data export request?')]
${data_export_request_cancel_button}                //button[contains(text(),'Cancel')]
${data_export_request_confirm_button}               //button[contains(text(),'Confirm')]
${data_export_request_confirmation_modal_header}    //h3[contains(text(),'Data export request approved')]
${data_export_request_confirmation_modal_text}      //p[contains(text(),'The data export request has been successfully approved.')]
${data_export_request_confirmation_ok_button}       //button[contains(text(),'Ok')]
${pending_data_export_request}                      ${data_export_requests_table}//table//tbody//td[text()='Pending']/following-sibling::td[text()='{}']