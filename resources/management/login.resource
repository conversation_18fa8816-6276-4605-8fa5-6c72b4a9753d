*** Settings ***
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Resource    ${EXECDIR}${/}resources${/}mailosaur.resource


*** Variables ***
${email_textbox}                            email
${password_textbox}                         pwd
${login_button}                             email-and-password-next-button
${language_dropdown}                        locale
${language_value}                           //*[@id='locale']//*[@role='option']/span[text()='{}']


*** Keywords ***
Login As Admin
    [Arguments]    ${email}=${SSO_NOONA_ADMIN}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${MANAGEMENT_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE
        Open Browser    ${MANAGEMENT_LOGIN_URL}    ${BROWSER}
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Try To Click Element    ${mgmt_landing_page_login_button}
    Wait Until Element Is Visible    ${ms_sso_email_textbox}
    Try To Input Text    ${ms_sso_email_textbox}   ${email}
    Try To Click Element    ${ms_sso_login_next_button}
    Try To Input Text    ${sso_password_textbox}    ${SSO_DEFAULT_PASSWORD}
    Try To Click Element    ${sso_pwd_login_button}
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${stay_sign_in}
    IF    ${status}
        Try To Click Element    //*[contains(@type, 'submit') and contains(@id,'idSIButton9')]
    END

Login As Admin With 2FA
    [Arguments]    ${email}=${SSO_NOONA_ADMIN_2FA}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${MANAGEMENT_LOGIN_URL}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE
        Open Browser    ${MANAGEMENT_LOGIN_URL}    ${BROWSER}
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Try To Click Element    ${mgmt_landing_page_login_button}
    Log    "Multifactor authentication with noona_admin oidc enabled is not supported for logging in as noona admin"
    Wait Until Element Is Visible    ${ms_sso_email_textbox}
    Try To Input Text    ${ms_sso_email_textbox}   ${email}
    Try To Click Element    ${ms_sso_login_next_button}
    Try To Input Text    ${sso_password_textbox}    ${SSO_DEFAULT_PASSWORD}
    Try To Click Element    ${sso_pwd_login_button}
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${stay_sign_in}
    IF    ${status}
        Try To Click Element    //*[contains(@type, 'submit') and contains(@id,'idSIButton9')]
    END

Logout As Admin
    [Arguments]    ${admin_sso_email}=${SSO_NOONA_ADMIN}
    Try To Click Element    ${logout_link_1}
    Wait Until Page Contains Element    ${mgmt_landing_page_login_button}

Input Noona Admin Verification Code
    Get Noona Admin SMS From Mailosaur
    Wait Until Page Contains Element    ${oidc_sms_input_admin}
    Try To Input Text    ${oidc_sms_input_admin}    ${get_sms_code}

Get Noona Admin SMS From Mailosaur
    [Documentation]    mobile sent to number should be without country code
    ${sms_message}    User Received SMS
    ${get_sms_code}    Get Substring    ${sms_message}    0    6
    Set Global Variable    ${get_sms_code}

