*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${clinic_settings_link}                             clinic-settings-link
${integration_settings_tab}                         integration-settings
${laboratory_results_label}                         //h4[contains(text(),'Laboratory Results')]
${lab_results_visible_yes}                          //*[@data-testid="lab-results-enabled"]//label
${lab_results_visible_no}                           //*[@data-testid="lab-results-disabled"]//label
${lab_results_notifications_yes}                    //*[@data-testid="lab-result-notifications-enabled"]
${lab_results_notifications_no}                     //*[@data-testid="lab-result-notifications-disabled"]
${lab_results_multi_notif_no}                       //*[@data-testid="lab-result-notify-once-per-day-disabled"]
${lab_results_multi_notif_yes}                      //*[@data-testid="lab-result-notify-once-per-day-enabled"]
${integration_save_button}                          //*[@data-testid="save"]
${save_toast_message}                               //div[@id="toast-container"]
${integration_header}                               //h4[text()="Integration"]
${patient_messaging_header_text}                    Patient messaging
${medication_list_setting_label}                    //*[contains(text(), " Should the medication list be available to the patient? ")]
${medication_list_setting_enabled_btn}              //*[@data-testid="medication-list-enabled"]//label
${medication_list_setting_disabled_btn}             //*[@data-testid="medication-list-disabled"]//label
${common_fhir_integration_settings}                 //*[@class='noona-fhir-integration-form-inner']
${customer_tenant_id}                               //*[@data-testid='tenant-id']
${data_integrations_checkbox}                       //*[contains(text(),"{}")]//ancestor::formly-field[1]/../formly-field[1]//mat-checkbox
${fhir_integration_settings_enabled_text}           Is the FHIR integration enabled for the customer?
${integration_enabled_for_clinic_text}              Is integration enabled for the clinic?
${aria_site_id_mandatory_id_text}                   ARIA Site ID is a mandatory patient identifier.
${fhir_integration_enabled_btn}                     //*[@data-testid='fhir-integration-enabled']//label
${fhir_integration_disabled_btn}                    //*[@data-testid='fhir-integration-disabled']//label
${integration_for_clinic_enabled_btn}               //*[@data-testid='integration-enabled']//label
${integration_for_clinic_disabled_btn}              //*[@data-testid='integration-disabled']//label
${patient_status_change_enabled_text}               Is patient status change integration enabled?
${status_change_integration_endpoint}               //*[@data-testid='statusChangeIntegrationEndpoint']
${status_change_integration_endpoint_value}         noonatest.com
${status_change_integration_username}               //*[@data-testid='statusChangeIntegrationUsername']
${status_change_integration_username_value}         username
${status_change_integration_password}               //*[@data-testid='statusChangeIntegrationPassword']
${status_change_integration_password_value}         password-1
${mapping_ariaid1_text}                             Mapping of ARIAID1
${mrn_ssn_ins_integration_text}                     Allow MRN/SSN/INS update through integration
${aria_care_team_initialisation_text}               Is ARIA care team initialisation enabled?
${patientmessage_questionnaire_casesummery_text}    Patient message, questionnaire and case summary export integration enabled?
${data_lake_integration_label}                      //*[contains(text(), " Is data lake integration enabled? ")]
${data_lake_integration_enabled_btn}                //*[@data-testid="datalake-integration-enabled"]//label
${data_lake_integration_disabled_btn}               //*[@data-testid="datalake-integration-disabled"]//label
${dli_account_name_text}                            //*[contains(text(), " Account name ")]
${dli_account_name_input}                           //input[@data-testid="datalakeConfiguration-accountName"]
${dli_account_name_value}                           ddata_lake_integration_account_name
${dli_container_name_text}                          //*[contains(text(), " Container name ")]
${dli_container_name_input}                         //input[@data-testid="datalakeConfiguration-containerName"]
${dli_container_name_value}                         data_lake_integration_container_name
${dli_encryption_key_url_text}                      //*[contains(text(), " Encryption key url ")]
${dli_encryption_key_url_input}                     //input[@data-testid="datalakeConfiguration-encryptionKey"]
${dli_encryption_key_url_value}                     data_lake_integration_encryption_key_url
${dli_public_key_text}                              //*[contains(text(), " Public key ")]
${dli_public_key_input}                             //input[@data-testid="datalakeConfiguration-publicKey"]
${dli_public_key_value}                             data_lake_integration_public_key
${dli_share_access_signature_token_text}            //*[contains(text(), " Shared Access Signature token ")]
${dli_share_access_signature_token_input}           //input[@data-testid="datalakeConfiguration-sasToken"]
${dli_share_access_signature_token_value}           data_lake_integration_share_access_signature_token
${tunit_header}                                     //div[contains(text(), 'Treatment units')]
${added_tunit_list}                                 //*[@data-testid='availableTreatmentUnits_0']
${remove_tunit_btn}                                 //*[@data-testid="availableTreatmentUnits-remove-button" and text()=' Remove ']
${add_tunit_btn}                                    //*[@data-testid="availableTreatmentUnits-add-button" and text()=' Add ']
# todo update following 3 locators to just use data_testid onc ethis issue is fixed NOONA-25692
${tunit_name_field}                                 (//*[starts-with(@data-testid, 'availableTreatmentUnits_')]//input[starts-with(@data-testid, 'treatment-units-name-')])[last()]
${tunit_id_field}                                   (//*[starts-with(@data-testid, 'availableTreatmentUnits_')]//input[starts-with(@data-testid, 'treatment-units-id-')])[last()]
${tunit_time_zone_field}                            (//*[starts-with(@data-testid, 'availableTreatmentUnits_')]//input[starts-with(@data-testid, 'treatment-units-time-zone-title-')])[last()]
${edit_tunit}                                       //*[@data-testid="availableTreatmentUnits-edit-button" and text()=' Edit ']
${tunit_name_value1}                                unitTest1
${tunit_id_value1}                                  12341
${tunit_time_zone_value1}                           Europe/Zurich
${tunit_name_value}                                 unitTest
${tunit_id_value}                                   1234
${tunit_time_zone_value}                            Europe/Helsinki
${disabled}                                         disabled
${time_zone_doc_header}                             //*[contains(text(),'For supported time zones see:')]
${time_zone_doc_link}                               http://joda-time.sourceforge.net/timezones.html
${time_zone_doc_link_https}                         https://joda-time.sourceforge.net/timezones.html
${time_zone_doc_link_element}                       //*[@class='formly-text-container']/a
${medical_record_visible_label}                     //*[contains(text(), "Should medical records be visible for the patient?")]
${medical_record_visible_enable_btn}                //*[@data-testid="medical-records-visible"]//label
${medical_record_visible_disable_btn}               //*[@data-testid='medical-records-invisible']//label
${launch_dhit_portal_label}                         //*[contains(text(), "Patient can launch DHIT portal from Noona (Cures Act 21st century)")]
${launch_dhit_portal_enabled_btn}                   //*[@data-testid="able-to-launch-dhit-portal"]//label
${method_fetching_ccd_dropdown}                     (//*[@data-testid="ccd-api-type"])[1]
${dhit_portal_url_label}                            //*[contains(text(), "DHIT portal URL")]
${dhit_portal_url_field}                            //*[@data-testid="dhitPortalUrl"]
${dhit_portal_url_value}                            https://test.clinic.noonatest.com
${launch_dhit_portal_disabled_btn}                  //*[@data-testid="unable-to-launch-dhit-portal"]//label
${is_clinic_mo_ro_label}                            //*[contains(text(), "Is clinic an MO or RO environment? (If CCS environment, setting can be either of the two)")]
${clinic_environment_mo_btn}                        //*[@data-testid="aria-medical-oncology-enabled"]
${clinic_environment_ro_btn}                        //*[@data-testid="aria-medical-oncology-disabled"]
${auh_type_label}                                   //*[contains(text(), "Authentication Type")]
${auh_type_dropdown}                                (//*[@data-testid="ccd-api-type"])[2]
${auh_type_basic_auth}                              (//*[@data-testid="ccd-api-type"])[2]//*[@data-testid="option-0"]
${auh_type_no_auth}                                 (//*[@data-testid="ccd-api-type"])[2]//*[@data-testid="option-1"]
${auh_type_client_credentials}                      (//*[@data-testid="ccd-api-type"])[2]//*[@data-testid="option-2"]
${api_key_label}                                    //*[contains(text(), "API Key")]
${api_key_field}                                    //*[@data-testid="aria-api-key"]
${api_key_value}                                    apikey
${endpoint_url_label}                               //*[contains(text(), "Endpoint Url")]
${endpoint_url_field}                               //*[@data-testid="ccd-endpoint-url"]
${endpoint_url_value}                               https://test.clinic.noonatest.com
${user_label}                                       //*[contains(text(), " User ")]
${user_field}                                       //*[@data-testid="ccd-username"]
${user_value}                                       <EMAIL>
${change_pwd_link}                                  //*[@data-testid='undefined-function-button' and contains(text(), "Change password")]
${pwd_field}                                        //*[@data-testid='ccd-password']
${pwd_value}                                        Password-1
${pwd_field_label}                                  //*[contains(text(), "Password")]
${cancel_pwd_change_link}                           //*[@data-testid='undefined-function-button' and contains(text(), "Cancel password change")]
${medical_record_visible_disabled}                  //*[@data-testid='medical-records-invisible' and contains(@class, "radio-checked")]
${ccd_method_s3}                                    //*[@data-testid='option-0']//*[contains(text(), "S3")]
${ccd_method_aria}                                  //*[@data-testid='option-1']//*[contains(text(), "ARIA")]
${lab_results_visible_enable_btn}                   //*[@data-testid='lab-results-enabled']//label
${clinical_report_enable_btn}                       //*[@data-testid='clinical-reports-enabled']//*[contains(text(),'Yes')]
${clinical_report_disable_btn}                      //*[@data-testid='clinical-reports-enabled']//*[contains(text(),'No')]
${lab_results_visible_label}                        //*[contains(text(), "Should laboratory results be visible for the patient?")]
${clinical_report_visible_label}                    //*[contains(text(), "Should clinical reports be visible for patient")]


*** Keywords ***
Open "Clinic settings" And Select Integration Settings Tab
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    Wait Until Noona Loader Is Not Visible
    ${status}    Run Keyword And Return Status    Element Should Be Visible    ${integration_header}
    WHILE    ${status}!=${TRUE}    limit=10
        Wait Until Element Is Visible    ${integration_settings_tab}
        Wait Until Noona Loader Is Not Visible
        Try To Click Element    ${integration_settings_tab}    wait_in_seconds=7s
        ${status}    Run Keyword And Return Status    Element Should Be Visible    ${integration_header}
    END

Enable Laboratory Results Settings
    Wait Until Element Is Visible    ${laboratory_results_label}
    Scroll Element Into View    ${lab_results_multi_notif_yes}
    Try To Click Element    ${lab_results_visible_no}
    Try To Click Element    ${lab_results_visible_yes}
    Try To Click Element    ${lab_results_notifications_yes}
    Try To Click Element    ${lab_results_multi_notif_yes}

Disable Laboratory Results Settings
    Wait Until Element Is Visible    ${laboratory_results_label}
    Scroll Element Into View    ${lab_results_multi_notif_yes}
    Try To Click Element    ${lab_results_visible_yes}
    Try To Click Element    ${lab_results_visible_no}

Save Integration Settings
    Wait Until Page Contains Element    ${integration_save_button}
    Scroll Element Into View    ${integration_save_button}
    Wait until keyword succeeds    3x    1s    Try To Click Element    ${integration_save_button}
    Wait Until Element Is Visible    ${save_toast_message}    30s
    Try To Click Banner Message
    Sleep    1    # add sleep to make sure that setting is saved before reloading the page
    Reload Page
    Sleep    3s
    Wait Until Element Is Visible    ${laboratory_results_label}    timeout=20s
    Scroll Element Into View    ${lab_results_multi_notif_yes}
    Close Browser

Check Status Of Medication List Settings
    Wait Until Element Is Visible    ${medication_list_setting_label}
    Scroll Element Into View    ${medication_list_setting_label}
    ${enabled_btn_attibute}    Get Element Attribute    ${medication_list_setting_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable Medication List Settings
    Wait Until Page Contains Element    ${medication_list_setting_enabled_btn}
    Try To Click Element    ${medication_list_setting_enabled_btn}

Disable Medication List Settings
    Wait Until Page Contains Element    ${medication_list_setting_disabled_btn}
    Try To Click Element    ${medication_list_setting_disabled_btn}

Clinic Admin Enables Lab Results In Clinic Settings
    [Arguments]    ${clinic}
    Login As Nurse    clinic=${clinic}    user_type=${USER_TYPE}[noona_admin]
    Wait Until Element Is Visible
    ...    ${clinic_user_list_last_name_header}
    Open "Clinic settings" And Select Integration Settings Tab
    Enable Laboratory Results Settings
    Save Integration Settings

Disable Data Integrations
    [Arguments]    ${data_type}
    ${checkbox}    Format String    ${data_integrations_checkbox}    ${data_type}
    ${attr}    Get Element Attribute    ${checkbox}    class
    IF    'checkbox-checked' in '${attr}'
        Try To Click Element    ${checkbox}/div
    ELSE
        Try To Click Element    ${checkbox}/div    # enable to disable to make sure save button is clickable
        Try To Click Element    ${checkbox}/div
    END
    ${attr}    Get Element Attribute    ${checkbox}    class
    Should Not Contain    ${attr}    checkbox-checked

Enable Data Integrations
    [Arguments]    ${data_type}
    ${checkbox}    Format String    ${data_integrations_checkbox}    ${data_type}
    ${attr}    Get Element Attribute    ${checkbox}    class
    IF    'checkbox-checked' in '${attr}'
        Try To Click Element    ${checkbox}/div
        Try To Click Element    ${checkbox}/div    # enable to disable to make sure save button is clickable
    ELSE
        Try To Click Element    ${checkbox}/div
    END
    ${attr}    Get Element Attribute    ${checkbox}    class
    Should Contain    ${attr}    checkbox-checked

Verify Data Integration Setting Status
    [Arguments]    ${data_type}    ${expected_status}
    ${checkbox}    Format String    ${data_integrations_checkbox}    ${data_type}
    Wait Until Element Is Visible    ${checkbox}
    ${attr}    Get Element Attribute    ${checkbox}    class
    IF    '${expected_status}'=='enabled'
        Should Contain    ${attr}    checkbox-checked
    ELSE
        Should Not Contain    ${attr}    checkbox-checked
    END

Check Status Of FHIR Integration Setting
    Wait Until Page Contains    ${fhir_integration_settings_enabled_text}
    ${enabled_btn_attibute}    Get Element Attribute    ${fhir_integration_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable FHIR Integration Settings
    Try To Click Element    ${fhir_integration_enabled_btn}

Disable FHIR Integration Settings
    Try To Click Element    ${fhir_integration_disabled_btn}

Enable FHIR Integration And Save The Settings
    Enable FHIR Integration Settings
    Save Settings

Check Status Of Integration For Clinic Setting
    Wait Until Page Contains    ${integration_enabled_for_clinic_text}
    ${enabled_btn_attibute}    Get Element Attribute    ${integration_for_clinic_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable Integration For Clinic Settings
    Try To Click Element    ${integration_for_clinic_enabled_btn}

Disable Integration For Clinic Settings
    Try To Click Element    ${integration_for_clinic_disabled_btn}

Enable Integration For Clinic And Save The Settings
    Enable Integration For Clinic Settings
    Save Settings

Input Required Fields Value For Patient Status Change Integration Settings
    Input Text    ${status_change_integration_endpoint}    ${status_change_integration_endpoint_value}
    Input Text    ${status_change_integration_username}    ${status_change_integration_username_value}
    Input Text    ${status_change_integration_password}    ${status_change_integration_password_value}

Check Status Of Data Lake Integration
    Wait Until Element Is Visible    ${data_lake_integration_label}
    Scroll Element Into View    ${data_lake_integration_label}
    ${enabled_btn_attibute}    Get Element Attribute    ${data_lake_integration_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable Data Lake Integration And Save Settings
    Try To Click Element    ${data_lake_integration_enabled_btn}
    Save Settings

Disable Data Lake Integration And Save Settings
    Try To Click Element    ${data_lake_integration_disabled_btn}
    Save Settings

Input Data Lake Account Name
    Input Text    ${dli_account_name_input}    ${dli_account_name_value}

Input Data Lake Container Name
    Input Text    ${dli_container_name_input}    ${dli_container_name_value}

Input Data Lake Encryption Key URL
    Input Text    ${dli_encryption_key_url_input}    ${dli_encryption_key_url_value}

Input Data Lake Public Key
    Input Text    ${dli_public_key_input}    ${dli_public_key_value}

Input Data Lake Shared Access Signature Token
    Input Text    ${dli_share_access_signature_token_input}    ${dli_share_access_signature_token_value}

Remove Values From Data Lake Setting Fields
    Clear Element Text    ${dli_account_name_input}
    Clear Element Text    ${dli_container_name_input}
    Clear Element Text    ${dli_encryption_key_url_input}
    Clear Element Text    ${dli_public_key_input}
    Clear Element Text    ${dli_share_access_signature_token_input}

Check Status Of Medical Records Be Visible For The Patient
    Wait Until Element Is Visible    ${medical_record_visible_label}
    Scroll Element Into View    ${medical_record_visible_label}
    ${enabled_btn_attibute}    Get Element Attribute    ${medical_record_visible_enable_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Select Method To Use In Fetching CCD
    [Arguments]    ${method}
    Try To Click Element    ${method_fetching_ccd_dropdown}
    IF    '${method}' == 'S3'
        Try To Click Element    ${ccd_method_s3}
    ELSE IF    '${method}' == 'ARIA'
        Try To Click Element    ${ccd_method_aria}
    END

Check Status Of Can Launch DHIT Portal
    Wait Until Element Is Visible    ${launch_dhit_portal_label}
    Scroll Element Into View    ${launch_dhit_portal_label}
    ${enabled_btn_attibute}    Get Element Attribute    ${launch_dhit_portal_enabled_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable Patient Can Launch DHIT Portal From Noona (Cures Act 21st Century)
    Try To Click Element    ${launch_dhit_portal_enabled_btn}

Disable Patient Can Launch DHIT Portal From Noona (Cures Act 21st Century)
    Try To Click Element    ${launch_dhit_portal_disabled_btn}

Verify That DHIT URL Field Is Not Visible
    Wait Until Element Is Not Visible    ${dhit_portal_url_label}
    Wait Until Element Is Not Visible    ${dhit_portal_url_field}

Input DHIT URL
    Wait Until Element Is Visible    ${dhit_portal_url_label}
    Clear Element Text    ${dhit_portal_url_field}
    Input Text    ${dhit_portal_url_field}    ${dhit_portal_url_value}

Verify That Is Clinic An MO Or RO Environment Option Is Visible
    Wait Until Element Is Visible    ${is_clinic_mo_ro_label}

Select Clinic Environment As Medical Oncology
    Try To Click Element    ${clinic_environment_mo_btn}

Check Selected Value Of Clinic Environment Radiation Oncology
    Wait Until Element Is Visible    ${clinic_environment_ro_btn}
    Scroll Element Into View    ${clinic_environment_ro_btn}
    ${enabled_btn_attibute}    Get Element Attribute    ${clinic_environment_ro_btn}/../..    class
    IF    'radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Select Clinic Environment As Radiation Oncology
    Try To Click Element    ${clinic_environment_ro_btn}

Select Authentication Type
    [Arguments]    ${auth_type}
    Wait Until Element Is Visible    ${auh_type_label}
    Try To Click Element    ${auh_type_dropdown}
    IF    '${auth_type}' == 'Basic Auth'
        Try To Click Element    ${auh_type_basic_auth}
    ELSE IF    '${auth_type}' == 'No Authentication'
        Try To Click Element    ${auh_type_no_auth}
    ELSE IF    '${auth_type}' == 'Client Credentials'
        Try To Click Element    ${auh_type_client_credentials}
    END

Verify That API Key Field Is Visible
    Scroll Element Into View    ${api_key_field}
    Wait Until Element Is Visible    ${api_key_label}
    Wait Until Element Is Visible    ${api_key_field}

Input API Key
    Clear Element Text    ${api_key_field}
    Input Text    ${api_key_field}    ${api_key_value}

Verify That Endpoint Url Field Is Visible
    Wait Until Element Is Visible    ${endpoint_url_label}
    Wait Until Element Is Visible    ${endpoint_url_field}

Input Endpoint Url
    Clear Element Text    ${endpoint_url_field}
    Input Text    ${endpoint_url_field}    ${endpoint_url_value}

Verify That User Field Is Visible
    Scroll Element Into View    ${user_field}
    Wait Until Element Is Visible    ${user_label}
    Wait Until Element Is Visible    ${user_field}

Input User
    Clear Element Text    ${user_field}
    Input Text    ${user_field}    ${user_value}

Verify That Change Password Link Is Visible
    Scroll Element Into View    ${change_pwd_link}
    Wait Until Element Is Visible    ${change_pwd_link}

Click Change Password Link
    Scroll Element Into View    ${change_pwd_link}
    Try To Click Element    ${change_pwd_link}

Password Field Should Be Visible
    Scroll Element Into View    ${pwd_field}
    Wait Until Element Is Visible    ${pwd_field_label}
    Wait Until Element Is Visible    ${pwd_field}

Input Password Into Password Field
    Scroll Element Into View    ${pwd_field}
    Clear Element Text    ${pwd_field}
    Input Text    ${pwd_field}    ${pwd_value}

Verify That Cancel Password Change Is Visible
    Scroll Element Into View    ${cancel_pwd_change_link}
    Wait Until Element Is Visible    ${cancel_pwd_change_link}

Try To Click On Cancel Password Change
    Try To Click Element    ${cancel_pwd_change_link}

Password Field Should Not Be Visible
    Wait Until Element Is Not Visible    ${pwd_field_label}
    Wait Until Element Is Not Visible    ${pwd_field}

Disable Medical Records Be Visible For The Patient
    Try To Click Element    ${medical_record_visible_disable_btn}

Verify That Medical Records Be Visible For The Patient Is Disabled
    Wait Until Element Is Visible    ${medical_record_visible_disabled}

Enable Medical Records Be Visible For The Patient
    Try To Click Element    ${medical_record_visible_enable_btn}

Check Status Of Laboratory Results Be Visible For The Patient
    Wait Until Element Is Visible    ${lab_results_visible_label}
    Scroll Element Into View    ${lab_results_visible_label}
    ${enabled_btn_attibute}    Get Element Attribute    ${lab_results_visible_enable_btn}/../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Check Status Of Clinical Reports Be Visible For The Patient
    Wait Until Element Is Visible    ${clinical_report_visible_label}
    Scroll Element Into View    ${clinical_report_visible_label}
    ${enabled_btn_attibute}    Get Element Attribute    ${clinical_report_enable_btn}/../../..    class
    IF    'mat-mdc-radio-checked' in '${enabled_btn_attibute}'
        RETURN    enabled
    ELSE
        RETURN    disabled
    END

Enable Clinical Reports Settings
    Scroll Element Into View    ${clinical_report_enable_btn}
    Wait Until Page Contains Element    ${clinical_report_enable_btn}
    Try To Click Element    ${clinical_report_enable_btn}

Disable Clinical Reports Settings
    Scroll Element Into View    ${clinical_report_disable_btn}
    Wait Until Page Contains Element    ${clinical_report_disable_btn}
    Try To Click Element    ${clinical_report_disable_btn}