*** Settings ***
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources/clinic_settings/clinic_settings.resource


*** Variables ***
${clinic_settings_link}                             clinic-settings-link
${clinic_settings_header}                           //*[@id="clinic"]/div[1]/div/h2
${clinical_settings_tab}                            clinical-settings
${basic_settings_tab}                               base-settings
${enable_uro_checkbox}                              //*[@data-testid="enabled-checkbox-treatmentModule-urologicSurgery"]
${uro_checked}                                      (//*[@data-testid="enabled-checkbox-treatmentModule-urologicSurgery" and contains(@class, 'checkbox-checked')])
${uro_not_checked}                                  (//*[@data-testid="enabled-checkbox-treatmentModule-urologicSurgery" and not(contains(@class, 'checkbox-checked'))])
${customised_content_clinic_en_input}               //*[@id="content-en_GB-submitted-to-clinic"]
${customised_content_urgent_en_input}               //*[@id="content-en_GB-urgent-symptom"]
${customised_content_distress_en_input}             //*[@id="content-en_GB-distress-questionnaire-custom"]
${customised_content_follow_up_en_input}            //*[@id="content-en_GB-distress-follow-up-custom"]
${customised_content_distress_en_tab_modified}      //*[@id="tab-en_GB-distress-questionnaire-custom"]//*[@id="default-text"]
${customised_content_clinic_en_tab_modified}        //*[@id="tab-en_GB-submitted-to-clinic"]//*[@id="default-text"]
${customised_content_urgent_en_tab_modified}        //*[@id="tab-en_GB-urgent-symptom"]//div[contains(@class, "circle-default-text")]
${customised_content_follow_up_en_tab_modified}     //*[@id="tab-en_GB-distress-follow-up-custom"]//div[contains(@class, "circle-default-text")]
${customised_content_clinic_en_tab}                 //*[@id="tab-en_GB-submitted-to-clinic"]
${customised_content_urgent_en_tab}                 //*[@id="tab-en_GB-urgent-symptom"]
${customised_content_distress_en_tab}               //*[@id="tab-en_GB-distress-questionnaire-custom"]
${customised_content_follow_en_tab}                 //*[@id="tab-en_GB-distress-follow-up-custom"]
${settings_updated_message}                         Clinic settings are updated
${pharmacotherapy_module}                           Details of the medicinal product are shown in the Pharmacotherapy module
${pharmacotherapy_module_yes}                       //*[@for="enable-medication-collection-yes"]
${pharmacotherapy_module_no}                        //*[@for="enable-medication-collection-no"]
${pharmacotherapy_details_yes}                      //*[@data-testid='enable-medication-collection-yes']
${pharmacotherapy_details_no}                      //*[@data-testid='enable-medication-collection-no']
${nurse_can_consult_ct}                             Nurse can consult a care team/person
${cu_get_notification}                              Clinic user will get notifications about his/her own consultation requests and replies
${export_text_enabled}                              Export text enabled for extended symptom questionnaire
${cu_last_name_displayed}                           The clinic user's last name will be displayed to patients
${discussion_point_field}                           At the end of the AEQ, show the question; "Please enter details here, if you wish to discuss something specific with your care team during your next clinic visit."
${discussion_point_field_text}                      Please enter details here if you wish to discuss something specific with your care team during your next clinic visit.
${enable-ask-next-of-kin}                           Ask if a caregiver has reported the symptom on behalf of the patient
${enable-ask-next-of-kin_yes}                       //*[@for="enable-ask-next-of-kin-yes-yes"]
${enable-ask-next-of-kin_no}                        //*[@for="enable-ask-next-of-kin-no"]
${enable-ask-next-of-kin_yes_id}                    enable-ask-next-of-kin-yes
${enable-ask-next-of-kin_no_id}                     enable-ask-next-of-kin-no
${pharmacotherapy_title_text}                       Pharmacotherapy
${select_questionnaires_header}                     //h5[text()="Select enabled questionnaires"]
${enable_status_questionnaire_text}                 Enable status check questionnaire
${stanford_patient_statuses_text}                   Stanford patient statuses
${patient_group_enable_setting_text}                Is the patient group feature enabled?

*** Keywords ***
Edit Customised Clinical Contents
    ${random_string}    Generate Random String    70
    ${random_string_2}    Generate Random String    70
    ${random_string_3}    Generate Random String    70
    ${random_string_4}    Generate Random String    70
    Set Test Variable    ${random_string}
    Set Test Variable    ${random_string_2}
    Set Test Variable    ${random_string_3}
    Set Test Variable    ${random_string_4}
    Update Confirmation Message After Sending Symptom Or Question    ${random_string}
    Update Confirmation Message After Sending Urgent Symptom    ${random_string_2}
    Update Modal Text For Distress And Problem List Questionnaire    ${random_string_3}
    Update Modal Text For Support And Guidance Follow Up Questionnaire    ${random_string_4}

Update Confirmation Message After Sending Symptom Or Question
    [Documentation]    Default to English. Update kw for other language.
    [Arguments]    ${new_message}
    Wait Until Element Is Visible    ${customised_content_clinic_en_tab}
    Try To Click Element    ${customised_content_clinic_en_tab}
    Try To Input Text    ${customised_content_clinic_en_input}    ${new_message}

Update Confirmation Message After Sending Urgent Symptom
    [Documentation]    Default to English. Update kw for other language.
    [Arguments]    ${new_message}
    Wait Until Element Is Visible    ${customised_content_urgent_en_tab}
    Try To Click Element    ${customised_content_urgent_en_tab}
    Try To Input Text    ${customised_content_urgent_en_input}    ${new_message}

Update Modal Text For Distress And Problem List Questionnaire
    [Documentation]    Default to English. Update kw for other language.
    [Arguments]    ${new_message}
    Wait Until Element Is Visible    ${customised_content_distress_en_tab}
    Try To Click Element    ${customised_content_distress_en_tab}
    Try To Input Text    ${customised_content_distress_en_input}    ${new_message}

Update Modal Text For Support And Guidance Follow Up Questionnaire
    [Documentation]    Default to English. Update kw for other language.
    [Arguments]    ${new_message}
    Wait Until Element Is Visible    ${customised_content_follow_en_tab}
    Try To Click Element    ${customised_content_follow_en_tab}
    Try To Input Text    ${customised_content_follow_up_en_input}    ${new_message}

Check Customised Clinical Contents
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${clinic_settings_header}
    Try To Click Element    ${basic_settings_tab}    wait_in_seconds=5x
    Try To Click Element    ${clinical_settings_tab}
    Wait Until Page Contains Element    ${customised_content_clinic_en_input}
    ${input_value}    Get Value    ${customised_content_clinic_en_input}
    Should Contain    ${input_value}    ${random_string}
    ${input_value_2}    Get Value    ${customised_content_urgent_en_input}
    Should Contain    ${input_value_2}    ${random_string_2}
    ${input_value_3}    Get Value    ${customised_content_distress_en_input}
    Should Contain    ${input_value_3}    ${random_string_3}
    ${input_value_4}    Get Value    ${customised_content_follow_up_en_input}
    Should Contain    ${input_value_4}    ${random_string_4}
    Page Should Contain Element    ${customised_content_distress_en_tab_modified}
    Page Should Contain Element    ${customised_content_clinic_en_tab_modified}
    Page Should Contain Element    ${customised_content_urgent_en_tab_modified}
    Page Should Contain Element    ${customised_content_follow_up_en_tab_modified}

Open "Clinic settings" And Select Clinical Settings Tab
    Wait Until Element Is Visible    ${admin_clinic_menu}
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    Try To Click Element    ${clinical_settings_tab}    wait_in_seconds=5x
    Wait Until Page Contains    ${pharmacotherapy_title_text}

Enable Urologic Surgery
    Wait Until Page Contains Element    ${enable_uro_checkbox}
    Scroll Element Into View    ${enable_uro_checkbox}
    ${uro_status}    Run Keyword And Return Status    Page Should Contain Element    ${uro_not_checked}
    IF    '${uro_status}'=='False'
        Try To Click Element    ${enable_uro_checkbox}
    END
    Try To Click Element    ${enable_uro_checkbox}
    Wait Until Page Contains Element    ${uro_checked}

Disable Urologic Surgery
    Wait Until Page Contains Element    ${enable_uro_checkbox}
    Scroll Element Into View    ${enable_uro_checkbox}
    Try To Click Element    ${enable_uro_checkbox}
    Wait Until Page Contains Element    ${uro_not_checked}

Settings Saved Module Enabled
    Wait Until Element Is Visible    ${clinic_settings_header}
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${clinic_settings_header}
    Wait Until Element Is Visible    ${basic_settings_tab}
    Try To Click Element    ${basic_settings_tab}    wait_in_seconds=5x
    Wait Until Element Is Visible    ${clinical_settings_tab}
    Try To Click Element    ${clinical_settings_tab}    wait_in_seconds=5x
    Wait Until Element Is Visible    ${enable_uro_checkbox}
    # scrolls to element so when it fails, it captures the correct screenshot
    Scroll Element Into View
    ...    ${enable_uro_checkbox}
    Sleep    1    # gives time to scroll before verifying the element
    Wait Until Page Contains Element    ${uro_checked}
    Wait Until Page Contains Element    ${uro_checked}

Settings Saved Module Disabled
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${clinic_settings_header}
    Try To Click Element    ${basic_settings_tab}    wait_in_seconds=5x
    Try To Click Element    ${clinical_settings_tab}
    Wait Until Page Contains Element    ${enable_uro_checkbox}
    Wait Until Page Contains Element    ${uro_not_checked}
    Wait Until Page Contains Element    ${uro_not_checked}

