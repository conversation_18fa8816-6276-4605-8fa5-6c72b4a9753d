*** Settings ***
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources${/}clinic_settings${/}clinical_settings.resource


*** Variables ***
${clinic_settings_link}                 clinic-settings-link
${clinical_settings_tab}                clinical-settings
${case_management_tab}                  case-management
${basic_settings_tab}                   base-settings
${save_button}                          save
${settings_updated_message}             Clinic settings are updated
${case_delay_reason_enabled_visible}    //*[contains(text(), "Case delay reason enabled") and @for="case-delay-reason-enabled"]
${case_delay_reason_enabled}            //*[@data-testid="case-delay-reason-enabled-enabled" and contains(@class, "mat-radio-checked")]
${case_delay_reason_disabled}           //*[@data-testid="case-delay-reason-enabled-disabled" and contains(@class, "mat-radio-checked")]
${case_delay_reason_enabled_no}         //*[@data-testid="case-delay-reason-enabled-disabled"]
${case_delay_reason_enabled_yes}        //*[@data-testid="case-delay-reason-enabled-enabled"]
${case_escalation_enabled_visible}      //*[contains(text(), "Case escalation enable") and @for="case-escalation-enabled"]
${case_escalation_enabled}              //*[@data-testid="case-escalation-enabled-enabled" and contains(@class, "mat-radio-checked")]
${case_escalation_disabled}             //*[@data-testid="case-escalation-enabled-disabled" and contains(@class, "mat-radio-checked")]
${case_escalation_enabled_no}           //*[@data-testid="case-escalation-enabled-disabled"]
${case_escalation_enabled_yes}          //*[@data-testid="case-escalation-enabled-enabled"]
${case_outcome_enabled_visible}         //*[contains(text(), "Case outcome enabled") and @for="case-outcome-enabled"]
${case_outcome_enabled_no}              //*[@data-testid="case-outcome-enabled-disabled"]
${case_outcome_enabled_yes}             //*[@data-testid="case-outcome-enabled-enabled"]
${case_outcome_enabled_no_id}           //*[@data-testid="case-outcome-enabled-disabled" and contains(@class, "mat-radio-checked")]
${case_outcome_enabled_yes_id}          //*[@data-testid="case-outcome-enabled-enabled" and contains(@class, "mat-radio-checked")]
${first_case_type}                      //*[@data-testid='caseTypeSettings_0']
${add_email_notification_yes}           //*[@data-testid="work-queue-notification-enabled"]
${add_email_notification_no}            //*[@data-testid="work-queue-notification-disabled"]
${case_management_tab_selected}         //*[@data-testid='case-management' and contains(@class, 'selected')]
${ssn_radio_button}                     //span[@class='form-label' and text()='Identity code / Social security number']
${ins_radio_button}                     //span[@class='form-label' and text()='INS']
${mrn_case_mgt_tab}                     //span[@class='form-label' and text()='Medical record number']

*** Keywords ***
Save Case Management Settings
    Try To Click Element    ${save_button}
    Wait Until Page Contains    ${settings_updated_message}

Open "Clinic settings" And Select Case Management Tab
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${clinic_settings_header}
    Try To Click Element    ${case_management_tab}

Enable Case Escalation
    Wait Until Element Is Visible    ${case_escalation_enabled_yes}
    Scroll Element Into View    ${case_escalation_enabled_yes}
    ${attr_yes}    Get Element Attribute    ${case_escalation_enabled_yes}     class
    IF    'mat-mdc-radio-checked' not in '${attr_yes}'
        Try To Click Element    ${case_escalation_enabled_yes}//label
    END

Enable Case Delay Reason
    Scroll Element Into View    ${case_delay_reason_enabled_yes}
    Wait Until Element Is Visible    ${case_delay_reason_enabled_yes}
    ${attr_yes}    Get Element Attribute    ${case_delay_reason_enabled_yes}    class
    IF    'mat-mdc-radio-checked' not in '${attr_yes}'
        Click Element    ${case_delay_reason_enabled_yes}//label
    END

Disable Case Escalation
    Scroll Element Into View    ${case_escalation_enabled_no}
    Wait Until Element Is Visible    ${case_escalation_enabled_no}
    ${attr_yes}    Get Element Attribute    ${case_escalation_enabled_no}     class
    IF    'mat-mdc-radio-checked' not in '${attr_yes}'
        Click Element    ${case_escalation_enabled_no}//input
    END

Disable Case Delay Reason
    Scroll Element Into View    ${case_delay_reason_enabled_no}
    Wait Until Element Is Visible    ${case_delay_reason_enabled_no}
    ${attr_yes}    Get Element Attribute    ${case_delay_reason_enabled_no}    class
    IF    'mat-mdc-radio-checked' not in '${attr_yes}'
        Click Element    ${case_delay_reason_enabled_no}//input
    END

Noona Administrator Edits Case Escalation
    [Documentation]    Keyword is for testing editing of clinic settings
    Wait Until Element Is Visible    ${case_escalation_enabled_yes}
    Click Element                    ${case_escalation_enabled_no}//input
    Click Element                    ${case_escalation_enabled_yes}//input
    Save Settings
    Reload Page
    Wait Until Element Is Visible    ${case_escalation_enabled_yes}
    ${attr_yes}    Get Element Attribute    ${case_escalation_enabled_yes}     class
    Should Contain    ${attr_yes}    mat-mdc-radio-checked
    Click Element                    ${case_escalation_enabled_no}//input
    Save Settings
    Reload Page
    Wait Until Element Is Visible    ${case_escalation_enabled_yes}
    ${attr_yes}    Get Element Attribute    ${case_escalation_enabled_yes}     class
    Should Not Contain    ${attr_yes}    mat-mdc-radio-checked

Noona Administrator Edits Case Delay Reason
    [Documentation]    Keyword is for testing / editing of clinic settings
    ...               option is only available if 'Case escalation enabled' is set to 'Yes'
    Scroll Element Into View         ${case_escalation_enabled_yes}
    Click Element                    ${case_escalation_enabled_no}//input
    Click Element                    ${case_escalation_enabled_yes}//input
    Save Settings                    # steps up to this save settings is needed to ensure the option below is activated
    Reload Page
    Wait Until Element Is Visible    ${case_escalation_enabled_yes}
    Scroll Element Into View         ${case_delay_reason_enabled_yes}
    Click Element                    ${case_delay_reason_enabled_no}//input
    Click Element                    ${case_delay_reason_enabled_yes}//input
    Save Settings
    Reload Page
    Wait Until Element Is Visible    ${case_delay_reason_enabled_yes}
    ${attr_yes}    Get Element Attribute    ${case_delay_reason_enabled_yes}     class
    Should Contain    ${attr_yes}    mat-mdc-radio-checked
    Click Element                    ${case_delay_reason_enabled_no}//input
    Save Settings
    Reload Page
    Wait Until Element Is Visible    ${case_delay_reason_enabled_no}
    ${attr_yes}    Get Element Attribute    ${case_delay_reason_enabled_yes}     class
    Should Not Contain    ${attr_yes}    mat-mdc-radio-checked

Noona Administrator Edits Case Outcome
    [Documentation]    Keyword is for testing editing of clinic settings
    Scroll Element Into View         ${case_outcome_enabled_yes}
    Click Element                    ${case_outcome_enabled_no}//input
    Click Element                    ${case_outcome_enabled_yes}//input
    Save Settings
    Reload Page
    Wait Until Element Is Visible    ${case_outcome_enabled_yes}
    ${attr_yes}    Get Element Attribute    ${case_outcome_enabled_yes}     class
    Should Contain    ${attr_yes}    mat-mdc-radio-checked
    Click Element                    ${case_outcome_enabled_no}//input
    Save Settings
    Reload Page
    Wait Until Element Is Visible    ${case_outcome_enabled_no}
    ${attr_yes}    Get Element Attribute    ${case_outcome_enabled_yes}     class
    Should Not Contain    ${attr_yes}    mat-mdc-radio-checked

Noona Administrator Edits Email Notification For New Work Queue Cases
    [Documentation]    Keyword is for testing editing of clinic settings
    Scroll Element Into View         ${add_email_notification_yes}
    Click Element                    ${add_email_notification_no}//input
    Click Element                    ${add_email_notification_yes}//input
    Save Settings
    Reload Page
    Wait Until Element Is Visible    ${add_email_notification_yes}
    ${attr_yes}    Get Element Attribute    ${add_email_notification_yes}     class
    Should Contain    ${attr_yes}    mat-mdc-radio-checked
    Click Element                    ${add_email_notification_no}//input
    Save Settings
    Reload Page
    Wait Until Element Is Visible    ${add_email_notification_no}
    ${attr_yes}    Get Element Attribute    ${add_email_notification_yes}     class
    Should Not Contain    ${attr_yes}    mat-mdc-radio-checked