*** Settings ***
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${clinic_information_menu_link}     clinic-information-link
${manage_nurses_link}               manage-nurses-link
${clinic_name_input}                //*[@data-testid="name"]
${street_address_input}             //*[@data-testid="streetAddress"]
${zip_code_input}                   //*[@data-testid="zipCode"]
${city_input}                       //*[@data-testid="city"]
${phone_number_input}               //*[@data-testid="phoneNumber"]
${web_site_input}                   //*[@data-testid="webSiteUrl"]
${email_address_input}              //*[@data-testid="emailAddress"]
${content_title_input}              (//*[@data-testid="title"])[1]
${content_body_input}               //*[@data-testid="body"]
${link_title_input}                 (//*[@data-testid="title"])[2]
${link_url_input}                   //*[@data-testid="url"]
${clinic_info_save}                 //*[@id="save"]


*** Keywords ***
Open Clinic Information
    # to make sure that page is completely loaded before the next step
    Wait Until Element Is Visible
    ...    ${clinic_user_list_last_name_header}
    Wait Until Element Is Visible    ${clinic_menu}
    Wait Until Element Is Enabled    ${clinic_menu}
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${clinic_information_menu_link}

Generate Contact Data
    ${first_name}    First Name
    Set Test Variable    ${first_name}
    ${last_name}    Last Name
    Set Test Variable    ${last_name}
    ${email}    Set Email For New User    ${first_name}    ${last_name}
    Set Test Variable    ${email}
    ${phone_number}    Generate Random String    9    [NUMBERS]
    Set Test Variable    ${phone_number}
    ${street_address}    Street Address
    Set Test Variable    ${street_address}
    ${zip_code}    Generate Random String    5    [NUMBERS]
    Set Test Variable    ${zip_code}
    ${city}    City
    Set Test Variable    ${city}
    ${random_number}    Random Number    4
    Set Test Variable    ${random_number}

Generate Content Data
    ${random_title}    Generate Random String
    Set Test Variable    ${random_title}
    ${random_text}    Generate Random String
    Set Test Variable    ${random_text}
    ${random_link_title}    Generate Random String
    Set Test Variable    ${random_link_title}
    ${random_number_link}    Generate Random String    4    [NUMBERS]
    Set Test Variable    ${random_number_link}

Edit Contact Information
    [Arguments]    ${clinic_name}
    Generate Contact Data
    Try To Input Text    ${clinic_name_input}    ${clinic_name}
    Set Test Variable    ${clinic_name}
    Try To Input Text    ${street_address_input}    ${street_address}
    Sleep    1    # input is very fast that the the value is not saved sometimes
    Try To Input Text    ${zip_code_input}    ${zip_code}
    Try To Input Text    ${city_input}    ${city}
    Try To Input Text    ${phone_number_input}    ${phone_number}
    Try To Input Text    ${web_site_input}    https://www.varian.com/${random_number}
    Try To Input Text    ${email_address_input}    ${email}

Edit Content
    Generate Content Data
    Try To Input Text    ${content_title_input}    ${random_title}
    Input Text    ${content_body_input}    ${random_text}
    Input Text    ${link_title_input}    ${random_link_title}
    Input Text    ${link_url_input}    https://www.varian.com/${random_number_link}

Save Clinic Information
    Try To Click Element        ${clinic_info_save}
    Wait Until Page Contains    Clinic information is updated
    Wait Until Page Does Not Contain    Clinic information is updated
    # It's been failing for several days and passing on rerun, added the steps below to check if changes are saved right after the street is updated
    Scroll Element Into View    ${street_address_input}
    Sleep    2
    Reload Page
    Wait Until Element Is Visible    ${street_address_input}
    Scroll Element Into View    ${street_address_input}
    Try For Textfield Value Should Be    ${street_address_input}    ${street_address}

Check Updated Information
    Try To Click Element    ${manage_nurses_link}
    Open Clinic Information
    Try For Textfield Value Should Be    ${clinic_name_input}    ${clinic_name}
    Try For Textfield Value Should Be    ${street_address_input}    ${street_address}
    Textfield Value Should Be    ${zip_code_input}    ${zip_code}
    Textfield Value Should Be    ${city_input}    ${city}
    Textfield Value Should Be    ${phone_number_input}    ${phone_number}
    Textfield Value Should Be    ${web_site_input}    https://www.varian.com/${random_number}
    Textfield Value Should Be    ${email_address_input}    ${email}
    Textfield Value Should Be    ${content_title_input}    ${random_title}
    Textarea Value Should Be    ${content_body_input}    ${random_text}
    Textfield Value Should Be    ${link_title_input}    ${random_link_title}
    Textfield Value Should Be    ${link_url_input}    https://www.varian.com/${random_number_link}
