*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource    ${EXECDIR}${/}resources${/}management${/}noona_management_site.resource
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource


*** Variables ***
${clinic_settings_link}                                         clinic-settings-link
${clinical_settings_tab}                                        clinical-settings
${basic_settings_tab}                                           base-settings
${basic_settings_save_button}                                   save
${time_zone_input}                                              (//*[@data-testid="timeZone"])
${clinic_experience_header}                                     //h4[contains(text(),'Clinic experience for patients')]
${custom_branding_disabled}                                     //*[@data-testid="custom-branding-disabled"]//label
${custom_branding_enabled}                                      //*[@data-testid="custom-branding-enabled"]//label
${custom_color_label}                                           //label[contains(text(),'Custom color')]
${custom_color_field}                                           //input[@id='customColor']
${custom_color_preview}                                         //div[@class="custom-color__preview"]
${custom_color_clear_button}                                    //div[contains(text(),'Clear')]
${custom_logo_label}                                            //label[contains(text(),'Clinic logo')]
${custom_logo_svg_text}
...                                                             //p[contains(text(),'Browse your computer to add an svg file.')]
${custom_logo_preview_text}                                     //span[contains(text(),'Logo preview')]
${custom_logo_id}                                               //*[@id='Capa_1']
${custom_logo_clear_button}                                     //button[@id='clear-logo']
${custom_service_name_noona}                                    //*[@data-testid="customized-name-enabled"]
${custom_service_name_clinic_specific_name}                     //*[@data-testid="customized-name-disabled"]
${clinic_specific_name_noonas}                                  //tbody/tr[1]/td[2]/input[1]
${clinic_specific_name_noona}                                   //tbody/tr[2]/td[2]/input[1]
${custom_color_too_light}
...                                                             The color cannot be applied. Reason: the color is too light.
${custom_color_too_dark}
...                                                             The color cannot be applied. Reason: the color is too dark.
${custom_logo_invalid_svg}
...                                                             //p[contains(text(),'Invalid svg file; please check it and try again.')]
${analytics_label}                                              //h4[contains(text(),'Analytics')]
${default_noona_logo}                                           //*[contains(@class,"logo--noona")]
${noona_default_color}                                          000
${terms_of_use_text_area}                                       //*[@id="content-en_GB-terms-and-conditions"]
${service_content_text_area}                                    //*[@data-testid="content-en_GB-service-consent"]
${service_content_full_text_area}
...                                                             //*[@data-testid="content-en_GB-use-of-patient-information"]
${new_consent_terms_checkbox}                                   //*[@data-testid="new-consent-terms-and-conditions"]
${new_consent_data_checkbox}                                    //*[@data-testid="new-consent-data-collection"]
${new_service_consent_checkbox}                                 //*[@data-testid="new-service-consent"]
${complete_questionnaire_on_behalf_of_patient}
...                                                             The care person can complete questionnaires on behalf of the patient
${patient_can_ask_about_symptoms}                               Patient can ask about symptoms
${patient_can_ask_other_issues}                                 The patient can ask about other issues
${nurse_can_contact_patient}                                    The nurse can contact the patient
${nurse_can_schedule_questionnaire_for_patient}
...                                                             A nurse can schedule symptom questionnaires for patients
${patient_access_link_from_message}
...                                                             Patient can access to Noona content via direct links from notification messages
${smart_symptom_monitoring}                                     Smart symptom monitoring enabled
${clinic_medical_records_visible}                               Medical records analytics are visible to the clinic
${adimn_medical_records_visible}                                Medical records analytics visible to Noona Admin
${clinic_inapp_analytics_visible}
...                                                             In-app analytics are visible to the allowed clinic users
${id_code_validation}                                           Select rule set for identity code validation
${finnish_id_code}                                              Finnish identity code
${no_validation}                                                No validation
${patient_auth_type_dropdown_title}                             Patient Authentication Type
${password}                                                     Password
${password_sms}                                                 Password and SMS verification code
${settings_updated_message}                                     Clinic settings are updated
${save_settings}                                                //*[@data-testid="save"]
${clinic_can_send_patient_education_messages}
...                                                             //div[contains(text(),'Clinic can send Patient Education messages to patients')]/../..
${patient_education_disabled}                                   //*[@data-testid="patient-education-disabled"]//label
${patient_education_enabled}                                    //*[@data-testid="patient-education-enabled"]//label
${patient_education_enabled_data_testid}                        //*[@data-testid="patient-education-enabled"]
${patient_education_disabled_data_testid}                       //*[@data-testid="patient-education-disabled"]
${patient_can_respond_to_patient_education_messages_label}      Patient can respond to a Patient Education message sent by the clinic
${patient_can_respond_to_patient_education_messages}
...                                                             //*[contains(text(),'Patient can respond to a Patient Education message sent by the clinic')]
${patient_education_patiend_respond_enabled}                    //*[@data-testid="patient-education-response-enabled"]
${patient_education_patient_respond_disabled}                   //*[@data-testid="patient-education-response-disabled"]
${patient_can_reset_password_label}                             Patient can reset password
${password_reset_yes}                                           //*[@data-testid="password-reset-yes"]
${password_reset_no}                                            //*[@data-testid="password-reset-no"]
${password_reset_yes_id}                                        password-reset-yes
${password_reset_no_id}                                         password-reset-no
${clinic_user_auth_type_dropdown_title}                         Clinic User Authentication Type
${clinic_user_auth_type_dropdown}
...                                                             //*[@for="name" and text()="Clinic User Authentication Type*"]//following-sibling::select
${patient_remember_login_time_title}
...                                                             //*[@for='name' and contains(text(), 'Patient Remember Login Life Time (Days, 0 = disabled)*')]
${patient_remember_login_time_input}                            //*[@id='patientLoginTokenPersistentLifeTime']
${clinic_usr_remember_login_time_title}
...                                                             //*[@for='name' and contains(text(), 'Clinic User Remember Login Life Time (Days, 0 = disabled)*')]
${clinic_usr_remember_login_time_input}                         //*[@id='clinicUserLoginTokenPersistentLifeTime']
${clinic_usr_session_time_title}
...                                                             //*[@for='name' and contains(text(), 'Clinic User Session Life Time (Minutes)*')]
${clinic_usr_session_time_input}                                //*[@id='clinicUserLoginTokenSessionLifeTime']
${patient_session_time_title}
...                                                             //*[@for='name' and contains(text(), 'Patient Session Life Time (Minutes)*')]
${patient_session_time_input}                                   //*[@id='patientLoginTokenSessionLifeTime']
${patient_consent_text}                                         Noona Service Consent
${patient_consent_full_text}                                    Consent full
${signature_input}                                              //input[@id="required-signature"]
${next_button}                                                  //div[contains(text(),'Next')]
${save_button}                                                  //*[@id="save"]
${consent_input_text}                                           test
${terms_text_location}                                          resources/test_data/terms_of_use.txt
${consent_text_location}                                        resources/test_data/consent_for_processing_personal_info.txt
${consent_full_text_location}                                   resources/test_data/consent_full_text.txt
${patient_can_capture_symptom_using_diary}                      Patient can capture a symptom using diary
${enable_file_attachment_message}                               Enable file attachments in patient messages
${clinic_can_send_pe_message_to_patients}                       Clinic can send Patient Education messages to patients
${oncolink_document_source_checkbox}                            //*[@data-testid='enabled-checkbox-Oncolink']
${clinic_document_source_checkbox}                              //*[@data-testid='enabled-checkbox-Clinic']
${clinic_settings_languages}                                    //*[@data-testid='manage-clinic-languages-table']//formly-clinic-text//span
${settings_date_formatting}                                     //ng-dropdown-panel//div[@class='wordbreak ng-star-inserted']
${date_formatting_text}                                         Date formatting
${clinic_settings_timezone}                                     //*[@data-testid='timeZone']
${reporting_is_visible_text}                                    Reporting is visible
${patients_use_2fa_when_logging_in_via_link}                    Patients will have to use two-factor authentication when logging in via a link that they have requested themselves
${clinic_user_login_session_lifetime}                           //*[@data-testid='clinicUserLoginTokenSessionLifeTimeMinutes']
${clinic_user_remember_login_lifetime}                          //*[@data-testid='clinicUserLoginTokenPersistentLifeTimeDays']
${show_terms_of_use_to_patients_label}                          Show "Terms of Use" to patients
${clinician_code_required_label}                                Clinician identity code required
${measurement_unit_metric}                                      Metric cm, kg, Celsius
${measurement_unit_imperial}                                    Imperial feet & in, lb, Fahrenheit
${measurement_unit_system_label}                                Measurement unit system
${contact_patient_enabled_radio_button}                         //*[@data-testid='contact-patient-enabled']
${finnish_identidy_code_selected}                               //*[@title='Finnish identity code']
${message_for_patient_problems_logging_in}                      //*[@id='content-en_GB-password-reset-instructions']
${ask_patient_to_accept_terms_checkbox_label}                   Ask patients if they accept Privacy Statement and Terms of Use
${privacy_statement_textarea}                                   //textarea[@id='content-en_GB-privacy-policy']
${new_consents_required_from_patients_label}                    New consent required from patients
${capture_symptom_using_diary}                                  //*[@data-testid="diarySymptomReportingEnabled"]
${capture_symptom_using_diary_yes}                              //*[@data-testid="diary-symptom-reporting-enabled"]
${capture_symptom_using_diary_no}                               //*[@data-testid="diary-symptom-reporting-disabled"]
${quiet_time_start_field_id}                                    //input[@data-testid='quietStart']
${quiet_time_end_field_id}                                      //input[@data-testid='quietEnd']
${ins_specific_for french_clinics}                              INS - specific for French clinics
${patient_identifier_ins_yes}                                   //*[@data-testid="ins-enabled"]
${patient_identifier_ins_no}                                    //*[@data-testid="ins-disabled"]


*** Keywords ***
Get Custom URL
    ${url_placeholder}    Get Text
    ...    //*[contains(@class,"customNameInstructions")]//*[contains(text(),"Clinic short name is used as identifier for custom login url")]
    ${custom_url}    Remove String    ${url_placeholder}    ${SPACE}
    ${custom_url}    Fetch From Right    ${custom_url}    ${PATIENT_PATH}#/login
    ${custom_url}    Convert To String    ${custom_url}
    ${actual_custom_url}    Convert To String    ${PATIENT_LOGIN_URL}${custom_url}
    Set Suite Variable    ${actual_custom_url}
    Should Contain    ${actual_custom_url}    ${clinic_shortname}
    ${expected_custom_url}    Convert To String    ${PATIENT_LOGIN_URL}?clinic=${clinic_shortname}
    Set Global Variable    ${expected_custom_url}
    Should Be Equal    ${actual_custom_url}    ${expected_custom_url}

Define Clinic Shortname And Custom Url
    [Arguments]    ${clinic_name}
    Get Clinic Short Name    ${clinic_name}
    Login As Nurse    clinic=${clinic_name}    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Basic Settings Tab
    Noona Administrator Edits Clinic Experience For Patients
    Get Custom URL
    Close Browser

Open "Clinic settings" And Select Basic Settings Tab
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    Wait Until Element Is Visible    ${basic_settings_tab}
    ${attr}    Get Element Attribute    ${basic_settings_tab}    class
    IF    'selected' not in '${attr}'    # if basic setting is not selected by default
        Try To Click Element    ${basic_settings_tab}
    END

Edit Time Zone Setting
    [Arguments]    ${timezone}
    Try To Input Text    ${time_zone_input}    ${timezone}

Check Saved Time Zone Setting
    [Arguments]    ${timezone}
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${clinic_settings_header}
    Try To Click Element    ${clinical_settings_tab}
    Try To Click Element    ${basic_settings_tab}
    Wait Until Page Contains Element    ${time_zone_input}
    ${timezone_value}    Get Value    ${time_zone_input}
    Should Contain    ${timezone_value}    ${timezone}

Noona Administrator Edits Clinic Experience For Patients
    Scroll Element Into View    ${clinic_experience_header}
    Try To Click Element    ${custom_branding_disabled}
    Scroll Element Into View    ${clinic_experience_header}
    Enable Custom Branding

Admin Can Preview Derived Color Before Saving
    [Arguments]    ${expected_hex_color_format}
    Wait Until Element Is Enabled    ${custom_color_field}
    Input Text    ${custom_color_field}    ${expected_hex_color_format}
    ${expected_hex_color_format}    Convert To Upper Case    ${expected_hex_color_format}
    Set Suite Variable    ${expected_hex_color_format}
    Wait Until Element Is Enabled    ${custom_color_clear_button}
    Get Element Color    ${custom_color_preview}
    IF    '${rbga_color}'!='rgba(0, 0, 0, 0)'
        Convert To Hex Color
    ELSE IF    '${rbga_color}'=='rgba(0, 0, 0, 0)'
        Verify Color Is Too Dark Or Too Light
    END

Clear Custom Color
    Scroll Element Into View    ${clinic_experience_header}
    Scroll Element Into View    ${custom_color_label}
    ${color_clear_button_exists}    Run Keyword And Return Status
    ...    Wait Until Element Is Enabled
    ...    ${custom_color_clear_button}
    IF    '${color_clear_button_exists}'=='True'
        Try To Click Element    ${custom_color_clear_button}
    END
    Custom Color Is Not Visible

Custom Color Is Not Visible
    [Documentation]    Default Noona color: HEX = 885DB3 or rgb(136, 93, 179)
    Scroll Element Into View    ${custom_color_label}
    Element Should Not Be Visible    ${custom_color_clear_button}
    ${expected_hex_color_format}    Convert To Upper Case    885DB3
    Set Suite Variable    ${expected_hex_color_format}

Get Element Color
    [Arguments]    ${element}
    ${element}    Get Webelement    ${element}
    ${rbga_color}    Call Method    ${element}    value_of_css_property    background-color
    Set Suite Variable    ${rbga_color}

Convert To Hex Color
    ${rbga_color}    Convert To String    ${rbga_color}
    ${rbga_color}    Remove String    ${rbga_color}    rgba(    )
    @{colors}    Split String    ${rbga_color}    ,
    ${RED}    Convert To Hex    ${colors}[0]
    ${GREEN}    Convert To Hex    ${colors}[1]
    ${BLUE}    Convert To Hex    ${colors}[2]
    ${actual_hex_value}    Catenate    ${RED}${GREEN}${BLUE}
    Should Be Equal    ${actual_hex_value}    ${expected_hex_color_format}

Verify Color Is Too Dark Or Too Light
    ${too_light}    Run Keyword And Return Status    Page Should Contain    ${custom_color_too_light}
    IF    '${too_light}'=='False'
        Page Should Contain    ${custom_color_too_dark}
    END

Enable Custom Branding
    Scroll Element Into View    ${clinic_experience_header}
    Try To Click Element    ${custom_branding_disabled}
    Try To Click Element    ${custom_branding_enabled}
    Scroll Element Into View    ${custom_logo_label}
    Wait Until Page Contains Element    ${custom_color_label}
    Wait Until Page Contains Element    ${custom_logo_label}
    Wait Until Page Contains Element    ${custom_service_name_noona}
    Wait Until Page Contains Element    ${custom_service_name_clinic_specific_name}

Update Clinic Custom Branding Settings
    [Arguments]    ${action}
    Scroll Element Into View    ${clinic_experience_header}
    IF    '${action}'=='enable'
        Try To Click Element    ${custom_branding_disabled}
        Try To Click Element    ${custom_branding_enabled}
        Scroll Element Into View    ${custom_logo_label}
        Wait Until Page Contains Element    ${custom_color_label}
        Wait Until Page Contains Element    ${custom_logo_label}
        Wait Until Page Contains Element    ${custom_service_name_noona}
        Wait Until Page Contains Element    ${custom_service_name_clinic_specific_name}
    ELSE IF    '${action}'=='disable'
        Try To Click Element    ${custom_branding_enabled}
        Try To Click Element    ${custom_branding_disabled}
        Element Should Not Be Visible    ${custom_color_label}
        Element Should Not Be Visible    ${custom_logo_label}
        Element Should Not Be Visible    ${custom_service_name_noona}
        Element Should Not Be Visible    ${custom_service_name_clinic_specific_name}
    END

Toggle Off Custom Branding
    Scroll Element Into View    ${clinic_experience_header}
    Try To Click Element    ${custom_branding_disabled}

Upload Logo With SVG Format
    [Arguments]    ${file}
    Scroll Element Into View    ${custom_logo_label}
    Scroll Element Into View    ${analytics_label}
    Wait Until Page Contains Element    ${custom_logo_label}
    Wait Until Page Contains Element    ${custom_logo_svg_text}
    Execute Javascript    document.querySelector("input[type='file']").style.visibility="visible";
    Execute Javascript    document.querySelector( "input[type='file']" ).style.visibility = "block";
    Choose File    //input[@type='file']    ${EXECDIR}${/}data${/}upload_photos${/}clinic_logo${/}${file}
    Wait Until Element Is Visible    ${custom_logo_id}    60s
    Set Suite Variable    ${custom_logo_id}

Upload Logo With Non-svg Format
    [Arguments]    ${file}
    Scroll Element Into View    ${custom_logo_label}
    Wait Until Page Contains Element    ${custom_logo_label}
    Wait Until Page Contains Element    ${custom_logo_svg_text}
    Execute Javascript    document.querySelector("input[type='file']").style.visibility="visible";
    Execute Javascript    document.querySelector( "input[type='file']" ).style.visibility = "block";
    Choose File    //input[@type='file']    ${EXECDIR}${/}data${/}upload_photos${/}JPG${/}${file}
    Wait Until Element Is Visible    //p[contains(text(),'Invalid svg file; please check it and try again.')]
    Wait Until Element Is Visible    //span[contains(text(),'Unsupported file type. Please upload the photo as ')]

Clear Uploaded Logo
    Scroll Element Into View    ${custom_logo_label}
    Scroll Element Into View    ${analytics_label}
    ${logo_clear_button_exists}    Run Keyword And Return Status
    ...    Wait Until Element Is Enabled
    ...    ${custom_logo_clear_button}
    IF    '${logo_clear_button_exists}'=='True'
        Try To Click Element    ${custom_logo_clear_button}
    END
    Element Should Not Be Visible    ${custom_logo_clear_button}
    Element Should Not Be Visible    ${custom_logo_id}

Save Basic Settings
    Wait Until Page Contains Element    ${basic_settings_save_button}
    Scroll Element Into View    ${basic_settings_save_button}
    Wait until keyword succeeds    3x    1s    Try To Click Element    ${basic_settings_save_button}
    Wait Until Page Contains    ${clinic_settings_are_updated_banner}
    Try To Click Banner Message
    Reload Page
    Sleep    3s
    Wait Until Element Is Visible    ${clinic_experience_header}
    Scroll Element Into View    ${analytics_label}

Save Basic Settings And Close Browser
    Wait Until Page Contains Element    ${basic_settings_save_button}
    Scroll Element Into View    ${basic_settings_save_button}
    Wait until keyword succeeds    3x    1s    Try To Click Element    ${basic_settings_save_button}
    Wait Until Page Contains    ${clinic_settings_are_updated_banner}
    Try To Click Banner Message
    Reload Page
    Sleep    3s
    Wait Until Element Is Visible    ${clinic_experience_header}
    Scroll Element Into View    ${analytics_label}
    #lines 334-337 were just added to see if the quiet time is updated successfully during the nightly run, should be removed after investigation is done
    Scroll Element Into View    ${quiet_time_start_field_id}
    Sleep    3s
    Close Browser

Select Custom Service Name
    [Arguments]    ${service_name}
    Wait Until Page Contains Element    ${custom_service_name_noona}
    Scroll Element Into View    ${custom_service_name_noona}
    IF    '${service_name}'=='no'
        Try To Click Element    ${custom_service_name_noona}
    ELSE
        Try To Click Element    ${custom_service_name_clinic_specific_name}
    END

Input Clinic Specific Name
    [Arguments]    ${noona's}    ${noona}
    Wait Until Element Is Visible    ${clinic_specific_name_noonas}
    Wait Until Element Is Visible    ${clinic_specific_name_noona}
    Try To Input Text    ${clinic_specific_name_noonas}    ${noona's}
    Try To Input Text    ${clinic_specific_name_noona}    ${noona}
    Set Suite Variable    ${noona's}
    Set Suite Variable    ${noona}

Convert And Compare Element To Default Color
    [Arguments]    ${default_color}
    ${default_color}    Convert To String    ${default_color}
    ${default_color}    Remove String    ${default_color}    rgba(    )
    @{colors}    Split String    ${default_color}}    ,
    ${RED}    Convert To Hex    ${colors}[0]
    ${GREEN}    Convert To Hex    ${colors}[1]
    ${BLUE}    Convert To Hex    ${colors}[2]
    ${actual_hex_value}    Catenate    ${RED}${GREEN}${BLUE}
    Should Be Equal    ${actual_hex_value}    ${noona_default_color}

Convert And Compare Element To Hex Color
    [Arguments]    ${rgb_color}
    ${rgb_color}    Convert To String    ${rgb_color}
    ${rgb_color}    Remove String    ${rgb_color}    rgba(
    ${rgb_color}    Remove String    ${rgb_color}    )
    @{colors}    Split String    ${rgb_color}    ,
    ${RED}    Convert To Hex    ${colors}[0]
    ${GREEN}    Convert To Hex    ${colors}[1]
    ${BLUE}    Convert To Hex    ${colors}[2]
    ${actual_hex_value}    Catenate    ${RED}${GREEN}${BLUE}
    Should Be Equal    ${actual_hex_value}    ${expected_hex_color_format}

Select Value From Drop Down And Verify Selected Value
    [Arguments]    ${dropdown_title}    ${dropdown}    ${selected_option_text}    ${select_code}
    Wait Until Page Does Not Contain    ${settings_updated_message}
    Wait Until Page Contains    ${dropdown_title}
    Wait Until Element Is Visible    ${dropdown}
    ${selected_option}    Get Selected List Label    ${dropdown}
    IF    '${selected_option}'=='${selected_option_text}'
        Try To Select By Label    ${dropdown}    ${select_code}
        Save Basic Clinic Settings
    END
    Wait Until Element Is Visible    ${dropdown}
    ${later_selected_option}    Get Selected List Label    ${dropdown}
    Should Match    ${later_selected_option}    ${select_code}

Check If Patient Education Settings Is Enabled Or Disabled
    Open "Clinic settings" And Select Basic Settings Tab
    Wait Until Element Is Visible    ${clinic_can_send_patient_education_messages}
    Scroll Element Into View    ${clinic_can_send_patient_education_messages}
    ${attr_enabled}    Get Element Attribute    ${patient_education_enabled_data_testid}    class
    ${attr_disabled}    Get Element Attribute    ${patient_education_disabled_data_testid}    class
    IF    'radio-checked' in '${attr_enabled}' and 'radio-checked' not in '${attr_disabled}'
        Set Test Variable    ${pe_status_enabled}    True
        Set Test Variable    ${pe_status_disabled}    False
    ELSE IF    'radio-checked' in '${attr_disabled}' and 'radio-checked' not in '${attr_enabled}'
        Set Test Variable    ${pe_status_enabled}    False
        Set Test Variable    ${pe_status_disabled}    True
    ELSE
        Fail    Something wrong with the patient education settings. Please check manually.
    END

Enable Patient Can Respond To Education Messages
    Wait Until Element Is Visible    ${patient_can_respond_to_patient_education_messages}
    ${attr_enabled}    Get Element Attribute    ${patient_education_patiend_respond_enabled}    class
    IF    'radio-checked' not in '${attr_enabled}'    Try To Click Element    ${patient_education_patiend_respond_enabled}

Enable Patient Education Settings
    [Arguments]    ${clinic}
    Login As Nurse    clinic=${clinic}    user_type=${USER_TYPE}[noona_admin]
    Check If Patient Education Settings Is Enabled Or Disabled
    IF     ${pe_status_disabled}==${TRUE}
        Try To Click Element    ${patient_education_enabled}
        Enable Patient Can Respond To Education Messages
        Sleep    1s
        Save Basic Clinic Settings
    END
    Close Browser

Disable Patient Education Settings
    [Arguments]    ${clinic}
    Login As Nurse    clinic=${clinic}    user_type=${USER_TYPE}[noona_admin]
    Check If Patient Education Settings Is Enabled Or Disabled
    IF     ${pe_status_enabled}==${TRUE}
        Try To Click Element    ${patient_education_disabled}
        Sleep    1s
        Save Basic Clinic Settings
    END
    Close Browser

Save Basic Clinic Settings
    Try To Click Element    ${save_settings}
    Wait Until Page Contains    ${settings_updated_message}

Modify Terms Of Use And Save
    [Documentation]    Modifies terms of use under Legal texts
    [Arguments]    ${clinic}
    Login As Nurse    clinic=${clinic}    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Basic Settings Tab
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    ${terms}    Convert Text File To String    ${terms_text_location}
    Set Test Variable    ${terms}    ${now}-${terms}
    Try To Input Text    ${terms_of_use_text_area}    ${terms}
    Sleep    1    #needed to make sure the update of terms is complete before saving
    ${text}    Get Value    ${terms_of_use_text_area}
    Should Be Equal    ${text}    ${terms}
    Select New Consent Required From Patients For Terms Of Use
    Try To Click Element    ${basic_settings_save_button}
    Wait Until Page Contains Element    ${banner_toast_message}
    Page Should Not Contain    Application error. Please try again.
    Wait Until Page Contains    ${clinic_settings_are_updated_banner}
    Wait Until Page Does Not Contain    ${clinic_settings_are_updated_banner}

Modify Consents And Save
    [Documentation]    Modifies Consent for the processing of personal information and Patient consent full text
    Open "Clinic settings" And Select Basic Settings Tab
    ${now}    Get Current Date    result_format=%Y%m%d%H%M%S
    ${consents}    Convert Text File To String    ${consent_text_location}
    Set Test Variable    ${consents}    ${now}-${consents}
    ${full_consent}    Convert Text File To String    ${consent_full_text_location}
    Set Test Variable    ${full_consent}    ${now}-${full_consent}
    Wait Until Page Contains Element    ${service_content_text_area}
    Try To Input Text    ${service_content_text_area}    ${consents}
    Select New Consent Required From Patients For Personal Info
    Try To Input Text    ${service_content_full_text_area}    ${full_consent}
    Select New Consent Required From Patients For Full Consent
    Try To Click Element    ${basic_settings_save_button}
    Wait Until Page Contains    ${clinic_settings_are_updated_banner}
    Wait Until Page Does Not Contain    ${clinic_settings_are_updated_banner}

Select New Consent Required From Patients For Personal Info
    [Documentation]    The checkbox should already be visible. Meaning, the consent was already updated at this point
    Wait Until Element Is Visible    ${new_service_consent_checkbox}
    ${attr}    Get Element Attribute    ${new_service_consent_checkbox}    class
    IF    'checkbox-checked' not in '${attr}'
        Try To Click Element    ${new_service_consent_checkbox}
        ${attr}    Get Element Attribute    ${new_service_consent_checkbox}    class
        Should Contain    ${attr}    checkbox-checked
    END

Select New Consent Required From Patients For Full Consent
    [Documentation]    The checkbox should already be visible. Meaning, the consent was already updated at this point
    Wait Until Element Is Visible    ${new_consent_data_checkbox}
    ${attr}    Get Element Attribute    ${new_consent_data_checkbox}    class
    IF    'checkbox-checked' not in '${attr}'
        Try To Click Element    ${new_consent_data_checkbox}
        ${attr}    Get Element Attribute    ${new_consent_data_checkbox}    class
        Should Contain    ${attr}    checkbox-checked
    END

Select New Consent Required From Patients For Terms Of Use
    [Documentation]    The checkbox should already be visible. Meaning, the terms was already updated at this point
    Wait Until Element Is Visible    ${new_consent_terms_checkbox}
    ${attr}    Get Element Attribute    ${new_consent_terms_checkbox}    class
    IF    'checkbox-checked' not in '${attr}'
        Try To Click Element    ${new_consent_terms_checkbox}
        ${attr}    Get Element Attribute    ${new_consent_terms_checkbox}    class
        Should Contain    ${attr}    checkbox-checked
    END

Give New Consents And E-signature
    [Documentation]    Modify Consents And Save    kw should be run before this to set the consents in variable
    Wait Until Page Contains Element    ${approve_elements_checkbox}
    ${consents}    Convert Text File To String    ${consent_text_location}
    Wait Until Page Contains    ${consents}
    Try To Click Element    ${approve_elements_checkbox}
    Try To Click Element    ${next_button}
    ${full_consent}    Convert Text File To String    ${consent_full_text_location}
    Wait Until Page Contains    ${full_consent}
    Try To Click Element    ${approve_elements_checkbox}
    Wait Until Page Contains Element    ${signature_input}
    Try To Input Text    ${signature_input}    ${consent_input_text}
    Try To Click Element    ${next_button}
    Wait Until Page Contains    ${diary}

Give Consents And E-signature
    Wait Until Page Contains Element    ${approve_elements_checkbox}
    ${consents}    Convert Text File To String    ${consent_text_location}
    Wait Until Page Contains    ${consents}
    Tick Consent Checkbox
    Click Next On Terms And Conditions
    ${full_consent}    Convert Text File To String    ${consent_full_text_location}
    Wait Until Page Contains    ${full_consent}
    Tick Consent Checkbox
    Wait Until Page Contains Element    ${signature_input}
    Try To Input Text    ${signature_input}    ${consent_input_text}
    Try To Click Element    ${next_button}
    Wait Until Page Contains    ${diary}

Get Random Language
    @{languages}    Get Webelements    ${clinic_settings_languages}
    ${count}    Get Element Count    ${clinic_settings_languages}
    ${random}    Evaluate    random.randint(0, ${count}-1)    modules=random
    ${random_lang}    Set Variable    ${languages}[${random}]
    ${random_lang}    Get Text    ${random_lang}
    RETURN    ${random_lang}

Get Random Date Formatting
    @{date_formats}    Get Webelements    ${settings_date_formatting}
    ${count}    Get Element Count    ${settings_date_formatting}
    ${random}    Evaluate    random.randint(0, ${count}-1)    modules=random
    ${random_format}    Set Variable    ${date_formats}[${random}]
    ${random_format}    Get Text    ${random_format}
    RETURN    ${random_format}

Update Quiet Time
    [Arguments]    ${outside_or_inside}
    Wait Until Element Is Visible    ${admin_clinic_menu}
    Wait Until Element Is Enabled    ${admin_clinic_menu}
    Wait Until Element Is Visible    ${first_user_in_list}
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    '${outside_or_inside}'=='outside'    #sets quiet time outside working hours
        IF    ${remote_url_exists}
            Try To Input Text    ${quiet_time_start_field_id}    1100PM
            Try To Input Text    ${quiet_time_end_field_id}    0600AM
        ELSE
            Try To Input Text    ${quiet_time_start_field_id}    2300
            Try To Input Text    ${quiet_time_end_field_id}    0600
        END
    ELSE
        ${now}    Get Current Date
        IF    ${remote_url_exists}
            ${1hr_ago}    Subtract Time From Date    ${now}    1 hour    result_format=%I%M%p
            ${5hrs_after}    Add Time To Date    ${now}    5 hours    result_format=%I%M%p
        ELSE
            ${1hr_ago}    Subtract Time From Date    ${now}    1 hour    result_format=%H%M
            ${5hrs_after}    Add Time To Date    ${now}    5 hours    result_format=%H%M
        END
        Try To Input Text    ${quiet_time_start_field_id}    ${1hr_ago}
        Try To Input Text    ${quiet_time_end_field_id}    ${5hrs_after}
    END
    Save Basic Settings And Close Browser
