*** Settings ***
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources/clinic_settings/clinic_settings.resource


*** Variables ***
${clinic_settings_link}                 clinic-settings-link
${sms_settings_tab}                     sms-settings
${case_management_tab}                  case-management
${sms_yes_radio_button}                 //*[@data-testid="enable-sms-notifications-yes"]
${sms_no_radio_button}                  //*[@data-testid="enable-sms-notifications-no"]
${sms_invitation_life_time_input}       //*[@data-testid="smsInvitationLifeTime"]
${sms_invitation_delay_input}           //*[@data-testid="smsInvitationDelay"]
${sms_reminder_delay_input}             //*[@data-testid="smsReminderDelay"]
${sms_invite_yes_radio_button}          //*[@data-testid="enable-sms-invitations-yes"]
${sms_invite_no_radio_button}           //*[@data-testid="enable-sms-invitations-no"]
${sms_yes_radio_button_enabled}         //*[@data-testid="enable-sms-notifications-yes" and contains(@class, "radio-checked")]
${save_button}                          //*[@data-testid="save"]
${patient_invitation_textbox1}          //*[@data-testid="content-en_GB-sms-invitations"]
${patient_invitation_textbox2}          //*[@data-testid="content-en_GB-sms-reminders"]
${patient_invitation_textbox3}          //*[@data-testid="content-en_GB-email-invitations"]
${patient_invitation_textbox4}          //*[@data-testid="content-en_GB-email-reminders"]
${patient_invitation_textbox5}          //*[@data-testid="content-en_GB"]

*** Keywords ***
Noona Administrator Navigates To Clinic Settings
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}

Noona Administrator Navigates To The SMS Settings Tab
    Wait Until Element Is Visible    ${sms_settings_tab}
    Try To Click Element    ${sms_settings_tab}    wait_in_seconds=7s

Noona Administrator Edits SMS Notifications
    Wait Until Element Is Visible       ${sms_yes_radio_button}
    Click Element    ${sms_no_radio_button}//label
    Click Element    ${sms_yes_radio_button}//label
    Wait Until Page Contains Element    ${sms_yes_radio_button_enabled}
    Save Settings
    Reload Page
    Wait Until Element Is Visible       ${sms_yes_radio_button}
    Click Element    ${sms_no_radio_button}//label
    Wait Until Page Does Not Contain Element    ${sms_yes_radio_button_enabled}
    Save Settings

Noona Administrator Edits SMS Invitations
    Wait Until Element Is Visible       ${sms_invite_yes_radio_button}
    Click Element    ${sms_invite_yes_radio_button}//label
    Try To Input Text    ${sms_invitation_life_time_input}    11
    Try To Input Text    ${sms_invitation_delay_input}    12
    Try To Input Text    ${sms_reminder_delay_input}    13
    Save Settings
    Reload Page
    Wait Until Page Contains Element    ${sms_invite_yes_radio_button}
    Page Should Contain    11
    Page Should Contain    12
    Page Should Contain    13
    Try To Input Text    ${sms_invitation_life_time_input}    7
    Try To Input Text    ${sms_invitation_delay_input}    8
    Try To Input Text    ${sms_reminder_delay_input}    9
    Save Settings
    Page Should Contain    7
    Page Should Contain    8
    Page Should Contain    9
    Click Element    ${sms_invite_no_radio_button}//label
    Save Settings