*** Settings ***
Library     OperatingSystem
Library     String
Library     RequestsLibrary
Library     JSONLibrary
Library     DateTime
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}questionnaires.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${appointment_reason_text}              Visit Dr. Jekyll for a cure
${appointment_tunit_text}               Joel's Angels
${appointment_reason}                   Radiotherapy
${location}                             HALO
${show_all_button}                      //*[@id="button-timeline-upcoming-show-events"]/button
${appointment_close_button}             //*[@id="diary-event-show-appointment-modal"]//button
${timestamp_format}                     %Y%m%d%H%M%S
${ui_date_format}                       %d.%m.%Y
${ui_time_format}                       %H:%M
${all_upcoming_events}                  //div[contains(@class, "upcoming-event")]
${no_upcoming_events}                   //p[contains(text(),'No upcoming events')]
${tomorrow_event}                       //div[contains(@class, "upcoming-event")]//*[contains(text(), "Tomorrow")]
${clinic_settings_link}                 clinic-settings-link
${integration_settings_tab}             //*[@id="integration-settings"]
${add_schedule_button}                  //*[@data-testid="appointmentClinicSettings-add-button"]
${delete_schedule_button}               //*[@data-testid="appointmentClinicSettings-remove-button"]
${schedule_evemt_id_text_field}         //*[@data-testid="appointmentType"]
${schedule_evemt_id_field}              //*[@data-testid="sentDays"]
${sent_to_patient_checkbox}             //*[@data-testid="sendable"]
${visible_to_patient_checkbox}          //*[@data-testid="visibleToOnlyActivePatients"]
${schdeduled_questionnaire_dropdown}    //*[@data-testid="scheduledType"]
${save_button}                          //*[@id="save"]
${scheduled_days}                       3
${sent_to_patient_status}               //*[@data-testid="sendable"]
${sent_to_patient_status_true}          //*[@data-testid="sendable" and contains(@class, "checkbox-checked")]
${visible_to_patient_status}            //*[@data-testid="visibleToOnlyActivePatients"]
${visible_to_patient_status_true}
...                                     //*[@data-testid="visibleToOnlyActivePatients" and contains(@class, "checkbox-checked")]
${patient_invitation_tab}               invitation-settings
${upcoming_events_header_element}       upcoming-events-header
${upcoming_events_header_text}          Upcoming events
${appointment_modal_content}            //*[@id="diary-event-show-appointment-modal"]//nh-diary-event-show-appointment
${diary_entries_header}                 //h1[contains(text(),'Diary entries')]
${show_less_button}                     //div[contains(text(),'Show less')]
${fhir_appointment_timestamp_format}    %Y-%m-%dT%H:%M:%S.000+00:00
###Update test depending on the appointment type below
###Blank appointment type fails the "Send Appointment Via API" keyword
${appointment_type}                     QUESTIONNAIRE_INQUIRY
${appointment_type_2}                   SYMPTOM_INQUIRY
${appointment_type_3}                   INTEGRATION_APPOINTMENT_TIMELINE
${end_of_events_element_text}           //div[contains(text(),"You've reached the end of your events.")]
${patient_instruction_text}                  Please bring your ID. Memorial Hospital. Physical distancing may not be permit a companion to attend all visits. Please bring all precriptions that you are currently taking including over the counter medications.Provider: Dr. Scott James. Please have bloodwork first. Requisitions at the clinic 3 desk. See the doctor on the 5th floor, clinic 35


*** Keywords ***
Remove Appointment Via API
    [Arguments]    ${appointment_id}    ${patient_mrn}
    &{header}    Create Dictionary    Content-Type=application/json    X-LOGIN-TOKEN=${appointment_user_token}
    ${file_data}    Get file
    ...    ${EXECDIR}${/}resources${/}patient${/}integration${/}removeAppointmentFromIntegration.json
    ${body}    Evaluate    json.loads('''${file_data}''')    json
    ${updated_file_data}    Update Value To Json    ${body}    $..appointmentId    ${appointment_id}
    ${updated_file_data}    Update Value To Json    ${updated_file_data}    $..medicalRecordNumber    ${patient_mrn}
    Create Session    noona    ${API_URL}    verify=true
    ${response}    POST On Session
    ...    noona
    ...    /api/system
    ...    json=${updated_file_data}
    ...    expected_status=200
    ...    headers=${header}
    Should Contain    '${response.content}'    "result":true
    Delete All Sessions

Appointment Teardown
    [Arguments]    ${appointment_id}    ${patient_mrn}
    Remove Appointment Via API    ${appointment_id}    ${patient_mrn}
    Close All Browsers

Check Appointment From The Timeline
    [Arguments]    ${appointment_reason_text}    ${appointment_tunit_text}    ${future_timezone_name}
    Wait Until Page Contains Element    //*[@id='${upcoming_events_header_element}']
    Wait Until Page Contains    ${upcoming_events_header_text}
    Click Show All Button
    Wait Until Noona Loader Is Not Visible
    Scroll Element Into View
    ...    //time[contains(text(),'${future_day_abbreviated}, ${future_appointment_date}, ${future_appointment_time} (${future_timezone_name})')]
    Scroll Element Into View
    ...    //div[contains(@class, "upcoming-event")]//time[contains(text(), "${future_appointment_date}")]
    ${upcoming_appointment_date_element}    Set Variable
    ...    //ds-icon[contains(@class, "icon-appointment-clinic")]/../..//div[contains(@class, "upcoming-event")]//time[contains(text(), "${future_appointment_date}")]
    Scroll Element Into View    ${upcoming_appointment_date_element}
    Try For Element Should Contain    ${upcoming_appointment_date_element}/../../p[1]    ${appointment_reason_text}
    Try For Element Should Contain    ${upcoming_appointment_date_element}/../../p[2]    ${future_appointment_date}
    Try For Element Should Contain    ${upcoming_appointment_date_element}/../../p[3]    ${appointment_tunit_text}
    # to confirm with screenshot if needed for investigation
    Scroll Element Into View
    ...    ${upcoming_appointment_date_element}/../../p[3]

Check Tomorrow's Appointment From The Timeline
    Wait Until Page Contains    ${upcoming_events_header_text}
    Click Show All Button
    Wait Until Page Contains Element    ${tomorrow_event}
    Try For Element Should Contain    ${tomorrow_event}    Tomorrow
    Try For Element Should Contain    ${tomorrow_event}/../../div[2]/p[1]    ${appointment_reason_text}
    Try For Element Should Contain    ${tomorrow_event}/../../div[2]/p[3]    ${appointment_tunit_text}

Click Show All Button
    Run Keyword And Ignore Error    Wait Until Page Contains Element    ${show_all_button}    10s
    ${show_all_button_present}    Run Keyword And Return Status    Page Should Contain Element    ${show_all_button}
    IF    '${show_all_button_present}'=='True'
        Try To Click Element    ${show_all_button}
        Wait Until Page Does Not Contain Element    ${show_all_button}
    END

Get Appointment Date
    [Documentation]    DateTime format, codes & conversion
    ...    https://docs.python.org/3/library/datetime.html#strftime-and-strptime-behavior
    [Arguments]    ${days}
    ${current_date}    Get Current Date
    ${future_date}    Add Time To Date    ${current_date}    ${days}
    Set Test Variable    ${future_date}
    # Converts to abbreviated day name
    ${future_day_abbreviated}    Convert Date    ${future_date}    result_format=%a
    Set Test Variable    ${future_day_abbreviated}
    # Converts to full day name
    ${future_day_fullname}    Convert Date    ${future_date}    result_format=%A
    Set Test Variable    ${future_day_fullname}
    ${future_timestamp}    Convert Date    ${future_date}    result_format=${timestamp_format}
    Set Test Variable    ${future_timestamp}
    ${future_appointment_date}    Convert Date    ${future_date}    result_format=${ui_date_format}
    Set Test Variable    ${future_appointment_date}
    ${future_appointment_time}    Convert Date    ${future_date}    result_format=${ui_time_format}
    Set Test Variable    ${future_appointment_time}
    ${future_fhir_appointment_datetime}    Convert Date
    ...    ${future_date}
    ...    result_format=${fhir_appointment_timestamp_format}
    Set Test Variable    ${future_fhir_appointment_datetime}

Set Appointment Date In The Future
    [Arguments]    ${days}
    ${current_date}    Get Current Date
    Set Test Variable    ${current_date}
    ${future_date}    Add Time To Date    ${current_date}    ${days}
    Set Test Variable    ${future_date}
    Convert X Date    ${future_date}

Open Appointment And Check The Content
    [Arguments]    ${appointment_reason_text}    ${appointment_tunit_text}    ${future_timezone_name}
    Try To Click Element
    ...    //div[contains(@class, "upcoming-event")]//*[contains(text(),'${future_day_abbreviated}, ${future_appointment_date}, ${future_appointment_time} (${future_timezone_name})')]
    Wait Until Page Contains    Scheduled by the clinic
    Wait For Element To Be Present
    ...    //dd[contains(text(),'${future_day_fullname}, ${future_appointment_date}, ${future_appointment_time} (${future_timezone_name})')]
    Try For Element Should Contain    ${appointment_modal_content}    ${future_day_fullname}
    Try For Element Should Contain    ${appointment_modal_content}    ${appointment_tunit_text}
    Try For Element Should Contain    ${appointment_modal_content}    ${future_appointment_date}
    Try For Element Should Contain    ${appointment_modal_content}    ${future_appointment_time}
    Try For Element Should Contain    ${appointment_modal_content}    ${future_timezone_name}
    Try To Click Element    ${appointment_close_button}

Appointment Removed From The Timeline
    Click Show All Button
    Wait Until Page Contains Element    ${all_upcoming_events}
    Page Should Not Contain Element
    ...    //div[contains(@class, "upcoming-event")]//*[contains(text(), "${future_appointment_date}")]/../../div[1]
    Page Should Not Contain Element
    ...    //div[contains(@class, "upcoming-event")]//*[contains(text(), "${future_appointment_date}")]/../../div[2]
    Page Should Not Contain Element
    ...    //div[contains(@class, "upcoming-event")]//*[contains(text(), "${future_appointment_date}")]/../../div[3]

Check Questionnaire From The Timeline
    [Documentation]    Test for scheduling an appointment + questionnaire to patient
    ...    Two different appointments should be displayed in Diary Upcoming Events
    [Arguments]
    ...    ${days}
    ...    ${appointment_reason_text}
    ...    ${future_timezone_name}
    ...    ${appointment_tunit_name}
    ...    ${questionnaire_type}
    # Questionare expected date based on clinic settings
    ${questionnaire_date}    Subtract Time From Date    ${future_date}    ${days}
    Set Test Variable    ${questionnaire_date}
    ${questionnaire_date_ui}    Convert Date    ${questionnaire_date}    result_format=${ui_date_format}
    Set Test Variable    ${questionnaire_date_ui}
    Wait For Element To Be Present    ${upcoming_events_header_element}
    Click Show All Button
    # Questionnaire date only
    Try For Element Should Contain
    ...    //ds-icon[contains(@class, "icon-questionnaire")]/../following-sibling::div//*[contains(text(), "${questionnaire_date_ui}")]/../../p[1]
    ...    ${questionnaire_type}
    # Appointment Date
    Wait For Element To Be Present
    ...    //*[contains(text(),'${future_day_abbreviated}, ${future_appointment_date}, ${future_appointment_time} (${future_timezone_name})')]
    Set Focus To Element
    ...    //ds-icon[contains(@class, "icon-appointment")]/../following-sibling::div//*[contains(text(),'${future_day_abbreviated}, ${future_appointment_date}, ${future_appointment_time} (${future_timezone_name})')]
    Try For Element Should Contain
    ...    //ds-icon[contains(@class, "icon-appointment-clinic")]/../following-sibling::div//*[contains(text(), "${future_appointment_date}")]/../../p[1]
    ...    ${appointment_reason_text}
    Try For Element Should Contain
    ...    //ds-icon[contains(@class, "icon-appointment-clinic")]/../following-sibling::div//*[contains(text(), "${future_appointment_date}")]/../../p[3]
    ...    ${appointment_tunit_name}

Questionnaire Removed From The Timeline
    [Arguments]    ${future_timezone_name}
    Reload Page
    Wait Until Element Contains    ${upcoming_events_header_element}    ${upcoming_events_header_text}
    Click Show All Button
    Page Should Not Contain Element
    ...    //span[contains(text(),'${future_day_abbreviated}, ${future_appointment_date}, ${future_appointment_time} (${future_timezone_name})')]
    Page Should Not Contain Element
    ...    //ds-icon[contains(@class, "icon-calendar")]/..//div[contains(@class, "upcoming-event")]//span[contains(text(), "${questionnaire_date_ui}")]/../../p[2]
    ...    ${BASELINE_QUESTIONNAIRE}

Check Questionnaire Appointment From The Diary Timeline
    [Documentation]    Clinic's Integration settings setup should match the Automated Questionnaire Scheduling Integration Event Code
    ...    and Appointment type from integration code, this is only for TNONC distress questionnaire to show
    ...    an event appointment in patient's diary but questionnaire is only visible in clinic side
    [Arguments]
    ...    ${days}
    ...    ${appointment_name_visible_for_patient}
    ...    ${questionnaire_timezone_name}
    ...    ${appointment_tunit_name}
    ${questionnaire_date}    Subtract Time From Date    ${future_date}    ${days}
    Set Test Variable    ${questionnaire_date}
    ${questionnaire_date_ui}    Convert Date    ${questionnaire_date}    result_format=${ui_date_format}
    Set Test Variable    ${questionnaire_date_ui}
    ${questionnaire_time_ui}    Convert Date    ${questionnaire_date}    result_format=${ui_time_format}
    Set Test Variable    ${questionnaire_time_ui}
    # Converts to abbreviated day name
    ${questionnaire_day_abbreviated}    Convert Date    ${questionnaire_date}    result_format=%a
    Set Test Variable    ${questionnaire_day_abbreviated}
    # Converts to full day name
    ${questionnaire_day_fullname}    Convert Date    ${questionnaire_date}    result_format=%A
    Set Test Variable    ${questionnaire_day_fullname}
    ${questionnaire_fhir_appointment_datetime}    Convert Date
    ...    ${questionnaire_date}
    ...    result_format=${fhir_appointment_timestamp_format}
    Set Test Variable    ${questionnaire_fhir_appointment_datetime}
    Wait For Element To Be Present    ${upcoming_events_header_element}
    Click Show All Button
    Wait Until Element Is Visible    (//h2)[last()]
    Scroll Element Into View    (//h2)[last()]
    Wait For Element To Be Present
    ...    //*[contains(text(),'${questionnaire_day_abbreviated}, ${questionnaire_date_ui}, ${questionnaire_time_ui} (${questionnaire_timezone_name})')]
    Set Focus To Element
    ...    //ds-icon[contains(@class, "icon-appointment")]/../following-sibling::div//*[contains(text(),'${questionnaire_day_abbreviated}, ${questionnaire_date_ui}, ${questionnaire_time_ui} (${questionnaire_timezone_name})')]
    Wait Until Page Contains Element
    ...    //ds-icon[contains(@class, "icon-appointment")]/../following-sibling::div//*[contains(text(),'${questionnaire_day_abbreviated}, ${questionnaire_date_ui}, ${questionnaire_time_ui} (${questionnaire_timezone_name})')]/../../p[1][contains(text(), "${appointment_name_visible_for_patient}")]
    Wait Until Page Contains Element
    ...    //ds-icon[contains(@class, "icon-appointment")]/../following-sibling::div//*[contains(text(),'${questionnaire_day_abbreviated}, ${questionnaire_date_ui}, ${questionnaire_time_ui} (${questionnaire_timezone_name})')]/../../p[3][contains(text(), "${appointment_tunit_name}")]

Questionnaire Appointment Removed From The Diary Timeline
    [Arguments]    ${questionnaire_timezone_name}
    Reload Page
    Wait Until Element Contains    ${upcoming_events_header_element}    ${upcoming_events_header_text}
    Click Show All Button
    Page Should Not Contain Element
    ...    //*[contains(text(),'${questionnaire_day_abbreviated}, ${questionnaire_date}, ${questionnaire_time_ui} (${questionnaire_timezone_name})')]

Check Scheduled Questionnaire From The Clinic Page
    [Arguments]    ${patient_ssn}    ${questionnaire_type}
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Wait Until Page Contains Element    (//td[contains(text(), "${questionnaire_date_ui}")]/..)[last()]
    Element Should Contain    (//td[contains(text(), "${questionnaire_date_ui}")]/..)[last()]    ${questionnaire_type}
    Element Should Contain
    ...    (//td[contains(text(), "${questionnaire_date_ui}")]/..)[last()]
    ...    ${future_appointment_date}
    Element Should Contain
    ...    (//td[contains(text(), "${questionnaire_date_ui}")]/..)[last()]
    ...    ${questionnaire_date_ui}
    Element Should Contain    (//td[contains(text(), "${questionnaire_date_ui}")]/..)[last()]    SCHEDULED

Questionnaire Removed From The Clinic Pages
    [Arguments]    ${patient_ssn}
    Search Patient By Identity Code    ${patient_ssn}
    Navigate To Questionnaires Tab
    Wait Until Page Contains Element    //div[contains(@class, "module-details")]
    Page Should Not Contain    (//td[contains(text(), "${questionnaire_date_ui}")]/..)[last()]    SCHEDULED

Go To Integration Settings
    Try To Click Element    ${admin_clinic_menu}
    Try To Click Element    ${clinic_settings_link}
    Wait Until Page Contains Element    ${integration_settings_tab}
    Try To Click Element    ${integration_settings_tab}

Add And Save A New Schedule
    Try To Click Element    ${add_schedule_button}
    Wait Until Page Contains Element    ${schedule_evemt_id_field}
    ${random_event_id}    Generate Random String    6
    Set Test Variable    ${random_event_id}
    Delete All Automated Schedules
    Try To Click Element    ${add_schedule_button}
    Try To Input Text    ${schedule_evemt_id_text_field}    ${random_event_id}
    Try To Input Text    ${schedule_evemt_id_field}    ${scheduled_days}
    Try To Click Element    ${sent_to_patient_checkbox}
    Try To Click Element    ${visible_to_patient_checkbox}
    Wait Until Page Contains Element    ${schdeduled_questionnaire_dropdown}
    Try To Click Element    ${schdeduled_questionnaire_dropdown}
    Try To Click Element    //*[@data-testid="scheduledType"]//*[@title="${BASELINE_QUESTIONNAIRE}"]
    Try To Click Element    ${save_button}
    Wait Until Page Contains    Clinic settings are updated
    Try To Click Banner Message

Delete All Automated Schedules
    Wait Until Page Contains Element    ${delete_schedule_button}
    ${number_of_delete_button_present}    Get Element Count    ${delete_schedule_button}
    ${delete_button_present}    Run Keyword And Return Status
    ...    Page Should Not Contain Element
    ...    ${delete_schedule_button}
    IF    '${delete_button_present}'=='False'
        Repeat Keyword    ${number_of_delete_button_present} times    Try To Click Element    ${delete_schedule_button}
    END

Verify Schedule Is Saved
    Wait Until Page Contains Element    ${schedule_evemt_id_field}
    Wait Until Keyword Succeeds    3x    1s    Scroll Element Into View    ${patient_invitation_tab}
    Try To Click Element    ${patient_invitation_tab}
    Try To Click Element    ${integration_settings_tab}
    Wait Until Page Contains Element    ${schedule_evemt_id_field}
    ${saved_id_value}    Get Value    ${schedule_evemt_id_text_field}
    ${saved_days}    Get Value    ${schedule_evemt_id_field}
    Should Be Equal    ${saved_id_value}    ${random_event_id}
    Should Be Equal    ${saved_days}    ${scheduled_days}
    ${saved_sent_to_patient_value}    Get Value    ${sent_to_patient_status}
    ${visible_to_patient_value}    Get Value    ${visible_to_patient_status}
    Wait Until Page Contains Element    ${visible_to_patient_status_true}
    Wait Until Page Contains Element    ${sent_to_patient_status_true}
    Wait Until Page Contains Element
    ...    (//*[@data-testid="scheduledType"]//*[contains(@title, "${BASELINE_QUESTIONNAIRE}")])[1]

###---Appointments Via FHIR---###

Send Appointments Via FHIR API
    [Documentation]    To update/change an appointment which has been sent before, provide value to these additional arguments: ${is_new_or_updated_appt}=update, appointmentId, paient instruction text
    [Arguments]    ${appointment_treatment_unit_id}    ${appointment_type_code}    ${patient_id}    ${is_new_or_updated_appt}=new    ${appointment_id}=${EMPTY}    ${instruction_to_patient}=${patient_instruction_text}
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${appointment_user_token}
    Set Test Variable    &{header}
    IF    '${is_new_or_updated_appt}' == 'new'
        ${appointment_id}    Generate random string    8    **********
        Set Test Variable    ${appointment_id}
    ELSE IF    '${is_new_or_updated_appt}' == 'updated'
        Set Test Variable    ${appointment_id}
    END
    ${body}    Get File    ${EXECDIR}${/}resources${/}patient${/}integration${/}addAppointmentSIUMO.json
    ${body_mod}    Replace String    ${body}    appointment-id    ${appointment_id}
    ${body_mod}    Replace String    ${body_mod}    treatment-id    ${appointment_treatment_unit_id}
    ${body_mod}    Replace String    ${body_mod}    recode    ${appointment_type_code}
    ${body_mod}    Replace String    ${body_mod}    Patient-id    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    fhir-datetime    ${future_fhir_appointment_datetime}
    ${body_mod}    Replace String    ${body_mod}    patient-instruction    ${instruction_to_patient}
    Log    ${body_mod}
    Set Test Variable    ${body_mod}
    Set Test Variable    ${appointment_id}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=true
    # expected_status=200
    ${response}    POST On Session
    ...    noona
    ...    /fhir-r4/Appointment
    ...    data=${body_mod}
    ...    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    Delete All Sessions

Remove Appointment Via FHIR API
    [Documentation]    Provide appointmentid as argument to remove a specific appointment.
    [Arguments]    ${appointment_type_code}    ${patient_id}    ${appointment_id}
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${appointment_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}patient${/}integration${/}addAppointmentViaFHIR.json
    ${body_mod}    Replace String    ${body}    appointment-id    ${appointment_id}
    ${body_mod}    Replace String    ${body_mod}    recode    ${appointment_type_code}
    ${body_mod}    Replace String    ${body_mod}    Patient-id    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    fhir-datetime    ${future_fhir_appointment_datetime}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    PUT On Session    noona    /fhir-r4/Appointment/${appointment_id}    data=${body_mod}
    ...    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

FHIR Appointment Teardown
    [Arguments]    ${appointment_type_code}    ${patient_id}
    Remove Appointment Via FHIR API    ${appointment_type_code}    ${patient_id}    ${appointment_id}
    Close All Browsers

Send X Future Appointment Dates
    [Arguments]    ${days}    ${appointment_treatment_unit_id}    ${appointment_type_code}    ${patient_id}
    Set Appointment Date In The Future    ${days}
    Send Appointments Via FHIR API    ${appointment_treatment_unit_id}    ${appointment_type_code}    ${patient_id}

Convert X Date
    [Documentation]    DateTime format, codes & conversion
    ...    https://docs.python.org/3/library/datetime.html#strftime-and-strptime-behavior
    [Arguments]    ${future_date}
    # Converts to abbreviated day name
    ${future_day_abbreviated}    Convert Date    ${future_date}    result_format=%a
    Set Test Variable    ${future_day_abbreviated}
    # Converts to full day name
    ${future_day_fullname}    Convert Date    ${future_date}    result_format=%A
    Set Test Variable    ${future_day_fullname}
    ${future_timestamp}    Convert Date    ${future_date}    result_format=${timestamp_format}
    Set Test Variable    ${future_timestamp}
    ${future_appointment_date}    Convert Date    ${future_date}    result_format=${ui_date_format}
    Set Test Variable    ${future_appointment_date}
    ${future_appointment_time}    Convert Date    ${future_date}    result_format=${ui_time_format}
    Set Test Variable    ${future_appointment_time}
    ${future_fhir_appointment_datetime}    Convert Date
    ...    ${future_date}
    ...    result_format=${fhir_appointment_timestamp_format}
    Set Test Variable    ${future_fhir_appointment_datetime}
