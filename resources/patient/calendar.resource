*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${calendar_div}                             //*[@id='upcoming-event-calandar-container']/div
${apppointment_list_in_date}                //nh-upcoming-events-list//li
${appointment_details_modal}                //*[@class='diary-event-show-appointment']
${appointment_date_in_modal}                //dt[text()="Date"]/following-sibling::dd[1]
${event_date_in_modal}                      //*[@class='diary-event-date flex']
${appointment_location_in_modal}            xpath=//dt[text()="Location"]/following-sibling::dd[1]
${appointment_header_title}                 xpath=//*[@class='header']/h1
${event_header_title}                       xpath=//*[@class='content']/h1
${appointment_details_close_modal}          //*[@id='diary-event-show-appointment-modal-button-close']/button
${event_details_close_modal}                //*[@id='diary-event-show-modal-button-close']/button
${appointment_list_close_modal}             //*[@data-testid='close-button']/button    # list of apptments after clicking calendar date
${calendar_next_month_button}               xpath=(//*[@data-testid='calendar-next-month-button']/button)[2]
${calendar_previous_month_button}           (//*[@data-testid='calendar-previous-month-button']/button)[3]
${switch_to_calendar_list_view_button}      //*[@id='switch-to-calendar-list-button']//div
${upcoming_appointment_list_view}           //*[@id='upcoming-event-list container']
${arrow_up_button}                          //*[@icon='icon-arrow-up']/button
${end_event_note_element}                   //*[starts-with(@class, 'end-of-events-note')]


*** Keywords ***
Patient Can See And Press The Calendar Button
    Try To Click Element    ${view_calender_button}
    IF    'native' in '${ENVIRONMENT}'
        Patient Is In The Right Page    Calendar
    ELSE
        Wait Until Element Contains    ${diary_page_title}    Calendar
    END

Patient Can See Calendar Two Months At A Time
    Wait Until Element Is Visible    (${calendar_div})[1]
    ${count}    Generic: Get Element Count    ${calendar_div}
    Should Be Equal    ${count}    2

Appointment Date Is Circled
    [Arguments]    ${full_date}    ${behavior}=true
    ${full_month_year}    Convert Date    ${full_date}    result_format=%B %Y
    Set Test Variable    ${full_month_year}
    ${date}    Convert Date    ${full_date}    result_format=%-d
    IF    '${behavior}'=='true'
        Verify Css Property Color    # checks if circled
        ...    //h2[text()="${full_month_year}"]/../..//td[@class="day active red-dot"]//div[text()="${date}"]
        ...    rgba(180, 228, 243, 1)    background-color
    ELSE
        Page Should Not Contain Element
        ...    //h2[text()="${full_month_year}"]/../..//td[@class="day active red-dot"]//div[text()="${date}"]
    END

Appointment Date Is Unopened
    [Arguments]    ${full_date}    ${behavior}=true
    Sleep    1s    # needs to wait for red dot to disappear
    ${full_month_year}    Convert Date    ${full_date}    result_format=%B %Y
    ${date}    Convert Date    ${full_date}    result_format=%-d
    ${date_element}    Set Variable
    ...    //h2[text()="${full_month_year}"]/../..//td[@class="day active red-dot"]//div[text()="${date}"]/..
    IF    '${behavior}'=='true'
        ${attr}    Get Element Attribute    ${date_element}    class
        Should Contain    ${attr}    day active red-dot
    ELSE
        Page Should Not Contain Element    ${date_element}
    END

Number Of Appointments Is Correct
    [Documentation]    Number of appointments when a date is clicked
    [Arguments]    ${expected_count}
    Wait Until Element Is Visible    xpath=(${apppointment_list_in_date})[last()]
    ${actual_count}    Generic: Get Element Count    ${apppointment_list_in_date}
    ${actual_count}    Convert To String    ${actual_count}
    Should Be Equal    ${actual_count}    ${expected_count}

Select Date From Calendar
    [Documentation]    with_red_dot is True when appointment is opened or it is a personal event
    [Arguments]    ${full_date}    ${with_red_dot}=${TRUE}
    ${full_month_year}    Convert Date    ${full_date}    result_format=%B %Y
    ${date}    Convert Date    ${full_date}    result_format=%-d
    IF    ${with_red_dot}==${TRUE}
        Try To Click Element
        ...    //h2[text()="${full_month_year}"]/../..//td[@class="day active red-dot"]//div[text()="${date}"]/..
    ELSE
        Try To Click Element
        ...    //h2[text()="${full_month_year}"]/../..//td[@class="day active"]//div[text()="${date}"]/..
    END

Appointment Title In List Is Correct
    [Documentation]    Checks title after calendar date is clicked and patient is in the list of appointments
    [Arguments]    ${index}    ${expected_title}
    Wait Until Element Is Visible
    ...    xpath=//nh-upcoming-events-list//li[${index}]//p[starts-with(@class, 'event-title')]
    ${text}    Get Text    xpath=//nh-upcoming-events-list//li[${index}]//p[starts-with(@class, 'event-title')]
    Should Be Equal    ${expected_title}    ${text}

Appointment In List Has Red Dot And Bolded
    [Arguments]    ${appointment_title}    ${behavior}=true
    # checks if red
    ${red_dot_element}    Set Variable    //p[text()="${appointment_title}"]/../../div[@class='icon']/div
    IF    '${behavior}'=='true'
        ${attr}    Get Element Attribute    xpath=${red_dot_element}    class
        Should Contain    ${attr}    unread-sign
        Verify Css Property Color    ${red_dot_element}    rgba(230, 61, 61, 1)    background-color
    ELSE
        Wait Until Page Does Not Contain Element    ${red_dot_element}
    END
    # checks if bolded
    ${title_element}    Set Variable    //p[text()="${appointment_title}"]
    ${attr}    Get Element Attribute    xpath=${title_element}    class
    IF    '${behavior}'=='true'
        Should Contain    ${attr}    font-family-bold
    ELSE
        Should Not Contain    ${attr}    font-family-bold
    END

Select Appointment From List
    [Documentation]    Clicks appointment from list of appointments after date is clicked from the calendar
    [Arguments]    ${appointment_title}
    Try To Click Element    xpath=//p[text()="${appointment_title}"]

Verify Appointment Details From Modal
    [Documentation]    Verifies details in the modal after appointment is clicked from the list
    [Arguments]    ${header_title}    ${date}    ${location}
    Wait Until Element Is Visible    ${appointment_header_title}
    Element Text Should Be    ${appointment_header_title}    ${header_title}
    ${date}    Convert Date    ${date}    result_format=%A, %d.%m.%Y, %H:%M (${future_timezone_EEST})
    Generic: Element Should Contain    ${appointment_date_in_modal}    ${date}
    Element Text Should Be    ${appointment_location_in_modal}    ${location}

Verify Event Details In Modal
    [Documentation]    Verifies details in the modal after appointment is clicked from the list
    [Arguments]    ${header_title}    ${date}    ${location}
    Wait Until Element Is Visible    ${event_header_title}
    Element Text Should Be    ${event_header_title}    ${header_title}
    ${date}    Convert Date    ${date}    result_format=%d.%m.%Y
    Generic: Element Should Contain    ${event_date_in_modal}    ${date}
    Element Text Should Be    ${appointment_location_in_modal}    ${location}

Go To Next Or Previous Month
    [Arguments]    ${action}
    IF    '${action}'=='next'
        Wait Until Element Is Visible    ${calendar_next_month_button}
        Try To Click Element    ${calendar_next_month_button}
    ELSE
        Wait Until Element Is Visible    ${calendar_previous_month_button}
        Try To Click Element    ${calendar_previous_month_button}
    END

Go To Appointment Date
    [Documentation]    Used in native app only
    [Arguments]    ${full_date}
    ${full_month_year}    Convert Date    ${full_date}    result_format=%B %Y
    ${date}    Convert Date    ${full_date}    result_format=%-d
    Scroll Element Into View
    ...    //h2[text()="${full_month_year}"]/../..//td[contains(@class, 'day active')]//div[text()="${date}"]/..
