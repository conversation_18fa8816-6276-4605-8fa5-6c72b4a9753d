*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_api_fhir.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource    ${EXECDIR}${/}resources${/}clinic_settings${/}integration_settings.resource
Resource    ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource


*** Keywords ***
Clinic Admin Disables Lab Results In Clinic Settings
    Login As Nurse    clinic=${patient_education_clinic}[name]    user_type=${USER_TYPE}[noona_admin]
    Open "Clinic settings" And Select Integration Settings Tab
    Disable Laboratory Results Settings
    Save Integration Settings

Verify Lab Report Is Visible In Patient UI
    [Arguments]    ${patient_email}    ${is_displayed}    ${logout}=false
    IF    '${logout}'=='true'
        IF    'native' in '${ENVIRONMENT}'
            Logout From Patient App
            Close Application
            Setup App Environment
        ELSE
            Close Browser
        END
        Login As Patient    ${patient_email}
    END
    Go To Library
    IF    '${is_displayed}'=='yes'
        Lab Report Header And Title Are Visible
    ELSE
        Laboratory Reports Intro Is Not Displayed
    END

Remove Laboratory Report Via API
    [Documentation]    Keyword is deprecated, end point is not in use anymore. Keeping it still due to hystorical reasons.
    [Arguments]    ${report_external_id}    ${patient_email}    ${is_displayed}
    Delete Lab Report Via API    ${report_external_id}
    Sleep    5s
    Verify Lab Report Is Visible In Patient UI    ${patient_email}    no    logout=true

Remove Laboratory Report Using FIHR-R4 Endpoint
    [Arguments]    ${json_file}    ${patient_id}    ${lab_result_internal_id}    ${effectiveDateTime}
    Delete Lab Report With FIHR-R4 Endpoint
    ...    ${json_file}
    ...    ${patient_id}
    ...    ${lab_result_internal_id}
    ...    ${effectiveDateTime}
    Sleep    5s
    Verify Lab Report Is Visible In Patient UI    ${patient_email}    no    logout=true

Create New Patient To Receive Lab Reports
    [Arguments]    ${usecase_id}    ${mailosaur_key}=no
    IF    'native' not in '${ENVIRONMENT}'
        Clinic Admin Enables Lab Results In Clinic Settings    clinic=${patient_education_clinic}[name]
        Add An Activated Patient Under Patient Education Clinic    ${usecase_id}    mailosaur=${mailosaur_key}    subscriber_id=${PATIENT_EDUCATION_SUB_ID_CARETEAM_1}
        Set Test Variable    ${integration_user_token}    ${PATIENT_EDUCATION_EHR_TOKEN}
    ELSE
        Add An Activated Patient Under Native App Clinic    ${usecase_id}    mailosaur=${mailosaur_key}
        Set Test Variable    ${integration_user_token}    ${NATIVE_APP_EHR_USER_TOKEN}
    END
