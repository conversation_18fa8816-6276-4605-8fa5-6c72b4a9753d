*** Settings ***
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Library     ${EXECDIR}${/}resources${/}libraries${/}helper.py


*** Variables ***
${url_diary_timeline}                               ${PATIENT_URL}${PATIENT_PATH}#/diary-timeline
${url_library}                                      ${PATIENT_URL}${PATIENT_PATH}#/library
${url_clinic}                                       ${PATIENT_URL}${PATIENT_PATH}#/clinic
${url_profile}                                      ${PATIENT_URL}${PATIENT_PATH}#/profile
${url_symptom_header}                               ${PATIENT_URL}${PATIENT_PATH}#/report-symptom
${url_open_question}                                ${PATIENT_URL}${PATIENT_PATH}#/clinic/open-question
${login_error}                                      //p[contains(text(),'Incorrect username or password. If you have forgot')]
${upcoming-events-header-element}                   //*[@id="upcoming-events-header"]
${library_header}                                   //h1[contains(text(),'All your information, in one place')]
${clinic_header}                                    clinic-contact-options-header
${clinic_messages_header}                           clinic-inbox-header
${patient_profie_header}                            //h1[contains(text(),'My profile')]
${add_symptom_header}                               //*[@id="wizard"]
${non_clinic_topic_first_topic}                     (//button[contains(@class, 'question-type-item-label')])[1]
${contact_clinic_close_button}                      //*[@id="open-question"]/div[1]/button
${add_symptom_close_button}                         //*[@id="wizard"]/div[1]/button
${activate_your_noona_account_text_2}               Activate your Noona account
${activate_button}                                  //button[contains(@type, "button")]
${activate_next_button}                             //button[@type='submit']
${accept_terms_next_button}                         //div[contains(text(),'Next')]/..
${new_password_textbox}                             newPassword
${about_modal_link}                                 //div[contains(text(),'About')]
${about_modal}                                      //*[@id="about-modal-modal"]/div[2]/div
${close_modal}                                      close-modal
${activate_your_account_button}                     //div[contains(text(),'Activate your account')]
${approve_elements_checkbox}                        //label[contains(@for, "approved-checkbox")]/..
${pendo_welcome_to_questionnaire_close_button}      //button[starts-with(@id, 'pendo-close-guide-')]
${create_password_header}                           //*[text()='Create a password']
${enter_email_header}                               //*[text()='Enter your email address']
${enter_email_description}                          //p[text()=' Your email address is your Noona username. You will also receive email notifications from your care team. ']
${enter_email_input}                                //input[@id='email']
${enter_email_accepted}                             //label[@for='accepted']
${enter_email_accepted_text}                        I have checked that my email is correct
${enter_email_next_button}                          //*[@id='remember-me-next-button']
${activation_complete_next_button}                  //*[@id='activation-complete-next-button']
${account_activation_success_message_texts}         Congratulations, your account is now ready!
${enter_email_page_url}                             ${PATIENT_LOGIN_BY_LINK_URL}email
${activation_2fa_code_field}                        //div[@class='password-input']/div
${create_password_logo}                             //div[contains(@class, 'logo--custom')]//*[@*='{}']
${create_password_next_button}                      remember-me-next-button    #ds-button id needed for custom branding
${activate_your_noona_account_next_button}          //*[@id='activate']//button


*** Keywords ***
User Is Redirected Upon Access Of Page X Before Login
    [Arguments]    ${url}
    Open Page    ${url}
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Set Suite Variable    ${url}
    User Redirects To Login Page
    Login As Patient From Direct Link    ${CLINIC_PATIENT}[email]
    Verify Landing Page After Login
    Close Form Modal
    Patient Selects Log Out From Menu
    Sleep    3s
    Go To    ${url}
    User Redirects To Login Page

User Redirects To Login Page
    Wait Until Element Is Visible    ${landing_page_login_button}

Verify Landing Page After Login
    Sleep    3s
    IF    '${url}'=='${url_diary_timeline}'
        Wait Until Element Is Visible    ${upcoming-events-header-element}
    ELSE IF    '${url}'=='${url_library}'
        Wait Until Element Is Visible    ${library_header}
    ELSE IF    '${url}'=='${url_clinic}'
        Wait Until Element Is Visible    ${clinic_header}
    ELSE IF    '${url}'=='${url_profile}'
        Wait Until Element Is Visible    ${patient_profie_header}
    ELSE IF    '${url}'=='${url_symptom_header}'
        Wait Until Element Is Visible    ${add_symptom_header}
    ELSE IF    '${url}'=='${url_open_question}'
        Wait Until Element Is Visible    ${non_clinic_topic_first_topic}
    END

Close Form Modal
    IF    '${url}'=='${url_symptom_header}'
        Try To Click Element    ${add_symptom_close_button}
    ELSE IF    '${url}'=='${url_open_question}'
        Try To Click Element    ${contact_clinic_close_button}
    END

Open Page
    [Arguments]    ${url}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser    ${url}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE
        Open Browser    ${url}    ${BROWSER}
    END

Reset Password From Link
    [Documentation]    Patient receives reset password link from email. Once the link is clicked, the URL will change with corresponding endpoint
    [Arguments]    ${link}    ${password}=${DEFAULT_PASSWORD}
    Open URL In Chrome    ${link}
    Accept All Cookies If Visible
    Wait Until Location Contains    ${PASSWORD_CHANGE_ENDPOINT}    timeout=5s
    Create New Password To Activate Account    password=${password}
    Wait Until Location Contains    ${PATIENT_LOGIN_URL}

Create New Password To Activate Account
    [Documentation]    Provide ${check_next_button_color} the rgba color if button needs to be checked
    [Arguments]    ${password}=${DEFAULT_PASSWORD}    ${check_next_button_color}=no
    Wait Until Page Contains Element    ${new_password_textbox}    5s
    Wait Until Element Is Enabled    ${new_password_textbox}    2s
    Input Text    ${new_password_textbox}    ${password}
    IF    '${check_next_button_color}'!='no'
        Wait Until Element Is Enabled    ${activate_next_button}
        Sleep    1
        Verify Css Property Color    ${create_password_next_button}    ${check_next_button_color}    background-color
    END
    Click Element    ${activate_next_button}

Verify User Can Login
    [Arguments]    ${email}
    Login As Patient    ${email}    remember_login=Yes
    Wait Until Page Contains    Welcome to Noona

Click Activate Your Account
    Wait Until Element Is Visible    (${activate_your_account_button})\[1]/../..
    Try To Click Element    (${activate_your_account_button})\[1]/../..

Input 2fa Code During Activation
    [Arguments]    ${sms_code}
    ${get_sms_code}    Get Substring    ${sms_code}    0    6
    Wait Until Element Is Visible    (${activation_2fa_code_field})[1]
    Try To Click Element    (${activation_2fa_code_field})[1]
    Press keys    (${activation_2fa_code_field})[1]    ${get_sms_code}

Activate Noona Account As Patient
    [Arguments]    ${link}    ${patient_email}    ${terms_enabled}=true    ${with_2fa}=no
    Go To    ${link}
    Accept All Cookies If Visible
    Wait Until Element Is Visible    (${activate_your_account_button})\[1]/../..
    ${count}    Get Element Count    ${activate_your_account_button}
    ${expected_count}    Convert To Integer    2
    Should Be Equal    ${count}    ${expected_count}
    Click Activate Your Account
    IF    '${with_2fa}'=='yes'
        ${sms_code}    User Received SMS
        Wait Until Element Is Visible    ${password_input_container_oidc_false}
        Input 2fa Code During Activation    ${sms_code}
    END
    Wait Until Page Contains    ${activate_your_noona_account_text_2}
    Wait Until Element Is Visible    ${activate_next_button}
    Try To Click Element    ${activate_next_button}
    Sleep    2
    ${current_url}    Get Location
    IF    '${current_url}' == '${enter_email_page_url}'
        Enter Own Email Address    ${patient_email}
        Create New Password To Activate Account
    ELSE
        Create New Password To Activate Account
    END
    Wait Until Page Contains    ${account_activation_success_message_texts}
    Try To Click Element    ${activation_complete_next_button}
    IF    '${with_2fa}'=='yes'
        Delete All SMS From Mailosaur
        Login As Patient From Direct Link    ${patient_email}
        Input Patient Verification Code
    ELSE
        Login As Patient From Direct Link    ${patient_email}
    END
    IF    '${terms_enabled}'=='true'
        Try To Click Element    ${accept_terms_next_button}
        Sleep    1
        Try To Click Element    ${approve_elements_checkbox}
        Sleep    1
        Try To Click Element    ${accept_terms_next_button}
        Sleep    1
        Try To Click Element    ${approve_elements_checkbox}    # consent to process data
        Sleep    1
        Try To Click Element    ${accept_terms_next_button}
        Sleep    1
        Try To Click Element    ${approve_elements_checkbox}
        Try To Click Element    ${accept_terms_next_button}
    END

Activate Noona Account Without Logging In
    [Arguments]    ${link}
    Open URL In Chrome    ${link}
    ${activate_page_is_visible}    Run Keyword And Return Status    Wait Until Location Contains    ${PATIENT_LOGIN_BY_LINK_URL}
    IF    '${activate_page_is_visible}' == 'False'
        Close Browser
        Open URL In Chrome    ${link}
    END
    Accept All Cookies If Visible
    Wait Until Page Contains Element    (${activate_your_account_button})\[1]
    Wait Until Element Is Visible    (${activate_your_account_button})\[1]
    ${count}    Get Element Count    ${activate_your_account_button}
    ${expected_count}    Convert To Integer    2
    Should Be Equal    ${count}    ${expected_count}
    Try To Click Element    (${activate_your_account_button})\[1]
    Wait Until Page Contains    ${activate_your_noona_account_text_2}
    Try To Click Element    ${activate_next_button}
    Sleep    2
    Create New Password To Activate Account
    Wait Until Page Contains    ${account_activation_success_message_texts}

Activate Noona Account With AEQ
    [Arguments]    ${link}    ${new_email}    ${pendo}=no
    Activate Noona Account As Patient    ${link}    ${new_email}
    Wait Until Location Contains    patient/#/symptom-inquiry
    IF    '${pendo}'=='yes'
        Wait Until Page Contains    Welcome to your questionnaire!
    END
    IF    '${pendo}'=='yes'    Close Pendo Guide

Activate Noona Account With QoL
    [Arguments]    ${link}    ${new_email}
    Activate Noona Account As Patient    ${link}    ${new_email}
    Wait Until Location Contains    patient/#/questionnaire    timeout=10s
    Wait Until Location Contains    questionnaireType=qol-15-d    timeout=5s

Close Pendo Guide
    ${pendo_visible}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${pendo_welcome_to_questionnaire_close_button}
    IF    ${pendo_visible}
        Try To Click Element    ${pendo_welcome_to_questionnaire_close_button}
    ELSE
        Wait Until Element Is Visible    main-navigation
    END

Approve Terms And Consent Only If Visible
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${approve_elements_checkbox}
    ...    timeout=15s
    IF    ${status}    Approve Terms And Click Next
    ${status}    Run Keyword And Return Status
    ...    Wait Until Page Contains    Diary
    ...    timeout=15s
    IF    ${status}==${FALSE}    Approve All Consents If Visible
    Wait Until Page Contains    Diary

Approve Terms And Click Next
    [Arguments]    ${add_tick_checkbox}=yes
    Wait Until Page Contains Element    xpath=${approve_elements_checkbox}
    Tick Consent Checkbox
    IF    '${add_tick_checkbox}'=='yes'
        Click Next On Terms And Conditions
    ELSE
        Try To Click Element    xpath=${approve_terms_consent_next}
    END
    Sleep    2s    #needs to make sure button is clicked completely

Approve Consents And Click Next
    Sleep    1    # needs sleep because it clicks the same checkbox instead of the next
    Tick Consent Checkbox
    Click Element    xpath=${approve_terms_consent_next}
    Sleep    1    # needs sleep because it clicks the same checkbox instead of the next
    Tick Consent Checkbox
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}'=='android'
        # Temporary fix until NOONA-24402 is done for 9.8
            Tick Consent Checkbox
        END
    END
    Click Element    xpath=${approve_terms_consent_next}

Click Next On Terms And Conditions
    Sleep    2s
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}'=='ios'
            Try To Click Element    xpath=${approve_terms_consent_next}
        ELSE    #updated step to pass due to NOONA-22641
            Try To Click Element    xpath=${approve_terms_consent_next}
            Try To Click Element    xpath=${approve_elements_checkbox}
        END
    ELSE
        Try To Click Element    xpath=${approve_terms_consent_next}
    END

Tick Consent Checkbox
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}'=='ios'
            Execute Script    document.getElementsByTagName('label')[0].click();
        ELSE
            Try To Click Element    xpath=${approve_elements_checkbox}
        END
    ELSE
        Try To Click Element    xpath=${approve_elements_checkbox}
    END

Approve All Consents If Visible
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${approve_elements_checkbox}
    ...    timeout=5s
    IF    ${status}
        Sleep    1    # needs sleep because it clicks the same checkbox instead of the next
        Try To Click Element    ${approve_elements_checkbox}
        Try To Click Element    ${next_button}
    END
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Not Visible
    ...    ${approve_elements_checkbox}
    ...    timeout=5s
    IF    ${status}==${FALSE}
        Sleep    1    # needs sleep because it clicks the same checkbox instead of the next
        Try To Click Element    ${approve_elements_checkbox}
        Try To Click Element    ${next_button}
    END

Enter Own Email Address
    [Arguments]    ${email}
    Page Should Contain Element    ${enter_email_header}
    Page Should Contain Element    ${enter_email_description}
    Input Text    ${enter_email_input}    ${email}
    ${description}    Get Text    ${enter_email_accepted}
    Should Be Equal    ${description}    ${enter_email_accepted_text}
    Try To Click Element    ${enter_email_accepted}
    Wait Until Element Is Enabled    ${enter_email_next_button}
    Try To Click Element    ${enter_email_next_button}

Page Has Correct Logo
    [Arguments]    ${svg}
    ${logo_element}    Format String    ${create_password_logo}    ${svg}
    Wait Until Element Is Visible    ${logo_element}