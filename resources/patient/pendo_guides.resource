*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource


*** Variables ***
${pendo_guide_diary_modal}                      //div[contains(@id, "pendo-text-")]
${pendo_guide_diary_modal_texts}                <PERSON><PERSON> connects you to your care team
${lets_go_button}                               //button[contains(text(),"Let's go")]
${pendo_guide_clinic_modal}                     //div[@id='pendo-guide-container']
${pendo_guide_clinic_texts}                     Go to Clinic if you want to ask your care team about symptoms or something else.
${pendo_guide_message_texts}
...                                             You can message your clinic 24/7. They will get back to you as soon as possible. You will receive a notification when they do.
${pendo_message_modal_ok_button}                //button[contains(text(),"OK")]
${pendo_guide_careteam_texts}                   Ask your care team about your symptoms
${biometrics_pendo_learn_more}                  //button[text()="Learn more"]
${biometrics_pendo_ok_got_it}                   //button[text()="Ok, go it!"]
${welcome_to_questionnaire_modal_title}         Welcome to your questionnaire!
${welcome_to_questionnaire_modal_texts_1_1}     Before you get started, we would like to walk you through what to expect.
${welcome_to_questionnaire_modal_texts_1_2}
...                                             Responding to a questionnaire is a simple way to communicate your symptoms with your care team. However, never use a questionnaire to report symptoms in an emergency situation.
${welcome_to_questionnaire_modal_texts_2_1}
...                                             Over the course of your treatment, your care team at Pendo Test Clinic will send you questionnaires.
...                                             By responding to each questionnaire, you can share how you are feeling after treatments so your care team can follow your progress for more personalized care.
${welcome_to_questionnaire_modal_texts_2_2}     If your care team determines that a symptom needs additional attention, they will follow-up with you.
${your_diary_in_your_questionnaire_title}       Your Diary symptoms will show in your questionnaire
${your_diary_in_your_questionnaire_text_1}      If you have recently reported any symptoms in your Diary, Noona will automatically include those symptoms in your questionnaire.
${your_diary_in_your_questionnaire_text_2}      Now it's time to get started reporting your symptoms in your first questionnaire!
${pendo_guide_next_button}                      //button[contains(text(),"Next")]
${pendo_guide_get_started_button}               //button[contains(text(),"Get started")]


*** Keywords ***
Check Pendo Guides From Diary
    Wait Until Page Contains Element    ${pendo_guide_diary_modal}    timeout=20s
    Wait Until Element Contains    ${pendo_guide_diary_modal}    ${pendo_guide_diary_modal_texts}
    Try To Click Element    ${lets_go_button}
    Wait Until Page Contains Element    ${pendo_guide_clinic_modal}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${pendo_guide_clinic_texts}

Check Pendo Guides From Clinic
    Wait Until Page Contains Element    ${pendo_guide_clinic_modal}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${pendo_guide_message_texts}
    Try To Click Element    ${pendo_message_modal_ok_button}
    Wait Until Page Contains Element    ${pendo_guide_clinic_modal}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${pendo_guide_careteam_texts}
    Try To Click Element    ${pendo_message_modal_ok_button}

Check Pendo Guides From Symptom Questionnaire Form
    Wait Until Page Contains    ${welcome_to_questionnaire_modal_title}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${welcome_to_questionnaire_modal_texts_1_1}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${welcome_to_questionnaire_modal_texts_1_2}
    Try To Click Element    ${pendo_guide_next_button}
    Wait Until Page Contains     ${welcome_to_questionnaire_modal_title}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${welcome_to_questionnaire_modal_texts_2_1}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${welcome_to_questionnaire_modal_texts_2_2}
    Try To Click Element    ${pendo_guide_next_button}
    Wait Until Page Contains     ${your_diary_in_your_questionnaire_title}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${your_diary_in_your_questionnaire_text_1}
    Wait Until Element Contains    ${pendo_guide_clinic_modal}    ${your_diary_in_your_questionnaire_text_2}
    Try To Click Element    ${pendo_guide_get_started_button}
