*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${header_account_preferences}               patient-account-preferences
${section_connected_devices}                connected-devices
${remove_connection_button}                 remove-device-0
${account_preferences_email_field}          //*[@id='email']
${patient_phone_number_field}               //*[@id='phoneNumber']
${notif_channel_radio_buttons}              notification-method-email
${notif_channel_email_sms}                  notification-method-email-and-sms
${notif_channel_email}                      notification-method-email
${notif_channel_sms}                        notification-method-sms
${login_method_2_factor_password}           two-factor-authentication-password
${login_method_2_factor_password_mobile}    two-factor-authentication-password-and-mobile
${input_password_modal}                     sms-password-modal
${input_password_next_button}               //div[@class='password-container']/following-sibling::div/ds-button[2]/button
${email_field_android}                      //*[@resource-id="email"]
${email_field_ios}                          //XCUIElementTypeTextField[1]
${phone_number_field_android}               //*[@resource-id="phoneNumber"]
${phone_number_field_ios}                   (//XCUIElementTypeTextField)[2]
${account_preference_password_field}        //*[@id='password']
${preferences_saved_banner}                 Preferences saved
${2fa_input_account_preference}             //div[@class='password-input']/div
${change_password_link}                     //*[@id='change-password-link']//button
${old_password_field}                       //*[@id='oldPassword']
${new_password_field}                       //*[@id='newPassword']
${new_pwd_again_field}                      //*[@id='newPasswordAgain']
${pwd_change_modal_cancel}                  //*[@id='password-change-modal-cancel']//button
${pwd_change_modal_save}                    //*[@id='password-change-modal-save']//button
${patient_sms_code_input_android}           //*[@resource-id="sms-password"]
${patient_sms_code_input_ios}               //XCUIElementTypeTextField
${last_login_element}                       //div[contains(@class,'tooltip-group')]//p[contains(@class,'last-login')]

*** Keywords ***
View List Of Connected Devices
    Wait Until Element Is Visible    ${header_account_preferences}
    Wait Until Element Is Visible    ${section_connected_devices}
    Scroll Element Into View    ${section_connected_devices}
    Wait Until Element Is Visible    ${remove_connection_button}

Click Remove Connection
    Wait Until Element Is Enabled    ${remove_connection_button}
    Try To Click Element    ${remove_connection_button}

Device Is No Longer Connected To Account
    Wait Until Page Does Not Contain Element    ${remove_connection_button}

Update Email Address
    [Arguments]    ${email}
    Set Test Variable    ${NEW_EMAIL}    ${email}
    Wait Until Element Is Visible    ${account_preferences_email_field}
    Sleep    1s
    ${email_value}    Get Email Value
    Set Test Variable    ${PREVIOUS_EMAIL}    ${email_value}
    Wait Until Element Is Visible    ${account_preferences_email_field}
    Generic: Clear Text    ${account_preferences_email_field}
    Generic: Clear Text    ${account_preferences_email_field}
    ${email_value}    Get Email Value
    Should Be Empty    ${email_value}
    Try To Input Text    ${account_preferences_email_field}    ${email}
    Set Test Variable    ${NEW_EMAIL}    ${email}

Get Email Value
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        ${email_field}    Set Variable If
        ...    '${PLATFORM_NAME}'=='android'
        ...    ${email_field_android}
        ...    ${email_field_ios}
        ${text}    Get Text    ${email_field}
        IF    '${PLATFORM_NAME}'=='android'
            IF    'test' in '${ENVIRONMENT}'
                Switch To Context    ${webview_noona_app_test}
            ELSE
                Switch To Context    ${webview_noona_app_staging}
            END
        ELSE
            Switch To Context    ${contexts}[1]
        END
    ELSE
        ${text}    Get Value    ${account_preferences_email_field}
    END
    RETURN    ${text}

Email address is automatically cast to lower case
    Wait Until Keyword Succeeds    20s    1s    Email Value Should Be Lowercase

Email Value Should Be Lowercase
    ${email_value}    Get Email Value
    Should Be Equal    ${NEW_EMAIL}    ${email_value}    ignore_case=True
    Should Be Lowercase    ${email_value}

Update Phone Number
    [Arguments]    ${phone_number}
    ${phone_number_str}    Convert To String    ${phone_number}
    Set Test Variable    ${NEW_NUMBER}    ${phone_number_str}
    ${number_value}    Get Phone Number Value
    Set Test Variable    ${PREVIOUS_NUMBER}    ${number_value}
    Wait Until Element Is Visible    ${patient_phone_number_field}
    Generic: Clear Text    ${patient_phone_number_field}
    ${number_value}    Get Phone Number Value
    Should Be Empty    ${number_value}
    Try To Input Text    ${patient_phone_number_field}    ${phone_number_str}
    Sleep    1
    ${number_value}    Get Phone Number Value
    Should Be Equal    ${phone_number_str}    ${number_value}

Get Phone Number Value
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        ${phone_number_field}    Set Variable If
        ...    '${PLATFORM_NAME}'=='android'
        ...    ${phone_number_field_android}
        ...    ${phone_number_field_ios}
        Wait Until Element Is Visible    xpath=${phone_number_field}
        ${text}    Get Text    xpath=${phone_number_field}
        IF    '${PLATFORM_NAME}'=='android'
            IF    'test' in '${ENVIRONMENT}'
                Switch To Context    ${webview_noona_app_test}
            ELSE
                Switch To Context    ${webview_noona_app_staging}
            END
        ELSE
            Switch To Context    ${contexts}[1]
        END
    ELSE
        ${text}    Get Value    ${patient_phone_number_field}
    END
    RETURN    ${text}

Select Notification Channel
    [Arguments]    ${method}
    Set Test Variable    ${NEW_NOTIF}    ${method}
    ${method_choice}    Set Variable If
    ...    '${method}'=='Email and SMS'    ${notif_channel_email_sms}
    ...    '${method}'=='Email'    ${notif_channel_email}
    ...    '${method}'=='SMS'    ${notif_channel_sms}
    ${element}    Format String    ${language_and_notification_label}    ${method_choice}
    Scroll Element Into View    ${element}
    Try To Click Element    ${element}

Select Login Method
    [Arguments]    ${method}
    Wait Until Element Is Visible    //label[@for='${login_method_2_factor_password}']/..
    ${password_checked}    Generic: Execute Javascript    return document.getElementById('${login_method_2_factor_password}').checked;
    ${2fa_checked}    Generic: Execute Javascript    return document.getElementById('${login_method_2_factor_password_mobile}').checked;
    IF    '${method}'=='password only' and '${password_checked}'=='False'
        Try To Click Element    //label[@for='${login_method_2_factor_password}']/..
        Click Account Preferences Save Button
    ELSE IF    '${method}'=='password and verification code' and '${2fa_checked}'=='False'
        Wait Until Element Is Visible    xpath=//label[@for='${login_method_2_factor_password_mobile}']
        Scroll Element Into View    xpath=//label[@for='${login_method_2_factor_password_mobile}']
        Sleep    1
        Click Element    xpath=//label[@for='${login_method_2_factor_password_mobile}']
        Click Account Preferences Save Button
    END


Enable Password Only Login Method
    [Arguments]    ${with_password_2fa}=no
    Go To Account Preferences
    Select Login Method    password only
    IF    '${with_password_2fa}'=='yes'    Input Password And 2fa Code To Update Account Preferences
    Logout As Patient
    Close Browser

Enable Password And Verification Code Login Method
    Go To Account Preferences
    Select Login Method    password and verification code
    Input Password And 2fa Code To Update Account Preferences
    Logout As Patient
    Close Browser

Input Password And 2fa Code To Update Account Preferences
    Delete All SMS From Mailosaur
    Input Password To Update Account Preferences
    ${sms_code}    User Received SMS
    Wait Until Element Is Visible    ${password_input_container_oidc_false}
    Input 2fa Code During Activation    ${sms_code}
    Wait Until Page Contains    ${preferences_saved_banner}
    Try To Click Banner Message

### Saving Account Preferences ###brow

Click Account Preferences Save Button
    Wait Until Element Is Visible    ${save_profile_button}
    Try To Click Element    ${save_profile_button}

Input Password To Update Account Preferences
    Wait Until Element Is Visible    ${account_preference_password_field}
    IF    'native' not in '${ENVIRONMENT}'
        Set Focus To Element    ${input_password_modal}
    END
    Try To Input Text    ${account_preference_password_field}    ${DEFAULT_PASSWORD}
    Try To Click Element    ${input_password_next_button}

Updated Email Is Correct
    ${email_value}    Get Email Value
    ${new_email}    Convert To Lower Case    ${NEW_EMAIL}
    Should Be Equal    ${email_value}    ${NEW_EMAIL}

Updated Contact Number Is Correct
    ${number_value}    Get Phone Number Value
    Should Be Equal    ${number_value}    ${NEW_NUMBER}

Get Original Field Values Account Preferences
    ${original_email}    Get Value    ${account_preferences_email_field}
    Set Global Variable    ${original_email}
    ${original_phone_number}    Get Value    ${patient_phone_number_field}
    Set Global Variable    ${original_phone_number}

Get Current Values Account Preferences
    ${current_email}    Get Value    ${account_preferences_email_field}
    Set Global Variable    ${current_email}
    ${current_phone_number}    Get Value    ${patient_phone_number_field}
    Set Global Variable    ${current_phone_number}

Save Preferences And Input Password If Needed
    [Arguments]    ${password}=yes
    Click Account Preferences Save Button
    IF    '${password}'=='yes'    Input Password To Update Account Preferences
    Wait Until Page Contains    ${preferences_saved_banner}
    Try To Click Banner Message
