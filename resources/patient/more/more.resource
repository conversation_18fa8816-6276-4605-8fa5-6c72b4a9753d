*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}single_patient_multi_clinic.resource


*** Variables ***
${menu_more_button}                 //*[@id="navigation-more-link"]/button
${account_preferences}              //*[@id="more-link-account.preferences"]
${clinic_preferences}               //*[@data-testid="more-link-clinic.preferences"]
${menu_about}                       //*[@id="more-link-about"]
${about_screen_modal}               //*[@id="about-modal-modal"]
${go_to_another_clinic_button}      //button[contains(text(),'Go to another clinic')]
${account_preferences_header}       //*[@id="patient-account-preferences"]//h1
${loading_spinner}                  //div[@id='noona-preloader-spinner-container']


*** Keywords ***
Go To Account Preferences
    Sleep    1    # needed because it clicks the more button before loading
    Try To Click More Button
    Wait Until Element Is Visible    ${account_preferences}
    Try To Click Element    ${account_preferences}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${account_preferences_header}

Go To Clinic Preferences
    Generic: Wait Until Element Is Not Visible    ${loading_spinner}     # needed because it clicks the more button before loading
    Try To Click More Button
    Wait Until Element Is Visible    ${clinic_preferences}
    Try To Click Element    ${clinic_preferences}
    Wait Until Keyword Succeeds    5s    0.3s    Scroll Element Into View    ${clinic_preferences_header}

Try To Click More Button
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        IF    '${PLATFORM_NAME}' == 'android'
            IF    'test' in '${ENVIRONMENT}'
                Switch To Context    WEBVIEW_com.noona.application.test
            ELSE
                Switch To Context    WEBVIEW_com.noona.application.staging
            END
        ELSE IF    '${PLATFORM_NAME}' == 'ios'
            Switch To Context    ${contexts}[1]
        END
        # needs additional wait because the page reloads after 1s
        Wait Until Element Is Visible
        ...    xpath=${more_button}
        ...    1s
        Try To Click Element    xpath=${more_button}
    ELSE
        Wait Until Page Contains Element    ${menu_more_button}    timeout=10s
        Wait Until Element Is Visible    ${menu_more_button}    1s
        Try To Click Element    ${menu_more_button}
    END

User Navigates To About Screen
    Try To Click More Button
    Wait Until Element Is Visible    ${menu_about}
    Try To Click Element    ${menu_about}
    Wait Until Element Is Visible    ${about_screen_modal}

Go To Another Clinic From More Menu
    [Arguments]    ${switch_clinic}    ${clinic_id}
    Try To Click More Button
    Wait Until Element Is Enabled    ${go_to_another_clinic_button}
    Try To Click Element    ${go_to_another_clinic_button}
    Wait Until Element Is Enabled    ${go_to_another_clinic_modal}
    Wait Until Element Is Visible    //*[contains(text(),'${switch_clinic}')]
    Try To Click Element    //*[@data-testid="${clinic_id}"]
    Approve Terms And Click Next
    Approve Consents And Click Next
    Try To Click More Button
    Wait Until Element Is Visible    //h2[contains(text(),'${switch_clinic}')]
