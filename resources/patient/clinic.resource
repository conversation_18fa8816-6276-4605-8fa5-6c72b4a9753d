*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}modules.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}library.resource
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource
Resource    ${EXECDIR}${/}resources${/}common_mobile.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${clinic-icon}                                      xpath=//*[@id='navigation-clinic-link']/button
${ask_about_other_issues_icon}                      //button[@aria-label='Ask about other issues']
${treatments-topic-button}                          //button[contains(text(),'Treatment plan')]
${question-title-input}                             name:questionTitle
${submit-question-button}                           //ds-button[@id='send-open-question']/button
${question-sent-confirmation-text}                  Your question was sent to the clinic
${question_sent_to_clinic_close}                    //ds-button[@id='open-question-go-to-home']/button
${topic}                                            set based on topic found on page
${edited-topic}                                     added date to topic string used as message title
${inbox-show-all-button}                            //ds-button[@icon='icon-caret-down']
${ask_about_symptom_button}                         //button[contains(@aria-label, "Ask about symptoms")]
${first_symptom_button}                             //button[contains(@class, "symptom-item")][1]
${other_symptom_button}                             other-symptom-symptom
${reply_to_message_textarea}                        //div[contains(@class,'reply-form-area')]/textarea
${clinic_messages}                                  //*[contains(@class, "clinic-inbox-message")]
${feedback_to_message_send_button}                  //*[@id="button-inbox-message-send-feedback"]/button
${reply_to_message_cancel_button}                   //*[@id="button-inbox-message-close"]/button
${reply_to_message_send_button}                     //*[@id="button-inbox-message-send-reply"]/button
${information_sufficient_radio_group}               information-sufficient-yes
${information_sufficient_textarea}                  //*[@class='form-group feedback-content ng-star-inserted']/textarea
${information_sufficient_yes}                       //input[@id='information-sufficient-yes']/..
${information_sufficient_no}                        //input[@id='information-sufficient-no']/..
${clinic_header_element}                            //div[starts-with(@class, 'clinic-contact-options')]
${clinic_contact_your_care_team_header}             //*[@id="clinic-contact-options-header"]
${clinic_messages_column}                           //div[@class='clinic-inbox flex flex--column']
${load_more_button}                                 //div[contains(text(),'Load more')]/..
${message_content}                                  //div[contains(@class, "message-text")]
${message_title}                                    //div[@class='modal-header']//h1
${message_datetime}                                 //*[@class='message-area']//div[starts-with(@class,'message-date')]
${message_modal_close_button}                       //div[@class='modal-buttons-container']/ds-button[1]/button
${inbox_message_mild_moderate_text}                 Patient sent a symptom entry.
${message_modal}                                    //div[contains(@class, 'noona-modal-dialog')]
${inbox_severe_symptom_instructions_text}=
...                                                 SEPARATOR=
...                                                 Hello,\n
...                                                 \nThe symptom you just described indicates that you might require immediate attention from a medical professional.\n
...                                                 \nIf you do not believe that you can manage until the following working day or if you feel otherwise weaker, \
...                                                 seek help at your local emergency clinic.
${severe_symptom_inbox_text}                        The symptom you just described indicates that you might require immediate attention from a medical professional.
${new_message_indicator}                            //div[starts-with(@class,'event-count')]
${unanswered_questionnaire_status_text}             //span[text()="- Unanswered"]
${latest_message_indicator}                         //*[contains(@class, "unread-marker")]
${patient_satisfaction_rating_for_instructions}     //input[contains(@class,'radio-rating')]
${questionnaire_answer_button}                      //div[@class='modal-buttons-container']/ds-button[2]/button
${new_message_button}                               //*[@id="button-inbox-message-show-reply"]/button/div
${question_content_field}                           //textarea[@id='question-content']
${auto_test_question_content}                       Autotest question content: Nordics åäöÅÄÖ.?!
${clinic_info_header}                               clinic-info-header
${clinic_info_weblink}                              //*[contains(@class,'clinic-web extrainfo-link')]
${new_clinic_messages}                              //div[@class='unread-marker-bottom ng-star-inserted']
${add_photo}                                        //photo-uploader/div
${browse_photo_link}                                //*[@id="browse-file-link"]
${choose_file}                                      //input[@type='file'][1]
${toast_message}                                    //div[@class='toast-message']
${inbox_attached_doc}                               //*[@class="open-attachment"]/div
${pendo_guide_close_button}                         //button[starts-with(@id, 'pendo-close-guide-')]
${document_attached_title}                          //message-attachment[@class='clinical-theme']//div[@class='title']
${message_modal_message_date}                       //div[contains(@class, 'message-date')]    # date seen when message is opened
${photo_uploaded}                                   //*[contains(@class, 'ng-star-inserted')]
${message_text}                                     //*[contains(@class,"message-text")]
${see_all_docs_in_library}                          //button[contains(text(),'See all documents in the Library')]
${answer_questionnaire_button}                      //*[@id='button-inbox-message-goto-questionnaire']/button
${clinic_inbox_attachment_icon}                     //*[text()="{}"]/..//span[contains(@class, 'attachment-icon')]
${close_external_pe_doc_android}                    //*[@resource-id='com.android.chrome:id/close_button']
${close_external_pe_doc_ios}                        //XCUIElementTypeButton[@name='Done']
${unactivated_symptom_reporting_message}            Symptom reporting is not yet activated for your account. If you have symptoms, please contact your clinic.
${unactivated_symptom_reporting_image}              //div[@class='no-symptom-modal-content-container']//img
${unactivated_symptom_reporting_close_button}       //*[@id='no-symptom-modal-close-button']/button
${message_from_clinic_response_field}               //*[@class="clinic-message-modal contactWithoutSymptom"]
${paperclip_icon}                                   //*[contains(@class, 'ds-icon ds-button__icon ds-icon--paperclip')]
${attach_file_button}                               //button/div/div[text()="Attach Files"]
${attach_file_info}                                 //p[text()="Optional: Add up to 10 images (.jpg, .jpeg, .png, .heic) or documents (.pdf, .doc, .docx, .xls, .xlsx) (max 10MB per file)"]
${remove_file_button}                               //button[@data-testid="file-cancel-button"]
${error_file_over_size_limit}                       //div[@data-testid="error-message" and text()="File size exceeds 10MB limit. Please choose a smaller file."]
${error_attach_files_limit}                         //div[@data-testid="attach-files-limit-error"]
${error_attach_files_limit_text}                    You can only upload 10 files.
${error_invalid_file_type}                          //div[@data-testid="error-message" and text()="Unsupported file type. Please choose a supported format."]
${attachment_location}                              ${EXECDIR}${/}data${/}file_storage_service_files${/}
${error_message_cancel_button}                      //div[@class="error-message"]/../div[@class="action"]/button
${cancel_send_message_button}                       //div/ds-button[@id="cancel-open-question"]
${file_icon}                                        //div[@class="icon"]/ds-icon
${doc_file_name}                                    FSS_doc_extension.doc
${docx_file_name}                                   FSS_docx_extension.docx
${xls_file_name}                                    FSS_xls_extension.xls
${xlsx_file_name}                                   FSS_xlsx_extension.xlsx
${heic_file_name}                                   FSS_heic_extension.heic
${jpg_file_name}                                    file_example_JPG_1MB.jpg
${jpeg_file_name}                                   FSS_jpeg_extension.JPEG
${png_file_name}                                    IMG_1554_2.6MB.PNG
${pdf_file_name}                                    FSS_pdf_29KB.pdf
${image_icon}                                       image_file
${document_icon}                                    docx_file
${excel_icon}                                       xls_file
${pdf_icon}                                         pdf_file
${message_attachments_list}                         //*[contains(@class, "message-attachments")]
${message_attachment_item}                          //*[contains(@class, "open-attachment")]
${attachment_image_preview_modal}                   //nh-view-attachment-image
${attachment_pdf_preview_modal}                     //nh-view-attachment-pdf
${attachment_preview_modal_primary_close_button}    //div[@class="modal-button-container"]
${attachment_preview_modal_x_button}                //button[@aria-label="Close dialog button"]
${first_question_type}                              //*[@class='question-type-selection']/button[1]


*** Keywords ***
Navigate to Clinic
    Try To Click Element    ${clinic-icon}
    Sleep    1

Select Ask about other issues
    Try To Click Element    ${ask_about_other_issues_icon}

Select topic
    Wait Until Element Is Visible    ${treatments-topic-button}
    ${txt}    Get Text    ${treatments-topic-button}
    Set Test Variable    ${topic}    ${txt}
    Try To Click Element    ${treatments-topic-button}

Message title is autofilled based on selected topic
    Wait Until Element Is Enabled    ${question-title-input}
    ${val}    Get Value    ${question-title-input}
    Should Be Equal    ${val}    ${topic}

Modify title if needed
    ${dat}    Get Current Date    exclude_millis=1
    Set Test Variable    ${edited-topic}    Autotest ${dat} ${topic} edited title
    Input Text    ${question-title-input}    ${edited-topic}

Enter question
    Try To Input Text    ${question_content_field}    ${auto_test_question_content}

Send question to clinic
    Try To Click Element    ${submit-question-button}
    Wait Until Page Contains    ${question-sent-confirmation-text}
    Try To Click Element    ${question_sent_to_clinic_close}

Sent message is displayed in the patient's inbox
    [Arguments]    ${topic}
    Wait Until Page Contains Element    ${clinic_messages}\[1]
    FOR    ${i}    IN RANGE    100
        Sleep    1s
        ${load_more_button_present}    Run Keyword And Return Status
        ...    Page Should Contain Element
        ...    ${load_more_button}
        IF    '${load_more_button_present}'=='True'
            Try To Click Element    ${load_more_button}
        END
        IF    '${load_more_button_present}'=='False'    BREAK
    END
    Wait Until Page Contains    ${topic}

Ask About Other Symptom
    [Arguments]    ${upload_photo}=no    ${go_to_homepage}=yes
    Select Ask About Symptom Option
    Add A Symptom    Other symptom
    Complete Other Symptom Form    Mild
    IF    '${upload_photo}'!='no'    Add Photo To Symptom    ${upload_photo}
    Click Next Button
    Questionnaire Summary Is Displayed
    Send Symptom To Clinic
    IF    '${go_to_homepage}'=='yes'    Go To Patient Homepage

Ask About Symptoms
    [Documentation]    generic keyword for selecting symptoms for Ask About Symptom scenario
    [Arguments]    ${symptom}
    Navigate to Clinic
    Select Ask About Symptom Option
    Add A Symptom    ${symptom}

Add Photo To Symptom
    [Documentation]    for some cases, add photo does not need to be clicked again, for example: multiple photo upload
    [Arguments]    ${upload_photo}    ${click_add_photo}=yes    ${uploaded}=yes    ${multiple}=no
    Scroll Element Into View    ${add_photo}
    IF    '${click_add_photo}'=='yes'    Try To Click Element    ${add_photo}
    Page Should Contain    Drag and drop photos or
    Page Should Contain    browse
    Page Should Contain    your computer.
    Scroll Element Into View    ${preview_image_section}
    Sleep    1    #needs time after scroll and before uploading photo
    IF    '${multiple}'=='yes'
        Sleep    3    #multiple photos take time to upload
    END
    Choose File    ${choose_file}    ${EXECDIR}${/}data${/}upload_photos${/}JPG${/}${upload_photo}
    IF    '${uploaded}'=='yes'
        Scroll Element Into View    ${preview_image_section}
        Wait Until Element Is Visible    ${photo_uploaded}    30s
    END

Select Ask About Symptom Option
    Sleep    1
    Try To Click Element    ${ask_about_symptom_button}
    Wait Until Page Does Not Contain Element    ${ask_about_symptom_button}

Verify Ask About Symptom Button Visibility
    [Arguments]    ${visible}
    Wait Until Location Contains    /patient/#/clinic
    IF    '${visible}'=='Yes'
        Wait Until Element Is Visible    ${ask_about_symptom_button}    timeout=3s
    ELSE
        Sleep    1
        Wait Until Element Is Not Visible    ${ask_about_symptom_button}    timeout=3s
    END

Verify Ask About Other Issues Button Visibility
    [Arguments]    ${visible}
    Wait Until Location Contains    /patient/#/clinic
    IF    '${visible}'=='Yes'
        Wait Until Element Is Visible    ${ask_about_other_issues_icon}    timeout=3s
    ELSE
        Sleep    1
        Wait Until Element Is Not Visible    ${ask_about_other_issues_icon}    timeout=3s
    END

Select Latest Clinic Message
    [Arguments]    ${message_title}=latest
    Navigate to Clinic
    ${pendo_visible}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${pendo_guide_close_button}
    ...    timeout=5s
    IF    ${pendo_visible}
        Try To Click Element    ${pendo_guide_close_button}
    END
    Scroll Element Into View    ${clinic_messages}\[1]
    Wait Until Element Is Visible    ${clinic_messages}\[1]
    IF    '${message_title}'!='latest'
        Try To Click Element    //div[contains(text(),"${message_title}")]/ancestor::li
    ELSE
        Try To Click Element    ${clinic_messages}
    END

Reload And Select Clinic Message When It Appears
    [Documentation]    Mostly used when sending questionnaires with appointment
    IF    'native' in '${ENVIRONMENT}'
        Sleep    10s
        Navigate to Clinic
    ELSE
        Navigate to Clinic
        ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${clinic_messages}\[1]
        WHILE    ${status}==${FALSE}    limit=60    # waits for 120s
            Reload Page
            Sleep    2s
            ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${clinic_messages}\[1]
        END
    END
    Try To Click Element    ${clinic_messages}\[1]

Reload Clinic Page
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Element Is Visible    ${clinic_messages}\[1]
        Go To Diary
        Navigate To Clinic
    ELSE
        Reload Page
    END

Close Pendo Guide If Visible
    ${pendo_visible}    Run Keyword And Return Status    Wait Until Element Is Visible    ${pendo_guide_close_button}
    IF    ${pendo_visible}
        Try To Click Element    ${pendo_guide_close_button}
    ELSE
        Wait Until Element Is Visible    main-navigation
    END

Reply To Clinic Message
    [Documentation]    new_message: clicks new message button if yes
    [Arguments]    ${message}    ${new_message}=no
    IF    '${new_message}'=='yes'
        Wait Until Element Is Visible    ${new_message_button}
        Try To Click Element    ${new_message_button}
    END
    Wait Until Element Is Visible    ${reply_to_message_textarea}
    Try To Input Text    ${reply_to_message_textarea}    ${message}
    IF    'native' in '${ENVIRONMENT}'
        Try To Click Element    ${message_text}
    END
    Wait Until Keyword Succeeds    20s    1s    Send Message

Send Message
    Try To Click Element    xpath=${reply_to_message_send_button}
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Page Does Not Contain Element    xpath=${reply_to_message_send_button}    5s
    ELSE
        Wait Until Element Is Not Visible    ${reply_to_message_send_button}    5s
    END

Respond If The Info Sufficient As Patient
    [Documentation]    Used when nurse sent Insructions to patient. Input message if info is NOT sufficient and
    ...    select rating if info is sufficient
    [Arguments]    ${option}    ${message}=default
    Wait Until Element Is Visible    ${reply_to_message_cancel_button}
    Scroll Element Into View    ${reply_to_message_cancel_button}
    IF    '${option}'=='Yes'
        Try To Click Element    ${information_sufficient_yes}
    ELSE
        Try To Click Element    ${information_sufficient_no}
        Input Text    ${information_sufficient_textarea}    ${message}
    END
    Try To Click Element    ${feedback_to_message_send_button}
    Wait Until Page Does Not Contain    ${feedback_to_message_send_button}
    Sleep    1

Verify If Patient Can Respond To PE Message
    [Arguments]    ${can_respond}
    IF    '${can_respond}'=='yes'
        Wait Until Element Is Visible    ${information_sufficient_yes}
        Wait Until Element Is Visible    ${information_sufficient_no}
    ELSE
        Sleep    1
        Element Should Not Be Visible    ${information_sufficient_yes}
        Element Should Not Be Visible    ${information_sufficient_no}
    END

Patient Is Directed To Inbox
    Wait Until Page Contains Element    ${clinic_header_element}
    Wait Until Element Is Visible    ${clinic_contact_your_care_team_header}
    Wait Until Element Is Visible    ${clinic_messages_column}

Verify Clinic Message Is Correct
    [Arguments]    ${expected_message}
    Wait Until Element Is Visible    ${message_content}
    ${text}    Get Text    ${message_content}
    ${text}    Convert To String    ${text}
    ${text}    Strip String    ${text}
    IF    'native' in '${ENVIRONMENT}'
        Element Text Should Be    ${message_content}    ${expected_message}
    ELSE
        Should Be Equal    ${expected_message}    ${text}
    END

Verify Clinic Message Title Is Correct
    [Arguments]    ${expected_title}
    Wait Until Element Is Visible    ${message_content}
    ${text}    Get Text    ${message_title}
    IF    'native' in '${ENVIRONMENT}'
        Element Text Should Be    ${message_title}    ${expected_title}
    ELSE
        Should Be Equal    ${expected_title}    ${text}
    END

Verify Clinic Message Date Is Correct
    [Arguments]    ${expected_date}
    Wait Until Element Is Visible    ${message_content}
    ${date}    Get Text    ${message_datetime}
    ${date}    Replace String    ${date}    ${\n}    ${EMPTY}
    Should Contain    ${date}    ${expected_date}

No Automated Selfcare Message For Mild Or Moderate Symptom
    [Documentation]    Automated selfcare message is not sent to patient inbox for Mild or Moderate symptoms
    ${date}    Get Current Date
    ${today}    Convert Date    ${date}    result_format=%d.%m.%Y
    ${day}    Convert Date    ${date}    result_format=%a
    ${day}    Convert To Upper Case    ${day}
    Set Test Variable    ${today}    ${day}${SPACE}${today}
    Wait Until Page Does Not Contain    ${today}
    Wait Until Page Does Not Contain    Mild
    Wait Until Page Does Not Contain    Moderate

Verify Inbox For Mild Or Moderate Symptom Message
    Select Latest Clinic Message
    Verify Clinic Message Is Correct    ${inbox_message_mild_moderate_text}
    Try To Click Element    ${message_modal_close_button}

Verify Inbox For Severe Symptom Message
    Select Latest Clinic Message
    ${expected_text}    Convert To String    ${inbox_severe_symptom_instructions_text}
    ${expected_text2}    Strip String    ${expected_text}
    Verify Clinic Message Is Correct    ${expected_text2}
    Try To Click Element    ${message_modal_close_button}

New Message Indicator Is Correct
    [Arguments]    ${message_count}
    Wait Until Element Is Visible    ${new_message_indicator}
    ${indicator_value}    Get text    ${new_message_indicator}
    ${element}    Get Webelement    ${new_message_indicator}
    ${rgb}    Call Method    ${element}    value_of_css_property    background-color
    Should Be Equal    ${rgb}    rgba(255, 0, 0, 1)
    Should Be Equal    ${indicator_value}    ${message_count}

Latest Message Is Highlighted
    Wait Until Page Contains Element    ${clinic_messages}
    Scroll Element Into View    ${clinic_messages}
    ${element}    Get Webelement    //*[contains(@class, "icon-container ng-star-inserted")]
    ${rgb}    Call Method    ${element}    value_of_css_property    background-color
    Should Be Equal    ${rgb}    rgba(37, 164, 204, 1)
    Element Should Be Visible    ${latest_message_indicator}

New Message Indicator Should Be Not Visible
    [Documentation]    New message indicator should not be visible in the message list and clinic icon
    Sleep    1s
    Page Should Not Contain Element    ${latest_message_indicator}
    Page Should Not Contain Element    ${new_message_indicator}

Read Unread Messages In Current Page
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${new_clinic_messages}
    WHILE    ${status}     limit=10
        @{messages}    Get Webelements    ${new_clinic_messages}
        FOR    ${message}    IN    @{messages}
            Try To Click Element    ${message}
            Wait Until Element Is Visible    ${message_modal_close_button}    timeout=5s
            Try To Click Element    ${message_modal_close_button}
            Sleep    1
        END
        ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${new_clinic_messages}
    END

Select Answer Questionnaire
    Wait Until Element Is Visible    ${questionnaire_answer_button}
    Try To Click Element    ${questionnaire_answer_button}

Close Clinic Message
    Wait Until Element Is Visible    ${message_modal_close_button}
    Try To Click Element    ${message_modal_close_button}
    Generic: Wait Until Element Is Not Visible    ${message_modal_close_button}

Verify Patient App Clinic Information Section
    Wait Until Element Is Visible    ${clinic_info_header}
    Scroll Element Into View    ${clinic_info_header}
    Verify Patient App Clinic Contact Information
    Verify Patient App Clinic Content Area

Verify Patient App Clinic Contact Information
    ${clinic_name}    Execute Javascript
    ...    return document.getElementsByClassName('clinic-name type--font-weight-semibold')[0].innerText;
    Should Be Equal    ${clinic_name}    ${clinic_name}
    Sleep    1
    ${clinic street_address}    Execute Javascript
    ...    return document.getElementsByClassName('clinic-street')[0].innerText;
    Should Be Equal    ${clinic street_address}    ${street_address}
    ${clinic_zip_code_city}    Execute Javascript
    ...    return document.getElementsByClassName('clinic-city mb-m')[0].innerText;
    Should Be Equal    ${clinic_zip_code_city}    ${zip_code}${SPACE}${city}
    ${clinic_phone_number}    Execute Javascript
    ...    return document.getElementsByClassName('clinic-phone')[0].href.split(':')[1];
    Should Be Equal    ${clinic_phone_number}    ${phone_number}
    ${clinic_email_address}    Execute Javascript
    ...    return document.getElementsByClassName('clinic-email mb-l')[0].innerText;
    Should Be Equal    ${clinic_email_address}    ${email}
    ${clinic_website_url}    Execute Javascript
    ...    return document.getElementsByClassName('clinic-web extrainfo-link text ng-star-inserted')[0].href;
    Should Be Equal    ${clinic_website_url}    https://www.varian.com/${random_number}
    Try To Click Element    ${clinic_info_weblink}
    Switch Window    NEW
    ${clinic_url}    Execute Javascript    return window.location.href;
    Should Be Equal    ${clinic_website_url}    ${clinic_url}

Verify Patient App Clinic Content Area
    Switch Window    MAIN
    Wait Until Keyword Succeeds    20s    1s    Scroll To Bottom Of Screen
    ${content_area_title}    Execute Javascript
    ...    return document.getElementsByClassName('extrainfo-title')[0].innerText;
    Should Be Equal    ${content_area_title}    ${random_title}
    ${content_area_text}    Execute Javascript
    ...    return document.getElementsByClassName('extrainfo-content')[0].innerText;
    Should Be Equal    ${content_area_text}    ${random_text}
    ${content_area_link_text}    Execute Javascript
    ...    return document.getElementsByClassName('extrainfo-links ng-star-inserted')[0].innerText;
    ${RANDOM_LINK_TITLE}    Convert To Upper Case    ${random_link_title}
    Should Be Equal    ${content_area_link_text}    ${RANDOM_LINK_TITLE}
    ${content_area_link_url}    Execute Javascript
    ...    return document.getElementsByClassName('extrainfo-link text ng-star-inserted')[1].href
    Should Be Equal    ${content_area_link_url}    https://www.varian.com/${random_number_link}
    Go To    ${content_area_link_url}
    ${content_url}    Execute Javascript    return window.location.href;
    Should Be Equal    ${content_area_link_url}    ${content_url}

Scroll To Bottom Of Screen
    Wait Until Element Is Enabled    //a[contains(text(),'${RANDOM_LINK_TITLE}')]
    Scroll Element Into View    //a[contains(text(),'${RANDOM_LINK_TITLE}')]

Verify Clinic Education Message Is Correct
    [Arguments]    ${expected_message}
    Wait Until Element Is Enabled    ${message_content}
    ${text}    Get Text    ${message_content}
    ${text}    Convert To String    ${text}
    ${text}    Strip String    ${text}
    ${education_text}    Remove String
    ...    ${text}
    ...    \n\n1 document attached\n${attached_education_document}\nSEE ALL DOCUMENTS IN THE LIBRARY
    Should Be Equal    ${expected_message}    ${education_text}

Verify Message Attachment
    [Arguments]    ${attached_doc_title}
    Wait Until Element Is Visible    //div[contains(text(),'${attached_doc_title}')]

Open Education Article From Message
    [Arguments]    ${attached_doc_title}
    Try To Click Element    ${inbox_attached_doc}
    IF    'native' not in '${ENVIRONMENT}'
        Wait until keyword succeeds    5x    1s    Switch Window    NEW
        Wait Until Page Contains    ${attached_doc_title}
        Wait until keyword succeeds    3x    1s    Switch Window    MAIN
    ELSE
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        IF    'android' in '${PLATFORM_NAME}'
            Wait Until Element Is Visible    ${close_external_pe_doc_android}
            Click Element    ${close_external_pe_doc_android}
            IF    'test' in '${ENVIRONMENT}'
                Switch To Context    WEBVIEW_com.noona.application.test
            ELSE
                Switch To Context    WEBVIEW_com.noona.application.staging
            END
        ELSE
            Wait Until Element Is Visible    ${close_external_pe_doc_ios}
            Click Element    ${close_external_pe_doc_ios}
            Switch To Context    ${contexts}[1]
        END
    END
    Wait Until Element Is Visible    //div[contains(text(),'${attached_doc_title}')]

Click See All Documents In The Library
    IF    'native' in '${ENVIRONMENT}'
        Try To Click Element    ${see_all_docs_in_library}
        ${status}    Run Keyword And Return Status
        ...    Wait Until Element Is Visible
        ...    ${patient_feedback_dialog_close_button}
        IF    ${status}
            Try To Click Element    ${patient_feedback_dialog_close_button}
        END
    ELSE
        Execute Javascript    document.getElementsByClassName('clinic-inbox-message--go-to-library-link')[0].click();
        ${status}    Run Keyword And Return Status
        ...    Wait Until Element Is Visible
        ...    ${patient_feedback_dialog_close_button}
        IF    ${status}
            Try To Click Element    ${patient_feedback_dialog_close_button}
        END
        Display Education Documents Page
    END

Close Clinic's Message Dialog
    Wait Until Element Is Visible    ${reply_to_message_cancel_button}
    Try To Click Element    ${reply_to_message_cancel_button}

Open Removed Document
    IF    'native' in '${ENVIRONMENT}'
        Try To Click Native App Element    ${inbox_attached_doc}
        Wait Until Page Contains    The document has been removed by your clinic.
        Sleep    2s
        Wait Until Page Does Not Contain    The document has been removed by your clinic.
    ELSE
        Try To Click Element    ${inbox_attached_doc}
        Wait until keyword succeeds    5x    1s    Switch Window    NEW
        Wait until keyword succeeds
        ...    5x
        ...    1s
        ...    Wait Until Page Contains
        ...    The document has been removed by your clinic.
        Wait until keyword succeeds    3x    1s    Switch Window    MAIN
    END

Document Attached Title Is Correct
    [Documentation]    document attached in the message
    [Arguments]    ${title}
    Wait Until Element Is Visible    ${document_attached_title}    timeout=7s
    ${text}    Get Text    ${document_attached_title}
    Capture Screenshot    # to see how the text looks like
    IF    'native' not in '${ENVIRONMENT}'
        Should Be Equal    ${title}    ${text}
    ELSE
        Should Contain    ${title}    ${text}
    END

Message Date Is Correct
    [Arguments]    ${date}
    Wait Until Element Is Visible    ${message_modal_message_date}
    ${text}    Get Text    ${message_modal_message_date}
    Should Contain    ${text}    ${date}

Select Answer Questionnaire Button
    Try To Click Element    ${answer_questionnaire_button}

Verify Attachment Icon For The Message
    [Arguments]    ${message_title}    ${behavior}
    Wait Until Element is Visible    ${clinic_messages}\[1]
    IF    '${behavior}'=='displayed'
        ${attachment_icon}    Format String    ${clinic_inbox_attachment_icon}    ${message_title}
        Wait Until Element Is Visible    ${attachment_icon}
    ELSE
        ${attachment_icon}    Format String    ${clinic_inbox_attachment_icon}    ${message_title}
        Generic: Wait Until Element Is Not Visible    ${attachment_icon}
    END

Ask About Other Issues
    Select Ask about other issues
    Select topic
    Enter question
    Send question to clinic

Verify Added Attachments On Patient Side
    Verify Added Attachments
    ${number_of_attachments}    Get Element Count    ${message_attachment_item}
    Page Should Contain    ${auto_test_question_content}
    Page Should Contain    ${number_of_attachments} documents attached

Verify Attach Files Link Visibility
    [Arguments]    ${visible}
    Wait Until Element Is Visible    ${question_content_field}
    IF    '${visible}'=='Yes'
        Wait Until Element Is Visible    ${attach_file_button}    timeout=3s
    ELSE
        Sleep    1s
        Element Should Not Be Visible    ${attach_file_button}    timeout=3s
    END

Cancel Open Question Sending
    Wait Until Element Is Visible    ${cancel_send_message_button}
    Try To Click Element    ${cancel_send_message_button}

Verify If Other Issues Topics Are Displayed
    [Arguments]    ${is_displayed}    @{topics}
    Wait Until Element Is Visible    ${first_question_type}
    FOR    ${topic}    IN    @{topics}
        IF    '${is_displayed}'=='Yes'
            Element Should Be Visible    //*[@class='question-type-selection']//button[text()=' ${topic} ']
        ELSE
            Element Should Not Be Visible    //*[@class='question-type-selection']//button[text()=' ${topic} ']
        END
    END
