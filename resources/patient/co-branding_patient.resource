*** Settings ***
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}clinic_settings${/}basic_settings.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource


*** Variables ***
${patient_login_welcome_header}         //legend[contains(text(),'Welcome to Noona')]
${patient_login_button}                 email-and-password-next-button
${kpl_no}                               remember-me-no
${kpl_yes}                              remember-me-yes
${keep_me_logged_next}                  remember-me-next-button
${patient_diary_navigation}             navigation-diary-link
${patient_library_navigation}           navigation-library-link
${patient_clinic_navigation}            navigation-clinic-link
${patient_more_navigation}              navigation-more-link
${more_feedback_link}                   more-link-feedback
${add_menu_navigation}                  navigation-add-link
${clinic_navigation_default_color}      25A4CC
${noona_default_hex_color}              885DB3
${value_of_css_property}                value_of_css_property
${background_color}                     background-color


*** Keywords ***
Verify Patient Log In Custom Clinic Name
    Scroll Element Into View    ${login_button}
    Sleep    3s
    Wait Until Element Contains    //legend[contains(text(),'Welcome to ${noona}')]    ${noona}

Verify Patient Log In Default Clinic Name
    Scroll Element Into View    ${language_dropdown}
    Sleep    3s
    Wait Until Element Contains    ${patient_login_welcome_header}    Noona

Wait Until Patient Log In Page Is Enabled
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${landing_page_login_button}
    ...    timeout=10s
    IF    ${status}
        Wait Until Element Is Enabled    ${language_dropdown}
        Try To Click Element    ${landing_page_login_button}
    END
    Wait Until Element Is Enabled    ${oidc_username_input}
    Wait Until Element Is Enabled    ${oidc_password_input}


Verify Patient Log In Custom Color
    [Arguments]    ${patient_email}
    Wait Until Patient Log In Page Is Enabled
    Try To Input Text    ${oidc_username_input}    ${patient_email}
    Input Password    ${oidc_password_input}    ${DEFAULT_PASSWORD}
    Wait Until Keyword Succeeds    20s    1s    Get Login Button Custom Color
    Go Back
    Try To Click Element    ${about_modal_link}
    Wait Until Element Is Enabled    ${about_modal}
    Wait Until Element Is Enabled    ${close_modal}
    Sleep    5s
    ${rbga_color}    Get WebElement    ${close_modal}
    ${cancel_button_color}    Call Method    ${rbga_color}    ${value_of_css_property}    ${background_color}
    Convert And Compare Element To Hex Color    ${cancel_button_color}
    Try To Click Element    ${close_modal}

Continue Patient Login
    Try To Click Element    ${patient_login_button}

Verify Keep Me Login Next Button Color
    Wait Until Element Is Enabled    ${kpl_no}
    ${option}    Format String    ${keep_me_logged_in_radio}    ${kpl_no}
    Sleep    5s
    Try To Click Element    ${option}
    Wait Until Element Is Enabled    ${keep_me_logged_next}
    Sleep    5s
    ${keep_me_logged_next_button_color}    Execute Javascript
    ...    return${SPACE}
    ...    document.defaultView.getComputedStyle(document.getElementById("${keep_me_logged_next}"),null)['background-color']
    IF    '${keep_me_logged_next_button_color}'!='rgba(0, 0, 0, 0)'
        Convert And Compare Element To Hex Color    ${keep_me_logged_next_button_color}
    ELSE IF    '${keep_me_logged_next_button_color}'=='rgba(0, 0, 0, 0)'
        Convert And Compare Element To Default Color    ${keep_me_logged_next_button_color}
    END
    Try To Click Element    ${keep_me_logged_next}

Get Login Button Custom Color
    ${login_button_element}    Set Variable    ${oidc_login_button_id}
    Wait Until Element Is Enabled    ${login_button_element}
    ${login_button_element}    Get WebElement    ${login_button_element}
    FOR    ${INDEX}    IN RANGE    0    3
        ${rbga_color}    Call Method    ${login_button_element}    ${value_of_css_property}    ${background_color}
        # wait until button updates to custom branding color
        IF    '${rbga_color}'=='rgba(0, 0, 0, 0)'    Sleep    1    ELSE    BREAK
    END
    Convert And Compare Element To Hex Color    ${rbga_color}

Verify Patient Log In Default Color
    [Arguments]    ${patient_email}
    Wait Until Patient Log In Page Is Enabled
    Try To Input Text    ${oidc_username_input}    ${patient_email}
    Input Password    ${oidc_password_input}    ${DEFAULT_PASSWORD}
    Wait Until Keyword Succeeds    20s    1s    Get Login Button Default Color
    Go Back
    Try To Click Element    ${about_modal_link}
    Wait Until Element Is Enabled    ${about_modal}
    Wait Until Element Is Enabled    ${close_modal}
    ${cancel_button_color}    Get WebElement    ${close_modal}
    ${cancel_button_color}    Call Method    ${cancel_button_color}    ${value_of_css_property}    ${background_color}
    Convert And Compare Element To Default Color    ${cancel_button_color}
    Try To Click Element    ${close_modal}

Get Login Button Default Color
    ${login_button_element}    Set Variable    ${oidc_login_button_id}
    Wait Until Element Is Enabled    ${login_button_element}
    ${rbga_color}    Get WebElement    ${login_button_element}
    ${login_button_color}    Call Method    ${rbga_color}    ${value_of_css_property}    ${background_color}
    Convert And Compare Element To Default Color    ${login_button_color}

Verify Patient Log In Custom Logo
    Wait Until Patient Log In Page Is Enabled
    Scroll Element Into View    ${language_dropdown}
    Sleep    3s
    Wait Until Page Contains Element    ${custom_logo_id}

Verify Patient Log In Default Logo
    Wait Until Patient Log In Page Is Enabled
    Wait Until Page Contains Element    ${default_noona_logo}

Custom Branding Is Visible To Patient
    [Arguments]    ${patient_email}    ${custom_color}    ${custom_logo}    ${custom_clinic_name}
    Open Page    ${actual_custom_url}
    Wait Until Patient Log In Page Is Enabled
    ${status}    Run Keyword And Ignore Error
    ...    Verify Custom Branding
    ...    ${patient_email}
    ...    ${custom_color}
    ...    ${custom_logo}
    ...    ${custom_clinic_name}
    Skip If    '${status}[0]'=='FAIL'    # TODO: delete when custom branding is implemented with OIDC

Verify Custom Branding
    [Arguments]    ${patient_email}    ${custom_color}    ${custom_logo}    ${custom_clinic_name}
    IF    '${custom_color}'!='default_color'
        Verify Patient Log In Custom Color    ${patient_email}
    ELSE
        Verify Patient Log In Default Color    ${patient_email}
    END
    IF    '${custom_logo}'!='default_logo'
        Verify Patient Log In Custom Logo
    ELSE
        Verify Patient Log In Default Logo
    END
    IF    '${custom_clinic_name}'!='default_noona'
        Verify Patient Log In Custom Clinic Name
    ELSE
        Verify Patient Log In Default Clinic Name
    END

Verify Color In Navigation Bar
    Convert Field Color To Hex Format    ${patient_diary_navigation}
    Convert Field Color To Hex Format    ${patient_library_navigation}
    Clinic Navigation Color

Convert Field Color To Hex Format
    [Arguments]    ${patient_element}
    Wait Until Element Is Enabled    ${patient_element}
    Try To Click Element    ${patient_element}
    Sleep    5s
    ${rbga_color}    Get WebElement    ${patient_element}
    ${patient_element_color}    Call Method    ${rbga_color}    ${value_of_css_property}    ${background_color}
    IF    '${patient_element_color}'!='rgb(136, 93, 179)'
        Convert And Compare Element To Hex Color    ${patient_element_color}
    ELSE IF    '${patient_element_color}'=='rgb(136, 93, 179)'
        Compare To Other Default Color    ${patient_element_color}    ${noona_default_hex_color}
    END

Clinic Navigation Color
    [Documentation]    Clinic navigation default color is light blue
    Wait Until Element Is Enabled    ${patient_clinic_navigation}
    Try To Click Element    ${patient_clinic_navigation}
    Sleep    5s
    ${rbga_color}    Get WebElement    ${patient_clinic_navigation}
    ${clinic_button_color}    Call Method    ${rbga_color}    ${value_of_css_property}    ${background_color}
    IF    '${clinic_button_color}'=='rgb(37, 164, 204)'
        Compare To Other Default Color    ${clinic_button_color}    ${clinic_navigation_default_color}
    ELSE IF    '${clinic_button_color}'!='rgb(37, 164, 204)'
        Convert And Compare Element To Hex Color    ${clinic_button_color}
    END

Compare To Other Default Color
    [Arguments]    ${rgb_color}    ${expected_hex_color}
    ${rgb_color}    Convert To String    ${rgb_color}
    ${rgb_color}    Remove String    ${rgb_color}    rgb(    )
    @{colors}    Split String    ${rgb_color}    ,
    ${RED}    Convert To Hex    ${colors}[0]
    ${GREEN}    Convert To Hex    ${colors}[1]
    ${BLUE}    Convert To Hex    ${colors}[2]
    ${actual_hex_value}    Catenate    ${RED}${GREEN}${BLUE}
    Should Be Equal    ${actual_hex_value}    ${expected_hex_color}
