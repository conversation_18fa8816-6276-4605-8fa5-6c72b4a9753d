*** Settings ***
Resource    ../try_keywords.resource


*** Variables ***
${burger-menu}                      //*[@id="navigation-more-link"]/button
${feedback-menuitem}                //*[@data-testid="more-link-feedback"]
${feedback-message-field}           //*[@id="message"]
${save-button-label}                Save
${save-button-element}              //*[@id="save-feedback"]/button
${contact-ok-checkbox}              //*[contains(text(),"It is OK for Noona support to contact me about my feedback")]
${toaster-message-element}          //*[contains(text(),"Thank you for your feedback")]
${contact_clinic_header}            //*[@id="clinic-contact-options-header"]
${support_link}                     //*[@id="support-link"]
${feedback_thread1}                 //div[@id="main"]//feedback-thread[1]/div
${feedback_reply_field}             //*[@id="main"]//feedback-input/div/textarea
${send_feedback_reply}              //*[@id="main"]//feedback-input/div/span
${patient_message_reply_field}      //div[contains(@class, 'reply-form-area')]/textarea
${send_reply}                       //*[@id="button-inbox-message-send-reply"]
${last_response_content}            (//p[@class='content'])[1]    # the last message is index 1


*** Keywords ***
Patient navigates to Feedback
    Wait Until Keyword Succeeds    22    2    Click Element    ${burger-menu}
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    ${contexts}[1]
    END
    Wait Until Page Contains Element    ${feedback-menuitem}
    Wait Until Keyword Succeeds    9    1    Try To Click Element    ${feedback-menuitem}

The patient selects their level of satisfaction
    ${happymeter}    Evaluate    random.randint(1, 5)    modules=random
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    ${contexts}[1]
    END
    Wait Until Keyword Succeeds    9    1    Click Element    //*[@name='icon-moodsmily-${happymeter}']
    ${feedback-txt}    Set Variable    Autotest robot random feedback: ${happymeter}/5
    #    Optionally, the patient enters a feedback message
    Sleep    1
    Input Text    ${feedback-message-field}    ${feedback-txt}
    Sleep    1
    IF    'native' not in '${ENVIRONMENT}'
        Textarea Value Should Be    ${feedback-message-field}    ${feedback-txt}
    END

The patient selects if Noona (the company) can contact the patient concerning the feedback
    Scroll Element Into View    ${contact-ok-checkbox}
    Wait Until Keyword Succeeds    3    1    Try To Click Element    ${contact-ok-checkbox}

The patient clicks the "Save" button of the feedback dialog
    Wait Until Keyword Succeeds    9    1    Try To Click Element    ${save-button-element}

Noona displays a notification toaster message
    Wait Until Element Is Visible    ${toaster-message-element}
    Wait Until Keyword Succeeds    5x    200ms    Wait Until Page Does Not Contain Element    ${toaster-message-element}

Support Has Replied To F05P02 Patient Anonymous Feedback
    [Arguments]    ${email}
    Login As Patient    ${email}
    Patient navigates to Feedback
    The patient selects their level of satisfaction
    The patient selects if Noona (the company) can contact the patient concerning the feedback
    The patient clicks the "Save" button of the feedback dialog
    Noona displays a notification toaster message
    Close Browser
    Login As Admin
    Reply F05P02 Patient Feedback

Reply F05P02 Patient Feedback
    Try To Click Element    ${support_link}
    Wait Until Page Contains Element    ${feedback_thread1}
    Try To Click Element    ${feedback_thread1}
    Wait Until Page Contains Element    ${feedback_reply_field}
    ${now}    Get Current Date    result_format=%Y%m%d%H%M
    ${reply_to_patient}    Set Variable    Thank you F05P02 Patient for your feedback ${now}
    Input Text    ${feedback_reply_field}    ${reply_to_patient}
    Try To Click Element    ${send_feedback_reply}
    Wait Until Element Contains    ${last_response_content}    ${reply_to_patient}
    Close Browser

Patient navigates to messages
    [Arguments]    ${email}
    Login As Patient    ${email}
    Navigate to Clinic
    Wait Until Page Contains Element    ${contact_clinic_header}

Patient opens message sent by support help desk person
    Select Latest Clinic Message
    Wait Until Page Contains Element    ${send_reply}
    Try To Click Element    ${patient_message_reply_field}

Patient enters reply message and selects send
    ${random_reply}    Generate Random String    45
    Set Test Variable    ${random_reply}
    Try To Input Text    ${patient_message_reply_field}    ${random_reply}
    Try To Click Element    ${send_reply}
    Wait Until Page Contains Element    ${contact_clinic_header}

Support Receives Patient Reply
    Login As Admin
    Try To Click Element    ${support_link}
    Wait Until Page Contains Element    ${feedback_thread1}
    Sleep    2s
    Try To Click Element    ${feedback_thread1}
    Wait Until Page Contains    ${random_reply}
