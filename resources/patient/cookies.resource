*** Settings ***
Resource             ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource             ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource
Resource             ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource


*** Variables ***
${cookies_image}                                            //img[@class="cookies-icon"]
${clinic_cookies_image}                                     //*[@id="cookie"]/div/mat-icon
${cookie_settings_title}                                    //h2/center[text()="Cookie Settings"]
${cookie_settings_text}                                     //p[text()="We use necessary cookies to provide you with the Noona services. In addition to the necessary cookies we use analytical cookies to gather information about how users interact with <PERSON><PERSON>. This data enables us to generate reports and insights that help us to improve Noona. Please select your cookie preferences below. Further information can be found in the "]
${necessary_cookies_button}                                 //*[@class="action-buttons"]/ds-button[1]
${necessary_cookies_selected}                               //*[@class="action-buttons"]/ds-button[1]/button/div/ds-icon
${all_cookies_button}                                       //*[@class="action-buttons"]/ds-button[2]
${all_cookies_button_selected}                              //*[@class="action-buttons"]/ds-button[2]/button/div/ds-icon
${local_storage_key}                                        noona-cookieConsent
${sessionID_cookie}                                         JSESSIONID
${patient_sticky_cookie}                                    noona-web-patient_sticky
${clinic_sticky_cookie}                                     noona-web-clinic_sticky
${clinic_preferred_lang}                                    preferred-language
${auth_sessionID_cookie}                                    authJSESSIONID
${dt_cookie}                                                dtCookie
${dtPC_cookie}                                              dtPC
${dtSa_cookie}                                              dtSa
${visitor_cookie}                                           rxVisitor
${vt_cookie}                                                rxvt
${more_cookie_policy_button}                                //button/span[text()="Cookie Policy"]
${close_cookie_button}                                      //button[@class="close"]
${change_consent_title_clinic}                              //p[contains(@class, 'call-to-action') and contains(text(), 'Review and change your consent')]
${change_consent_title}                                     //p/b[text()="Review and change your consent"]
${cookie_setting_section}                                   //div[@class="showdown-content"]
${cookie_policy_link}                                       //div[@class="showdown-content"]/p/a
${cookie_policy_section}                                    //nh-cookie-policy//*[@class="cookie-policy-modal"]//*[@class="content"]
${clinic_cookie_policy_section}                             //*[@id="cookie"]/div[contains(@class, 'cookie-policy')]
${clinic_cookie_policy_link}                                //*[@data-testid='open-cookie-policy']
${cookie_policy_button_landing_page}                        //ds-button[@data-testid="cookie-policy"]
${cookie_policy_button_more_menu}                           //button[@data-testid="more-link-cookies"]


*** Keywords ***
Open Url
    [Documentation]    Starts a Chrome Browser with specified locale
    [Arguments]    ${url}    ${locale}
    ${options}    Evaluate    sys.modules['selenium.webdriver'].ChromeOptions()    sys, selenium.webdriver
    Call Method    ${options}    add_argument    --lang\=${locale}
    Call Method
    ...    ${options}
    ...    add_argument
    ...    --user-agent\=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_8_2) AppleWebKit/537.17 (KHTML, like Gecko) Chrome/24.0.1309.0 Safari/537.17
    &{prefs}    Create Dictionary    intl.accept_languages=${locale}
    Call Method    ${options}    add_experimental_option    prefs    ${prefs}
    IF    '${BROWSER}'=='headlesschrome'
        Call Method    ${options}    add_argument    --headless
    END
    Call Method    ${options}    add_argument    --disable-search-engine-choice-screen
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists}
        Open Browser
        ...    ${url}
        ...    browser=chrome
        ...    remote_url=${REMOTE_URL}
        ...    options=${options}
    ELSE
        Open Browser    ${url}    browser=chrome    options=${options}
    END
    IF    '${url}' == '${NURSE_LOGIN_URL}'
        Set Window Size       1250       1080      #high resolution is needed here for cookie img
        Wait Until Element Is Visible    ${clinic_cookies_image}    timeout=30s
    ELSE IF    '${url}' == '${PATIENT_URL}'
        Wait Until Element Is Visible    ${cookies_image}    timeout=30s    #long timeout added due to ticket NOONA-23328
    END

Check That Cookie Settings Are Displayed
    Wait Until Element Is Visible    ${cookies_image}
    Wait Until Element Is Visible    ${cookie_settings_title}
    Wait Until Element Is Visible    ${cookie_settings_text}
    Wait Until Element Is Visible    ${cookie_policy_link}
    Wait Until Element Is Visible    ${necessary_cookies_button}
    Wait Until Element Is Visible    ${all_cookies_button}

Check That Cookie Settings Are Displayed On Clinic Landing Page
    Wait Until Element Is Visible    ${clinic_cookies_image}
    Wait Until Element Is Visible    ${accept_necessary_cookies_for_clinic}
    Wait Until Element Is Visible    ${accept_all_cookies_for_clinic}
    Wait Until Element Is Visible    ${cookie_settings_title}
    Wait Until Element Is Visible    ${cookie_settings_text}
    Wait Until Element Is Visible    ${cookie_policy_link}

Select Necessary Cookies
    Try To Click Element    ${necessary_cookies_button}

Clinic User Selects Necessary Cookies
    Try To Click Element    ${accept_necessary_cookies_for_clinic}

Get Local Storage Value
    ${value}    Execute Javascript    return window["localStorage"].getItem("${local_storage_key}")
    Set Test Variable    ${value}

Get Browser Cookies
    ${cookies}    Get Cookies
    Convert To String    ${cookies}
    Set Test Variable    ${cookies}
    Log    ${cookies}

Verify Necessary Cookies Selection In Browser Session
    Sleep    2s    # necessary to get the analytical cookies populated, if any
    Get Local Storage Value
    Should Be Equal    ${value}    "necessary"
    Get Browser Cookies
    Should contain    ${cookies}    ${sessionID_cookie}
    Should contain    ${cookies}    ${patient_sticky_cookie}
    Should contain    ${cookies}    ${auth_sessionID_cookie}
    Should not contain    ${cookies}    ${dt_cookie}
    Should not contain    ${cookies}    ${dtPC_cookie}
    Should not contain    ${cookies}    ${visitor_cookie}
    Should not contain    ${cookies}    ${vt_cookie}
    Should not contain    ${cookies}    ${dtSa_cookie}

Verify Necessary Cookies Selection In Clinic Browser Session
    Sleep    2s    # necessary to get the analytical cookies populated, if any
    Get Local Storage Value
    Should Be Equal    ${value}    "necessary"
    Get Browser Cookies
    Should contain    ${cookies}    ${clinic_sticky_cookie}
    Should contain    ${cookies}    ${sessionID_cookie}

Verify Necessary Cookies Selection In Cookie Policy From Landing Page
    Try To Click Element    ${cookie_policy_button_landing_page}
    Wait Until Element Is Visible    ${cookie_policy_section}
    Wait Until Element Is Visible    ${necessary_cookies_selected}

Verify Necessary Cookies Selection In Cookie Policy From Clinic Landing Page
    Try To Click Element              ${clinic_cookie_policy_link}/button
    Wait Until Element Is Visible     ${clinic_cookie_policy_section}
    Wait Until Element Is Visible     ${accept_necessary_cookies_for_clinic}/mat-icon

Verify Necessary Cookies Selection In Cookie Policy From More Menu
    Select Cookie Policy From More Menu
    Wait Until Element Is Visible    ${necessary_cookies_selected}

Select All Cookies
    Try To Click Element    ${all_cookies_button}

Clinic User Selects All Cookies
    Try To Click Element    ${accept_all_cookies_for_clinic}

Verify All Cookies Selection In Browser Session
    # necessary to get the analytical cookies populated , if any; checking only one of them which is shown all the time
    Sleep
    ...    2s
    Get Local Storage Value
    Should Be Equal    ${value}    "all"
    Get Browser Cookies
    Should contain    ${cookies}    ${sessionID_cookie}
    Should contain    ${cookies}    ${patient_sticky_cookie}
    Should contain    ${cookies}    ${auth_sessionID_cookie}
    Should contain    ${cookies}    ${dt_cookie}

Verify All Cookies Selection In Clinic Browser Session
    # necessary to get the analytical cookies populated , if any; checking only one of them which is shown all the time
    Sleep
    ...    2s
    Get Local Storage Value
    Should Be Equal    ${value}    "all"
    Get Browser Cookies
    Should contain    ${cookies}    ${sessionID_cookie}
    Should contain    ${cookies}    ${clinic_sticky_cookie}
    Should contain    ${cookies}    ${dt_cookie}
    Should contain    ${cookies}    ${dtPC_cookie}
    Should contain    ${cookies}    ${visitor_cookie}
    Should contain    ${cookies}    ${vt_cookie}

Verify All Cookies Selection In Cookie Policy From Landing Page
    Try To Click Element    ${cookie_policy_button_landing_page}
    Wait Until Element Is Visible    ${cookie_policy_section}
    Wait Until Element Is Visible    ${all_cookies_button_selected}

Verify All Cookies Selection In Cookie Policy From Clinic Landing Page
    Try To Click Element              ${clinic_cookie_policy_link}/button
    Wait Until Element Is Visible     ${clinic_cookie_policy_section}
    Wait Until Element Is Visible     ${accept_all_cookies_for_clinic}/mat-icon

Clinic User Clicks Cookie Policy From Help Menu
    Try To Click Element    ${help_link}
    Wait Until Page Contains Element      ${clinic_cookie_policy_link}
    Try To Click Element                  ${clinic_cookie_policy_link}
    Wait Until Page Contains Element      ${change_consent_title_clinic}

Select Cookie Policy From More Menu
    Try To Click More Button
    Try To Click Element    ${more_cookie_policy_button}
    Wait Until Page Contains Element    ${change_consent_title}

Verify All Cookies Selection In Cookie Policy From More Menu
    Select Cookie Policy From More Menu
    Wait Until Element Is Visible    ${all_cookies_button_selected}

Set Locale List
#    @{locales_list}    Create List    da    de    en_GB    es    fi    fr    it    nl    pt    sv    tr    no
    @{locales_list}    Create List    de    en_GB    es    fi    fr    it    nl    pt    sv    tr    # Norwegian language excluded for now; runs locally but not in gitlab; need time to investigate
    Set Suite Variable    @{locales_list}

Verify Cookie Settings Modal Content
    [Documentation]   This keyword works on both clinic and patient side
    [Arguments]    ${language_name}
    Log    ${language_name}
    Wait Until Element Is Visible    xpath=${cookie_setting_section}
    Scroll Element Into View    xpath=${cookie_setting_section}
    ${actual_cookie_setting_text}    Get Text    xpath=${cookie_setting_section}
    ${cookie_settings_content}    Get File
    ...    ${EXECDIR}${/}data${/}cookie_policy_content${/}cookie_policy_setting_${language_name}.txt
    ${are_equal}    Run Keyword And Return Status
    ...    Should Be Equal
    ...    ${actual_cookie_setting_text}
    ...    ${cookie_settings_content}
    IF    '${are_equal}' == 'False'
        Fail    The content of the cookie setting modal does not match. ${are_equal}.
    END

Verify Cookie Policy Content From Cookie Settings Modal
    [Arguments]    ${language_name}
    Try To Click Element    ${cookie_policy_link}
    Wait Until Element Is Visible    xpath=${cookie_policy_section}
    Scroll Element Into View    xpath=${cookie_policy_section}
    ${actual_cookie_policy_text}    Get Text    xpath=${cookie_policy_section}
    Log    ${actual_cookie_policy_text}
    ${cookie_policy_content}    Get File
    ...    ${EXECDIR}${/}data${/}cookie_policy_content${/}cookie_policy_${language_name}.txt
    Log    ${cookie_policy_content}
    ${are_equal}    Run Keyword And Return Status
    ...    Should Be Equal As Strings
    ...    ${actual_cookie_policy_text}
    ...    ${cookie_policy_content}
    IF    '${are_equal}' == 'False'
        Fail    The content of the cookie policy from cookie setting modal does not match.
    END

Verify Clinic Cookie Policy Content From Cookie Settings Modal
    [Arguments]    ${language_name}
    Try To Click Element    ${cookie_policy_link}
    Wait Until Element Is Visible    ${clinic_cookie_policy_section}
    Scroll Element Into View    ${clinic_cookie_policy_section}
    ${actual_clinic_cookie_policy_text}    Get Text    ${cookie_setting_section}
    Log    ${actual_clinic_cookie_policy_text}
    ${clinic_cookie_policy_content}    Get File
    ...    ${EXECDIR}${/}data${/}cookie_policy_content${/}clinic_cookies_data${/}clinic_cookie_policy_${language_name}.txt
    Log    ${clinic_cookie_policy_content}
    ${are_equal}    Run Keyword And Return Status
    ...    Should Be Equal As Strings
    ...    ${actual_clinic_cookie_policy_text}
    ...    ${clinic_cookie_policy_content}
    IF    '${are_equal}' == 'False'
        Fail    The content of the clinic cookie policy from cookie setting modal does not match.
    END

Verify Cookie Policy Content From Landing Page
    [Arguments]    ${language_name}
    Try To Click Element    ${necessary_cookies_button}
    Wait Until Element Is Visible    ${landing_page_login_button}
    Try To Click Element    ${cookie_policy_button_landing_page}
    Wait Until Element Is Visible    xpath=${cookie_policy_section}
    Scroll Element Into View    xpath=${cookie_policy_section}
    ${actual_cookie_policy_text}    Get Text    xpath=${cookie_policy_section}
    ${cookie_policy_content}    Get File
    ...    ${EXECDIR}${/}data${/}cookie_policy_content${/}cookie_policy_${language_name}.txt
    ${are_equal}    Run Keyword And Return Status
    ...    Should Be Equal As Strings
    ...    ${actual_cookie_policy_text}
    ...    ${cookie_policy_content}
    IF    '${are_equal}' == 'False'
        Fail    The content of the cookie policy from Landing page does not match.
    END

Set Language Dictionary
    &{language_dict}    Create Dictionary
    ...    German=de_DE
    ...    English=en_GB
    ...    Spanish=es_ES
    ...    Finnish=fi_FI
    ...    French=fr_FR
    ...    Italian=it_IT
    ...    Dutch=nl_NL
    ...    Norwegian=no_NO
    ...    Portuguese=pt_PT
    ...    Swedish=sv_FI
    ...    Turkish=tr_TR
    Set Suite Variable    &{language_dict}

Convert Language Selection To File Extension
    [Arguments]    ${language_ext}
    ${extension}    Set Variable If
    ...    '${language_ext}'=='English'    en_GB
    ...    '${language_ext}'=='Finnish'    fi
    ...    '${language_ext}'=='Swedish'    sv
    ...    '${language_ext}'=='Norwegian'    no
    ...    '${language_ext}'=='German'    de
    ...    '${language_ext}'=='Spanish'    es
    ...    '${language_ext}'=='Turkish'    tr
    ...    '${language_ext}'=='Dutch'    nl
    ...    '${language_ext}'=='Italian'    it
    ...    '${language_ext}'=='Portuguese'    pt
    ...    '${language_ext}'=='French'    fr
    Set Suite Variable    ${extension}

Switch Language In Clinic Preferences For Cookie Selection
    [Arguments]    ${language-id}
    Go To Clinic Preferences
    Try To Click Element    xpath=//*[@id="locale-${language-id}"]/../label
    Click Clinic Preferences Save Button
    Try To Click Banner Message

Close Cookie Policy Modal
    Try To Click Element    ${close_cookie_button}

Verify Cookie Policy Content From More Menu For Web
    [Arguments]    ${language_name}
    Wait Until Element Is Visible    xpath=${cookie_policy_section}
    Scroll Element Into View    xpath=${cookie_policy_section}
    ${actual_cookie_policy_text}    Get Text    xpath=${cookie_policy_section}
    Set Suite Variable    ${language_name}
    Convert Language Selection To File Extension    ${language_name}
    ${cookie_policy_content}    Get File
    ...    ${EXECDIR}${/}data${/}cookie_policy_content${/}cookie_policy_${extension}.txt
    ${are_equal}    Run Keyword And Return Status
    ...    Should Be Equal As Strings
    ...    ${actual_cookie_policy_text}
    ...    ${cookie_policy_content}
    IF    '${are_equal}' == 'False'
        Fail    The content of the cookie policy from More menu does not match.
    END

Verify Cookie Policy Content From More Menu For Native
    [Arguments]    ${language_name}
    Wait Until Element Is Visible    xpath=${cookie_policy_section}
    Scroll Element Into View    xpath=${cookie_policy_section}
    ${actual_cookie_policy_text}    Get Text    xpath=${cookie_policy_section}
    Set Suite Variable    ${language_name}
    Convert Language Selection To File Extension    ${language_name}
    ${cookie_policy_content}    Get File
    ...    ${EXECDIR}${/}data${/}cookie_policy_content${/}mobile_cookie_policy_${extension}.txt
    ${are_equal}    Run Keyword And Return Status
    ...    Should Be Equal As Strings
    ...    ${actual_cookie_policy_text}
    ...    ${cookie_policy_content}
    IF    '${are_equal}' == 'False'
        Fail    The content of the cookie policy from More menu does not match.
    END
