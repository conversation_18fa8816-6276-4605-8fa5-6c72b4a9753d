*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}upload_or_remove_photo.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}timeline.resource


*** Variables ***
${treatment_phase}                      //*[@id='select-event-treatment-phase']/div
${event_type}                           //*[@id='select-event-type']/div
${select_event_date_dropdown}           //*[@data-testid='datetime-selector-date']
${calendar}                             //*[@id='datetime-selector-event-date']
${selected date_27}                     //div[contains(@class,'circle')][contains(text(),'27')]
${tomorrow_calendar_date}               //td[@class='day active']/following-sibling::td[1]
${event_location}                       //*[@id='input-event-location']
${event_notes}                          //*[@id='input-event-note']
${event_add_photo}                      //div[@class='photo-uploader dz-clickable']
${event_browse_link}                    //input[@type='file'][2]
${timeline_upcoming_event_header}       upcoming-events-header
${timeline_upcoming_event_details}      //div[@class='upcoming-event__day-wrapper ng-star-inserted']//descendant::div/div[1]
${tomorrow_event}                       //div[contains(@class, "upcoming-event")]//span[contains(text(), "Tomorrow")]
${no_upcoming_events}                   //div[contains(@class, "upcoming-event")]//p[contains(text(), "No upcoming events")]
${personal_label}                       //div[contains(@class, "upcoming-event__content")]//p[contains(text(),"Personal")]
${event_modal_close_button}             //button[@class='close']
${diary_button}                         navigation-diary-link
${event_modal_edit_button}              //*[@id="diary-event-show-modal-button-edit"]//button/div
${save_event_in_diary}                  //p[@class='placeholder-action type--font-weight-bold ng-star-inserted'][last]
${wellness_container}                   //div[@class='view-wellness-container-new']
${event_modal}                          diary-event-show-modal
${add_a_new_event_header}               //h1[contains(text(),'Add a new event')]
${next_month}                           (//th[@class='next'])[1]
${add_event_photo}                      //*[@id='photo-uploader-event-container']
${event_save_button}                    //*[@id='diary-event-modal-button-save']/button
${image_thumbnail}                      //*[@class="dz-image dz-image__loaded"]
${event_date_next_year_button}          (//th[@class='next'])[2]
${event_hour_selector}                  //*[@class='hour-selector']/input
${event_minute_selector}                //*[@class='minute-selector']/input
${event_tile_event_title}               //*[contains(@class, "event-title")]
${event_tile_event_date}                //*[contains(@class, "event-date")]
${event_tile_event_personal_label}      //*[contains(@class, "personal-event")]
${personal_event_diagnosis}             Diagnosis date
${personal_event_clinic_appointment}    Clinic appointment

*** Keywords ***
Select Event From Add Menu
    Click Add Menu Button
    Add Event
    Wait Until Element Is Visible    ${add_a_new_event_header}

Select Treatment Phase
    [Arguments]    ${option}
    Wait Until Page Contains Element    ${treatment_phase}
    Try To Click Element    ${treatment_phase}
    ${selected_treatment}    Set Variable    xpath=(//div[text()="${option}"])[last()]
    Wait Until Page Contains Element    ${selected_treatment}
    Try To Click Element    ${selected_treatment}
    ${treatment_phase_value}    Get Text    ${treatment_phase}
    Set Test Variable    ${TREATMENT_PHASE_VALUE}    ${treatment_phase_value}

Select Event Type
    [Arguments]    ${option}
    Wait Until Page Contains Element    ${event_type}
    Try To Click Element    ${event_type}
    ${selected_event}    Set Variable    xpath=//div[text()="${option}"]
    Wait Until Page Contains Element    ${selected_event}
    Try To Click Element    ${selected_event}
    ${event_type_value}    Get Text    ${event_type}
    Set Test Variable    ${EVENT_TYPE_VALUE}    ${event_type_value}

Select Tomorrow As Event Date
    ${current_date_timestamp}    Get Current Date    exclude_millis=yes
    Set Test Variable    ${current_date_timestamp}
    # Commenting out the below IF condition when running native-app locally. In Gitlab run we need to add +3hrs from UTC timestamp
    IF    'native' in '${ENVIRONMENT}'
        ${expected_date_UTC}    Add Time To Date    ${current_date_timestamp}    1 days    
        ${expected_date_UTC+3}    Add Time To Date    ${expected_date_UTC}    03:00:00    result_format=%d.%m.%Y %H:%M
        ${expected_time_label}    Fetch From Right    ${expected_date_UTC+3}    ${SPACE}
        ${expected_date_label}    Fetch From Left    ${expected_date_UTC+3}    ${SPACE}
    ELSE
        ${expected_date}    Add Time To Date    ${current_date_timestamp}    1 days    result_format=%d.%m.%Y %H:%M
        ${expected_time_label}    Fetch From Right    ${expected_date}    ${SPACE}
        ${expected_date_label}    Fetch From Left    ${expected_date}    ${SPACE}
    END
    Set Test Variable    ${expected_time_label}
    ${expected_date_ddmm}    Add Time To Date    ${current_date_timestamp}    1 days    result_format=%d.%m
    Set Test Variable    ${expected_date_ddmm}
    IF   'native' not in '${ENVIRONMENT}'
        Execute Javascript    document.querySelector("${wellbeing_date_jelement}").removeAttribute('readonly')
    END
    Try To Click Element    ${select_event_date_dropdown}
    Wait Until Element Is Visible    ${calendar}
    Try To Click Element    ${tomorrow_calendar_date}
    Try To Click Element    ${select_event_date_dropdown}
    IF   'native' not in '${ENVIRONMENT}'
        # The Get Value is working in app only. This part is comparing the actual selected date with the expected date
        ${actual_event_date}    Get Value    ${select_event_date_dropdown}
        Set Test Variable    ${actual_event_date}
        Set Test Variable    ${expected_date_ddmm}
        ${actual_date}    Fetch From Left    ${actual_event_date}    ${SPACE}
        ${selected_time}    Fetch From Right    ${actual_event_date}    ${SPACE}
        Set Test Variable    ${selected_time}
        Should Be Equal    ${actual_date}    ${expected_date_label}
    END
    # TODO: Selection of calendar date/future date
    # Selection of time
    # Conversion of time to UI format with AM/PM

Select Event Date
    [Arguments]    ${full_date}
    ${abbv_month}    Convert Date    ${full_date}    result_format=%b
    ${expected_year}    Convert Date    ${full_date}    result_format=%Y
    ${date}    Convert Date    ${full_date}    result_format=%-d
    Wait Until Element Is Visible    ${select_event_date_dropdown}
    Click Element    ${select_event_date_dropdown}
    Wait Until Element Is Visible    ${calendar}
    Try To Click Element    xpath=(//*[@class='datepicker-switch'])[1]    # monthyear
    ${year_text}    Get Text    xpath=(//*[@class='datepicker-switch'])[2]    # year
    IF    '${year_text}'!='${expected_year}'
        Try To Click Element    ${event_date_next_year_button}    # only checks until next year
    END
    Try To Click Element    xpath=//span[text()="${abbv_month}"]
    Try To Click Element    xpath=//*[@class="day"]//div[text()="${date}"]

Add Event Location
    [Arguments]    ${location}
    Wait Until Element Is Visible    ${event_location}
    Input Text    ${event_location}    ${location}
    Set Test Variable    ${LOCATION}    ${location}

Add Event Notes
    [Arguments]    ${notes}
    Wait Until Element Is Visible    ${event_notes}
    Input Text    ${event_notes}    ${notes}
    Set Test Variable    ${NOTES}    ${notes}

Save Event
    Wait Until Element Is Visible    ${event_save_button}
    Try To Click Element    ${event_save_button}
    Wait Until Page Does Not Contain Element    ${event_save_button}

Event Is Saved In Diary And User Is Directed To Diary
    Wait Until Element Is Visible    ${timeline_upcoming_event_header}
    ${expected_date}    Get Current Date    exclude_millis=yes    result_format=%a %e.+%m+%B %Y
    ${day_date}    ${month}    ${month_year}    Split String    ${expected_date}    +
    ${month}    Convert To Integer    ${month}
    ${day}    Execute Javascript    return document.getElementsByClassName('date-name')[0].innerText;
    ${date}    Execute Javascript    return document.getElementsByClassName('date')[0].innerText;
    ${actual_month_year}    Execute Javascript
    ...    return document.getElementsByClassName('timeline-month-title type--heading-6')[0].innerText;
    Should Be Equal    ${day_date}${month}.    ${day}${SPACE}${date}
    Should Be Equal    ${actual_month_year}    ${month_year}

Open Selected Event
    ${no_upcoming_event_present}    Run Keyword And Return Status
    ...    Page Should Contain Element
    ...    ${no_upcoming_events}
    IF    ${no_upcoming_event_present}
        Try To Click Element    ${save_event_in_diary}
    ELSE
        ${show_all_button_present}    Run Keyword And Return Status
        ...    Wait Until Page Contains Element
        ...    ${show_all_events}
        IF    '${show_all_button_present}'=='True'
            Try To Click Element    ${show_all_events}
            Sleep    1
        END
        Scroll Element Into View
        ...    //*[@class="upcoming-event__content"]//p[2]//time[contains(text(),"${expected_time_label}")]
        Try To Click Element    //*[@class="upcoming-event__content"]//p[2]//time[contains(text(),"${expected_time_label}")]
    END
    Wait Until Element Is Visible    ${event_modal_close_button}

Verify Event Details
    [Arguments]    ${date}
    Text Should Be In The Page    ${EVENT_TYPE_VALUE}
    Text Should Be In The Page    ${date}
    Text Should Be In The Page    ${TREATMENT_PHASE_VALUE}
    Text Should Be In The Page    ${LOCATION}
    Text Should Be In The Page    ${NOTES}
    Wait Until Element Is Visible    ${event_modal_close_button}
    # New events on the same day and same time is displayed in Upcoming events
    # After 15 mins the event on the same date will be displayed in Diary Timeline
    # TODO: Add Future Event Date
    # If the event date is in the future, the event is displayed in Upcoming events
    # If the event date is in the past, the event is displayed in the diary timeline at a corresponding date

Event Is Not Visible To Clinic Users
    [Arguments]    ${ssn}
    IF    'native' not in '${ENVIRONMENT}'
        Login As Nurse    ${appointment_clinic}[user_email]
        Search Patient By Identity Code    ${ssn}
        Navigate To Timeline Tab
        Wait Until Page Contains Element    ${wellness_container}
        Page Should Not Contain    ${actual_timeline_event}
    END

Select Patient
    Search Patient By Identity Code    ${patient_ssn}

Add Event Photo
    [Arguments]    ${file}    ${update}=no
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Element Is Visible    ${add_event_photo}
        # TODO: Photo upload on native app
    ELSE
        IF    '${update}'=='no'    Try To Click Element    ${add_event_photo}
        IF    '${update}'=='yes'
            ${index}    Set Variable    2
        ELSE
            ${index}    Set Variable    1
        END
        Choose File    //input[@type='file'][${index}]    ${EXECDIR}${/}data${/}upload_photos${/}JPG${/}${file}
        Scroll Element Into View    ${event_save_button}
        Wait Until Element Is Visible    ${image_thumbnail}    60s
    END
