*** Settings ***
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}diary_note_wellbeing.resource


*** Variables ***
${diary}                    Diary
${diary_button}             //*[@id='navigation-diary-link']/button
${clinic_button}            //*[@id='navigation-clinic-link']/button
${add_menu_button}          navigation-add-link
${event_button}             //*[@id='navigation-add-menu-new-event']/button
${add_event_modal}          //*[@id='diary-event-modal']
${symptom_entry}            xpath=//*[@id='navigation-add-menu-new-symptom']/button
${symptom_entry_modal}      xpath=//*[@id='wizard']
${diary_note_button}        xpath=//*[@id='navigation-add-menu-new-entry']


*** Keywords ***
Go To Diary
    Try To Click Element    ${diary_button}

Go To Clinic
    Try To Click Element    ${clinic_button}

Click Add Menu Button
    Try To Click Element    xpath=//*[@id='${add_menu_button}']/button

Add Event
    Try To Click Element    ${event_button}
    Wait Until Page Contains Element    ${add_event_modal}

Add Symptom Entry
    Try To Click Element    ${symptom_entry}

Add Diary Note
    Try To Click Element    ${diary_note_button}
    Wait Until Page Contains Element    ${diary_entry_modal}

Verifiy Add A Symptom Button Visibility
    [Arguments]    ${visible}
    Click Add Menu Button
    IF    '${visible}'=='Yes'
        Wait Until Element Is Visible    ${symptom_entry}    timeout=3s
    ELSE
        Sleep    1
        Wait Until Element Is Not Visible    ${symptom_entry}    timeout=3s
    END
