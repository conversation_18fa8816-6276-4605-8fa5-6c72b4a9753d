*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource


*** Variables ***
${privacy_statement_content_container}    //*[@class="showdown-content"]
${menu_more_button}             xpath=//*[@id="navigation-more-link"]/button
${privacy_link}                 xpath=//*[@id="more-link-privacy"]
${register_data_file_link}      more-link-register-data-file
${close_privacy_button}         //*[@id="privacy-policy-modal"]//button
${close_register_button}        //*[@id="register-modal"]//button
${privacy_button_front_page}    //*[@data-testid="privacy-policy"]
${enter_credentials_note}       Please enter your email address and password.
${upcoming_events_text}         Upcoming events
${noona_hompage_text}           Noona mobile service
${shs_list_of_companies_table}    //*[@class="showdown-content"]//table


*** Keywords ***
Select Privacy Notice From More Menu
    Wait Until Page Contains Element    ${menu_more_button}
    Try To Click Element    ${menu_more_button}
    Wait Until Page Contains Element    ${privacy_link}
    Try To Click Element    ${privacy_link}

Verify Privacy Statement Content
    [Documentation]    This keyword is used to verify content of privacy statement in different languages
    [Arguments]    ${name}
    ${content}    Get File    ${EXECDIR}${/}data${/}privacy_content${/}${name}.txt
    @{content_lines}    Split To Lines    ${content}
    Sleep    1s
    IF    'Europe' in '${name}' or 'PL' in '${name}'
        FOR    ${line}    IN    @{content_lines}
            Element Should Contain    ${privacy_statement_content_container}    ${line}
        END
    ELSE
        FOR    ${line}    IN    @{content_lines}
            Page Should Contain    ${line}
        END
    END


Open Privacy Notice From Front Page
    IF    'native' in '${ENVIRONMENT}'
        Click Compliance Button
        ${contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        Wait Until Element Is Visible    ${privacy_button}
        Click Element    ${privacy_button}
        Wait Until Element Is Visible    ${privacy_about_close_button}
        Switch To Context    ${contexts}[1]
    ELSE
        Sleep    1
        Wait Until Page Contains Element    ${privacy_button_front_page}
        Try To Click Element    ${privacy_button_front_page}
    END

Close Privacy Page And Return To Main Menu
    Wait Until Page Contains Element    ${close_privacy_button}
    Try To Click Element    ${close_privacy_button}
    Wait Until Page Contains Element    ${menu_more_button}

Close Privacy Page And Return To Front Page
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    ${contexts}[1]
    END
    Wait Until Page Contains Element    ${close_privacy_button}
    Try To Click Element    ${close_privacy_button}
    Generic: Wait Until Element Is Not Visible    ${close_privacy_button}

Verify That Content Displayed Is Correct
    IF    'native' in '${ENVIRONMENT}'
        Verify Privacy Text Is Displayed    ${EXECDIR}${/}resources${/}native_app${/}native_privacy_labelling.txt
    ELSE
        Verify Privacy Statement Content    PrivacyPolicy_en_GB
    END

Verify Privacy Text Is Displayed
    [Arguments]    ${source_file_path}
    ${content}    Get File    ${source_file_path}
    @{content_lines}    Split To Lines    ${content}
    FOR    ${line}    IN    @{content_lines}
        Wait Until Page Contains    ${line}
    END

Verify That Europe Privacy Statement Content Is Correct
    IF    'native' in '${ENVIRONMENT}'
        Verify Privacy Text Is Displayed    ${EXECDIR}${/}data${/}privacy_content${/}Europe_Noona_PrivacyStatement_EN_GB.txt
    ELSE
        Verify Privacy Statement Content    Europe_Noona_PrivacyStatement_EN_GB
    END
    Verify List Of European Companies Are Correct

Verify List Of European Companies Are Correct
    Element Text Should Be    ${shs_list_of_companies_table}//thead//tr/th[1]    Country
    Element Text Should Be    ${shs_list_of_companies_table}//thead//tr/th[2]    Company
    # Loop through list of table rows
    &{list_of_companies_in_table}    Create Dictionary
    FOR    ${row}    IN RANGE    1   15
        ${country_name}    Get Text    ${shs_list_of_companies_table}//tbody//tr[${row}]//td[1]
        ${company_address}   Get Text    ${shs_list_of_companies_table}//tbody//tr[${row}]//td[2]
        Set To Dictionary    ${list_of_companies_in_table}    ${country_name}=${company_address}
    END
    Log    ${list_of_companies_in_table}
    ${companies_list_source_json}    Get File    ${EXECDIR}${/}data${/}privacy_content${/}list_of_companies.json
    ${expected_companies_dictionary}=    Evaluate    json.loads('''${companies_list_source_json}''')    json
    Dictionaries Should Be Equal    ${list_of_companies_in_table}    ${expected_companies_dictionary}

Verify About Text Is Displayed
    IF    'staging' in '${ENVIRONMENT}'
        ${content}    Get File    ${EXECDIR}${/}resources${/}native_app${/}native_labelling_staging.txt
    ELSE
        ${content}    Get File    ${EXECDIR}${/}resources${/}native_app${/}native_labelling.txt
    END
    @{content_lines}    Split To Lines    ${content}
    FOR    ${line}    IN    @{content_lines}
        Wait Until Page Contains    ${line}
    END
