*** Settings ***
Resource            ${EXECDIR}${/}resources${/}try_keywords.resource
Resource            ${EXECDIR}${/}resources${/}mailosaur.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource            ${EXECDIR}${/}resources${/}nurse${/}patient_management.resource
Resource            ${EXECDIR}${/}resources${/}test_data${/}API_test_data.resource
Resource            ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource



*** Variables ***
${update_password_page_header}              //div/*[contains(text(), 'Update Password')]
${update_password_paragraph_text}           //p[contains(text(), 'Your password has expired. Please update it.')]
${input_current_password_field}             //input[@data-testid='input-current-password']
${input_new_password_field}                 //input[@data-testid='input-new-password']
${input_confirm_password_field}             //input[@data-testid='input-confirm-password']
${show_hide_current_password_button}        //button[@data-testid='show-hide-current-password-button']
${show_hide_new_password_button}            //button[@data-testid='show-hide-new-password-button']
${show_hide_confirm_password_button}        //button[@data-testid='show-hide-confirm-password-button']
${forgot_password_button}                   //button/div/div[contains(text(), 'FORGOT PASSWORD?')]
${password_criteria_one}                    //span[contains(text(), 'At least 8 characters')]
${password_criteria_two}                    //span[contains(text(), 'At least one lowercase letter')]
${password_criteria_three}                  //span[contains(text(), 'At least one capital letter')]
${password_criteria_four}                   //span[contains(text(), 'At least one number')]
${password_criteria_five}                   //span[contains(text(), 'At least one special character (.!,:;?%/*+-$()@#)')]
${password_criteria_six}                    //span[contains(text(), 'At least one character should be different between the new and old passwords')]
${update_password_button}                   //div/ds-button[@data-testid='update-password-next-button']
${show_password_text}                       SHOW
${hide_password_text}                       HIDE
${validation_criteria_check}                //*[contains(@class, 'check-mark')]
${password_update_success_modal}            //div[@data-testid='password-update-success-modal']
${close_button_password_update_modal}       //ds-button[@data-testid='password-update-success-close-button']
${diary_header}                             //h1[@id="header-page-title" and text()="Diary "]


*** Keywords ***
Check That Update Password Page Is Displayed
    Wait Until Element Is Visible    ${update_password_page_header}
    Wait Until Element Is Visible    ${update_password_paragraph_text}
    Wait Until Element Is Visible    ${input_current_password_field}
    Wait Until Element Is Visible    ${input_new_password_field}
    Wait Until Element Is Visible    ${input_confirm_password_field}
    Wait Until Element Is Visible    ${show_hide_current_password_button}
    Wait Until Element Is Visible    ${show_hide_new_password_button}
    Wait Until Element Is Visible    ${show_hide_confirm_password_button}
    Wait Until Element Is Visible    ${forgot_password_button}
    Wait Until Element Is Visible    ${password_criteria_one}
    Wait Until Element Is Visible    ${password_criteria_two}
    Wait Until Element Is Visible    ${password_criteria_three}
    Wait Until Element Is Visible    ${password_criteria_four}
    Wait Until Element Is Visible    ${password_criteria_five}
    Wait Until Element Is Visible    ${password_criteria_six}
    Wait Until Element Is Visible    ${update_password_button}

Check Password Visibility Functionality For
    [Arguments]    ${button}
    ${password_display}    Get Element Attribute    ${button}    innerText
    Should Be Equal As Strings    ${password_display}    ${show_password_text}
    Try To Click Element    ${button}
    ${password_display}    Get Element Attribute    ${button}    innerText
    Should Be Equal As Strings    ${password_display}    ${hide_password_text}

Check NEXT button status
    [Arguments]    ${status}=disabled
    ${button_status}    Get Element Attribute    ${update_password_button}    class
    IF    '${status}' == 'disabled'
        Should Contain    ${button_status}    disabled
    ELSE
        Should Not Contain    ${button_status}    disabled
    END

Check New Password Match Criterias
    ${validation_count}    Get Element Count    ${validation_criteria_check}
    Should Be Equal As Integers    ${validation_count}    6

Add Valid Current Password
    Try To Input Text    ${input_current_password_field}    ${DEFAULT_PASSWORD}
    Check Password Visibility Functionality For    ${show_hide_current_password_button}
    Check NEXT button status    disabled

Add Valid New Password
    ${random_number}    Generate Random String    3    chars=[NUMBERS]
    ${new_password}    CATENATE    SEPARATOR=    Pass-    ${random_number}
    Try To Input Text    ${input_new_password_field}    ${new_password}
    Check Password Visibility Functionality For    ${show_hide_new_password_button}
    Check New Password Match Criterias
    Check NEXT button status    disabled
    Set Test Variable    ${new_password}

Add Valid Confirmed Password
    Try To Input Text    ${input_confirm_password_field}    ${new_password}
    Check Password Visibility Functionality For    ${show_hide_confirm_password_button}
    Check NEXT button status    enabled

Update Password From Login
    Try To Click Element    ${update_password_button}
    Wait Until Element Is Visible    ${password_update_success_modal}
    Try To Click Element    ${close_button_password_update_modal}
    Wait Until Element Is Visible    ${diary_header}

Update Password From Message
    Try To Click Element    ${update_password_button}
    Wait Until Element Is Visible    ${password_update_success_modal}
    Try To Click Element    ${close_button_password_update_modal}
    Wait Until Element Is Visible    ${clinic_header}

Change Password As A Nurse
    Login As Nurse    ${ta_pasword_expiry}[clinic_user_email]    ${ta_pasword_expiry}[name]
    Search Patient By Identity Code    ${patient_expired_password_one}[ssn]
    Choose General Information Tab
    Change Patient Password    ${DEFAULT_PASSWORD}
