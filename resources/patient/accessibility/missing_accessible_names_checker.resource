*** Settings ***
Library    SeleniumLibrary
Library    Collections
Library    JSONLibrary

*** Variables ***
${CONTAINER_ID}    #container-id           # Leave blank to scan the whole page
${JS_PATH}    ${EXECDIR}${/}data${/}accessibility_tools${/}missing_accessible_names_checker.js
${REPORT_FILE}     ${EXECDIR}${/}accessibility_tools${/}json_report_output${/}accessibility-report.json
${SCREENSHOT_FILE}    ${EXECDIR}${/}accessibility_tools${/}json_report_output${/}accessibility-screenshot.png

*** Keywords ***
Check Missing Accessible Names On Page
    Set Selenium Timeout    60s
    ${script}=    Get File    ${JS_PATH}
    ${raw}=    Execute JavaScript    window.prompt = () => "${CONTAINER_ID}"; ${script}
    Log Many    ${raw}

    # Convert result to JSON string manually
    ${json}=    Evaluate    json.dumps(${raw})    modules=json
    Create File    ${REPORT_FILE}    ${json}
    Log    Accessibility report saved to ${REPORT_FILE}

    # Capture screenshot if issues exist
    Run Keyword If    ${raw}    Sleep    1s
    Run Keyword If    ${raw}    Capture Page Screenshot    ${SCREENSHOT_FILE}

    # Optional: Fail if warnings exist
    Run Keyword If    ${raw}    Fail    Accessibility issues found:\n${json}

    [Teardown]    Close Browser