*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}native_app${/}app_library.resource
Library     ${EXECDIR}${/}resources${/}libraries${/}verify_downloads.py


*** Variables ***
${library_button}                               //*[@id='navigation-library-link']/button
${discover_library_header}                      //h1[contains(text(),'All your information, in one place')]
${medical_records_banner}                       //*[@id='library-intro-medical-records']//button
${medical_records_header}                       //h1[contains(text(),'Medical records')]
${select_all_records}                           //*[@for='ccd-alldata']
${select_between_dates}                         //input[@id='ccd-daterange']
${view_records_button}                          //nh-medical-records//button//*[contains(text(), "View")]
${view_records_modal}                           //nh-medical-records-view-modal/div/h5[contains(text(),"All records")]
${ccd_record_1}                                 //nh-medical-records-view-modal/div[contains(@class,"checkbox no-checked")][1]
${ccd_record_10}                                //nh-medical-records-view-modal/div[contains(@class,"checkbox no-checked")][10]
${ccd_record_modal}                             //nh-medical-records-summary/div/div/div[@id="documentheader"]
${ccd_record_list_close_button}                 //nh-medical-records-view-modal/div/h5[contains(text(),"All records")]/../../div/ds-button/button
${ccd_record_modal_close_button}                //nh-medical-records-summary/div/div/div[@id="documentheader"]/../../../../div/ds-button/button
${download_button}                              //nh-medical-records//button//*[contains(text(), "Download")]
${download_records_modal}                       //nh-medical-records-download-modal/h1[contains(text(),"Download Records")]
${ccd_records}                                  //*[contains(@for,"ccd-selection")]
${list_ccd_record_1}                            //*[@for="ccd-selection-0"]
${list_ccd_record_10}                           //*[@for="ccd-selection-9"]
${download_preview_ccd_record_1}
...                                             //nh-medical-records-download-modal/div/form/div/ds-button/button/div/div[contains(text(),"Preview")][1]
${download_preview_ccd_record_10}
...                                             //nh-medical-records-download-modal/div/form/div/ds-button/button/div/div[contains(text(),"Preview")][last()]
${ccd_preview_modal_close_button}               //nh-medical-records-download-modal/div/ds-button/button
${download_checkbox_ccd_record_1}               //label[contains(text(),'01.10.2020')]
${download_records_button}                      //div[contains(text(),'Download Records')]
${download_records_modal_password}              //h1[@id='dialog-title']
${download_modal_close_button}                  //div[contains(text(),'Close')]
${send_button}                                  //nh-medical-records//button//*[contains(text(), "Send")]
${send_records_modal}                           //nh-medical-records-send-modal
${send_preview_ccd_record_1}                    //nh-medical-records-send-modal/form/div/ds-button/button[1]
${send_preview_ccd_record_11}
...                                             //nh-medical-records-send-modal/form/div/ds-button/button[12]
${input_email}                                  //input[@id='ccd-email-recipient']
${send_medical_records_button}                  //button/div/div[contains(text(),'Send medical records')]
${send_records_modal_password}                  //*[@id="email-ccd-modal"]/div[2]/div
${send_modal_close_button}                      //div[contains(text(),'Close')]
${medical-string}                               Medication
${open_access_history_button}
...                                             //nh-medical-records//button//*[contains(text(), "Open access history")]
${medical_history_modal}                        //nh-medical-records-access-history-modal/h1[contains(text(),"Medical records access history")]
${medical_history_dialog_title}                 //*[@id="dialog-title"]
${medical_history_date}                         //div[contains(text(),'Date')]
${medical_history_name}                         //div[contains(text(),'Name')]
${medical_history_action}                       //div[contains(text(),'Action')]
${medical_history_content}                      //div[contains(text(),'Content')]
${lab_reports_banner}                           //*[@data-testid="library-lab-results"]//button
${lab_results_page_heading}                     //*[@id="header-page-title" and contains(text(), "Laboratory")]
${lab_report_title}                             //*[@id='lab-results-report-0-name']
${lab_results_heading_date}                     //p[@id='lab-results-report-0-date']
${lab_history_dropdown}                         //ng-select[@id='select-lab-results-dates']/div
${lab_history_dropdown_value}                   //*[@id="select-lab-results-dates-option-0"]//child::span[1]
${education_banner}                             //*[@id="intro-patient-education"]
${education_header}                             //*[@id="header-page-title"]
${education_documents_header}                   //*[@id="education-main-heading"]
${education_documents_header_texts}
...                                             //p[contains(text(),'All the useful links and documents your clinic sen')]
${education_documents_link_in_library}          //*[@id="intro-patient-education"]//button
${header_back_button}                           //*[@id="button-header-back"]/button/div
${education_document_modal}                     //*[@data-testid="message-attachment-actions"]
${education_add_bookmark}                       //*[@data-testid="add-bookmark"]
${education_icon_bookmarked}                    //*[contains(@class, 'html-bookmarked')]
${education_remove_bookmark}                    //*[@data-testid="remove-bookmark"]
${education_open_doc}                           //*[@data-testid="externalLink"]
${library_attached_doc}                         //*[@class="open-attachment"]/div
${see_original_message_button}                  //*[@data-testid="openMessage"]
${first_three_dot_icon}                         (//*[@name="action-menu"])[1]
${document_title}                               (//div[@class='title'])[{}]
${document_date}                                (//time[@class='date date-desktop ng-star-inserted'])[{}]
${document_date_native}                         (//time)[{}]
${lab_contact_clinic_button}                    //*[@id='lab-result-contact-clinic-button']//button
${contact_clinic_lab_report_title}              //h4[@class='modal-title']
${contact_clinic_text_area}                     //*[@id='question-content']
${lab_report_contact_clinic_send_button}        //*[@data-testid='send-button']/button
${lab_report_contact_clinic_close_button}       //*[@data-testid='close-button']/button
${access_history_table}                         //table[contains(@class, 'history-table')]
${access_history_table_cell_content}            //table[contains(@class,'history-table')]/tbody/tr[1]
${manage_medical_records}                       //*[@data-testid='medical-record-external-link-button']
${delegate_last_login_element}                  //p[contains(@class, 'delegate-user-last-login') and contains(@class, 'label')]
${empty_library_header}                         //h1[@id='tutorial-display-header-tutorialLibraryComingup']


*** Keywords ***
Navigate Back Using Header
    IF    'native' in '${ENVIRONMENT}'
        Try To Click Native App Element    //*[@id="button-header-back"]/button/div
    ELSE
        Try To Click Element    ${header_back_button}
    END

Go To Education Documents
    IF    'native' in '${ENVIRONMENT}'
        Try To Click Native App Element    ${education_documents_link_in_library}
        Wait Until Element Is Visible    ${education_documents_header}
    ELSE
        Try To Click Element    ${education_documents_link_in_library}
        Wait Until Element Is Visible    ${education_documents_header}
        Wait Until Element Is Visible    ${education_documents_header_texts}
    END

Go To Library
    IF    'native' in '${ENVIRONMENT}'
        Navigate To Library
    ELSE
        Try To Click Element    ${library_button}
        Wait Until Element Is Visible    ${discover_library_header}
    END

Go To Empty Library
    [Documentation]    For Web only, clinic settings should set Disabled for all library features
    Try To Click Element    ${library_button}
    Wait Until Element Is Visible    ${empty_library_header}

Click Medical Records Intro
    Wait Until Element Is Visible    ${medical_records_banner}
    Try To Click Element    ${medical_records_banner}
    Wait Until Element Is Visible    ${medical_records_header}

Select All Records
    Wait Until Page Contains Element    ${select_all_records}
    ${all_record_status}    Get Element Attribute    ${select_all_records}    checked
    IF    '${all_record_status}' != 'true'
        Try To Click Element    ${select_all_records}
    END
    # TODO: Select Records Between Specific Dates
    # User needs to select start and end dates

###---Medical Records---###

Click View Patient Records
    Try To Click Element    ${view_records_button}
    Wait Until Page Contains Element    ${view_records_modal}

Display CCD Record List
    Wait Until Element Is Visible    ${ccd_record_1}
    ${medical_record_list}    Get Webelements    ${ccd_records}
    ${medical_record_1}    Get Text    ${medical_record_list}[0]
    Set Suite Variable    ${MEDICAL_RECORD_1}    ${medical_record_1}
    Wait Until Page Contains Element    ${ccd_record_10}
    ${medical_record_2}    Get Text    ${medical_record_list}[1]
    Set Suite Variable    ${MEDICAL_RECORD_2}    ${medical_record_2}
    ${medical_record_3}    Get Text    ${medical_record_list}[2]
    Set Suite Variable    ${MEDICAL_RECORD_3}    ${medical_record_3}
    ${medical_record_4}    Get Text    ${medical_record_list}[3]
    Set Suite Variable    ${MEDICAL_RECORD_4}    ${medical_record_4}
    ${medical_record_5}    Get Text    ${medical_record_list}[4]
    Set Suite Variable    ${MEDICAL_RECORD_5}    ${medical_record_5}
    ${medical_record_6}    Get Text    ${medical_record_list}[5]
    Set Suite Variable    ${MEDICAL_RECORD_6}    ${medical_record_6}
    ${medical_record_7}    Get Text    ${medical_record_list}[6]
    Set Suite Variable    ${MEDICAL_RECORD_7}    ${medical_record_7}
    ${medical_record_8}    Get Text    ${medical_record_list}[7]
    Set Suite Variable    ${MEDICAL_RECORD_8}    ${medical_record_8}
    ${medical_record_9}    Get Text    ${medical_record_list}[8]
    Set Suite Variable    ${MEDICAL_RECORD_9}    ${medical_record_9}
    ${medical_record_10}    Get Text    ${medical_record_list}[9]
    Set Suite Variable    ${MEDICAL_RECORD_10}    ${medical_record_10}
    ${all_medical_records}    Catenate    ${MEDICAL_RECORD_1},
    ...    ${MEDICAL_RECORD_2},
    ...    ${MEDICAL_RECORD_3},
    ...    ${MEDICAL_RECORD_4},
    ...    ${MEDICAL_RECORD_5},
    ...    ${MEDICAL_RECORD_6},
    ...    ${MEDICAL_RECORD_7},
    ...    ${MEDICAL_RECORD_8},
    ...    ${MEDICAL_RECORD_9},
    ...    ${MEDICAL_RECORD_10}
    Set Suite Variable    ${ALL_MEDICAL_RECORDS}    ${all_medical_records}

Click CCD Record
    ${current_date}    Get Current Date
    IF    'native' not in '${ENVIRONMENT}'
        ${expected_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y %H:%M
        ${date_plus_one_minute}    Add Time To Date    ${current_date}    1 minute    exclude_millis=yes    result_format=%d.%m.%Y %H:%M
        Set Suite Variable    ${expected_date}    ${EXPECTED_DATE}
        Set Suite Variable    ${date_plus_one_minute}    ${DATE_PLUS_ONE_MINUTE}
    ELSE
#        # Local run steps to be commented when running in gitlab
#        ${expected_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y %H:%M
#        ${date_plus_two_minutes}   Add Time To Date    ${current_date}    2 minutes    exclude_millis=yes    result_format=%d.%m.%Y %H:%M
        # Gitlab run steps to be commented when running locally
        ${expected_date}    Add Time To Date    ${current_date}    3 hours    exclude_millis=yes    result_format=%d.%m.%Y %H:%M    #2 hours when it's not dst
        ${date_plus_two_minutes}   Add Time To Date    ${current_date}    2 hours 58 minutes    exclude_millis=yes    result_format=%d.%m.%Y %H:%M
        # Setting Suite variables
        Set Suite Variable    ${expected_date}    ${EXPECTED_DATE}
        Set Suite Variable    ${date_plus_two_minutes}    ${DATE_PLUS_TWO_MINUTES}
    END
    Try To Click Element    ${list_ccd_record_1}
    Wait Until Page Contains Element    ${ccd_record_modal}

Close CCD Record Modal
    Scroll Element Into View    ${ccd_record_modal_close_button}
    Try To Click Element    ${ccd_record_modal_close_button}
    Wait Until Element Is Visible    ${view_records_modal}

Close CCD Record List Modal
    Try To Click Element    ${ccd_record_list_close_button}
    Wait Until Element Is Visible    ${medical_records_header}

Close CCD Record Modals
    Close CCD Record Modal
    Close CCD Record List Modal

Click Download Patient Records
    Try To Click Element    ${download_button}
    Wait Until Page Contains Element    ${download_records_modal}

Display All CCD Record To Download
    Wait Until Page Contains Element    ${list_ccd_record_1}
    Wait Until Page Contains Element    ${list_ccd_record_10}
    ${list_record_1}    Execute Javascript    return document.getElementsByClassName('checkbox')[0].innerText;
    ${list_record_1_date}    Get Substring    ${list_record_1}    0    10
    Set Suite Variable    ${download_record_1}    ${list_record_1_date}
    ${list_record_2}    Execute Javascript    return document.getElementsByClassName('checkbox')[1].innerText;
    ${list_record_2_date}    Get Substring    ${list_record_2}    0    10
    Set Suite Variable    ${download_record_2}    ${list_record_2_date}
    ${list_record_3}    Execute Javascript    return document.getElementsByClassName('checkbox')[2].innerText;
    ${list_record_3_date}    Get Substring    ${list_record_3}    0    10
    Set Suite Variable    ${download_record_3}    ${list_record_3_date}
    ${list_record_4}    Execute Javascript    return document.getElementsByClassName('checkbox')[3].innerText;
    ${list_record_4_date}    Get Substring    ${list_record_4}    0    10
    Set Suite Variable    ${download_record_4}    ${list_record_4_date}
    ${list_record_5}    Execute Javascript    return document.getElementsByClassName('checkbox')[4].innerText;
    ${list_record_5_date}    Get Substring    ${list_record_5}    0    10
    Set Suite Variable    ${download_record_5}    ${list_record_5_date}
    ${list_record_6}    Execute Javascript    return document.getElementsByClassName('checkbox')[5].innerText;
    ${list_record_6_date}    Get Substring    ${list_record_6}    0    10
    Set Suite Variable    ${download_record_6}    ${list_record_6_date}
    ${list_record_7}    Execute Javascript    return document.getElementsByClassName('checkbox')[6].innerText;
    ${list_record_7_date}    Get Substring    ${list_record_7}    0    10
    Set Suite Variable    ${download_record_7}    ${list_record_7_date}
    ${list_record_8}    Execute Javascript    return document.getElementsByClassName('checkbox')[7].innerText;
    ${list_record_8_date}    Get Substring    ${list_record_8}    0    10
    Set Suite Variable    ${download_record_8}    ${list_record_8_date}
    ${list_record_9}    Execute Javascript    return document.getElementsByClassName('checkbox')[8].innerText;
    ${list_record_9_date}    Get Substring    ${list_record_9}    0    10
    Set Suite Variable    ${download_record_9}    ${list_record_9_date}
    ${list_record_10}    Execute Javascript    return document.getElementsByClassName('checkbox')[9].innerText;
    ${list_record_10_date}    Get Substring    ${list_record_10}    0    10
    Set Suite Variable    ${download_record_10}    ${list_record_10_date}
    ${all_download_medical_records}    Catenate    ${download_record_1},
    ...    ${download_record_2},
    ...    ${download_record_3},
    ...    ${download_record_4},
    ...    ${download_record_5},
    ...    ${download_record6},
    ...    ${download_record_7},
    ...    ${download_record_8},
    ...    ${download_record_9},
    ...    ${download_record_10}
    Set Suite Variable    ${ALL_DOWNLOAD_MEDICAL_RECORDS}    ${all_download_medical_records}

Preview Record Before Download
    Try To Click Element    ${download_preview_ccd_record_1}
    Try To Click Element    ${ccd_preview_modal_close_button}
    Try To Click Element    ${download_preview_ccd_record_10}
    Try To Click Element    ${ccd_preview_modal_close_button}

Download All Records
    Wait until keyword succeeds    3x    1s    Scroll Element Into View    ${download_records_button}
    Try To Click Element    ${download_records_button}
    ${expected_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y %H:%M
    ${get_a_minute_more}    Get Current Date
    ${date_plus_one_minute}    Add Time To Date    ${get_a_minute_more}    1 minute    result_format=%d.%m.%Y %H:%M
    Set Suite Variable    ${EXPECTED_DATE}
    Set Suite Variable    ${DATE_PLUS_ONE_MINUTE}
    ${progress_bar_status}    Run Keyword And Return Status    Wait Until Page Contains    Generating files, please wait
    Set Test Variable    ${progress_bar_status}
    IF    ${progress_bar_status} == True
        Wait Until Page Does Not Contain    Generating files, please wait
    END
    Wait Until Page Contains Element    ${download_records_modal_password}
    ${ccd_download_zip_password}    Execute Javascript
    ...    return document.getElementsByClassName('ccd-password-content ng-star-inserted')[0].innerText;
    Set Suite Variable    ${ccd_download_zip_password}
    Wait Until Page Contains Element    ${download_modal_close_button}
    Try To Click Element    ${download_modal_close_button}

Dowloaded ZIP Contains HTML File
    Wait Until Keyword Succeeds    ${download-wait}    3    Directory Should Exist    ${downloads-dir}
    Wait Until Keyword Succeeds    ${download-wait}    3    File Should Exist    ${downloads-dir}${/}*.zip
    Medical Records Download Should Contain HTML
    ...    ${downloads-dir}
    ...    ${medical-string}
    ...    ${ccd_download_zip_password}

Click Send Records
    Try To Click Element    ${send_button}
    Wait Until Page Contains Element    ${send_records_modal}

Display All CCD Record To Send
    Wait Until Page Contains Element   ${list_ccd_record_1}
    Wait Until Page Contains Element    ${list_ccd_record_10}

Input Recipient Email
    [Arguments]    ${email}
    Input Text    ${input_email}    ${email}

Click Send Medical Records
    Wait Until Element Is Enabled    ${send_medical_records_button}
    ${expected_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y %H:%M
    Set Suite Variable    ${expected_date}    ${EXPECTED_DATE}
    Try To Click Element    ${send_medical_records_button}
    Wait Until Element Is Visible    ${medical_history_dialog_title}
    Wait Until Element Is Visible    //nh-medical-records-send-modal/div/h4    20s
    Wait Until Element Is Visible    ${send_modal_close_button}
    ${ccd_zip_password}    Execute Javascript
    ...    return document.getElementsByClassName('ccd-password-content ng-star-inserted')[0].innerText;
    Set Suite Variable    ${ccd_zip_password}
    Wait Until Page Contains Element    ${send_modal_close_button}
    Try To Click Element    ${send_modal_close_button}

Click Open Access History
    Try To Click Element    ${open_access_history_button}
    Wait Until Element Is Visible    ${medical_history_modal}
    Wait Until Element Is Visible    ${medical_history_dialog_title}
    Wait Until Element Is Visible    ${access_history_table}

Verify Medical History Details
    [Arguments]    ${email}    ${expected_action}    ${expected_content}
    # Checking history for View and Send medical records
    IF    '${expected_action}' != 'Downloaded medical records'
        IF    'native' not in '${ENVIRONMENT}'
            ${actual_date}       Get Text    xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[1]
            ${actual_name}       Get Text    xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[2]
            ${actual_action}     Get Text    xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[3]
            ${actual_content}    Get Text    xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[4]
            Should Be Equal    ${EXPECTED_DATE}    ${actual_date}
            Should Be Equal    ${email}    ${actual_name}
            Should Be Equal    ${expected_action}    ${actual_action}
            Should Be Equal    ${expected_content}    ${actual_content}
        ELSE
            ${raw_date}          Get Text         xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[1]
            @{parts1}            Split String     ${raw_date}    \n
            ${actual_date}       Get From List    ${parts1}        1
            Should Be Equal      ${EXPECTED_DATE}    ${actual_date}
            ${raw_name}          Get Text         xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[2]
            @{parts2}            Split String     ${raw_name}      \n
            ${actual_name}       Get From List    ${parts2}         1
            Should Be Equal      ${email}         ${actual_name}
            ${raw_action}        Get Text         xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[3]
            @{parts3}            Split String     ${raw_action}    \n
            ${actual_action}     Get From List    ${parts3}         1
            Should Be Equal      ${expected_action}        ${actual_action}
            ${raw_content}       Get Text         xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[4]
            @{parts4}            Split String     ${raw_content}    \n
            ${actual_content}    Get From List    ${parts4}         1
            Should Be Equal      ${expected_content}    ${actual_content}
        END
    ELSE
    # Checking history for Downloading medical records - functionality described in NOONA-23971
        IF    ${progress_bar_status} == True    # code adressing download when progress bar is shown
            ${actual_date}    Get Text    xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[1]
            ${actual_name}    Get Text    xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[2]
            ${actual_action}    Get Text    xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[3]
            ${actual_content}    Get Text    xpath=//table[contains(@class,'history-table')]/tbody/tr[1]/td[4]
            ${status}    Run Keyword And Return Status    Should Be Equal    ${EXPECTED_DATE}    ${actual_date}
            IF    ${status}==${FALSE}
                IF    'native' not in '${ENVIRONMENT}'
                    Should Be Equal    ${actual_date}    ${DATE_PLUS_ONE_MINUTE}
                ELSE
                    Should Be Equal    ${actual_date}    ${DATE_PLUS_TWO_MINUTES}
                END
            END
            Should Be Equal    ${email}    ${actual_name}
            Should Be Equal    ${expected_action}    ${actual_action}
            Should Be Equal    ${expected_content}    ${actual_content}
        ELSE
            # code adressing same download in the next hour, when progress bar is not shown and history is not updated; needs removing after improvement ticket NOONA-23971 is implemented
            Wait Until Page Contains Element    xpath=//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[1]
            ${date_elements_list}    Get Webelements    xpath=//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[1]
            ${actual_date}    Get Text    xpath=(//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[1])[1]
            ${name_elements_list}    Get Webelements    (//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[2])[1]
            ${actual_name}    Get Text    xpath=(//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[2])[1]
            ${action_elements_list}    Get Webelements   xpath=(//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[3])[1]
            ${actual_action}    Get Text    xpath=(//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[3])[1]
            ${actual_content_elements_list}    Get Webelements    xpath=(//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[4])[1]
            ${actual_content}    Get Text    xpath=(//table[contains(@class,'history-table')]//tr[td[3][contains(.,'Downloaded medical records')]]/td[4])[1]
            ${EXPECTED_DATE1}    Convert Date    ${EXPECTED_DATE}    date_format=%d.%m.%Y %H:%M    result_format=%Y.%m.%d %H:%M:%S
            ${actual_date1}    Convert Date    ${actual_date}    date_format=%d.%m.%Y %H:%M    result_format=%Y.%m.%d %H:%M:%S
            ${diff}    Subtract Date From Date    ${EXPECTED_DATE1}    ${actual_date1}    verbose
            Should Not Be Equal    ${diff}    0 minutes
            ${diff_int}    Convert To Integer    ${diff.split()[0]}
            Run Keyword If    ${diff_int} >= 60    Fail    Value Is Out Of Range
            Should Be Equal    ${email}    ${actual_name}
            Should Be Equal    ${expected_action}    ${actual_action}
            Should Be Equal    ${expected_content}    ${actual_content}

        END
    END

###---Laboratory Reports---###

Click Laboratory Reports Intro
    Wait Until Element Is Visible    ${lab_reports_banner}
    Try To Click Element    ${lab_reports_banner}

Lab Report Header And Title Are Visible
    Click Laboratory Reports Intro
    Wait Until Element Is Visible    ${lab_results_page_heading}
    Wait Until Element Is Visible    ${lab_report_title}
    Sleep    2s

Laboratory Reports Intro Is Not Displayed
    Wait Until Page Contains    Medical Records
    Page Should Not Contain Element    ${lab_reports_banner}
    Sleep    2s

Latest Report Date Should Be Available
    Wait Until Page Contains Element    ${lab_results_heading_date}
    Wait Until Element Is Visible    ${lab_results_heading_date}
    ${report_date}    Get Text    ${lab_results_heading_date}
    ${report_date}    Get Substring    ${report_date}    0    10
    Set Global Variable    ${report_date}

    ##TODO:    Verify if date is the same with what is expected in the reference (JSON file)

Latest Date In Lab History Is Selected By Default
    Wait Until Element Is Visible    ${lab_history_dropdown}
    Wait Until Element Is Visible    //span[contains(text(),'${report_date}')]
    Try To Click Element    ${lab_history_dropdown}
    ${latest_dropdown_value}    Format String    ${lab_history_dropdown_value}    index=1
    Wait Until Element Is Visible    ${latest_dropdown_value}
    ${displayed_dropdown_date}    Get Text    ${latest_dropdown_value}
    Should Contain    ${report_date}    ${displayed_dropdown_date}
    Try To Click Element    ${lab_history_dropdown}

###---Patient Education---###

Display Education Documents Page
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Element Contains    ${education_header}    Education
        Wait Until Page Contains    All the useful links and documents your clinic sends you are gathered below.
        Wait Until Page Contains Element    ${inbox_attached_doc}
    ELSE
        Wait Until Element Contains    ${education_header}    Education
        Wait Until Element Is Visible    ${education_documents_header}
        Wait Until Element Is Visible    ${education_documents_header_texts}
    END

Open Education Document In Library
    [Arguments]    ${attached_doc_title}
    ${educ_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y
    Wait Until Element Is Enabled    ${education_documents_header_texts}
    Wait Until Element Is Enabled    //div[contains(text(),'${attached_doc_title}')]
    ${library_doc_title}    Execute Javascript
    ...    return document.getElementsByClassName('open-attachment')[0].innerText;
    ${library_doc_title}    Remove String    ${library_doc_title}    \n${educ_date}
    Should Be Equal    ${attached_doc_title}    ${library_doc_title}
    Try To Click Element    ${library_attached_doc}
    Wait until keyword succeeds    3x    1s    Switch Window    NEW
    Sleep    1
    Wait Until Page Contains    ${attached_doc_title}
    Wait until keyword succeeds    3x    1s    Switch Window    MAIN
    Wait Until Element Is Visible    //div[contains(text(),'${attached_doc_title}')]

Click Education Documents Intro
    Wait Until Element Is Visible    ${education_banner}
    Try To Click Element    ${education_banner}
    Wait Until Element Is Visible    ${education_header}

Click 3-dot Icon
    [Documentation]    Clicks first instance
    Try To Click Element    xpath=${first_three_dot_icon}
    Wait Until Element Is Visible    ${education_document_modal}

Click 3-dot Icon Per Document
    [Arguments]    ${document_title}
    Try To Click Element    xpath=//*[contains(text(),"${document_title}")]/../../..//*[@name="action-menu"]
    Wait Until Element Is Visible    ${education_document_modal}

Add Document To Bookmarks
    [Arguments]    ${document_title}
    Sleep    2s
    Click 3-dot Icon Per Document    ${document_title}
    Try To Click Element    ${education_add_bookmark}
    Generic: Wait Until Element Is Not Visible    ${education_document_modal}

Open Education Document From Action Menu
    Click 3-dot Icon
    Try To Click Element    ${education_open_doc}
    Wait until keyword succeeds    5x    1s    Switch Window    NEW
    Wait until keyword succeeds    5x    1s    Wait Until Page Contains    ${document_topic}
    Wait until keyword succeeds    3x    1s    Switch Window    MAIN

Remove Document To Bookmarks
    [Arguments]    ${document_title}
    Click 3-dot Icon Per Document    ${document_title}
    Try To Click Element    ${education_remove_bookmark}
    Generic: Wait Until Element Is Not Visible    ${education_document_modal}

See Original Message Of First Document
    Click 3-dot Icon
    Wait Until Element Is Visible    ${see_original_message_button}    timeout=10s
    Try To Click Element    ${see_original_message_button}

Get Document Title
    [Arguments]    ${index}
    ${title}    Format String    ${document_title}    ${index}
    Wait Until Element Is Visible    xpath=${title}
    ${doc_title}    Get Text    xpath=${title}
    Set Test Variable    ${doc_title}

Get Document Date
    [Arguments]    ${index}
    ${document_date_element}    Set Variable If
    ...    'native' in '${ENVIRONMENT}'
    ...    ${document_date_native}
    ...    ${document_date}
    ${date}    Format String    ${document_date_element}    ${index}
    Wait Until Element Is Visible    xpath=${date}
    ${doc_date}    Get Text    xpath=${date}
    Set Test Variable    ${doc_date}

Contact Clinic About Lab Report
    [Arguments]    ${lab_reports_banner}    ${message}
    Try To Click Element    ${lab_contact_clinic_button}
    Wait Until Element Is Visible    ${contact_clinic_lab_report_title}
    IF    'native' in '${ENVIRONMENT}'
        Element Should Contain Text    ${contact_clinic_lab_report_title}    ${lab_reports_banner}
    ELSE
        Element Should Contain    ${contact_clinic_lab_report_title}    ${lab_reports_banner}
    END
    Input Text    ${contact_clinic_text_area}    aa    # to check minimum input
    Sleep    1
    Element Should Be Disabled    ${lab_report_contact_clinic_send_button}
    ${current_date}    Get Current Date    exclude_millis=yes    result_format=%d.%m.%Y
    Input Text    ${contact_clinic_text_area}    ${message}
    Try To Click Element    ${lab_report_contact_clinic_send_button}

Contact Clinic Modal Can Be Closed
    Try To Click Element    ${lab_contact_clinic_button}
    Try To Click Element    ${lab_report_contact_clinic_close_button}
    Wait Until Page Does Not Contain Element    ${lab_report_contact_clinic_close_button}
