*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource


*** Variables ***
${diary_icon}                               navigation-diary-link
${icon_calendar}                            //ds-icon[contains(@class, "upcoming-event__icon mr-m")]
${upcoming_events_header_element}           //h2[@id='upcoming-events-header']
${upcoming_events_header_text}              Upcoming events
${no_upcoming_events_text}                  No upcoming events
${events_list}                              //div[@id='main-scroll-container']//li[1]
${show_all_events}                          //*[@data-testid="button-timeline-upcoming-show-events"]/button
${personal_label}                           //div[contains(@class, "upcoming-event__content")]//p[contains(text(),"Personal")]
${timeline_event_list}                      //div[@id='main-scroll-container']//li[last()]//div[@class='card']
${diary_entries_header}                     //h1[contains(text(),'Diary entries')]
${diary_timeline_graphical_view}            //ds-button[@icon='icon-graphs']/button
${diary_latest_questionnaire_day}           (//nh-timeline-month)[1]/descendant::*/span[2]
${diary_latest_questionnaire}               (//nh-timeline-month)[1]/descendant::div[starts-with(@class, 'timeline-questionary-text')]/span[1]
${diary_latest_questionnaire_status}        (//nh-timeline-month)[1]/descendant::div[starts-with(@class, 'timeline-questionary-text')]/span[2]
${diary_latest_entry_section}               (//div[@class='timeline-symptoms-list']/..)[1]
${diary_latest_symptom_entry}               (//div[@class='timeline-symptoms-list'])[1]/descendant::p[contains(text(),"{}")]
${symptom_summary_sent_to_clinic_label}     //div[@class='symptom-summary-content']/descendant::span[1]
${symptom_summary_next_of_kin_label}        //div[@class='symptom-summary-content']//*[contains(@class, "next-of-kin-info")]//*
${timeline_symptom_pain}                    //span[contains(text(), "30.3")]/../..//p[contains(@class, "timeline-symptom-pain")]
${symptom_status}                           Sent to clinic
${symptom_summary_modal}                    //div[contains(@class, "symptom-summary-modal")]
${close_button}                             //div[contains(text(), "Close")]
${month_selector}                           //div[contains(@class, "month-selector")]
${expected_tooltip_details}=                SEPARATOR=
...                                         Pain\n
...                                         Most severe on 07.12.2020 \n
...                                         Intensity of pain 9.0
${previous_month}                           //div[@id='prev-month']
${latest_questionnaire_in_diary}            //*[@id="patient"]//nh-timeline-month/nh-timeline-day/div/div/div/button/nh-timeline-questionary
${timeline_followup_component}              //*[@id='timeline-followup-header']/..
${go_to_followup_button}                    //div[text()="Go to follow-up"]
${followup_selections}                      //div[@class='actions clearfix']
${symptoms_have_ended_button}               //div[text()="Symptoms have ended"]
${follow_up_action_modal}                   //div[contains(@class, 'modal-padding overlay-content')]
${end_follow_up_button}                     //div[text()="End follow-up"]
${contact_clinic_to_followup_button}        //div[text()="Contact clinic"]
${send_report_button}                       //div[text()="Send report"]
${symptom_summary_section}                  //div[@class='symptom-summary-content']
${modify_symptom_radio}                     //*[@id='earlier-symptom-action-modify']/following-sibling::label
${upcoming_even_calender_icon}              //li[starts-with(@class, 'upcoming-event')]/nh-timeline-upcoming-event/div
${edit_symptom_diary_button}                //*[@id="symptom-summary-modal-modal-button-edit"]
${contact_clinic_symptom_diary_button}      //*[@id="symptom-summary-modal-modal-button-send"]
${symptom_diary_sent_to_clinic_label}       //span[contains(text(),"Sent to clinic")]
${update_symptoms_button}                   //*[text()="Update symptoms"]
${update_symptoms_yes}                      //*[@id='smart-symptom-inquiry-generic-question-form-secondary-0']/button
${update_symptoms_no}                       //*[@id='smart-symptom-inquiry-generic-question-form-primary-1']/button
${edit_symptom_button}                      symptom-summary-modal-modal-button-edit
${description_field}                        //*[@id="description"]
${diary_page_title}                         header-page-title
${diary_page_noona_logo}                    //*[@name='icon-noona-head']
${show_all_upcoming_events}                 //nh-upcoming-events-list/ul/li[last()]
${save_entry_to_diary_link}                 //*[text()="Save an event in your Diary!"]
${diary_timeline_entry}                     //nh-timeline-diary-entry
${view_calender_button}                     //*[@id='switch-to-calendar-button']/button/div
${diary_entries_section}                    //section[contains(@class,'timeline-month') and starts-with(@aria-label,'Diary entries for')]
${diary_month_date}                         //section[contains(@class,'container') and contains(@class,'timeline-month')]//span[contains(@class,'date')][2]
${diary_month_weekday}                      //section[contains(@class,'container') and contains(@class,'timeline-month')]//span[contains(@class,'date-name')][1]
${answered_symptom_questionnaire_header}    //div[@class='modal-content-container']//h1[text()="{}"]
${have_you_had_any_symptoms_text}           Have you had any symptoms?
${information_saved_text}                   Information saved
${diary_page_open_message_button}           //h2[text()="You have a new message"]/following-sibling::ds-button//*[text()="Open message"]
${update_symptoms_button_ds_button}         //*[@id='sst-start-header']/following-sibling::ds-button
${welcome_to_noona_ok_ds_button}            //ds-button[contains(@class, 'dismiss-button')]
${diary_main_header}                        //*[@id='main-header']
${wellbeing_date_jelement}                  input[data-testid='datetime-selector-date']
${end_of_event_list}                        You have reached the end of your events


*** Keywords ***
Open Graphical Timeline View
    Wait Until Element Is Visible    ${diary_entries_header}
    Scroll Element Into View    ${diary_timeline_graphical_view}
    Try To Click Element    ${diary_timeline_graphical_view}

Questionnaire Schedule Is Displayed In The Upcoming Events
    [Documentation]    Upcoming questionnaires
    [Tags]    ${questionnaire}=${baseline_questionnaire}
    ${show_all_button_present}    Run Keyword And Return Status
    ...    Wait Until Element Contains
    ...    ${show_all_events}
    ...    SEE ALL
    ...    timeout=7s
    IF    '${show_all_button_present}'=='True'
        Wait Until Element Contains    ${upcoming_events_header_element}    ${upcoming_events_header_text}
        Wait Until Element Is Visible    ${events_list}
        Try To Click Element    ${show_all_events}
    END
    Wait Until Page Contains    ${questionnaire}
    Text Should Not Be In The Page    ${no_upcoming_events_text}
    ${questionnaire_event_icon}    Set Variable    //*[text()="${questionnaire}"]/../preceding-sibling::div/ds-icon
    Wait Until Element Is Visible    ${questionnaire_event_icon}
    ${attr}    Get Element Attribute    ${questionnaire_event_icon}    class
    Should Contain    ${attr}    icon-questionnaire

Appointment Schedule Is Displayed In The Upcoming Events
    [Documentation]    Appointments scheduled by the clinic with hospital icon
    [Arguments]    ${appointment_reason}=test2
    ${show_all_button_present}    Run Keyword And Return Status
    ...    Wait Until Element Contains
    ...    ${show_all_events}
    ...    SEE ALL
    ...    timeout=7s
    IF    '${show_all_button_present}'=='True'
        Wait Until Element Contains    ${upcoming_events_header_element}    ${upcoming_events_header_text}
        Wait Until Element Is Visible    ${events_list}
        Try To Click Element    ${show_all_events}
    END
    Wait Until Element Is Visible    ${timeline_event_list}
    Scroll Element Into View    ${timeline_event_list}
    ${appointment_event_icon}    Set Variable    //*[text()="${appointment_reason}"]/../preceding-sibling::div/ds-icon
    Wait Until Element Is Visible    ${appointment_event_icon}
    ${attr}    Get Element Attribute    ${appointment_event_icon}    class
    Should Contain    ${attr}    icon-appointment-clinic

Event Schedule Is Displayed In The Upcoming Events
    [Documentation]    Event added by the patient, marked as 'Personal', with calendar icon
    [Arguments]    ${treatment_phase}
    ${show_all_button_present}    Run Keyword And Return Status
    ...    Wait Until Element Contains
    ...    ${show_all_events}
    ...    SEE ALL
    ...    timeout=7s
    IF    '${show_all_button_present}'=='True'
        Wait Until Element Contains    ${upcoming_events_header_element}    ${upcoming_events_header_text}
        Wait Until Element Is Visible    ${events_list}
        Try To Click Element    ${show_all_events}
    END
    Wait Until Element Is Visible    ${timeline_event_list}
    Scroll Element Into View    ${timeline_event_list}
    ${personal_event_icon}    Set Variable    //*[text()="${treatment_phase}"]/../preceding-sibling::div/ds-icon
    Wait Until Element Is Visible    ${personal_event_icon}
    ${attr}    Get Element Attribute    ${personal_event_icon}    class
    Should Contain    ${attr}    icon-calendar    AND    ${personal_label}

View Symptom From Diary Entries
    Wait Until Page Contains Element    ${timeline_symptom_pain}    5s
    Seleniumlibrary.Click Element    ${timeline_symptom_pain}
    Wait Until Page Contains Element    ${symptom_summary_modal}
    Sleep    3s
    Element Should Contain    ${symptom_summary_modal}    ${symptom_status}

Check Reported Symptom Content And Close
    ${content}    Get File    ${EXECDIR}${/}resources${/}patient${/}reported_pain.txt
    @{content_lines}    Split To Lines    ${content}
    FOR    ${line}    IN    @{content_lines}
        Page Should Contain    ${line}
    END
    Seleniumlibrary.Click Element    ${close_button}

Check Timeline Month Year
    [Arguments]    ${test_month_year}
    # Check timeline date = December 7, 2020
    Wait Until Element Is Visible    ${month_selector}    5s
    ${month_year}    Get Text    ${month_selector}
    Set Global Variable    ${test_month_year}
    IF    '${month_year}'!='${test_month_year}'
        Check Month
    ELSE
        Should Be Equal    ${test_month_year}    ${month_year}
    END

Check Month
    Try To Click Element    ${previous_month}
    Wait Until Element Is Visible    ${month_selector}
    ${month_year}    Get Text    ${month_selector}
    IF    '${month_year}'!='${test_month_year}'    Check Month

Verify Numerical Intensity Information
    [Arguments]    ${intensity_rate}
    Wait Until Element Is Visible    //div[contains(text(),'${intensity_rate}')]

Select Symptom Circle From Timeline
    [Arguments]    ${yyyy-mm-dd}
    Wait Until Element Is Visible    //div[@id='pain_${yyyy-mm-dd}']
    Click Element    //div[@id='pain_${yyyy-mm-dd}']

Verify Tooltip Symptom Entry Information
    [Arguments]    ${yyyy-mm-dd}
    Wait Until Element Is Visible    //*[@id="pain_${yyyy-mm-dd}"]/tooltip/tip
    ${actual_tooltip_details}    Get Text    //*[@id="pain_${yyyy-mm-dd}"]/tooltip/tip
    Should Be Equal    ${expected_tooltip_details}    ${actual_tooltip_details}

Latest Questionnaire's Status Is Correct
    [Documentation]    today=no if latest status is verified regardless of the date
    [Arguments]    ${questionnaire}    ${status}    ${today}=yes
    Wait Until Element Is Visible    ${diary_latest_questionnaire}
    Scroll Element Into View    ${diary_latest_questionnaire}
    IF    '${today}'=='yes'    Latest Diary Entry Date Is Today
    ${actual_questionnaire}    Get Text    ${diary_latest_questionnaire}
    ${actual_status}    Get Text    ${diary_latest_questionnaire_status}

Latest Diary Entry Date Is Today
    ${current_date}    Get Current Date    result_format=%-d.%-m.
    ${text}    Get Text    ${diary_latest_questionnaire_day}
    Should Be Equal    ${current_date}    ${text}

Diary Latest Entry Section Contain Symptom
    [Arguments]    ${symptom}
    Wait Until Element Is Visible    xpath=${diary_latest_entry_section}
    Sleep    1
    Generic: Element Should Contain    xpath=${diary_latest_entry_section}    ${symptom}

Select Latest Symptom Diary Entry
    [Documentation]    Selects the first occurence of the symptom entry in diary
    [Arguments]    ${symptom}
    Wait Until Element Is Visible    xpath=${diary_latest_entry_section}
    ${latest_entry}    Format String    ${diary_latest_symptom_entry}    ${symptom}
    Try To Click Element    xpath=${latest_entry}
    Sleep    1

Diary Symptom Is Sent To Clinic
    [Arguments]    ${symptom}
    Wait Until Element Is Visible    ${symptom_summary_sent_to_clinic_label}
    Generic: Element Should Contain    ${symptom_summary_sent_to_clinic_label}    Sent to clinic

Diary Symptom Information Is Entered By A Caregiver/ Next Of Kin
    Generic: Element Should Contain    ${symptom_summary_next_of_kin_label}    Information entered by a caregiver

Select Latest Questionnaire In Diary
    Sleep    1
    Wait Until Element Is Visible    ${latest_questionnaire_in_diary}
    Try To Click Element    ${latest_questionnaire_in_diary}

Reload Until Questionnaire Is Available
    FOR    ${INDEX}    IN RANGE    1    60    # waits for 120s
        IF    'native' in '${ENVIRONMENT}'
            Navigate to Clinic
            Go To Diary
            Sleep    1s
        ELSE
            Reload Page
        END
        ${unanswered_displayed}    Run Keyword And Return Status    Wait Until Page Contains    Unanswered
        IF    ${unanswered_displayed}    BREAK    ELSE    Sleep    1s
    END

Patient Adds Symptom From Diary
    [Arguments]    ${symptom}
    Click Add Menu Button
    Add Symptom Entry
    Add A Symptom    ${symptom}

Follow Up Component Is Displayed In The Timeline
    Wait Until Element Is Visible    ${timeline_followup_component}    20s

Follow Up Component Is Not Displayed In The Timeline
    Generic: Wait Until Element Is Not Visible    ${timeline_followup_component}

Go To Follow Up
    Wait Until Element Is Visible    ${go_to_followup_button}
    Try To Click Element    ${go_to_followup_button}
    Wait Until Element Is Visible    ${followup_selections}

Select Symptoms Have Ended
    Try To Click Element    ${symptoms_have_ended_button}
    Wait Until Element Is Visible    ${follow_up_action_modal}
    Text Should Be In The Page
    ...    If you feel that your symptoms have lessened and you no longer require assistance from your clinic, you can end the follow-up period.
    Text Should Be In The Page
    ...    If the same symptoms become an issue again, please contact your clinic and send a new symptom report.
    Try To Click Element    ${end_follow_up_button}
    Wait Until Page Contains    The follow-up period has been ended
    Try To Click Banner Message
    Wait Until Page Does Not Contain Element    ${followup_selections}

Select Contact Clinic
    [Arguments]    ${option}
    Try To Click Element    ${contact_clinic_to_followup_button}
    Wait Until Element Is Visible    ${follow_up_action_modal}
    Text Should Be In The Page    Send follow-up report to the clinic
    Text Should Be In The Page
    ...    You can send follow-up report to your clinic if your symptoms have changed and you require help from the clinic.
    Try To Click Element    ${send_report_button}
    Wait Until Page Contains    Symptom entry in your diary
    Text Should Be In The Page
    ...    You have created a symptom entry in your diary within the last 24 hours. Do you want to send it to the clinic?
    Page Should Contain Element    ${symptom_summary_section}
    IF    '${option}'=='modify'
        Wait Until Keyword Succeeds    20s    1s    Modify Symptom
    END
    # TODO: Add Create new symptom diary if needed in another tc. Not covered by current tc.

Modify Symptom
    Try To Click Element    ${modify_symptom_radio}
    Click Next Button
    Complete Other Symptom Form    Mild
    Click Next Button
    Send Symptom To Clinic
    Click View Your Diary

Symptom Diary Modal Is Displayed
    [Documentation]    Modal that pops up after clicking symptom entry from diary
    ...    symptom_origin is symptom_diary if symptom was added as diary entry from the + icon,
    ...    symptom_report if symptom was added by asking about symptoms,
    ...    symptom_questionnaire if symptom was added by answering AEQ
    [Arguments]    ${symptom}    ${symptom_origin}
    Wait Until Element Is Visible    //h1[contains(text(),"${symptom}")]
    IF    '${symptom_origin}'=='symptom_diary'
        Element Should Be Visible    ${edit_symptom_diary_button}
        Element Should Be Visible    ${contact_clinic_symptom_diary_button}
    END
    IF    '${symptom_origin}'=='symptom_report' or '${symptom_origin}'=='symptom_questionnaire'
        Element Should Be Visible    ${symptom_diary_sent_to_clinic_label}
    END

Close Symptom Diary Modal
    Wait Until Element Is Visible    ${close_button}
    Try To Click Element    ${close_button}

Patient Is Asked Wellbeing In Diary
    Wait Until Page Contains    How are you feeling today?
    Page Should Contain Element    ${update_symptoms_button}

Update Symptoms
    [Documentation]    Answer to Have you had any symptoms?
    [Arguments]    ${option}
    Wait Until Element Is Visible    ${update_symptoms_button}    timeout=25s
    Try To Click Element    ${update_symptoms_button}
    IF    '${option}'=='yes'
        Try To Click Element    ${update_symptoms_yes}
    ELSE
        Try To Click Element    ${update_symptoms_no}
    END

Edit Content In Symptom Wizard View
    Try To Click Element    ${edit_symptom_button}
    ${random_description_2}    Generate Random String    40
    Set Test Variable    ${random_description_2}
    Try To Input Text    ${description_field}    ${random_description_2}

Save To Diary Call To Action Is Displayed
    Wait Until Element Is Visible    ${save_entry_to_diary_link}
    Wait Until Element Is Visible    ${save_entry_to_diary_link}/preceding-sibling::p[text()="No upcoming events"]
    Wait Until Element Is Visible    ${save_entry_to_diary_link}/../preceding-sibling::div/ds-icon
    ${attr}    Get Element Attribute    ${save_entry_to_diary_link}/../preceding-sibling::div/ds-icon    class
    Should Contain    ${attr}    icon-calendar

Questionnaire Header Is Displayed In Modal
    [Documentation]    Checks the questionnaire header after opening already answered questionnaire from the diary
    [Arguments]    ${questionnaire}
    ${element}    Format String    ${answered_symptom_questionnaire_header}    ${questionnaire}
    Wait Until Element Is Visible    ${element}
    ${text}    Get Text    ${element}
    Should Be Equal    ${text}    ${questionnaire}

Open Message
    Try To Click Element    ${diary_page_open_message_button}

Select A Sent Questionnaire From Diary
    [Documentation]     Assumming that the patient is now in Diary-page and the sent questionnaire is Unanswered
    [Arguments]    ${questionnaire_title}
    Try To Click Element    //nh-timeline-questionary//span[text()="${questionnaire_title}"]


