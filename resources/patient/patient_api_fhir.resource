*** Settings ***
Library     OperatingSystem
Library     String
Library     RequestsLibrary
Library     JSONLibrary
Library     DateTime
Library     Collections


*** Variables ***
${encodedSlash}                     %2F
${default_number}                   +35800000
${ehr_diagnostic_report_data_1}     EHR_diagnostic_report_TenOnc_CMP_sample
${ehr_diagnostic_report_data_2}     EHR_diagnostic_report_TenOnc_BMP_sample
${ehr_diagnostic_report_data_3}     ORU_DiagnosticReport
${report_title}                     CMP
${updated_report_title}             CMP_new


*** Keywords ***
Invite Patient Via SMS Invitation
    [Arguments]
   ...    ${integration_user_token}
    ...    ${tunit}
    ...    ${birth_date}
    ...    ${phone_number}=${default_number}
    ...    ${gender}=male
    Set Test Variable    ${integration_user_token}
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientViaSMSInvite.json
    Generate Random Data
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    ${tunit}
    ${body_mod}    Replace String    ${body_mod}    hipaa_value    ${hipaacode}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    birth_date_value    ${birth_date}
    ${body_mod}    Replace String    ${body_mod}    gender_option    ${gender}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    sms_number_value    ${phone_number}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/Patient    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${patient_location_url}    Get From Dictionary    ${response.headers}    location
    ${patient_id}    Remove String    ${patient_location_url}    ${API_URL}/fhir-r4/Patient/
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Delete All Sessions
    RETURN    ${body_json}

Invite SPMC Patient Via SMS Invitation
    [Arguments]
    ...    ${integration_user_token}
    ...    ${tunit}
    ...    ${spmc_birth_date}
    ...    ${spmc_patient_email}
    ...    ${gender}=male
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientViaSMSInvite.json
    Generate Random Data
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    Set Test Variable    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    ${tunit}
    ${body_mod}    Replace String    ${body_mod}    hipaa_value    ${hipaacode}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    first_name_value    ${first_name}
    ${body_mod}    Replace String    ${body_mod}    birth_date_value    ${spmc_birth_date}
    ${body_mod}    Replace String    ${body_mod}    gender_option    ${gender}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${spmc_patient_email}
    ${body_mod}    Replace String    ${body_mod}    sms_number_value    ${default_number}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/Patient    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${patient_location_url}    Get From Dictionary    ${response.headers}    location
    ${patient_id}    Remove String    ${patient_location_url}    ${API_URL}/fhir-r4/Patient/
    Set Test Variable    ${patient_id}
    Delete All Sessions
    RETURN    ${body_json}

Invite Patient Via SMS Invitation With Invalid Treatment Unit
    [Arguments]    ${integration_user_token}    ${gender}=male
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    ${body}    Get File    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientViaSMSInvite.json
    Generate Random Data
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    4363463
    ${body_mod}    Replace String    ${body_mod}    hipaa_value    ${hipaacode}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    birth_date_value    ${birth_date}
    ${body_mod}    Replace String    ${body_mod}    gender_option    ${gender}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session
    ...    noona
    ...    /fhir-r4/Patient
    ...    data=${body_mod}
    ...    headers=${header}
    ...    expected_status=400
    Should Contain    '${response.status_code}'    '400'
    Should Contain    '${response.content}'    ErrorCode: INVALID_CARE_TEAM
    Delete All Sessions

Generate Random Data
    ${patient_mrn}    Generate Random String    8
    Set Test Variable    ${patient_mrn}
    ${patient_ssn}    Generate Random String    8
    Set Test Variable    ${patient_ssn}
    ${hipaacode}    Generate Random String    8
    Set Test Variable    ${hipaacode}
    ${external_id}    Generate Random String    8
    Set Test Variable    ${external_id}
    ${family_name}    Generate Random String    8    [LETTERS]
    Set Test Variable    ${family_name}
    ${first_name}    Generate Random String    8    [LETTERS]
    Set Test Variable    ${first_name}
    ${random_string}    Generate Random String    8    [LOWER]
    Set Test Variable    ${patient_email}    ${random_string}@noona.fi

Get Patient Details
    Create Session    noona    ${API_URL}    verify=True
    ${response}    GET On Session    noona    /fhir-r4/Patient/${patient_id}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    Set Test Variable    ${response_json}
    Delete All Sessions

Update Details
    [Arguments]    ${tunit}    ${gender}=male
    ${body}    Get File    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientViaSMSInvite.json
    Generate Random Data
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    id_value    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    ${tunit}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    hipaa_value    ${hipaacode}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    birth_date_value    ${birth_date}
    ${body_mod}    Replace String    ${body_mod}    gender_option    ${gender}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    sms_number_value    ${default_number}
    ${body_json_updated}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}
    ${response}    PUT On Session    noona    /fhir-r4/Patient/${patient_id}    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions
    RETURN    ${body_json_updated}

Compare Data
    [Arguments]    ${sent_data}
    Remove From Dictionary    ${sent_data}    extension
    Remove From Dictionary    ${sent_data}    generalPractitioner
    Remove From Dictionary    ${response_json}    active
    Remove From Dictionary    ${sent_data}    managingOrganization
    Remove From Dictionary    ${response_json}    managingOrganization
    Remove From Dictionary    ${sent_data}    id
    Remove From Dictionary    ${response_json}    id
    Remove From Dictionary    ${sent_data}    contained
    Remove From Dictionary    ${response_json}    contained
    Remove From Dictionary    ${sent_data}    resourceType
    Remove From Dictionary    ${response_json}    resourceType
    Remove From Dictionary    ${sent_data}    identifier
    Remove From Dictionary    ${response_json}    identifier
    Remove From Dictionary    ${sent_data}    meta
    Remove From Dictionary    ${response_json}    meta
    Dictionaries Should Be Equal    ${sent_data}    ${response_json}

Response Status should be Success 200
    [Arguments]    ${status}
    Should Be Equal As Strings    ${status}    200

Post EHR Diagnostic Report WBC
    [Documentation]    Keyword is deprecated, end point is not in use anymore. Keeping it still due to hystorical reasons.
    [Arguments]    ${json_file}    ${mrn_id}    ${report_external_id}    ${effectiveDateTime}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}data${/}ehr_json${/}${json_file}.json
    ${body_mod}    Replace String    ${body}    patient-mrn    Patient/Patient-${mrn_id}
    ${body_mod}    Replace String    ${body_mod}    Diagnostic-Report-ID    ${report_external_id}
    ${body_mod}    Replace String    ${body_mod}    effective-date-time    ${effectiveDateTime}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir/diagnostic-report/    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    Delete All Sessions

Post EHR Diagnostic Report FHIR-R4 Endpoint
    [Arguments]    ${json_file}    ${patient_id}    ${report_external_id}    ${effectiveDateTime}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}data${/}ehr_json${/}${json_file}.json
    ${body_mod}    Replace String    ${body}    patient-id    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    Diagnostic-Report-ID    ${report_external_id}
    ${body_mod}    Replace String    ${body_mod}    effective-date-time    ${effectiveDateTime}
    ${body_mod}    Replace String    ${body_mod}    report-title    ${report_title}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/DiagnosticReport/    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${response_headers}    Convert To Dictionary    ${response.headers}
    ${location}    Get From Dictionary    ${response_headers}    Location
    ${location}    Convert To String    ${location}
    ${lab_result_internal_id}    Fetch From Right    ${location}    /
    Set Global Variable    ${lab_result_internal_id}
    Delete All Sessions

Send Default Lab Reports To Patient
    [Arguments]    ${integration_user_token}    ${patient_id}
    Set Report ID And Effective DateTime
    Set Test Variable    ${integration_user_token}
    Post EHR Diagnostic Report FHIR-R4 Endpoint
    ...    ${ehr_diagnostic_report_data_3}
    ...    ${patient_id}
    ...    ${report_external_id}
    ...    ${effectiveDateTime}

Delete Lab Report Via API
    [Documentation]    Keyword is deprecated, end point is not in use anymore. Keeping it still due to hystorical reasons.
    [Arguments]    ${reportExternalId}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    Create Session    noona    ${API_URL}    verify=True
    ${response}    DELETE On Session
    ...    noona
    ...    /fhir/diagnostic-report/DiagnosticReport${encodedSlash}${reportExternalId}
    ...    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Delete Lab Report With FIHR-R4 Endpoint
    [Arguments]    ${json_file}    ${patient_id}    ${lab_result_internal_id}    ${effectiveDateTime}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}data${/}ehr_json${/}${json_file}.json
    ${body_mod}    Replace String    ${body}    patient-id    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    Diagnostic-Report-ID    ${lab_result_internal_id}
    ${body_mod}    Replace String    ${body_mod}    effective-date-time    ${effectiveDateTime}
    ${body_mod}    Replace String    ${body_mod}    final    entered-in-error
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    PUT On Session
    ...    noona
    ...    /fhir-r4/DiagnosticReport/${lab_result_internal_id}
    ...    data=${body_mod}
    ...    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Update Lab Report With FIHR-R4 Endpoint
    [Arguments]    ${json_file}    ${patient_id}    ${lab_result_internal_id}    ${effectiveDateTime}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}data${/}ehr_json${/}${json_file}.json
    ${body_mod}    Replace String    ${body}    patient-id    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    Diagnostic-Report-ID    ${lab_result_internal_id}
    ${body_mod}    Replace String    ${body_mod}    effective-date-time    ${new_effectiveDateTime}
    ${body_mod}    Replace String    ${body_mod}    report-title    ${updated_report_title}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    PUT On Session
    ...    noona
    ...    /fhir-r4/DiagnosticReport/${lab_result_internal_id}
    ...    data=${body_mod}
    ...    headers=${header}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Update Lab Report With FIHR-R4 Endpoint With Wrong Patient ID
    [Arguments]    ${json_file}    ${patient_id}    ${lab_result_internal_id}    ${effectiveDateTime}
    &{header}    Create Dictionary    Content-Type=application/json    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File    ${EXECDIR}${/}data${/}ehr_json${/}${json_file}.json
    ${body_mod}    Replace String    ${body}    patient-id    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    Diagnostic-Report-ID    ${lab_result_internal_id}
    ${body_mod}    Replace String    ${body_mod}    effective-date-time    ${new_effectiveDateTime}
    ${body_mod}    Replace String    ${body_mod}    report-title    ${updated_report_title}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    PUT On Session
    ...    noona
    ...    /fhir-r4/DiagnosticReport/${lab_result_internal_id}
    ...    data=${body_mod}
    ...    headers=${header}
    ...    expected_status=400
    Delete All Sessions

Send Candidate Request
    [Arguments]    ${integration_user_token}    ${tunit}      ${treatment_module}=cytostaticsGeneral      #added  ${treatment_module}  variable if you want to create EHR patient with a specific Tx
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File
    ...    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientAsCandidateWithCareProvider.json
    Generate Random Data
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    ${tunit}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    phone_number    0000000
    ${body_mod}    Replace String    ${body_mod}    treatment_module     ${treatment_module}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/Patient    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${patient_location_url}    Get From Dictionary    ${response.headers}    location
    ${patient_id}    Remove String    ${patient_location_url}    ${API_URL}/fhir-r4/Patient/
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Delete All Sessions
    RETURN    ${body_json}

Invite Patient As Candidate With Specific Patient Details
    [Documentation]    Generate data should be done before this keyword
    [Arguments]    ${integration_user_token}    ${tunit}    ${treatment_module}=cytostaticsGeneral
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File
    ...    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientAsCandidateWithCareProvider.json
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    ${tunit}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    phone_number    0000000
    ${body_mod}    Replace String    ${body_mod}    treatment_module     ${treatment_module}
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/Patient    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${patient_location_url}    Get From Dictionary    ${response.headers}    location
    ${patient_id}    Remove String    ${patient_location_url}    ${API_URL}/fhir-r4/Patient/
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Delete All Sessions
    RETURN    ${body_json}

Invite Patient As Proxy From EMR
    [Documentation]    Generate data should be done before this keyword
    [Arguments]    ${integration_user_token}    ${tunit}
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File
    ...    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientAsProxyFromEMR.json
    ${body_mod}    Replace String    ${body}    mrn_value    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value    ${tunit}
    ${body_mod}    Replace String    ${body_mod}    ssn_value    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    external_id_value    ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    email_value    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    phone_number    0000000
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/Patient    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${patient_location_url}    Get From Dictionary    ${response.headers}    location
    ${patient_id}    Remove String    ${patient_location_url}    ${API_URL}/fhir-r4/Patient/
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Delete All Sessions
    RETURN    ${body_json}

Send Candidate Request With Invalid Name Characters
    [Arguments]    ${integration_user_token}    ${tunit}
    &{header}    Create Dictionary
    ...    Content-Type=application/fhir+json
    ...    Authorization=Bearer ${integration_user_token}
    Set Test Variable    &{header}
    ${body}    Get File
    ...    ${EXECDIR}${/}resources${/}patient${/}integration${/}invitePatientAsCandidateWithCareProvider.json
    Generate Random Data
    ${body_mod}    Replace String    ${body}        mrn_value           ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    tunit_value         ${tunit}
    ${body_mod}    Replace String    ${body_mod}    ssn_value           ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    external_id_value   ${external_id}
    ${body_mod}    Replace String    ${body_mod}    family_name_value   ${family_name}?!
    ${body_mod}    Replace String    ${body_mod}    Middle              ?!
    ${body_mod}    Replace String    ${body_mod}    email_value         ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    phone_number        0000000
    Set Test Variable    ${body_mod}
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_URL}    verify=True
    ${response}    POST On Session    noona    /fhir-r4/Patient    data=${body_mod}    headers=${header}
    Should Contain    '${response.status_code}'    '201'
    ${patient_location_url}    Get From Dictionary    ${response.headers}    location
    ${patient_id}    Remove String    ${patient_location_url}    ${API_URL}/fhir-r4/Patient/
    Set Test Variable    ${patient_id}
    Set Test Variable    ${patient_email}
    Delete All Sessions
    RETURN    ${body_json}

Clear Queue
    ${header}    Create Dictionary    Content-Type=application/json    X-LOGIN-TOKEN=${multi_clinic1_server_token}
    ${response}    GET    ${outbound_url_clear_queue}    headers=${header}

Fetch QoL Data
    [Documentation]    questionnaire code is currently for QoL15D
    ...    Need to get other questionnaire codes for comparison
    [Arguments]    ${patient_email}
    ${header}    Create Dictionary    Content-Type=application/json    X-LOGIN-TOKEN=${multi_clinic1_server_token}
    ${status}    ${body}    Get Response    ${header}
    WHILE    ${status}==${FALSE}    limit=120
        Sleep    1s    # sleep needed to give time for the job to complete before fetching data
        ${status}    Run Keyword And Return Status    Should Contain    ${body}    system
        ${status}    ${body}    Get Response    ${header}
    END
    ${result_quest}    Get Regexp Matches    ${body}    questionnaire
    ${result_quest_code}    Get Regexp Matches    ${body}    qol15D
    ${result_patient_email}    Get Regexp Matches    ${body}    ${patient_email}

Get Response
    [Arguments]    ${header}
    ${resp}    GET    ${outbound_url_queue}    headers=${header}
    Status Should Be    200    ${resp}
    ${body}    Convert To String    ${resp.content}
    ${status}    Run Keyword And Return Status    Should Contain    ${body}    system
    RETURN    ${status}    ${body}

Verify Data Is Not Fetch
    ${header}    Create Dictionary    Content-Type=application/json    X-LOGIN-TOKEN=${multi_clinic1_server_token}
    ${response}    GET    ${outbound_url_queue}    headers=${header}
    ${status}    Run Keyword And Return Status    Should Contain    '${response.content}'    '[]'
    WHILE    ${status}==${FALSE}    limit=120
        Sleep    1s    # sleep needed to give time for the job to complete before fetching data
        ${response}    GET    ${outbound_url_queue}    headers=${header}
        Should Contain    '${response.status_code}'    '200'
        ${status}    Run Keyword And Return Status    Should Contain    '${response.content}'    '[]'
    END

Set Report ID And Effective DateTime
    ${time}    Get Current Date    time_zone=UTC
    ${string_datetime}    Convert To String    ${time}
    ${timestamp}    Remove String    ${string_datetime}    ${SPACE}    -    :    .
    Set Global Variable    ${report_external_id}    DiagnosticReport-200122377380-${timestamp}-CBC
    ${effective_datetime}    Replace String    ${string_datetime}    ${SPACE}    T
    Set Global Variable    ${effectiveDateTime}    ${effective_datetime}+00:00

Set New Effective DateTime
    ${time}    Get Current Date    time_zone=UTC
    ${new_time}    Subtract Time From Date    ${time}    10 days
    ${string_datetime}    Convert To String    ${new_time}
    ${timestamp}    Remove String    ${string_datetime}    ${SPACE}    -    :    .
    ${new_effective_datetime}    Replace String    ${string_datetime}    ${SPACE}    T
    Set Global Variable    ${new_effectiveDateTime}
