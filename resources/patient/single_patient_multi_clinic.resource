*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}more${/}more.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary.resource


*** Variables ***
${clinic_name_header_id}                header-current-clinic    #id needs in custom branding
${clinic_name_header_button}            //button[@id="header-current-clinic"]
${go_to_another_clinic_modal}           xpath=//*[@data-testid='clinic-switch-actions']
${go_to_another_clinic_close_button}    xpath=//*[@data-testid='close-button']


*** Keywords ***
Verify Visited Clinic
    [Arguments]    ${clinic_name}
    Wait Until Page Contains Element    ${clinic_name_header_button}
    Wait Until Element Is Visible       //*[contains(text(),'${clinic_name}')]
    Wait Until Page Does Not Contain    You are now in ${clinic_name}

Switch Clinic From Clinic Header
    [Arguments]    ${to_clinic}
    Try To Click Element    ${clinic_name_header_button}
    Wait Until Element Is Visible    ${go_to_another_clinic_modal}
    Wait Until Element Is Visible    //*[contains(text(),'${to_clinic}')]
    Try To Click Element    //label[text()="${to_clinic}"]/..
    Sleep    1
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    //*[@id='toast-container']//*[contains(text(), '${to_clinic}')]
    IF    ${status}
        Try To Click Element    //*[@id='toast-container']//*[contains(text(), '${to_clinic}')]
    END

Clinic Header Is Not Visible
    [Arguments]    ${delegate}=no
    Wait Until Element Is Visible    ${diary_page_title}
    IF    '${delegate}'=='no'
        Wait Until Element Is Visible    ${diary_page_noona_logo}
    END
    Element Should Not Be Visible    ${clinic_name_header_button}
