{"resourceType": "Patient", "meta": {"tag": [{"system": "http://varian.com/fhir/ValueSet/noona-patientimportmode", "code": "Candidate"}], "source": "TEST"}, "name": [{"use": "official", "family": "family_name_value", "given": ["Testy", "Middle"]}], "gender": "female", "birthDate": "2010-04-12", "active": true, "identifier": [{"system": "http://varian.com/fhir/identifier/Patient/MRN", "value": "mrn_value"}, {"system": "http://hl7.org/fhir/sid/us-ssn", "value": "ssn_value"}, {"system": "http://varian.com/fhir/identifier/Patient/noona-externalid", "value": "external_id_value"}], "managingOrganization": {"reference": "#Organization-1", "identifier": {"system": "http://varian.com/fhir/identifier/Organization/noona-clinicsitelabel", "value": ""}}, "contained": [{"resourceType": "Organization", "id": "Organization-1", "identifier": [{"system": "http://varian.com/fhir/identifier/Organization/noona-clinicsitelabel", "value": ""}], "active": true}, {"resourceType": "Practitioner", "id": "Practitioner-123", "identifier": {"system": "http://varian.com/fhir/identifier/Practitioner/noona-externalid", "value": "123"}, "name": [{"use": "official", "family": "Doctor", "given": ["Dr", "Fine"]}]}], "extension": [{"url": "http://varian.com/fhir/StructureDefinition/Patient/noona-careteamtreatmentunitid", "valueReference": {"identifier": {"system": "http://varian.com/fhir/identifier/CareTeam/noona-treatmentunitid", "value": "tunit_value"}}}, {"url": "http://varian.com/fhir/StructureDefinition/Patient/noona-treatmentmodulecode", "valueCodeableConcept": {"coding": [{"system": "http://varian.com/fhir/CodeSystem/noona-treatmentmodulecategory", "code": "treatment_module"}]}}], "address": [{"use": "home", "type": "both", "line": ["Knolweg.", "1002"], "city": "STITSWERD", "postalCode": "999988", "country": ""}, {"use": "temp", "type": "both", "line": ["Knolweg", "1001"], "city": "STITSWERD", "postalCode": "9999XX", "country": ""}], "telecom": [{"value": "phone_number", "system": "phone", "use": "home"}, {"value": "phone_number", "system": "phone", "use": "home"}, {"value": "phone_number", "system": "phone", "use": "work"}, {"value": "email_value", "system": "email"}], "generalPractitioner": [{"id": "Practitioner/123", "reference": "#Practitioner-123"}]}