{"resourceType": "Bundle", "entry": [{"resource": {"resourceType": "MessageHeader", "meta": {"profile": ["http://varian.com/fhir/r4/StructureDefinition/MessageHeader"]}, "source": {"name": "ARIA", "software": "ARIA"}, "extension": [{"url": "http://hl7.org/fhir/StructureDefinition/receivingApplication", "valueString": "NOONA"}, {"url": "http://hl7.org/fhir/StructureDefinition/receivingFacility", "valueString": "NOONA_FAC"}, {"url": "http://hl7.org/fhir/StructureDefinition/messageControlId", "valueString": "20220829194445"}], "eventCoding": {"code": "mdm-pd-in", "system": "T02_PD"}, "id": "20220829194445"}}, {"resource": {"resourceType": "Parameters", "parameter": [{"name": "CurrentDateTime", "valueString": "2023-01-17T11:57:15"}]}}, {"resource": {"resourceType": "DiagnosticReport", "identifier": [{"system": "http://varian.com/fhir/identifier/DiagnosticReport/id", "value": "clinical_report_id"}], "subject": {"identifier": {"value": "mrn_value", "system": "http://varian.com/fhir/identifier/Patient/MRN"}}, "status": "report_status", "code": {"text": "report_title"}, "effectiveDateTime": "report_date", "text": {"div": "This is a test for Patient Document into Noona\\nThis should be in new line", "status": "generated"}, "category": [{"coding": [{"code": "loin_code", "system": "http://loinc.org", "display": "Handled by Noona Translation Table"}]}]}}]}