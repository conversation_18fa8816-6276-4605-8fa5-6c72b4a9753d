{"resourceType": "Patient", "meta": {"tag": [{"system": "http://varian.com/fhir/ValueSet/noona-patientimportmode", "code": "Proxy"}], "source": "TEST"}, "name": [{"use": "official", "family": "family_name_value", "given": ["Testy", "Middle"]}], "gender": "female", "birthDate": "2010-04-12", "active": true, "identifier": [{"system": "http://varian.com/fhir/identifier/Patient/MRN", "value": "mrn_value"}, {"system": "http://hl7.org/fhir/sid/us-ssn", "value": "ssn_value"}], "extension": [{"url": "http://varian.com/fhir/StructureDefinition/Patient/noona-careteamtreatmentunitid", "valueReference": {"identifier": {"system": "http://varian.com/fhir/identifier/CareTeam/noona-treatmentunitid", "value": "tunit_value"}}}, {"url": "http://varian.com/fhir/StructureDefinition/Patient/noona-treatmentmodulecode", "valueCodeableConcept": {"coding": [{"system": "http://varian.com/fhir/CodeSystem/noona-treatmentmodulecategory", "code": "cytostaticsGeneral"}]}}], "address": [{"use": "home", "type": "both", "line": ["Knolweg.", "1002"], "city": "Test City", "postalCode": "31321", "country": ""}, {"use": "temp", "type": "both", "line": ["Knolweg", "1001"], "city": "<PERSON><PERSON><PERSON><PERSON>", "postalCode": "31321", "country": ""}], "telecom": [{"value": "+35800001", "system": "phone", "use": "mobile"}, {"value": "+3580000", "system": "phone", "use": "home"}, {"value": "+3580000", "system": "phone", "use": "work"}, {"value": "email_value", "system": "email"}]}