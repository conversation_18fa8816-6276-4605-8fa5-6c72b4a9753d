{"resourceType": "Appointment", "identifier": [{"system": "http://varian.com/fhir/identifier/Appointment/id", "value": "appointment-id"}], "start": "fhir-datetime", "minutesDuration": 15, "status": "", "reasonCode": [{"coding": [{"system": "http://varian.com/fhir/CodeSystem/noona-appointmentReasonCode", "code": "recode"}]}], "participant": [{"actor": {"type": "Patient", "reference": "Patient-id"}}, {"actor": {"type": "Location", "identifier": {"system": "http://varian.com/fhir/identifier/Location/id", "value": "treatment-id"}}}], "serviceType": [{"coding": [{"system": "http://varian.com/fhir/CodeSystem/noona-appointmenttype", "code": "SYMPTOM_INQUIRY"}]}], "patientInstruction": "patient-instruction"}