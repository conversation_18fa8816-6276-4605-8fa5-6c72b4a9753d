{"resourceType": "Appointment", "id": "appointment-id", "status": "entered-in-error", "identifier": [{"system": "http://varian.com/fhir/identifier/Appointment/id", "value": "appointment-id"}], "serviceType": [{"coding": [{"system": "http://varian.com/fhir/CodeSystem/noona-appointmenttype", "code": "treatment-id"}]}], "reasonCode": [{"coding": [{"system": "http://varian.com/fhir/CodeSystem/noona-appointmentReasonCode", "code": "recode"}]}], "start": "fhir-datetime", "minutesDuration": 15, "participant": [{"actor": {"type": "Patient", "reference": "Patient-id"}}, {"actor": {"type": "Location", "identifier": {"system": "http://varian.com/fhir/identifier/Location/id", "value": "treatment-id"}}}]}