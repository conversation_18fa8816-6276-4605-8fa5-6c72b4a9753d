*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource


*** Variables ***
${spmc_clinic_toggle_header}    header-clinic-title
${patient_confirmation_modal}    //*[@id="confirm-modal"]


*** Keywords ***
Login As Patient Via API
    [Documentation]    ${login_token} is clinic's EHR token
    [Arguments]    ${email}    ${clinic_id}    ${level}=test    ${patient_password}=default
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    IF    '${level}'=='suite'
        Set Suite Variable    &{header}
    ELSE
        Set Test Variable    &{header}
    END
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}loginPatient.json
    ${body_mod}    Replace String    ${body}    email    ${email}
    ${body_mod}    Replace String    ${body_mod}    clinic_id    ${clinic_id}
    IF    '${patient_password}'!='default'
        ${body_mod}    Replace String    ${body_mod}    ${DEFAULT_PASSWORD}    ${patient_password}
    END
    IF    '${level}'=='suite'
        Set Suite Variable    ${body_mod}
    ELSE
        Set Test Variable    ${body_mod}
    END
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${PATIENT_URL}    verify=True
    ${response}    POST On Session    noona    /api/patient    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${result_json}    Get From Dictionary    ${response_json}    result
    ${login_token}    Get From Dictionary    ${result_json}    loginToken
    ${patient_user_id}    Get From Dictionary    ${result_json}    userId
    IF    '${level}'=='suite'
        Set Suite Variable    ${login_token}
        Set Suite Variable    ${patient_user_id}
    ELSE
        Set Test Variable    ${login_token}
        Set Test Variable    ${patient_user_id}
    END
    Delete All Sessions

Send Delete Patient Request
    [Documentation]    Precondition: Login As Patient Via API has been executed first
    [Arguments]    ${level}=test    ${patient_password}=default
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    IF    '${level}'=='suite'
        Set Suite Variable    &{header}
    ELSE
        Set Test Variable    &{header}
    END
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}createDeletePatientRequest.json
    IF    '${patient_password}'!='default'
        ${body}    Replace String    ${body}    ${DEFAULT_PASSWORD}    ${patient_password}
    END
    IF    '${level}'=='suite'
        Set Suite Variable    ${body}
    ELSE
        Set Test Variable    ${body}
    END
    ${body_json}    Evaluate    json.loads("""${body}""")    json
    Create Session    noona    ${PATIENT_URL}    verify=True
    ${response}    POST On Session    noona    /api/patient    headers=${header}    data=${body}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions
