*** Settings ***
Resource    ${EXECDIR}${/}resources${/}shared_login.resource
Resource    ${EXECDIR}${/}resources${/}common_mobile.resource
Resource    ${EXECDIR}${/}resources${/}common.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}patient_screens.resource
Resource    ${EXECDIR}${/}resources${/}mailosaur.resource


*** Variables ***
${native_app_login_button}                              //div[contains(text(),"Email And Password")]
${native_app_login_android}                             //*[@resource-id="login-button"]
${native_app_login_button_placeholder_ios}              //XCUIElementTypeButton[contains(@label, "{}")]
${remember_me_next_button}                              //ds-button[@id='remember-me-next-button']/button
${login_error_message}                                  //*[@id="login-form-error"]/p
${incorrect_password}                                   !passmenot?
${setup_pin_code_header}
...                                                     //*[@id="login-page"]/div[2]/login/div/div/div[2]/form[1]/div/legend
${pin_code_input_1}                                     //*[@id="set-pin"]/div/nh-set-pin/div/div[1]/div[1]
${pin_code_input_2}                                     //*[@id="set-pin"]/div/nh-set-pin/div/div[1]/div[2]
${pin_code_input_3}                                     //*[@id="set-pin"]/div/nh-set-pin/div/div[1]/div[3]
${pin_code_input_4}                                     //*[@id="set-pin"]/div/nh-set-pin/div/div[1]/div[4]
${do_not_set_pin_button}                                //*[@id="set-pin"]//nh-set-pin//div[3]/ds-button
${allow_notifications_header}                           //*[@id="login-page"]/div[2]/login//./div[2]/form[1]/div/legend
${allow_notifications_next_button}                      //div[text()='Next']
${allow_notifications_next_placeholder_android}         //div[text()='{}']    # use for different languages
${allow_notifications_next_button_placeholder_ios}      //XCUIElementTypeButton[@label="{}"]
${main_navigation_bar}                                  //nav[@id='main-navigation']
${more_button}                                          //*[@id="navigation-more-link"]/button
${app_logout_button}                                    //*[@id="more-link-logout"]
${app_logout_modal_logout_button}                       (//*[@id="logout-confirm-modal"]//button)[2]
${app_dont_set_pin_link}                                //div[contains(text(),"I don't want to set a PIN")]
${oidc_login_button_app}                                //XCUIElementTypeButton[contains(@label, 'Log in')]
${oidc_login_button_android}                            //*[@resource-id="kc-login"]
${app_about_link}                                       //*[contains(@text,"About") or contains(@label,"About")]
${compliance_button}                                    //*[@data-testid='compliance-button']/button
${native_app_compliance_close_button}                   //*[@text='Close dialog button' or @label='Close dialog button']
${app_close_about_box_modal}                            //*[@id='close-modal']/button
${allow_notifications_button_native_ios}                //XCUIElementTypeButton[@name='Allow']
${launcher_not_available_alert_button}                  //XCUIElementTypeButton[@name='OK']
${allow_notifications_android_higher_version}           //*[@resource-id='com.android.permissioncontroller:id/permission_allow_button']
${allow_notifications_button_oidc_false}                //XCUIElementTypeButton[@name='NEXT']
${more_information_link}                                //*[contains(text(),"More information")]
${privacy_screen_enabled}                               //span[contains(text(),"Enabled")]
${pin_input_field}                                      //input[@name='pin-code']
${pin_visible_field}                                    //div[@class='pin-input']/div
${pin_android_input_field_1}                            //*[@id="pin-code-digit-1"]
${pin_android_input_field_2}                            //*[@id="pin-code-digit-2"]
${pin_android_input_field_3}                            //*[@id="pin-code-digit-3"]
${pin_android_input_field_4}                            //*[@id="pin-code-digit-4"]
${live_update}                                          //*[@id="live-update-modal"]
${have_you_forgotten_your_pin}                          //*[@id="reset-pin-button"]
${set_pin_code_field}                                   //XCUIElementTypeTextField
${oidc_username_input_app}                              //XCUIElementTypeTextField
${oidc_username_input_android}                          //android.widget.EditText[1]
${oidc_password_input_app}                              //XCUIElementTypeSecureTextField
${oidc_password_input_app_android}                      //android.widget.EditText[2]
${landing_page_login_button_app}                        //div[contains(text(),'Email And Password')]
${landing_page_login_button_app_generic}                //*[@data-testid='landing-page-login-button']    #used by different language
${incorrect_password_error_message}
...                                                     Incorrect username or password. If you have forgotten your password, click "Problems logging in?"
${incorrect_password_error_message_oidc_android}        //*[@text='Incorrect username or password. If you have forgotten your password, click "Problems logging in?"']
${incorrect_password_error_message_oidc_ios}
...                                                     //XCUIElementTypeStaticText[contains(@label, 'Incorrect username or password. If you have forgotten your password, click "Problems logging in?"')]
${problems_logging_in_app}                              //*[@text="PROBLEMS LOGGING IN?" or @name="PROBLEMS LOGGING IN?"]
${problems_logging_in_instructions_line_1}              //*[@text="Enter the email address associated with your account, and we'll email you a link to reset your password." or @name="Enter the email address associated with your account, and we'll email you a link to reset your password."]
${problems_logging_in_instructions_line_2}              //*[@text="If you can't remember the email address, you can contact your clinic for help." or @name="If you can't remember the email address, you can contact your clinic for help."]
${problems_logging_in_title}                            //*[@text="Problems logging in?" or @name="Problems logging in?"]
${problems_logging_in_email_field}                      //XCUIElementTypeTextField | //android.widget.EditText
${problems_logging_in_email_field_android}              //*[@resource-id="password-reset-email"]
${problems_logging_in_modal_send_android}               //android.view.View[@resource-id="password-reset-submit"]/android.widget.Button
${problems_logging_in_modal_send_ios}                   (//XCUIElementTypeOther[@name="web dialogue"])[2]/XCUIElementTypeButton[2]
${password_reset_modal_close_button_app}                //*[@text="CLOSE" or @name="CLOSE"]
${connect_to_your_clinic_text}                          Connect to your clinic
${password_input_oidc_false}                            //input[@id='sms-password']
${password_input_container_oidc_false}                  //*[@class='sms-password-container']
${oidc_ios_2fa_field}                                   //*[@type='XCUIElementTypeTextField']
${webview_noona_app_test}                               WEBVIEW_com.noona.application.test
${webview_noona_app_staging}                            WEBVIEW_com.noona.application.staging
${language_web_dialog}                                  //*[@name='web dialog' or @label='web dialog']
${language_selection_label_button_android}              //*[@resource-id='language-button']
${language_selection_label_button_ios}                  //XCUIElementTypeButton[4]
${language_label_on_login_page_default_ENG}             //div[contains(text(),"English")]
${login_page_title}                                     Log in to Noona
${close_landing_page_android}                           //*[@resource-id="com.android.chrome:id/close_button"]
${native_app_accept_all_cookies_button}                 //*[@name='ACCEPT ALL' or @text='ACCEPT ALL']
${native_app_email_and_password_button}                 //*[@name='EMAIL AND PASSWORD' or *[contains(@text, 'EMAIL AND PASSWORD')]]
${native_app_problems_logging_in_link}                  //*[@name='PROBLEMS LOGGING IN?' or @text='PROBLEMS LOGGING IN?']
${native_app_password_reset_modal_close_button}         //*[@name='CLOSE' or @text='CLOSE']



*** Keywords ***
Login To Native App In Browserstack
    [Arguments]    ${email}    ${password}=${DEFAULT_PASSWORD}
    IF    '${PLATFORM_NAME}'=='android'    Disable Privacy Screen
    Login To Native App From Landing Page    ${email}    ${password}

Login To Native App In Browserstack As Delegate User
    [Arguments]    ${email}    ${password}=${DEFAULT_PASSWORD}
    IF    '${PLATFORM_NAME}'=='android'    Disable Privacy Screen
    Login To Native App From Landing Page Without Notification    ${email}    ${password}
    Wait Until Page Contains    Log out
    Wait Until Page Contains    Privacy statement

Login To Native App From Landing Page
    [Arguments]    ${email}    ${password}
    IF    '${PLATFORM_NAME}'=='android'    Scroll Element Into View    ${landing_page_login_button_app}
    Try To Click Element    ${landing_page_login_button_app}    wait_in_seconds=10s
    Input Login Credentials And Login With OIDC    ${email}    ${DEFAULT_PASSWORD}
    Allow Notifications
    ${status}    Run Keyword And Return Status    Wait Until Page Contains Element    ${approve_elements_checkbox}    timeout=10s
    IF    ${status}!=${TRUE}
        Wait Until Page Contains Element    ${main_navigation_bar}
    END

Login To Native App From Landing Page With Selected Language
    [Documentation]    Use this keyword and expand the logic if needed when testing native app in other languages than ENG & FI
    [Arguments]    ${email}    ${selected_language}
    Sleep    5s
    ${expected_button_label}  Set Variable If    '${selected_language}' == 'English'    EMAIL AND PASSWORD
    ...    '${selected_language}' == 'Suomi'    SÄHKÖPOSTI JA SALASANA
    ...    '${selected_language}' == 'Norsk'    E-POSTADRESSE OG PASSORD
    ${actual_label}    Get Text    ${landing_page_login_button_app_generic}
    Should Be Equal    ${actual_label}    ${expected_button_label}
    Try To Click Element    ${landing_page_login_button_app_generic}/button
    Input Login Credentials And Login With OIDC In Selected Language    ${email}    ${DEFAULT_PASSWORD}

Login To Native App From Landing Page Without Notification
    [Arguments]    ${email}    ${password}
    Try To Click Element    ${landing_page_login_button_app}    wait_in_seconds=10s
    Input Login Credentials And Login With OIDC    ${email}    ${DEFAULT_PASSWORD}

Login To Native App With Incorrect Password
    [Arguments]    ${email}    ${password}=${incorrect_password}    ${remember_login}=yes
    Try To Click Element    ${native_app_login_button}
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    IF    '${PLATFORM_NAME}'=='android'
        Wait Until Keyword Succeeds    5x    200ms    Hide Keyboard
        Wait Until Element Is Visible    xpath=${oidc_password_input_app_android}    timeout=7s
        Clear Patient Login Details
        Switch To Context    NATIVE_APP
        Input Text    xpath=${oidc_password_input_app_android}    ${password}
        Input Text    xpath=${oidc_username_input_android}    ${email}
        Try To Click Native App Element    ${oidc_login_button_android}
        Wait Until Page Contains    ${incorrect_password_error_message}
    ELSE IF    '${PLATFORM_NAME}'=='ios'
        Wait Until Element Is Visible    xpath=${oidc_password_input_app}    timeout=10s
        Input Text    xpath=${oidc_password_input_app}    ${password}
        Input Text    xpath=${oidc_username_input_app}    ${email}${\n}
        Wait Until Element Is Visible
        ...    xpath=//XCUIElementTypeStaticText[@label='${incorrect_password_error_message}']
    END
    Switch To Context    ${contexts}[1]

Input Login Credentials And Login With OIDC
    [Arguments]    ${username}    ${password}
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    IF    '${PLATFORM_NAME}'=='android'
        Wait Until Keyword Succeeds    5x    200ms    Hide Keyboard
        ${keyboard_status}    Is Keyboard Shown
        IF    ${keyboard_status} == True
            Click Text    ${login_page_title}    exact_match=True
        END
        Wait Until Element Is Visible    ${oidc_password_input_app_android}    timeout=20s
        Input Text    xpath=${oidc_password_input_app_android}    ${password}
        Input Text    xpath=${oidc_username_input_android}    ${username}
        Try To Click Native App Element    ${oidc_login_button_android}
    ELSE IF    '${PLATFORM_NAME}'=='ios'
        ${is_password_field_visible}    Run Keyword And Return Status    Wait Until Element Is Visible    xpath=${oidc_password_input_app}    timeout=5s
        ${is_launcher_alert_visible}    Run Keyword And Return Status    Element Should Be Visible    ${launcher_not_available_alert_button}    #remove when TESTING-352 is fixed
        IF    ${is_password_field_visible}==${FALSE} and ${is_launcher_alert_visible}==${TRUE}
            Click Element    ${launcher_not_available_alert_button}
        END
        Input Text    xpath=${oidc_password_input_app}    ${password}
        Input Text    xpath=${oidc_username_input_app}    ${username}${\n}
    END
    Switch To Context    ${contexts}[1]

Input Login Credentials And Login With OIDC In Selected Language
    [Documentation]    This keyword is specfic to language testing test suite for a reason: specifically targetting to NOONA_WEBVIEW while "Input Login Credentials And Login With OIDC"
    ...    is changing to the context at index 1 which we cannot control.
    [Arguments]    ${username}    ${password}
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    IF    '${PLATFORM_NAME}'=='android'
        Wait Until Keyword Succeeds    5x    200ms    Hide Keyboard
        ${keyboard_status}    Is Keyboard Shown
        IF    ${keyboard_status} == True
            Click Text    ${login_page_title}    exact_match=True
        END
        ${is_translation_popup_visible}    Run Keyword And Return Status
        ...    Wait Until Element Is Visible
        ...    ${google_translation_popup_close_button}
        IF    '${is_translation_popup_visible}' == 'True'
            Dismiss Translation Popup
        END
        Sleep    1s
        Wait Until Element Is Visible    ${oidc_password_input_app_android}
        Input Text    xpath=${oidc_password_input_app_android}    ${password}
        Input Text    xpath=${oidc_username_input_android}    ${username}
        IF    '${PLATFORM_VERSION}'=='13.0' or '${PLATFORM_VERSION}'=='14.0'    # steps required due to a minimize tab element displayed, which makes page not interactable
            Try To Click Native App Element    ${oidc_username_input_android}
            Hide Keyboard
        END
        Try To Click Native App Element    ${oidc_password_input_app_android}    #to show the send button
        Press Keycode    61    #to show the send button
        Try To Click Native App Element    ${oidc_login_button_android}
    ELSE IF    '${PLATFORM_NAME}'=='ios'
        Wait Until Element Is Visible    xpath=${oidc_password_input_app}    timeout=5s
        Input Text    xpath=${oidc_password_input_app}    ${password}
        ${email_input_text}    Get Text    xpath=${oidc_username_input_app}
        IF    '${email_input_text}' == '${EMPTY}'
            Input Text    xpath=${oidc_username_input_app}    ${username}${\n}
        ELSE
            Clear Text    xpath=${oidc_username_input_app}
            Input Text    xpath=${oidc_username_input_app}    ${username}${\n}
        END
    END
    @{current_context}    Get Contexts
    IF    '${NOONA_WEBVIEW}' in @{current_context}
        Switch To Context    ${NOONA_WEBVIEW}
    ELSE
        Switch To Context    ${current_context}[1]
    END

Input Password With OIDC
    [Arguments]    ${password}
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    IF    '${PLATFORM_NAME}'=='android'
        Hide Keyboard
        Wait Until Element Is Visible    xpath=${oidc_password_input_app_android}    timeout=5s
        Input Text    xpath=${oidc_password_input_app_android}    ${password}
        Hide Keyboard
        Try To Click Native App Element    ${oidc_login_button_android}
    ELSE IF    '${PLATFORM_NAME}'=='ios'
        Wait Until Element Is Visible    xpath=${oidc_password_input_app}    timeout=5s
        Hide Keyboard
        Wait Until Element Is Visible    xpath=${oidc_password_input_app}    timeout=5s
        Input Text    xpath=${oidc_password_input_app}    ${password}${\n}
    END
    Switch To Context    ${contexts}[1]

Click Compliance Button
    Wait Until Element Is Visible    ${compliance_button}
    Try To Click Element    ${compliance_button}

Disable Privacy Screen
    [Documentation]    works with android only
    Click Compliance Button
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    Wait Until Element Is Visible    ${app_about_link}    timeout=10s
    Try To Click Native App Element    ${app_about_link}
    Switch To Context    ${contexts}[1]
    Wait Until Element Is Visible    ${more_information_link}
    Try To Click Native App Element    ${more_information_link}
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${privacy_screen_enabled}
    ...    timeout=5
    IF    ${status}==${FALSE}
        Try To Click Element    ${app_close_about_box_modal}
        Wait Until Element Is Visible    ${app_about_link}
        Try To Click Element    ${app_about_link}
        Try To Click Native App Element    ${more_information_link}
    END
    Try To Click Element    ${privacy_screen_enabled}
    Sleep    1    # waits for 1s for privacy to be completely disabled
    Try To Click Element    ${app_close_about_box_modal}
    Wait Until Page Does Not Contain Element    ${app_close_about_box_modal}
    Sleep    1    # gives enough time to transition from closing modal to landing page (only on android)
    Switch To Context    NATIVE_APP
    Try To Click Native App Element    ${native_app_compliance_close_button}
    Switch To Context    ${contexts}[1]

Wait For Target Window
    [Documentation]    During transition, target window closes and opens again. nms9-ver-357-app android fails without it.
    ${status}    Run Keyword And Return Status    Element Should Be Visible    ${allow_notifications_next_button}
    WHILE    ${status}==${FALSE}    limit=5
        IF    '${PLATFORM_NAME}'=='android'
            Hide Keyboard
            @{contexts}    Get Contexts
            IF    'test' in '${ENVIRONMENT}'
                Switch to Context    ${webview_noona_app_test}
            ELSE
                Switch To Context    ${webview_noona_app_staging}
            END
        END
        ${status}    Run Keyword And Return Status    Element Should Be Visible    ${allow_notifications_next_button}
        Sleep    1
    END

Set Pin Code
    Wait Until Element Is Visible    xpath=${pin_visible_field}
    IF    '${PLATFORM_NAME}'=='android'
        Set Pin For Android
    ELSE
        Set Pin For IOS
    END
    # Wait Until Element Is Visible    ${allow_notifications_next_button}    timeout=10s

Set 4-digit Code
    ${4_random_numbers}    Generate Random String    4    [NUMBERS]
    @{4_digits}    Convert To List    ${4_random_numbers}
    Set Suite Variable    ${4_digits}

Set Pin For Android
    Sleep    1
    Input Text    xpath=${pin_input_field}    ${4_digits}
    Wait Until Page Contains    Confirm your PIN code
    Input Text    xpath=${pin_input_field}    ${4_digits}
    Sleep    1

Set Pin For IOS
    Switch To Context    NATIVE_APP
    Input Text    ${set_pin_code_field}    ${4_digits}
    Wait Until Page Contains    Confirm your PIN code
    Input Text    ${set_pin_code_field}    ${4_digits}
    @{contexts}    Get Contexts
    Switch To Context    ${contexts}[1]

Allow Notifications
    @{contexts}    Get Contexts
    IF    '${PLATFORM_NAME}'=='ios'
        Wait Until Element Is Visible    ${allow_notifications_next_button}
        Click Element    ${allow_notifications_next_button}
        Switch To Context    NATIVE_APP
        Wait Until Element Is Visible    ${allow_notifications_button_native_ios}
        Click Element    ${allow_notifications_button_native_ios}
        Switch To Context    ${contexts}[1]
    ELSE
        ${current_context}    Get Current Context
        IF    'WEBVIEW_com' not in '${current_context}'
            IF    'test' in '${ENVIRONMENT}'
                Switch to Context    ${webview_noona_app_test}
            ELSE
                Switch To Context    ${webview_noona_app_staging}
            END
        END
        Wait Until Element Is Visible    ${allow_notifications_next_button}    timeout=15s
        Click Element    ${allow_notifications_next_button}
        Switch To Context    NATIVE_APP
        ${status_second_notification}    Run Keyword And Return Status    Wait Until Element Is Visible    ${allow_notifications_android_higher_version}    5s
        IF    ${status_second_notification}==True
            Try To Click Native App Element    ${allow_notifications_android_higher_version}
        END
        IF    'test' in '${ENVIRONMENT}'
            Switch to Context    ${webview_noona_app_test}
        ELSE
            Switch To Context    ${webview_noona_app_staging}
        END
    END

Allow Notifications In Selected Language
    [Arguments]    ${language}=English
    Sleep    1s
    @{contexts}    Get Contexts
    IF    '${PLATFORM_NAME}'=='ios'
        @{contexts}    Get Contexts
        Switch To Context    ${contexts}[1]
    ELSE
        @{contexts}    Get Contexts
        Switch To Context    ${NOONA_WEBVIEW}
        # only click this allow button in Android if it is visible otherwise not (use case: simutaneously login/ logout on same device)
    END
    IF    '${language}' == 'Suomi'
        ${android_allow_notif_button}    Format String
        ...    ${allow_notifications_next_placeholder_android}
        ...    Seuraava
    ELSE
        ${android_allow_notif_button}    Set Variable    ${allow_notifications_next_button}
    END
    Try To Click Element    ${android_allow_notif_button}    wait_in_seconds=7s
    Switch To Context    NATIVE_APP
    IF    '${PLATFORM_NAME}'=='android'
        ${status_second_notification}    Run Keyword And Return Status    Wait Until Element Is Visible    ${allow_notifications_android_higher_version}    5s
        IF    ${status_second_notification}==True
            Try To Click Native App Element    ${allow_notifications_android_higher_version}
        END
        IF    'test' in '${ENVIRONMENT}'
            Switch to Context    ${webview_noona_app_test}
        ELSE
            Switch To Context    ${webview_noona_app_staging}
        END
    ELSE
        ${status}    Run Keyword And Return Status
        ...    Wait Until Element Is Visible
        ...    ${allow_notifications_button_native_ios}
        Set Test Variable    ${status}
        IF    ${status}
            Click Element    ${allow_notifications_button_native_ios}
        END

    END

Allow Notifications Upon Re-login
    @{contexts}    Get Contexts
    IF    '${PLATFORM_NAME}'=='ios'
        Try To Click Native App Element    ${allow_notifications_next_button}
        Switch To Context    ${contexts}[1]
    ELSE
        ${current_context}    Get Current Context
        IF    'WEBVIEW_com' not in '${current_context}'
            IF    'test' in '${ENVIRONMENT}'
                Switch to Context    ${webview_noona_app_test}
            ELSE
                Switch To Context    ${webview_noona_app_staging}
            END
        END
        Wait Until Element Is Visible    ${allow_notifications_next_button}    timeout=15s
        Click Element    ${allow_notifications_next_button}
        IF    'test' in '${ENVIRONMENT}'
            Switch to Context    ${webview_noona_app_test}
        ELSE
            Switch To Context    ${webview_noona_app_staging}
        END
    END

Put App In Backgroud
    [Documentation]    Add "Portrait" keyword since app automatically rotates to landscape when comming back from Background
    [Arguments]    ${time_set_for_app_in_backgroud}
    Background Application    ${time_set_for_app_in_backgroud}
    Portrait

Patient selects "Have you forgotten your PIN?"
    Wait Until Element Is Visible    ${have_you_forgotten_your_pin}
    Try To Click Native App Element    ${have_you_forgotten_your_pin}

Patient is logged out from the application
    AppiumLibrary.Wait Until Element Is Visible    ${native_app_login_button}

Re-login To Native App In Browserstack
    [Arguments]    ${email}    ${password}=${DEFAULT_PASSWORD}    ${remember_login}=no    ${manage_pin}=yes
    IF    '${PLATFORM_NAME}'=='android'    Disable Privacy Screen
    Wait Until Page Contains Element    ${native_app_login_button}    timeout=10
    Click Element    ${native_app_login_button}
    Input Password With OIDC    ${password}
    Wait For Target Window
    Allow Notifications Upon Re-login
    Wait Until Page Contains Element    ${main_navigation_bar}

Input Login Credentials With Error Message    # oidc
    [Documentation]    This uses special logic when clicking login as button is out of view when error message is displaye
    [Arguments]    ${username}    ${password}
    IF    '${PLATFORM_NAME}'=='android'
        Hide Keyboard
        Wait Until Element Is Visible    xpath=${oidc_password_input_app_android}    timeout=5s
        Input Text    xpath=${oidc_username_input_android}    ${username}
        Input Text    xpath=${oidc_password_input_app_android}    ${password}
        Hide Keyboard
        Press Keycode    66
        Wait Until Page Contains Element    ${incorrect_password_error_message_oidc_android}    timeout=5s
    ELSE IF    '${PLATFORM_NAME}'=='ios'
        Wait Until Element Is Visible    xpath=${oidc_password_input_app}    timeout=5s
        Input Text    xpath=${oidc_password_input_app}    ${password}
        Input Text    xpath=${oidc_username_input_app}    ${username}${\n}
        Wait Until Page Contains Element    ${incorrect_password_error_message_oidc_ios}    timeout=5s
    END

Launch Noona
    IF    '${PLATFORM_NAME}'=='android'    Disable Privacy Screen

Login To Native App With 2FA In Browserstack
    [Arguments]
    ...    ${email}
    ...    ${password}=${DEFAULT_PASSWORD}
    ...    ${remember_login}=no
    ...    ${manage_pin}=yes
    ...    ${autho}=yes
    # mailosaur server id = tiazhwes
    IF    '${autho}'=='yes'
        Delete All SMS From Mailosaur
    END
    IF    '${PLATFORM_NAME}'=='android'    Disable Privacy Screen
    Try To Click Native App Element    ${landing_page_login_button_app}
    Input Login Credentials And Login With OIDC    ${email}    ${DEFAULT_PASSWORD}
    IF    '${autho}'=='yes'
        Get 2FA code
        IF    '${PLATFORM_NAME}'=='android'
            Input 2FA Code Android
            Wait For Target Window
        ELSE
            Input 2FA Code iOS
        END
    END
    Allow Notifications
    Wait Until Page Contains Element    ${main_navigation_bar}

Get 2FA code
    ${sms_message}    User Received SMS
    ${get_sms_code}    Get Substring    ${sms_message}    0    6
    Set Test Variable    ${get_sms_code}

Input 2FA Code iOS
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    Sleep    3s
    Click Element    xpath=(${oidc_ios_2fa_field})[1]
    Input Text    xpath=(${oidc_ios_2fa_field})[1]    ${get_sms_code}[0]
    Input Text    xpath=(${oidc_ios_2fa_field})[2]    ${get_sms_code}[1]
    Input Text    xpath=(${oidc_ios_2fa_field})[3]    ${get_sms_code}[2]
    Input Text    xpath=(${oidc_ios_2fa_field})[4]    ${get_sms_code}[3]
    Input Text    xpath=(${oidc_ios_2fa_field})[5]    ${get_sms_code}[4]
    Input Text    xpath=(${oidc_ios_2fa_field})[6]    ${get_sms_code}[5]
    @{contexts}    Get Contexts
    Switch To Context    ${contexts}[1]
    Sleep    1s

Input 2FA Code Android
    Switch To Context    WEBVIEW_chrome
    Sleep    3
    Try To Input Native App Text    //*[@id='sms-facade-0']    ${get_sms_code}[0]
    Try To Input Native App Text    //*[@id='sms-facade-1']    ${get_sms_code}[1]
    Try To Input Native App Text    //*[@id='sms-facade-2']    ${get_sms_code}[2]
    Try To Input Native App Text    //*[@id='sms-facade-3']    ${get_sms_code}[3]
    Try To Input Native App Text    //*[@id='sms-facade-4']    ${get_sms_code}[4]
    Try To Input Native App Text    //*[@id='sms-facade-5']    ${get_sms_code}[5]
    Sleep    1s

Re-login To Native App With 2FA In Browserstack
    [Arguments]
    ...    ${email}
    ...    ${password}=${DEFAULT_PASSWORD}
    ...    ${remember_login}=no
    ...    ${manage_pin}=yes
    ...    ${autho}=yes
    # mailosaur server id = tiazhwes
    IF    '${autho}'=='yes'
        Delete All SMS From Mailosaur
    END
    Try To Click Native App Element    ${landing_page_login_button_app}
    ${text}    Get Username
    IF    '${text}'=='${email}'
        Input Password With OIDC    ${DEFAULT_PASSWORD}
    ELSE
        Input Login Credentials And Login With OIDC 2FA    ${email}    ${DEFAULT_PASSWORD}
    END
    IF    '${autho}'=='yes'
        Get 2FA code
        IF    '${PLATFORM_NAME}'=='android'
            Input 2FA Code Android
        ELSE
            Input 2FA Code iOS
        END
    END
    Wait For Target Window
    Allow Notifications Upon Re-login
    Wait Until Page Contains Element    ${main_navigation_bar}

Input Login Credentials And Login With OIDC 2FA
    [Arguments]    ${username}    ${password}
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    IF    '${PLATFORM_NAME}'=='android'
        Hide Keyboard
        Wait Until Element Is Visible    xpath=${oidc_password_input_app_android}    timeout=7s
        Input Text    xpath=${oidc_username_input_android}    ${username}
        Input Text    xpath=${oidc_password_input_app_android}    ${password}
        Try To Click Native App Element    ${oidc_login_button_android}
    ELSE IF    '${PLATFORM_NAME}'=='ios'
        Wait Until Element Is Visible    xpath=${oidc_password_input_app}    timeout=5s
        Input Text    xpath=${oidc_password_input_app}    ${password}
        Input Text    xpath=${oidc_username_input_app}    ${username}${\n}
    END
    Switch To Context    ${contexts}[1]

Get Username
    [Documentation]    checks if username repopulates upon relogin
    ${username_field}    Set Variable If    '${PLATFORM_NAME}'=='android'    ${oidc_username_input_android}
    ...    ${oidc_username_input_app}
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    ${text}    Get Text    ${username_field}
    RETURN    ${text}

Select Native App Language
    [Arguments]    ${preferred_language}    ${switch_to_webview_android}=no
    @{contexts}    Get Contexts
    Switch to Context    NATIVE_APP
    IF    '${PLATFORM_NAME}' == 'android'
        ${language_select_button}    Set Variable    ${language_selection_label_button_android}
    ELSE
        ${language_select_button}    Set Variable    ${language_selection_label_button_ios}
    END
    Wait Until Element Is Visible     ${language_select_button}
    ${current_language_label}    Get Text    ${language_select_button}
    ${current_language}    Strip String    ${current_language_label}
    ${current_language_lowercase}    Convert To Lower Case    ${current_language}
    ${current_language_login_page}    Convert To Title Case    ${current_language_lowercase}
    Wait Until Element Is Visible    ${language_select_button}
    Click Element    ${language_select_button}
    IF    '${switch_to_webview_android}'=='yes'
        Switch to Context    ${NOONA_WEBVIEW}
    ELSE
        Switch to Context    ${contexts}[1]
    END
    Wait Until Page Contains Element    xpath=//*[contains(text(), "${preferred_language}")]    timeout=5s
    Click Element    xpath=//*[contains(text(), "${preferred_language}")]

Input Login Credentials And Login On Native App
   [Documentation]    Keyword created as replica of Input Login Credentials And Login With OIDC and should be used for those cases where multiple logins are needed in same session
    [Arguments]    ${username}    ${password}
    @{contexts}    Get Contexts
    Switch To Context    NATIVE_APP
    IF    '${PLATFORM_NAME}'=='android'
        Wait Until Keyword Succeeds    5x    200ms    Hide Keyboard
        ${keyboard_status}    Is Keyboard Shown
        IF    ${keyboard_status} == True
            Click Text    ${login_page_title}    exact_match=True
        END
        Wait Until Element Is Visible    ${oidc_password_input_app_android}    timeout=20s
        Input Text    xpath=${oidc_password_input_app_android}    ${password}
        Input Text    xpath=${oidc_username_input_android}    ${username}
        Try To Click Native App Element    ${oidc_login_button_android}
    ELSE IF    '${PLATFORM_NAME}'=='ios'
        Wait Until Element Is Visible    xpath=${oidc_password_input_app}    timeout=5s
        Input Text    xpath=${oidc_password_input_app}    ${password}
        Input Text    xpath=${oidc_username_input_app}    ${username}${\n}
    END
    IF    '${PLATFORM_NAME}'=='ios'
        Wait Until Keyword Succeeds    5x    500ms    Switch To Context    ${contexts}[1]
    ELSE
        IF    'test' in '${ENVIRONMENT}'
            Switch to Context    ${webview_noona_app_test}
        ELSE
            Switch To Context    ${webview_noona_app_staging}
        END
    END

Switch To Noona App Web Context
    IF    'test' in '${ENVIRONMENT}'
        Switch to Context    ${webview_noona_app_test}
    ELSE
        Switch To Context    ${webview_noona_app_staging}
    END