*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}modules.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}questionnaires.resource


*** Variables ***
${show_more_button}                             //div[text()=" Show more "]
${description_field}                            description    # text fields for most symptoms
${symptom_form_next_button}                     (//button/descendant::div[contains(text(),"Next")])[last()]
${symptom_form_summary_area}                    //*[@id='report-symptom-form-summary']
${attention_icon}                               //div[@class='dialog-icon']
${send_symptom_entry_to_clinic_button}          //label[text()='Send this symptom entry to the clinic']
${modify_symptom_entry_button}                  //label[text()='Modify this symptom entry and send it to the clinic']
${create_symptom_entry_button}                  //label[text()='Create a new symptom entry']
${emergency_symptom_instructions_paragraph1}
...    Your symptom description indicates that you might require immediate attention
...    from a medical professional. Please seek help from an emergency health service provider in your area if your
...    symptom gets more severe before a cancer nurse contacts you during the next working day.
${emergency_symptom_instructions_paragraph2}    Read more information from your message inbox. Symptom description is also saved to your diary.
${symptom_entry_sent_to_clinic_text}            Symptom entry sent to the clinic
${symptom_entry_in_your_diary_text}             Symptom entry in your diary
${earlier_symptom_next_button}                  //ds-button[contains(@class, 'earlier-symptom-action-button-next')]/button


*** Keywords ***
Select A Symptom From List
    [Arguments]    ${symptom}
    ${symptom_displayed}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    //div[text()=" ${symptom} "]
    ${show_more_displayed}    Run Keyword And Return Status    Wait Until Element Is Visible    ${show_more_button}
    IF    ${symptom_displayed}
        Try To Click Native App Element    //div[text()=" ${symptom} "]
    ELSE IF    ${symptom_displayed}==${FALSE} and ${show_more_displayed}
        Try To Click Native App Element    ${show_more_button}
        Try To Click Native App Element    //div[text()=" ${symptom} "]
    END

Input Symptom Description
    [Arguments]    ${text}
    Wait Until Element Is Visible    ${description_field}
    Try To Input Native App Text    ${description_field}    ${text}

Select Specific Answer To Question
    [Arguments]    ${question}    ${answer}
    ${element}    Set Variable    //h4[text()="${question}"]/../../../../div/descendant::label[text()="${answer}"]
    Wait Until Element Is Visible    ${element}
    Try To Click Native App Element    ${element}

Select Symptom Day
    [Documentation]    symptom_day could be Today, Symptom is chronic (persistent, long-standing, long-term)
    [Arguments]    ${symptom_day}
    ${element}    Set Variable
    ...    //h4/span[text()="When did you have this symptom?"]/../../div/descendant::label[text()=" ${symptom_day} "]
    Wait Until Element Is Visible    ${element}
    Try To Click Element    ${element}
    # TODO: Select symptomatic days

Click Symptom Form Next Button
    Wait Until Element Is Visible    xpath=${symptom_form_next_button}
    Try To Click Native App Element    xpath=${symptom_form_next_button}

Click Send Symptom To Clinic
    Wait Until Element Is Visible    xpath=(${submit_symptom_button})[last()]
    Try To Click Native App Element    xpath=(${submit_symptom_button})[last()]

Emergency Symptom Modal Is Displayed
    Wait Until Page Contains    ${symptom_entry_sent_to_clinic_text}
    Wait Until Page Contains Element    ${attention_icon}
    Wait Until Page Contains    Attention!
    Wait Until Page Contains    ${emergency_symptom_instructions_paragraph1}
    Wait Until Page Contains    ${emergency_symptom_instructions_paragraph2}

Go To Homepage
    Wait Until Element Is Visible    ${move_to_home_button}
    Try To Click Native App Element    ${move_to_home_button}
