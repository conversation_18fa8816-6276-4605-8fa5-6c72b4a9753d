*** Settings ***
Resource    ${EXECDIR}${/}resources${/}native_app${/}app_more.resource
Resource    ${EXECDIR}${/}resources${/}common_mobile.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}more${/}clinic_preferences.resource


*** Variables ***
${connection_with_device_removed_toast_message}     //*[@id="toast-container"]
${connected_devices_label}                          //*[@id="connected-devices-legend"]
${connected_devices_section}                        //*[@id="connected-devices"]


*** Keywords ***
Verify Connected Device From Account Preferences
    app_more.Go To Account Preferences
    Wait Until Element Is Visible    ${connected_devices_label}
    Wait Until Element Is Visible    ${connected_devices_section}

Remove Connected Device From Account Preferences
    app_more.Go To Account Preferences
    Scroll Element Into View    ${save_profile_button}
    Try To Click Native App Element    //*[@id="${remove_connection_button}"]/button
    Wait Until Element Is Visible    ${connection_with_device_removed_toast_message}

# Update Email In Account Preferences
# Update Phone Number In Account Preferences
# Update Notification About Clinic Messages
# Update Login Preference From Account Preferences
# Change Password From Account Preferences
# Reset Changes From Account Preferences
# Save Changes Of Account Preferences
