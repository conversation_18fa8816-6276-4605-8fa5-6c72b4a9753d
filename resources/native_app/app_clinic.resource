*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}clinic.resource


*** Variables ***
${app_question_field}                           //textarea[@id='question-content']
${app_sent_nonclinical_issue_close_button}      //*[@id='open-question-go-to-home']/button/div
${app_question_sent_modal_close_button}         //XCUIElementTypeButton[@name='CLOSE']
${inbox_message_topic}                          //div[contains(text(),"{}")]
${your_question_static_text}                    //XCUIElementTypeStaticText[@name='Your question']
${open_question_test_question}                  Autotest question content: Nordics <PERSON>.?!
${app_send_question_button}                     //ds-button[@id='send-open-question']/button
${question_sent_to_clinic_close}                //ds-button[@id='open-question-go-to-home']/button
${clinic_header}                                //h1[text()='Clinic ']
${message_date}
...                                             //div[@class='message-area']//div[contains(@class, 'message-date')]/span[2]
${app_first_clinic_message}                     (//h1[@id='clinic-inbox-header']/../ul/li)[1]
${patient_sent_a_symptom_entry_text}            Patient sent a symptom entry.
${emergency_instructions}                       The symptom you just described indicates that you might require immediate attention from a medical professional.
${message_content_area}                         //div[contains(@class, 'message-text')]/p
${privacy_button}                               //*[@text='Open terms and conditions dialog' or @label='Open terms and conditions dialog']
${about_button}                                 //*[@text='ABOUT' or @label='ABOUT']
${privacy_about_close_button}                   //*[@text='Close dialog button' or @label='Close dialog button']
${about_page_header}                            //*[@class="about-heading"]
${privacy_text_first_paragraph}                 //*[@id="showdown"]//p[2]


*** Keywords ***
Select Ask about non-clinical topic
    Wait Until Element Is Visible    ${ask_about_other_issues_icon}
    Try To Click Native App Element    ${ask_about_other_issues_icon}

Select Ask about symptoms
    Wait Until Element Is Visible    ${ask_about_symptom_button}
    Try To Click Native App Element    ${ask_about_symptom_button}

Select topic
    [Arguments]    ${topic}
    ${topic_element}    Set Variable    //label[contains(text(),'${topic}')]
    Wait Until Element Is Visible    ${topic_element}
    Try To Click Native App Element    ${topic_element}

Enter question
    ${now}    Get Current Date    result_format=%-d.%-m
    Input Text    ${app_question_field}    ${now}${SPACE}${open_question_test_question}

Send question to clinic
    Wait Until Element Is Visible    ${app_send_question_button}
    Try To Click Native App Element    ${app_send_question_button}

Close Question Was Sent Modal
    Wait Until Page Contains    Your question was sent to the clinic
    Try To Click Native App Element    ${question_sent_to_clinic_close}

Sent message is displayed in the patient's inbox
    [Arguments]    ${topic}    ${message_content}
    ${expected_topic}    Format String    ${inbox_message_topic}    ${topic}
    Wait Until Page Contains Element    xpath=(${expected_topic})[1]    timeout=10
    Try To Click Native App Element    xpath=(${expected_topic})[1]/../..
    ${now}    Get Current Date    result_format=%-d.%-m
    Wait Until Element Is Visible    ${message_content_area}
    ${actual_content}    Get Text    ${message_content_area}
    Should Contain    ${actual_content}    ${message_content}
    Wait Until Page Contains    ${now}

Select Latest Clinic Message
    [Documentation]    patient is already in clinic menu
    Wait Until Element Is Visible    xpath=${app_first_clinic_message}
    Try To Click Native App Element    xpath=${app_first_clinic_message}

Message Date In Details Is Correct
    [Documentation]    message date inside message modal when message is clicked
    [Arguments]    ${date}
    Wait Until Element Is Visible    ${message_date}    timeout=10s
    ${text}    Get Text    ${message_date}
    Should Contain    ${text}    ${date}
