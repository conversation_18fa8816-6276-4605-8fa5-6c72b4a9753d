*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}diary_add_menu${/}shared_add_menu.resource
Resource    ${EXECDIR}${/}resources${/}common_mobile.resource


*** Keywords ***
Add Symptom To Diary
    Try To Click Native App Element    xpath=//*[@id='${add_menu_button}']/button
    Try To Click Native App Element    ${symptom_entry}

# Go To Diary

# Go To Library

# Go To Plus Icon

# Go To Clinic (Once this keyword is added, delete duplicate keyword from app_clinic.resource and update related test case with this keyword)

# Go To More
