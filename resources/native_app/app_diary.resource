*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}clinic.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}diary.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${diary_modal_close_button}     //div[contains(text(),'Close')]
${diary_header}                 //h1[text()='Diary']
${date_when_most_severe}        //div[contains(text(),"Date when most severe")]/following-sibling::div
${sent_to_clinic_text}          Sent to clinic
${upcoming_event_title}         //p[starts-with(@class, 'event-title')]


*** Keywords ***
Select Latest Symptom Diary Entry
    [Documentation]    Selects the first occurence of the symptom entry in diary
    [Arguments]    ${symptom}
    Wait Until Element Is Visible    xpath=${diary_latest_entry_section}
    ${latest_entry}    Format String    ${diary_latest_symptom_entry}    ${symptom}
    Try To Click Native App Element    xpath=(${latest_entry})[1]

Date When Most Severe Is Correct
    [Documentation]    symptom date when diary entry is clicked
    [Arguments]    ${date}
    Wait Until Element Is Visible    xpath=${date_when_most_severe}
    ${text}    Get Text    xpath=${date_when_most_severe}
    Should Be Equal    ${date}    ${text}

Diary Menu Is Selected
    Wait Until Element Is Visible    //*[@id='${diary_icon}']
    ${attr}    Get Element Attribute    //*[@id='${diary_icon}']    class
    Should Contain    ${attr}    active ds-button

Upcoming Event Title Is Correct
    [Documentation]    checks the first occurence of the event
    [Arguments]    ${title}
    Wait Until Element Is Visible    ${upcoming_event_title}
    ${actual_text}    Get Text    ${upcoming_event_title}
    Should Be Equal    ${actual_text}    ${title}
