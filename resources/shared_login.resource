*** Settings ***
Resource    ${EXECDIR}${/}resources${/}common_setup.resource
Resource    ${EXECDIR}${/}resources${/}native_app${/}app_patient_login.resource

*** Variables ***
${email_textbox}                                //*[@id='email']
${password_textbox}                             //*[@id='pwd']
${login_button}                                 //*[@id='email-and-password-next-button']
${language_dropdown}                            //*[@id='locale']
${language_value}                               //*[@id='locale']//*[@role='option']/span[text()='{}']
${keep_me_logged_in_yes}                        rememberMe_1
${keep_me_logged_in_no}                         rememberMe_0
${keep_me_logged_in_yes_patient}                remember-me-yes
${keep_me_logged_in_no_patient}                 remember-me-no
${keep_me_logged_in_radio}                      //input[@id='{}']/following-sibling::label
${keep_me_logged_in_next}                       remember-me-next-button
${choose_clinic_next_button}                    //button[text()='NEXT']
${choose_clinic_next_button_1}                  //*[@id="clinic-next-button"]
${clinic_list}                                  //*[@data-testid='clinic_selection--filter']
${incorrect_username_password}
...                                             Incorrect username or password.
...                                             If you have forgotten your password, please click Problems logging in -link.
${incorrect_error_message_oidc}
...                                             Incorrect username or password. If you have forgotten your password, click "Problems logging in?"
${logout_link}                                  //*[@data-testid='logout-link']
${more_link}                                    //*[@id='navigation-more-link']
${patient_logout_link}                          //*[@data-testid='more-link-logout']
${clinic_list_1}                                //div[@class='form-group ng-scope']/select
${ccd_record_1}                                 //nh-medical-records-view-modal/div[contains(@class,"checkbox no-checked")][1]
${verification_code_input}                      //*[@data-testid="codeInput"]
${verification_code_first_input}                (//*[@data-testid="codeInput"]/../div)[1]
${2fa_next_button}                              //*[@id="two-factor-auth-next"]
${patient_sms_code_first_input}                 (//*[@id="sms-password"]/../div)[3]
${mgmt_landing_page_login_button}               //*[@id="open-login"]
${login_verification_code_header}               //h2[contains(text(),'Verification code')]
${profile_incorrect_2FA_code_error}             //div[contains(text(),'Incorrect verification code.')]
${login_incorrect_2FA_code_error}               //p[contains(text(),'Incorrect verification code.')]
${clinic_send_new_verification_code_link}       //noona-two-factor-auth//a[contains(@class, "send-new-code-link")]
${patient_send_new_verification_code_link}
...                                             //*[@id="sms-password"]//a[contains(text(),'Send a new verification code')]
${landing_page_login_ds_button}                 landing-page-login-button    #ds-button needed in custom branding
${landing_page_login_button}                    //*[@id='landing-page-login-button']/button
${oidc_username_input}                          xpath=//*[@id="username"]
${oidc_password_input}                          xpath=//*[@id="password"]
${oidc_login_button}                            login
${oidc_sms_input}                               //*[@id="sms-facade-{}"]
${oidc_sms_input_admin}                         //*[@id="sms1"]
${oidc_sms_login_button}                        //*[@id="kc-login"]
${activation_login_button}                      //*[@id='activation-complete-next-button']/button
${oidc_login_button_id}                         //*[@id="kc-login"]    # specifically needed for getting custom branding color and Demo login
${accept_all_cookies}                           //*[contains(text(),"Accept all")]
${accept_all_cookies_for_clinic}                //*[@data-testid='accept-all-cookies']
${accept_necessary_cookies_for_clinic}          //*[@data-testid='accept-necessary-cookies']
${sso_login_link}                               //*[@data-testid="login-using-sso"]
${sso_email_textbox}                            //*[@id='ProviderId']
${ms_sso_email_textbox}                         //input[contains(@name,'loginfmt') and contains(@type,'email')]
${ms_sso_login_next_button}                     //input[contains(@id,'idSIButton9') and contains(@type,'submit')]
${sso_password_textbox}                         //input[contains(@name,'passwd') and contains(@type,'password')]
${sso_email_login_button}                       //input[contains(@type,'submit') and contains(@value,'Login')]
${sso_pwd_login_button}                         //input[contains(@type,'submit') and contains(@value,'Sign in')]
${stay_sign_in}                                 //*[contains(text(), 'Stay signed in?')]
${profile_drop_down}                            //*[@data-testid='profile-dropdown']
${switch_clinic_option}                         //*[@data-testid='change-clinic']
${clinic_selection_dropdown}                    //*[@data-testid='clinic_selection--filter']
${clinic_search_box}                            //input[contains(@type,'text')]
${switch_clinic_button}                         //*[@data-testid='confirm-clinic-change']
${logout_link_1}                                //*[@id='logout-link']


*** Keywords ***
Login To Noona
    [Arguments]    ${user}    ${email}    ${open_browser}=True
    IF    '${user}'=='patient'
        ${url}    Set Variable    ${PATIENT_LOGIN_URL}
    ELSE IF    '${user}'=='nurse'
        ${url}    Set Variable    ${NURSE_LOGIN_URL}
    ELSE IF    '${user}'=='admin'
        ${url}    Set Variable    ${MANAGEMENT_LOGIN_URL}
    END
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists} and ${open_browser}
        Open Browser    ${url}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE IF    ${open_browser}
        Open Browser    ${url}    ${BROWSER}    options=add_argument("--disable-search-engine-choice-screen")
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Sleep    1s
    Page Should Not Contain    503 Service Temporarily Unavailable
    IF    '${user}'=='nurse'
        Accept All Cookies If Visible For Clinic
    END
    IF    '${user}'=='patient'
        Accept All Cookies If Visible
    END
    IF    '${user}'!='admin'
        Wait until keyword succeeds    5x    1s    Select Language    ${language_value}
    END
    IF    '${user}'=='patient'
        Accept All Cookies If Visible
        Login As Patient From OIDC Login Page    ${email}
        Set Global Variable    ${remember_login}    none    # oidc does not handle keep me logged in atm
    ELSE
        Login As Patient From Legacy Login Page    ${email}
    END

Login As Patient From OIDC Login Page
    [Arguments]    ${email}    ${password}=${DEFAULT_PASSWORD}
    Wait Until Element Is Visible    ${landing_page_login_button}
    Try To Click Element    ${landing_page_login_button}
    Try To Input Text    ${oidc_username_input}    ${email}
    Try To Input Text    ${oidc_password_input}    ${password}
    Sleep    2    # add sleep due to NOONA-18026
    Try To Click Element    ${oidc_login_button}

Login As Patient From OIDC Login Page With Selected Language
    [Arguments]    ${email}    ${selected_language}=English
    IF    '${selected_language}' == 'English'
        ${landing_page_login_button}    Format String    //*[contains(text(),'{}')]    Email And Password
        Set Test Variable    ${landing_page_login_button}
    ELSE IF    '${selected_language}' == 'Suomi'
        ${landing_page_login_button}    Format String    //div[contains(text(),'{}')]    Sähköposti ja salasana
        Set Test Variable    ${landing_page_login_button}
    ELSE IF   '${selected_language}' == 'Deutsch'
        ${landing_page_login_button}    Format String    //div[contains(text(),'{}')]    E-Mail und Passwort
        Set Test Variable    ${landing_page_login_button}
    ELSE IF   '${selected_language}' == 'Français'
        ${landing_page_login_button}    Format String    //div[contains(text(),'{}')]    E-mail et mot de passe
        Set Test Variable    ${landing_page_login_button}
    ELSE IF   '${selected_language}' == 'Norsk'
        ${landing_page_login_button}    Format String    //div[contains(text(),'{}')]    E-postadresse og passord
        Set Test Variable    ${landing_page_login_button}
    END
    Accept All Cookies If Visible
    Wait Until Element Is Visible    ${landing_page_login_button}
    Try To Click Element    ${landing_page_login_button}
    Try To Input Text    ${oidc_username_input}    ${email}
    Try To Input Text    ${oidc_password_input}    ${DEFAULT_PASSWORD}
    Sleep    2s
    Try To Click Element    ${oidc_login_button}

Login As Patient From Legacy Login Page
    [Arguments]    ${email}
    Wait until keyword succeeds    9x    1s    Input Text    ${email_textbox}    ${email}
    Input Password    ${password_textbox}    ${DEFAULT_PASSWORD}
    Try To Click Element    ${login_button}

Select Language
    [Arguments]    ${language_value}
    Wait Until Element Is Enabled    ${language_dropdown}    10s
    ${language_value}    Format String    ${language_value}    ${LANGUAGE_LABEL}
    Try To Click Element    ${language_dropdown}
    ${lang_code}    Format String    ${language_value}    ${LANGUAGE_LABEL}
    ${status}    Run Keyword And Return Status    Get WebElement    ${lang_code}
    IF    ${status}
        Try To Click Element    ${lang_code}
    ELSE
        Try To Click Element    ${language_dropdown}
        Select Language    ${language_value}
    END

Keep Me Logged In
    [Arguments]    ${remember}
    Wait Until Page Contains Element    ${keep_me_logged_in_yes}
    IF    '${remember}'=='Yes'
        ${option}    Format String    ${keep_me_logged_in_radio}    ${keep_me_logged_in_yes}
    ELSE IF    '${remember}'=='No'
        ${option}    Format String    ${keep_me_logged_in_radio}    ${keep_me_logged_in_no}
    ELSE
        ${option}    Set Variable    ${None}
    END
    Try To Click Element    ${option}
    Try To Click Element    ${keep_me_logged_in_next}

Choose Clinic
    [Arguments]    ${clinic}
    Wait Until Page Contains Element    ${choose_clinic_next_button}
    Select Clinic Option From Dropdown      ${clinic}
    Try To Click Element     ${choose_clinic_next_button}

False Login
    [Arguments]    ${user}    ${email}
    IF    'native' not in '${ENVIRONMENT}'
        Login To Noona    ${user}    ${email}
        Wait Until Page Contains    ${incorrect_error_message_oidc}    timeout=5s
    ELSE
        Login To Native App With Incorrect Password    ${email}
    END

Select Log Out From Menu
    Wait Until Page Contains Element    ${logout_link_1}
    Wait Until Keyword Succeeds    9s    0.3s    Click Element    ${logout_link_1}

Patient Selects Log Out From Menu
    Wait Until Page Contains Element    ${more_link}
    Wait Until Keyword Succeeds    9s    0.3s    Click Element    ${more_link}
    Wait Until Page Contains Element    ${more_link}
    Wait Until Keyword Succeeds    9s    0.3s    Click Element    ${patient_logout_link}

Random Patient From The List
    Load Patient List
    ${patient}    Evaluate    random.choice(${patient_keys})    random
    Set Test Variable    ${patient_ssn}    ${patient}
    ${patient_email}    Get From Dictionary    ${patient_dict}    ${patient_ssn}
    Set Test Variable    ${patient_email}

Load Patient List
    ${patient_dict}    Get File    ${EXECDIR}${/}resources${/}nurse${/}patient_list.json
    ${patient_dict}    Evaluate    json.loads("""${patient_dict}""")    json
    Convert To Dictionary    ${patient_dict}
    ${patient_keys}    Get Dictionary Keys    ${patient_dict}
    Set Test Variable    ${patient_keys}
    Log    ${patient_dict}
    Set Test Variable    ${patient_dict}

Close All Local Browsers
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    Set Suite Variable    ${remote_url_exists}
    IF    '${remote_url_exists}'=='False'    Close All Browsers

Select Noona Admin Demo
    [Arguments]    ${clinic}=${DEFAULT_CLINIC_DEMO}
    Wait Until Page Contains    Choose clinic
    Click Element    ${clinic_list}
    Select From List By Label    ${clinic_list_1}    ${clinic}
    Wait Until Keyword Succeeds    0.3    9    Click Element    ${choose_clinic_next_button_1}

Login To Noona Demo
    [Arguments]
    ...    ${user}
    ...    ${email}
    ...    ${default_password}=${DEFAULT_PASSWORD_DEMO}
    ...    ${clinic}=${DEFAULT_CLINIC_DEMO}
    ...    ${remember_login}=Yes
    ...    ${open_browser}=True
    ${url}    Set Variable If
    ...    '${user}'=='patient'
    ...    ${PATIENT_LOGIN_URL}
    ...    '${user}'=='nurse'
    ...    ${NURSE_LOGIN_URL}
    ...    '${user}'=='admin'
    ...    ${MANAGEMENT_LOGIN_URL}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    ${remote_url_exists} and ${open_browser}
        Open Browser    ${url}    ${BROWSER}    remote_url=${REMOTE_URL}
    ELSE IF    ${open_browser}
        Open Browser    ${url}    ${BROWSER}
    END
    Set Window Size    ${WINDOW_WIDTH}    ${WINDOW_HEIGHT}
    Accept All Cookies If Visible
    IF    '${user}'!='admin'
        Wait until keyword succeeds    9x    1s    Select Language    ${language_value}
    END
    IF    '${user}'=='patient'
        Try To Click Element    ${landing_page_login_button}
        Wait until keyword succeeds    9x    1s    Input Text    ${oidc_username_input}    ${email}
        Input Password    ${oidc_password_input}    ${default_password}
        Try To Click Element    ${oidc_login_button_id}
    ELSE
        Wait until keyword succeeds    9x    1s    Input Text    ${email_textbox}    ${email}
        Input Password    ${password_textbox}    ${default_password}
        Try To Click Element    ${login_button}
    END
    IF    '${user}'=='admin'    Select Noona Admin Demo    ${clinic}
    IF    '${email}'=='${SSO_NOONA_ADMIN}'    Choose Clinic    ${clinic}    user_type=${USER_TYPE}[noona_admin]
    IF    '${user}'=='nurse'
        Keep Me Logged In    ${remember_login}
    END

Display First CCD Record
    Wait Until Element Is Visible    ${ccd_record_1}
    ${medical_record_1}    Execute Javascript    return document.getElementsByClassName('checkbox')[0].innerText;
    Set Suite Variable    ${medical_record_1}    ${MEDICAL_RECORD_1}
    ${first_medical_record}    Catenate    ${MEDICAL_RECORD_1}
    Set Suite Variable    ${FIRST_MEDICAL_RECORD}    ${first_medical_record}

Clinic User Is Logged Out
    Wait Until Page Contains    Welcome to Noona
    Location Should Contain    /nurse#/login

Login As Patient From Direct Link
    [Arguments]    ${email}
    Wait Until Location Contains    ${PATIENT_LOGIN_URL}    timeout=5s
    Accept All Cookies If Visible
    Login As Patient From OIDC Login Page    ${email}

Login As Patient Without Setting Up PIN
    [Arguments]    ${patient_email}    ${password}
    IF    'native' in '${ENVIRONMENT}'
        IF    '${PLATFORM_NAME}'=='android'    Disable Privacy Screen
        Try To Click Native App Element    ${landing_page_login_button_app}
        Input Login Credentials And Login On Native App    ${patient_email}    ${password}
    ELSE
        Login To Noona    patient    ${patient_email}
    END

Accept All Cookies If Visible
    [Documentation]    Some native app cases require accept cookies step
    [Arguments]    ${accept_cookies_in_nativeapp}=no
    IF    '${accept_cookies_in_nativeapp}'=='no'
        ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${accept_all_cookies}    timeout=5s
        IF    ${status}    Try To Click Element    ${accept_all_cookies}
    ELSE IF    '${accept_cookies_in_nativeapp}'=='yes'    #native app patient
        ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${native_app_accept_all_cookies_button}    timeout=5s
        IF    ${status}    Try To Click Native App Element    ${native_app_accept_all_cookies_button}
    END

Accept All Cookies If Visible For Clinic
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${accept_all_cookies_for_clinic}    timeout=3s
    IF    ${status}    Try To Click Element    ${accept_all_cookies_for_clinic}

Select Clinic Option From Dropdown
    [Arguments]     ${clinic}
    Try To Click Element    ${clinic_list}
    ${actual_options}    Get WebElements    //div[contains(@class, "ng-option")]
    FOR    ${actual_option}    IN    @{actual_options}
        ${option_text}    Get Text    ${actual_option}
        ${contains_text}    Run Keyword And Return Status    Should Contain    ${option_text}    ${clinic}
        IF    '${contains_text}'=='True'
            Try To Click Element    ${actual_option}
            BREAK
        END
    END

Switch Clinic From User Profile Menu Options
    [Arguments]    ${clinic}=${automated_tests_clinic}[name]
    Try To Click Element    ${profile_drop_down}
    Try To Click Element    ${switch_clinic_option}
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    ${clinic_selection_dropdown}
    Try To Input Text    ${clinic_selection_dropdown}${clinic_search_box}    ${clinic}    #clinic name
    Try To Click Element    //*[contains(@role,'listbox')]//div[contains(text(),"${clinic}")]    #clinic name
    Try To Click Element    ${switch_clinic_button}
    Wait Until Noona Loader Is Not Visible
    Wait Until Element Is Visible    ${profile_drop_down}//div[contains(text(),"${clinic}")]    timeout=60s

Verify Management Login Page Is Displayed
    Wait Until Element Is Visible    ${mgmt_landing_page_login_button}

Input Login Credentials And Login
    [Arguments]    ${patient_email}    ${password}
    IF    'native' in '${ENVIRONMENT}'
        Input Login Credentials And Login On Native App    ${patient_email}    ${password}
    ELSE
        Try To Input Text    ${oidc_username_input}    ${patient_email}
        Try To Input Text    ${oidc_password_input}    ${password}
        Sleep    2    # add sleep due to NOONA-18026
        Try To Click Element    ${oidc_login_button}
    END

Prepare Next Login
    Logout As Patient
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        IF    '${PLATFORM_NAME}'=='ios'
            Switch To Context    ${contexts}[1]
        ELSE
            IF    'test' in '${ENVIRONMENT}'
                Switch to Context    ${webview_noona_app_test}
            ELSE
                Switch To Context    ${webview_noona_app_staging}
            END
        END
        Wait Until Noona Loader Is Not Visible
        Wait Until Element Is Visible    ${landing_page_login_button_app}
        Try To Click Native App Element    ${landing_page_login_button_app}
        Clear Patient Login Details
    ELSE
        Try To Click Element    ${landing_page_login_button}
    END

Clear Patient Login Details
    [Arguments]    ${email}=${patient_email}
    IF    'native' in '${ENVIRONMENT}'
        @{contexts}    Get Contexts
        Switch To Context    NATIVE_APP
        IF    '${PLATFORM_NAME}'=='android'
            Wait Until Keyword Succeeds    5x    200ms    Hide Keyboard
            ${keyboard_status}    Is Keyboard Shown
            IF    ${keyboard_status} == True
                Click Text    ${login_page_title}    exact_match=True
            END
            Wait Until Element Is Visible    ${oidc_password_input_app_android}
            Clear Text    xpath=${oidc_password_input_app_android}
            Clear Text    xpath=${oidc_username_input_android}
        ELSE IF    '${PLATFORM_NAME}'=='ios'
            Wait Until Element Is Visible    ${oidc_password_input_app}
            ${text_exists}    Run Keyword And Return Status    Text Should Be Visible    ${email}
            IF    ${text_exists}==True
                Click Text    ${email}
                Clear Text    xpath=${oidc_username_input_app}
                Clear Text    xpath=${oidc_password_input_app}
            END
        END
    ELSE
        Clear Element Text    ${oidc_username_input}
        Clear Element Text    ${oidc_password_input}
    END
