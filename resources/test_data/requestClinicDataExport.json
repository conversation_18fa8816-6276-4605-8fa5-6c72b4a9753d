{"jsonrpc": "2.0", "method": "requestClinicDataExport", "params": ["custom", ["treatment-module"], [{"id": "f64ce7ae-2310-43e8-9ea9-144d8bd21e31", "created": 1703067595992, "modified": 1728495442035, "name": "Care Team 1", "users": [{"userId": "53b1adce-2de3-4f33-960e-fd4a2713a9b3", "emailAddress": "<EMAIL>", "emailAddressValidated": false, "firstName": "F13NA01", "lastName": "Manager", "phoneNumber": "+358451712930", "failedLoginCount": 0, "lockedOut": false, "passwordExpirationDate": null, "created": 1728495441739, "modified": 1728560172697, "fullName": "F13NA01 Manager"}, {"userId": "982ee6e5-cfcb-4d1d-8f1a-82f1894502ea", "emailAddress": "<EMAIL>", "emailAddressValidated": false, "firstName": "Clinic", "lastName": "User", "phoneNumber": "+3580000", "failedLoginCount": 0, "lockedOut": false, "passwordExpirationDate": null, "created": 1703145330949, "modified": 1728558112394, "fullName": "Clinic User"}, {"userId": "d9c5b262-7d51-4faf-8ebb-2c2553013ee0", "emailAddress": "<EMAIL>", "emailAddressValidated": false, "firstName": "Clinic", "lastName": "Manager", "phoneNumber": "+35800000", "failedLoginCount": 0, "lockedOut": false, "passwordExpirationDate": null, "created": 1703071958259, "modified": 1728557868095, "fullName": "Clinic Manager"}], "removed": false, "uranusIntegrationView": null, "notificationEmail": null, "treatmentUnit": null}], ["1.2.246.537.6.1/CD50"], ["data-types"], "request-description", true, true, true], "id": 1}