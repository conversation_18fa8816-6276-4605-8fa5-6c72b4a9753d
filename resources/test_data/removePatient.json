{
    "jsonrpc": "2.0",
    "method": "removePatient",
    "params": [
        {
            "patientId": "patient_id",
            "userId": "patient_user_id",
            "identityCode": "ssn",
            "firstName": "first_name",
            "lastName": "last_name",
            "emailAddress": "patient_email",
            "phoneNumber": "phone_number",
            "phoneNumberType1": "mobile",
            "phoneNumber2": null,
            "phoneNumberType2": null,
            "phoneNumber3": null,
            "phoneNumberType3": null,
            "hipaaCode": null,
            "medicalRecordNumber": "mrn",
            "address": null,
            "city": null,
            "zipCode": null,
            "state": null,
            "clinicSite": null,
            "primaryProvider": null,
            "localeId": "en_GB",
            "status": 10,
            "statusChangedDate": status_changed_date,
            "accountLockedBy": "patient_user_id",
            "gender": "gender_option",
            "birthDate": "birth_date",
            "emailProblemDetails": null,
            "emailProblemType": null,
            "emailProblemTime": null,
            "statuses": [],
            "nurseControlled": false,
            "initialCareTeamId": null,
            "declinedNoonaInvite": false,
            "accountLocked": false
        }
    ],
    "id": 1
}