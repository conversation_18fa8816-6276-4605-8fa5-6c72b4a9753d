{"jsonrpc": "2.0", "method": "addPatientTreatmentModule", "params": [{"id": null, "modified": null, "created": null, "main": true, "startDate": "start_date", "treatmentModule": {"symptomTypes": ["rash", "abdominal", "respiratory", "eatingProblemsOrMouthSymptoms", "nausea", "pain", "fever", "fatigue", "eyeSymptoms", "neuropathyOrMuscleWeakness", "mental", "swelling", "joint", "urination", "hairChanges", "vertigo", "mentalPerformance", "generalCondition", "otherSymptom"], "treatmentModuleCategory": "activeTreatment", "treatmentModuleControlType": "treatment", "treatmentModuleType": "immunologicTreatments", "created": 1468713600000, "modified": 1468713600000, "id": "503ffa04-2cc1-43ad-80cc-215002e0cdaf"}, "subscriptions": [{"subscriberType": "careTeam", "contactTypes": ["symptomReport", "contactClinic"], "subscriberId": "subscriber_id", "created": null, "modified": null, "id": null}], "symptomInquiries": [], "scheduledMessages": [], "questionnaireInquiries": [], "templateUsed": null, "scheduledActivationDate": null, "scheduledForMain": false}, "patient_id"], "id": 1}