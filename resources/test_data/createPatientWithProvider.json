{"jsonrpc": "2.0", "method": "addPatient", "params": [{"nurseControlled": false, "medicalRecordNumber": "mrn_value", "identityCode": "ssn_value", "birthDate": "birth_date_value", "gender": "gender_option", "hipaaCode": null, "firstName": "first_name_value", "lastName": "family_name_value", "emailAddress": "email_value", "phoneNumberType1": "work", "phoneNumber": "sms_number_value", "phoneNumberType2": null, "phoneNumber2": null, "phoneNumberType3": null, "phoneNumber3": null, "address": null, "zipCode": null, "city": null, "state": null, "clinicSite": null, "primaryProvider": {"id": "provider_id", "created": 1703182778208, "modified": 1743181836768, "firstName": "Dr", "lastName": "Doctor", "middleName": "Fine", "suffix": "", "enabled": true, "fullName": "Doctor, Dr <PERSON>. "}}, "en_GB", null, {"id": "module_id", "created": 1648944000000, "modified": 1648944000000, "symptomTypes": [], "treatmentModuleCategory": "activeTreatment", "treatmentModuleType": "module_type", "collectLineOfTreatmentInformation": true, "treatmentModuleControlType": "treatment"}, null, null, [{"subscriberType": "careTeam", "subscriberId": "subscriber_id", "contactTypes": ["symptomReport", "contactClinic"]}], []], "id": 1}