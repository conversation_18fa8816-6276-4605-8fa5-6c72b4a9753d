*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}common_mobile.resource
Resource    ${EXECDIR}${/}resources${/}shared_login.resource


*** Keywords ***
Set Libraries Order
    [Documentation]    All test cases should set libraries order in suite setup
    IF    'native' in '${ENVIRONMENT}'
        Set Library Search Order    AppiumLibrary    SeleniumLibrary
    ELSE
        Set Library Search Order    SeleniumLibrary
    END

Close All App Instances
    IF    'native' in '${ENVIRONMENT}'
        Close Application
    ELSE
        Close Browser
    END

Close All App Instances For Medical Records Cases
    IF    'native' in '${ENVIRONMENT}'
        Close Application
    ELSE
        Close All Local Browsers
    END

Setup App Environment
    [Documentation]    All hybrid tcs should use this as a setup to make sure tcs can be used in both app and web
    IF    'native' in '${ENVIRONMENT}'    Setup Native App In Browserstack