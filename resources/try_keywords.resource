*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource


*** Variables ***
${banner_toast_message}     //div[contains(@class, 'toast-message')]


*** Keywords ***
Try To Select By Label
    [Arguments]    ${select}    ${label}
    Wait For Element To Be Present    ${select}
    Select From List By Label    ${select}    ${label}

Try To Select By Index
    [Arguments]    ${select}    ${index}
    Wait For Element To Be Present    ${select}
    Select From List By Index    ${select}    ${index}

Try To Select By Value
    [Arguments]    ${select}    ${value}
    Wait For Element To Be Present    ${select}
    Select From List By Value    ${select}    ${value}

Try To Click Element
    [Arguments]    ${element}    ${wait_in_seconds}=3s
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Page Contains Element    ${element}    timeout=10s
        Scroll Element Into View    ${element}
        Wait Until Element Is Visible    ${element}    # appiumlib doesnt have Wait For Element To Be Present
    ELSE
        Wait Until Page Contains Element    ${element}
        Wait For Element To Be Present    ${element}
        Wait Until Element Is Enabled    ${element}    # required for Seleniumlib
    END
    Wait Until Keyword Succeeds    ${wait_in_seconds}    1s    Click Element    ${element}

Try For Equal Element Text
    [Arguments]    ${locator}    ${text}
    Wait For Element To Be Present    ${locator}
    Wait Until Keyword Succeeds    5s    1s    Element Text Should Be    ${locator}    ${text}

Try For Element Should Contain
    [Arguments]    ${locator}    ${text}
    Wait For Element To Be Present    ${locator}
    IF    'native' in '${ENVIRONMENT}'
        Wait Until Keyword Succeeds    5s    1s    Element Should Contain Text    ${locator}    ${text}
    ELSE
        Wait Until Keyword Succeeds    5s    1s    Element Should Contain    ${locator}    ${text}
    END

Try For Textfield Value Should Be
    [Arguments]    ${locator}    ${value}
    Wait For Element To Be Present    ${locator}
    Wait Until Keyword Succeeds    5s    1s    Textfield Value Should Be    ${locator}    ${value}

Try To Input Text
    [Arguments]    ${locator}    ${text}
    Wait For Element To Be Present    ${locator}
    Wait Until Keyword Succeeds    5s    1s    Input Text    ${locator}    ${text}

Wait For Element To Be Present
    [Arguments]    ${locator}
    Wait Until Keyword Succeeds    5s    1s    Scroll Element Into View    ${locator}
    Wait Until Keyword Succeeds    5s    1s    Element Should Be Visible    ${locator}

Wait For Element To Not Be Present
    [Arguments]    ${locator}
    Wait Until Keyword Succeeds    10s    1s    Element Should Not Be Visible    ${locator}

Try To Click Banner Message
    ${status}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${banner_toast_message}
    ...    timeout=5s
    IF    ${status}    Try To Click Element    ${banner_toast_message}