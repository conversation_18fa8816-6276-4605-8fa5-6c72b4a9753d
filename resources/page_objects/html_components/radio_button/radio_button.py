"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from base import Base, run_on_failure, save_driver
from robot.api.deco import keyword
from robot.libraries.BuiltIn import logger


class RadioButton(Base):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(RadioButton, self).__init__()

    @keyword
    @save_driver
    @run_on_failure
    def select_radio_button(self, radio_button_text, title=None):
        """Select radio button based on input text

        :param radio_button_text: text of the radio button
        :param title: title of the radio button group
        """
        if title:
            radio_xpath = '(//label[text()="{0}"]/following::label[text()="{1}"])[1]'.format(
                title, radio_button_text
            )
        else:
            radio_xpath = "(//label[text()='{0}'])[1]".format(radio_button_text)
        logger.trace("xpath of the radio button: {0}".format(radio_xpath))
        self.sl.click_element(radio_xpath)
