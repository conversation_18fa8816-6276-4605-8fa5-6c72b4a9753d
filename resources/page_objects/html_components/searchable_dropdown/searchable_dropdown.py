"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure


class SearchableDropdown(Base):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(SearchableDropdown, self).__init__()

    @keyword
    @save_driver
    @run_on_failure
    def select_from_dropdown_by_text(self, title, text):
        """Select item from searchable dropdown

        Use this keyword for searchable-dropdown. For normal dropdwon
        use Selenium select_from_list keywords

        :param title: title of the searchable dropdown
        :param text: text to be selected
        """
        prompt_xpath = '//label[text()="{0}"]/following::div[@class="prompt"]'.format(
            title
        )
        self.sl.click_element(prompt_xpath)
        self.sl.click_element('//li/span[text()="{0}"]'.format(text))
