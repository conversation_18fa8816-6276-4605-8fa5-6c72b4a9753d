"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.api.deco import keyword
from base import Base, run_on_failure, save_driver
from robot.libraries.BuiltIn import logger


class Checkbox(Base):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(Checkbox, self).__init__()

    @keyword
    @save_driver
    @run_on_failure
    def select_checkbox(self, checkbox_text):
        """Select a checkbox based on the text of the label

        :param checkbox_text: text of the checkbox label
        """
        checkbox_xpath = '//label[text()="{0}"]'.format(checkbox_text)
        logger.trace("xpath of the checkbox: {0}".format(checkbox_xpath))
        self.sl.click_element(checkbox_xpath)
