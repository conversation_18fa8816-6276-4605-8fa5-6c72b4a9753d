"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure


class FormDropdown(Base):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(FormDropdown, self).__init__()

    @keyword
    @save_driver
    @run_on_failure
    def select_from_dropdown_by_text(self, title, text):
        """
        Select item from the new form engine dropdown

        User this keyword for the case management dropdown and
        for others that use the same form engine.

        :param title: title of the dropdown
        :param text: text to be selected

        Examples:
        | Select From Dropdown By Text | Case Type * | Symptom Management |
        | Select From Dropdown By Text | Case Case Team * | Team 1 |
        """
        element_xpath = '//div[contains(text(), "{0}")]//following::button[1]'.format(
            title
        )
        self.sl.wait_until_element_is_enabled(element_xpath)
        self.sl.set_focus_to_element(element_xpath)
        self.sl.click_element(element_xpath)
        self.sl.wait_until_element_is_visible('//*[contains(text(),"{0}")]'.format(text))
        if (text) == "New":
            self.sl.click_element('//*[@id="nh-dropdown-list-item-new"]')
        self.sl.click_element('//*[contains(text(),"{0}")]'.format(text))
