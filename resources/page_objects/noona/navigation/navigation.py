"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.api.deco import keyword
from robot.libraries.BuiltIn import BuiltIn
from base import Base, save_driver


class Navigation(Base):
    """Parent class for all page object navigation classes"""

    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(Navigation, self).__init__()
        self.builtin = BuiltIn()

    @keyword
    @save_driver
    def click_main_menu(self, menu):
        """Click a main menu

        :param menu: text of the menu to be clicked
        """
        menu_xpath = '//div[text()="{0}"]/parent::a'.format(menu)
        self.sl.wait_until_page_contains_element(menu_xpath)
        self.poll_keyword(5, 1, "Click Link", menu_xpath)

    @keyword
    @save_driver
    def click_tab_by_id(self, tab_id):
        """Click tab based on the id

        :param tab_id: id of the tab div
        """
        self.sl.click_element('//div[@id="{0}"]'.format(tab_id))
