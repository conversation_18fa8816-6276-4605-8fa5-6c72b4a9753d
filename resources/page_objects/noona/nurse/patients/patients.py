"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
import time
from datetime import datetime
from faker import Faker
from robot.api.deco import keyword
from robot.libraries.BuiltIn import logger, BuiltIn
from base import run_on_failure, save_driver
from noona.nurse.patients.patients_navigation import PatientsNavigation
from html_components.radio_button.radio_button import RadioButton
from html_components.searchable_dropdown.searchable_dropdown import SearchableDropdown
from find_element import FindElement


class Patients(PatientsNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(Patients, self).__init__()
        self.faker = Faker()
        self.radio_button = RadioButton()
        self.dropdown = SearchableDropdown()
        self.find_element = FindElement().find_element
        self.builtin = BuiltIn()

    @keyword
    @save_driver
    @run_on_failure
    def create_patient(self, proxy=False, patient_dict=None, **more_values):
        """Add a new patient

       The keyword accepts arguments as dictionary or arbitrary keyword
       arguments.

       # TODO: add function for filling up missing required fields

        :param patient_dict: dictionary that has patient data
        :param proxy: True if patient is proxy
        :param more_values: first name=John

        Examples:
        | &{dict}= | Create Dictionary | first name=James | last name=Watson |
        | Create Patient | &{dict}
        | Create Patient | &{dict} | email=<EMAIL>
        """
        patient_info_dict = self.join_arguments_into_dictionary(
            patient_dict, **more_values
        )
        patient_info_dict = dict(
            (key.lower(), val) for key, val in patient_info_dict.items()
        )
        self.page_object_navigation()
        self.open_create_patient_tab()
        if proxy:
            self.sl.click_element("//input[@id='nurse-controlled']/following-sibling::label")
        for key, val in patient_info_dict.items():
            if key in "social security number":
                logger.debug("... filling up ")
                self.sl.input_text("ssn", val)
            elif key in "date of birth":
                self.sl.input_text(
                    "birthDate", datetime.strptime(val, "%d.%m.%Y").strftime("%d%m%Y")
                )
            elif key in "gender":
                if val == "Female":
                    self.sl.click_element(
                        "//input[@id='gender-female']/following-sibling::label"
                    )
                elif val == "Male":
                    self.sl.click_element(
                        "//input[@id='gender-male']/following-sibling::label"
                    )
            elif key in "first name":
                self.sl.input_text("fname", val)
            elif key in "last name":
                self.sl.input_text("lname", val)
            elif key in "email":
                if not proxy:
                    self.sl.input_text("email", val)
            elif key in "mobile type":
                value_xpath = "//option[@value='string:{0}']"
                if val == "Mobile":
                    val = value_xpath.format("mobile")
                elif val == "Work":
                    val = value_xpath.format("work")
                elif val == "Home":
                    val = value_xpath.format("home")
                self.sl.click_element(val)
            elif key in "mobile number":
                self.sl.input_text("phoneNumber", val)
            elif key in "choose language":
                self.radio_button.select_radio_button(val, "Choose language")
            elif key in "icd-10 classification":
                self.sl.click_element("//label[@for='diagnosis']/following-sibling::div")
                diagnosis_xpath = "//label[@for='diagnosis']/following-sibling::div/descendant::ul/descendant::span"
                diagnosis_number = self.sl.get_element_count(diagnosis_xpath)
                for i in range(int(diagnosis_number)):
                    i += 1
                    option = self.sl.get_text(diagnosis_xpath + "[{0}]".format(i))
                    if val in option:
                        self.sl.click_element(diagnosis_xpath + "[{0}]".format(i))
                        break
            elif key in "treatment module":
                # TODO: No unique identifier other than index. To be updated once id is provided.
                self.sl.select_from_list_by_index("treatment-module", str(val))
            elif key in "responsible care team":
                self.sl.select_from_list_by_label("care-team", val)
            elif key in "responsible care person":
                self.sl.select_from_list_by_label("responsible-nurse", val)
            elif key in "schedule template":
                self.sl.select_from_list_by_label("questionnaire-template", val)
            elif key in "treatment start date":
                self.sl.wait_until_element_is_visible("treatment-start-date")
                self.sl.input_text(
                    "treatment-start-date",
                    datetime.strptime(val, "%d.%m.%Y").strftime("%d%m%Y"),
                )
        # self.fill_missing__mandatory_fields()
        self.sl.click_button("create-patient")
        # TODO: This does not check the message itself but only if it shows up after creation.
        # We need to create a separate test for all toast message including translation
        self.sl.wait_until_page_contains_element("//div[@class='toast-message']", 30)
        self.sl.wait_until_page_does_not_contain_element("//div[@class='toast-message']", 30)

    @keyword
    @save_driver
    @run_on_failure
    def find_patient_by_id(self, patient_id):
        """Find a patient

        The keyword uses the search box and enters the
        id/ssn to find the patient

        :param patient_id: patient id/social security number

        Examples:
        | Find Patient By Id | 12345678 |
        """
        self.page_object_navigation()
        self.sl.input_text("input-search-ssn-search", patient_id)
        # TODO: Wait for the app to find the ID before clicking enter
        time.sleep(3)
        self.sl.press_keys("input-search-ssn-search", "RETURN")
        self.sl.wait_until_page_contains_element(
            "//*[@id='ssn']",
            error="Unable to find patient with id: {0}".format(patient_id),
        )
        self.sl.wait_until_page_contains(patient_id)

    def fill_missing__mandatory_fields(self):
        mandatory_elements = (
            '//span[contains(@class, "mandatory-mark") or @class="mandatory-mark"]'
            '/following::select | //span[contains(@class, "mandatory-mark") '
            'or @class="mandatory-mark"]/following::input'
        )
        elements = self.find_element(self.driver, mandatory_elements)
        for element in elements:
            element_id = self.sl.get_element_attribute(element, "id")
            attribute_value = self.sl.get_element_attribute(element, "value")
            if not attribute_value:
                if element_id in "ssn":
                    self.sl.input_text("ssn", self.dummy_ssn)
                elif element_id in "fname":
                    self.sl.input_text("fname", self.dummy_first_name)
                elif element_id in "lname":
                    self.sl.input_text("lname", self.dummy_last_name)
                elif "searchable-dropdown" in element_id:
                    self.dropdown.select_from_dropdown_by_text(
                        "ICD-10 classification", self.default_icd_10
                    )
                elif element_id in "birthDate":
                    self.sl.input_text("birthDate", self.dummy_date_of_birth)
                # TODO: complete the function

    @keyword
    def random_ssn(self):
        return self.faker.ssn()

    @property
    def dummy_ssn(self):
        return self.faker.ssn()

    @property
    def dummy_date_of_birth(self):
        return self.faker.date_of_birth(minimum_age=1, maximum_age=100).strftime(
            "%d%m%Y"
        )

    @property
    def dummy_first_name(self):
        return self.faker.first_name_female()

    @property
    def dummy_last_name(self):
        return self.faker.last_name_female()

    @property
    def dummy_email(self):
        return self.faker.safe_email()

    @property
    def default_icd_10(self):
        return "C00& Malignant neoplasm of lip"

    @property
    def default_treatment_module(self):
        return "Breast cancer radiation therapy"
