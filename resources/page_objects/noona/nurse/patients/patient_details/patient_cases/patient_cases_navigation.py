"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.libraries.BuiltIn import logger
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure
from noona.nurse.patients.patient_details.patient_details_navigation import (
    PatientDetailsNavigation,
)


class PatientCasesNavigation(PatientDetailsNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(PatientCasesNavigation, self).__init__()

    @keyword("Open Patient Page - Patient Cases Tab")
    @save_driver
    @run_on_failure
    def page_object_navigation(self, patient_id):
        """Navigate to patient cases tab

        :param patient_id: ID/SSN of the patient
        """
        if "handle-patient" in self.sl.get_location():
            self.sl.wait_until_element_is_enabled("id:back-to-button")
            self.sl.click_button("id:back-to-button")
        self.open_patient_page(patient_id)