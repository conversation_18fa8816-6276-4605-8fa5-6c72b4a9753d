"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from collections import OrderedDict
from datetime import datetime
import time
from robot.api.deco import keyword
from base import Base, run_on_failure, save_driver
from html_components.checkbox.checkbox import Checkbox
from html_components.form_dropdown.form_dropdown import FormDropdown
from noona.nurse.patients.patient_details.patient_cases.patient_case.patient_case_navigation import (
    PatientCaseNavigation,
)


# TODO: Patient case test is heavily using hardcoded string comparison.
#       It will break the test for any other language than English and
#       should be reconsidered for multi-language support.
class PatientCase(PatientCaseNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(PatientCase, self).__init__()
        self.checkbox = Checkbox()
        self.dropdown = FormDropdown()

    @keyword
    @save_driver
    @run_on_failure
    def fill_in_main_case_details(
        self,
        case,
        patient_id,
        action,
        acute_symp,
        case_to_edit="",
        date_created="",
        case_status="",
        **case_details
    ):
        """Open a new case.
        Acute Symptoms may include:
            - Chest Pain
            - Currently Bleeding
            - High-grade Fever
            - Intractable Pain
            - Trouble Breathing
        Case details may include:
            - Case Type
            - Case Care Team
            - Description
            - Case Priority
            - Case Origin
            - Case Status
            - Action (action done after filling out the details, can be Close, Continue, Save)

        :param case: type of case management either open new case, edit or process
        :param patient_id: patient's ssn
        :param action: action done to the case after filling in the details
        :param case_to_edit: case to edit if case management == edit
        :param case_status: status of the case if case management == edit
        :param date_created: date the case to edit is created
        :param acute_symp: list of acute symptoms
        :param case_details: dictionary containing other case details

        :returns if case management is edit
            created: date the case is created for later comparison and validation
            details: existing case notes details

        Example:
            | Fill In Main Case Details | 12345678 | Continue | Chest Pain | Intractable Pain |
            ... Case Type=Symptom Management | Case Care Team=Team 1 | Case Priority=High |
            ... Case Origin=Patient Initiated Call | Case Status=Callback Requested |
        """
        global case_index
        created_date = ""
        details = ""
        if case == "new":
            self.navigate_to_new_case_page(patient_id)
            case_index = 1
        elif case == "edit":
            case_index_temp, created_date, details = self.navigate_to_edit_case_page(
                patient_id, case_to_edit, date_created, case_status
            )
            case_index = case_index_temp
        elif case == "process":
            self.navigate_to_new_case_page(patient_id)  # TODO: To be updated
        self.sl.wait_until_element_is_enabled("//button[@class='button-select'][1]")
        for key, value in case_details.items():
            if key in "Case Type":
                # dropdown menu is intermittently not clickable after a selenium wait
                # workaround the issue by adding explicitely a sleep
                time.sleep(1)
                self.dropdown.select_from_dropdown_by_text("Case Type *", value)
            elif key in "Case Care Team":
                self.dropdown.select_from_dropdown_by_text("Case Care Team *", value)
            elif key in "Description":
                self.sl.input_text('//textarea[@name="case-description"]', value)
            if value == "Symptom Management" and len(acute_symp) != 0:
                for symptom in acute_symp:
                    self.checkbox.select_checkbox(symptom)
            elif key in "Case Priority":
                self.dropdown.select_from_dropdown_by_text("Case Priority *", value)
            elif key in "Case Origin":
                self.dropdown.select_from_dropdown_by_text("Case Origin *", value)
            elif key in "Case Status":
                self.dropdown.select_from_dropdown_by_text("Case Status *", value)
        self.do_case_action(case, action)
        return created_date, details

    @save_driver
    @run_on_failure
    def do_case_action(self, case, action):
        """Click either Cancel, Continue, Save and Exit button
        after filling out case details

        :param case: type of case management either open new case, edit or process
        :param action: Action made after filling out case details
            - can be Continue, Save, Cancel
        """
        if action == "Continue":
            self.sl.click_element("continue-case")
        elif action == "Save":
            self.sl.click_element("close-new-case")
            if case == "new":
                self.sl.wait_until_page_contains("Case created successfully")
            elif case == "edit":
                self.sl.wait_until_page_contains(
                    "The patient case was successfully updated"
                )
        elif action == "Cancel":
            self.sl.click_element("cancel-template-edit")

    @keyword
    @save_driver
    @run_on_failure
    def fill_in_case_notes_details(self, case, **dict):
        """Fill in case note details before saving the case

        :param case: type of case management either open new case, edit or process
        :param dict: Accepts dictionary of case notes details.
        Values can be:
            - HIPAA Verified=Checked if the patient is verified
            - Patient Voiced Understanding=Checked
            - Notes that includes additional notes about the patient

        Example:
            | Fill In Case Notes Details | HIPAA Verified=Checked | Notes=Some case notes. |
        """
        for key, value in dict.items():
            if key in "HIPAA Verified" and value == "Checked":
                hipaa_status = self.sl.execute_javascript(
                    'return document.getElementById("hipaa-verified").checked'
                )
                if hipaa_status is not True:
                    self.sl.click_element('//label[contains(text(), "HIPAA Verified")]')

            elif key in "Patient Voiced Understanding" and value == "Checked":
                understanding_status = self.sl.execute_javascript(
                    'return document.getElementById("patient-voice-understanding").checked'
                )
                if understanding_status is not True:
                    self.sl.click_element(
                        '//label[contains(text(), "Patient Voiced Understanding")]'
                    )
            elif key in "Notes":
                self.sl.input_text('//textarea[@name="case-notes"]', value)
        self.sl.click_element("close-new-case")
        if case == "new":
            self.sl.wait_until_page_contains("Case created successfully")
        elif case == "edit":
            self.sl.wait_until_page_contains("The patient case was successfully updated")

    @keyword
    @save_driver
    @run_on_failure
    def fill_in_medication_information_form(self, *additional_info, **kwargs):
        """Fill in medication information form if case type == Medication Refill.

        :param additional_info: additional information about the patient's medication refill
            List can include:
            - Intervention Required
            - Patient Needs Lab Work
            - Patient Needs Office Visit
            - Screening Survey for Chemical Dependency Risk Needed
            - Prior Authorizations Needed
        :param kwargs: medication refill information
            Listed below are the expected keys with user defined values
            - Medication Category=Oral oncolytic
            - Medication Name=Some Medication Name
            - Number of Refills=2
            - Dose=2.5
            - Unit=ml
            - Frequency=Daily
            - Prescribing physician=Daniel, Davey B. MD

        Example:
            | Fill In Medication Information Form | @{additional_info_list} | &{med_info_dict} |
        """
        text_field = '//div[contains(text(), "{0}")]//following-sibling::input'
        self.sl.wait_until_element_is_visible(text_field.format("Medication Name"))
        for key, value in kwargs.items():
            if key in "Medication Category":
                self.dropdown.select_from_dropdown_by_text("Medication Category", value)
                if value == "Controlled substance 1-2":
                    self.sl.click_element(
                        '//input[@id="enabled-checkbox-narcoticsChecked"]/parent::div'
                    )
            elif key in "Medication Name":
                self.sl.input_text(text_field.format("Medication Name"), value)
            elif key in "Number of Refills":
                self.sl.input_text(text_field.format("Number of Refills"), value)
            elif key in "Dose":
                dose = value.split(" ")
                self.sl.input_text(text_field.format("Dose"), dose[0])
                # elif key in 'Unit':
                self.dropdown.select_from_dropdown_by_text("Unit", dose[1])
            elif key in "Frequency":
                self.sl.input_text(text_field.format("Frequency"), value)
        if len(additional_info) != 0:
            for info in additional_info:
                self.checkbox.select_checkbox(info)
        for key, value in kwargs.items():
            if key in "Prescribing physician":
                self.dropdown.select_from_dropdown_by_text(
                    "Prescribing physician", value
                )
        self.sl.click_element('//button[contains(text(), "Next")]')
        self.sl.wait_until_page_does_not_contain_element(
            '//button[contains(text(), "Next")]'
        )

    @keyword
    @save_driver
    @run_on_failure
    def get_medication_refill_case_summary(self):
        """Get and return medication refill case summary before saving the case.

        :return:
            title_labels: returns a list of titles (Case Type, Case Care Team, Description, etc)
            value_labels: returns a list of values

        Example:
        | ${titles} | ${values} | Get Medication Refill Case Summary |
        """
        titles = self.sl.get_webelements('//div[@class="title"]')
        title_labels = [self.sl.get_text(title) for title in titles]
        values = self.sl.get_webelements('//div[@class="title"]/following-sibling::div')
        value_labels = [self.sl.get_text(value) for value in values]
        return title_labels, value_labels

    @keyword
    @save_driver
    @run_on_failure
    def get_case_row_details(self, case, given_index=0):
        """Return the case row details of the newly created case under Patient Cases tab

        :return: list of case details which can be Case Type, Date Created, Status,
        Nurse's initials (if action==Continue)

        Example:
        | ${case_row_details} | Get Case Row Details |
        """
        case_row_xpath = (
            '//div[@class="patient-requests-box"]/descendant::div[@class="card"][1]'
        )
        if case == "new":
            index = 1
        elif case == "edit":
            if given_index != 0:
                index = given_index
            else:
                index = case_index
        self.sl.wait_until_element_is_visible(case_row_xpath)
        case_row_elements = self.sl.get_webelements(
            '//div[@class="patient-requests-box"]'
            '/descendant::div[@class="case-header clearfix"][{0}]/child::div'.format(
                index
            )
        )
        case_row_details = [
            self.sl.get_text(detail)
            for detail in case_row_elements
            if self.sl.get_text(detail)
        ]
        return case_row_details

    @keyword
    @save_driver
    @run_on_failure
    def get_main_case_details(self):
        """Get and return the ff case details from the newly created / updated case:
            - Case Type
            - Created date
            - Updated date
            - Case Priority
            - Case Status
            - Case Care Team
            - Assigned to
            - Case Origin

        :return: a list of case details
        """
        case_title = self.sl.get_text('//div[@class="case-info-header"][1]/child::h5[1]')
        case_details = '//div[@class="title"][contains(text(),"{0}")]/following::div'
        details_dict = OrderedDict()
        details_dict["Case Type"] = case_title
        details_dict["Created"] = self.sl.get_text(case_details.format("Created"))
        details_dict["Updated"] = self.sl.get_text(case_details.format("Updated"))
        details_dict["Case Priority"] = self.sl.get_text(
            case_details.format("Case Priority")
        )
        details_dict["Case Status"] = self.sl.get_text(
            case_details.format("Case Status")
        )
        details_dict["Case Care Team"] = self.sl.get_text(
            case_details.format("Case Care Team")
        )
        details_dict["Assigned to"] = self.sl.get_text(
            case_details.format("Assigned to")
        )
        details_dict["Case Origin"] = self.sl.get_text(
            case_details.format("Case Origin")
        )
        details_dict["Description"] = self.sl.get_text(
            case_details.format("Description")
        )
        return details_dict

    @keyword
    @save_driver
    @run_on_failure
    def get_ordered_dictionary(self, **dict):
        """Return the original order of the dictionary.

        :param: kwargs accepts dictionary to be ordered

        Example:
            | ${titles} | ${values} | Get Ordered Dictionary | &{dict} |
        """
        titles = []
        details = []
        ordered_dict = OrderedDict(dict)
        keys = ordered_dict.keys()
        values = ordered_dict.values()
        titles.extend(keys)
        details.extend(values)
        return titles, details

    @keyword
    @save_driver
    @run_on_failure
    def set_expected_case_row_details(
        self, case, case_type, action, date_time, **kwargs
    ):
        """Set expected case row details into list based on the supplied parameters

        :param case: type of case management either open new case, edit or process
        :param case_type: type of case opened
        :param action: action done to the case after filling in the case details
        :param date_time: expected date and time the case is created/opened
        :param kwargs: expected dictionary of case row details
            dictionary may include:
            - Case Priority=Medium
            - Case Status=In progress
            - Case Care Team=Team 1
            - Assigned to=Jane Doe
            - Case Origin=In Person
            - Description=This is a case description

        :return: list of case row details with or without nurse's initials
            if action == Continue, nurse's initials are shown
            else if action == Save, nurse's initials are not shown

        Example:
            ${expected_list_of_details} | Set Expected Case Row Details | Symptom Management |
            ... Continue | ${case_created_datetime} | In progress | &{case_summary_dict} |
        """
        if case == "new":
            status = "New"
        elif case == "edit":
            for key, value in kwargs.items():
                if "Case Status" in key:
                    status = value
        if action == "Continue":
            status = "In progress"
        expected = [case_type, date_time, status]
        if action == "Continue" or case == "edit":
            for key, value in kwargs.items():
                if "Assigned to" in key:
                    nurse = value.split()
                    initials = ""
                    for name in nurse:
                        initials += name[0].upper()
            expected.append(initials)
        return expected

    @keyword
    @save_driver
    @run_on_failure
    def set_and_format_current_date_time(self):
        """Set and format current date and time when keyword is called.

        :return: formatted date and time

        Example:
            | ${date_time} | Set And Format Current Date Time |
        """
        now = datetime.now()
        now = now.strftime("%d.%m.%Y %H:%M")
        return now

    @keyword
    @save_driver
    @run_on_failure
    def combine_dictionaries(self, type=None, created=None, updated=None, **dict):
        """
        Add case type, created and updated dates to the existing dictionary to be used
        for verification of case details

        :param type: case type
        :param created: date and time when case is created
        :param updated: date and time when case is updated
        :param dict: existing dictionary where additional keys and values are added

        :return: new dictionary with the new details

        Examples:
            | ${new_dict} | Combine Dictionaries | type=Symptom Management | &{dict} |
            | ${new_dict} | Combine Dictionaries | type=Other | created=${time_created} |
            ... updated=${time_updated} | &{dict} |
        """
        if type is not None and created is None and updated is None:
            case_details_dict = {"Case Type": type}
            new_dict = case_details_dict.copy()
            new_dict.update(dict)
        elif type is not None and created is not None and updated is not None:
            case_details_dict = {
                "Case Type": type,
                "Created": created,
                "Updated": updated,
            }
            new_dict = case_details_dict.copy()
            new_dict.update(dict)
        return new_dict

    @keyword
    @save_driver
    @run_on_failure
    def get_acute_symptoms(self):
        """Get the acute symptoms from the newly created/updated case and sorts it.

        :return: list of acute symptoms
        """
        symptoms_elements = self.sl.get_webelements(
            "//div[@class='title'][contains(text(),'Acute Symptoms')]/parent::div/child::div"
        )
        symptoms_text = [
            self.sl.get_text(element)
            for element in symptoms_elements
            if self.sl.get_text(element)
        ]
        symptoms_text.remove("Acute Symptoms")
        symptoms_text.sort()
        return symptoms_text

    @keyword
    @save_driver
    @run_on_failure
    def get_case_notes_details(self):
        details_dict = self.get_existing_case_notes_details(case_index)
        return details_dict

    @keyword
    @save_driver
    @run_on_failure
    def get_medication_refill_details(self):
        """Get the medication refill details from the newly created/updated case and sets them into
        a dictionary.

        :return: dictionary of medication refill information
        """
        self.sl.wait_until_element_is_visible(
            '//h5[contains(text(), "Medication information")]'
        )
        titles = self.sl.get_webelements(
            '//div[@class="summary-item ng-star-inserted"]/child::div[1]'
        )
        titles_list = [
            self.sl.get_text(title) for title in titles if self.sl.get_text(title)
        ]
        details_list = []
        details_dict = {}
        titles_list_length = len(titles_list)
        for x in range(titles_list_length):
            i = x + 1
            xpath = (
                '//div[@class="summary-item ng-star-inserted"][{0}]'
                "/child::div[1]/following-sibling::div"
            )
            additional_info_details = self.sl.get_webelements(xpath.format(i))
            add_info = [
                self.sl.get_text(info)
                for info in additional_info_details
                if self.sl.get_text(info)
            ]
            add_info_str = "".join(add_info)
            details_list.append(add_info_str)
            details_dict[titles_list[x]] = details_list[x]
        return details_dict

    @keyword
    @save_driver
    @run_on_failure
    def insert_additional_info_into_dict(self, add_info_list, **med_info_dict):
        add_info = "".join(add_info_list)
        for key, value in med_info_dict.items():
            if "Additional information" in key:
                med_info_dict["Additional information"] = add_info
        return med_info_dict

    @keyword
    @save_driver
    @run_on_failure
    def delete_existing_case(self, patient_id, case_type, date_created, case_status):
        """Delete existing case from the list of cases under the Patient Cases tab and
        validate if the case is correctly deleted.

        Validation: Count the number of cases before and after deletion and if there were
        similar cases existing before deletion, the number and details of them are also
        verified.

        :param patient_id: patient's ssn
        :param case_type: type of case
        :param date_created: date and time the case is created
        :param case_status: case status
        :return:
            case_row_count: total number of case rows
            num_of_similar_cases: number of similar cases before the deletion

        Example:
        | ${case_row_count} | ${num_of_similar_cases} | Delete Existing Case | 12345678 |
        | ... Chemo / Tx Questions | 24.05.2019 10:10 | In progress |
        """
        case_row_index = self.navigate_to_patient_case_details(
            patient_id, case_type, date_created, case_status
        )
        num_of_similar_cases = self.get_count_of_similar_cases(
            case_type, date_created, case_status
        )
        global case_index
        case_index = case_row_index
        case_row_count = self.get_number_of_cases()
        settings_icon = (
            '//div[@class="patient-requests-box"]/'
            'descendant::div[@class="case-body"][{0}]/'
            'descendant::div[@class="icon-settings"]'.format(case_row_index)
        )
        self.sl.wait_until_element_is_enabled(settings_icon)
        self.poll_keyword(5, 1, "SeleniumLibrary.Click Element", settings_icon)
        delete_case_xpath = (
            '//div[@class="patient-requests-box"]/'
            'descendant::div[@class="case-body"][{0}]/descendant::label'.format(
                case_row_index
            )
        )
        self.sl.click_element(delete_case_xpath)
        self.verify_if_delete_modal_shows_up()
        self.sl.click_element("ok-confirm")
        self.sl.wait_until_page_contains("The case was deleted")
        return case_row_count, num_of_similar_cases

    @keyword
    @save_driver
    @run_on_failure
    def verify_if_delete_modal_shows_up(self):
        """Verify if delete modal shows up and its details are correct when
        delete button is clicked.

        Example:
        | Verify If Delete Modal Shows Up |
        """
        self.sl.wait_until_element_is_visible('//div[@class="modal-content"]')
        self.sl.element_text_should_be(
            '//div[@class="general-modal-title"]', "Delete case"
        )
        self.sl.element_text_should_be(
            '//div[@class="general-message"]',
            "Deleting a case cannot be reversed and "
            "all information will be removed from the system",
        )
        self.sl.element_text_should_be(
            '//div[@class="confirm-question ng-star-inserted"]',
            "Do you want to delete the case?",
        )
        self.sl.element_should_be_enabled("ok-confirm")
        self.sl.element_should_be_enabled("cancel-confirm")

    @keyword
    @save_driver
    @run_on_failure
    def get_number_of_cases(self):
        """Count and return the number of cases under the Patient Cases tab

        :return: number of case rows
        """
        cases_xpath_count = self.sl.get_element_count(
            '//div[@class="patient-requests-box"]/descendant::div[@class="case-body"]'
        )
        return cases_xpath_count

    @keyword
    @save_driver
    @run_on_failure
    def get_count_of_similar_cases(self, case_type, date_created, case_status):
        """Count and return the number of similar cases based on the details supplied

        :param case_type: type of cases
        :param date_created: date and time case is created
        :param case_status: case status
        :return: number of similar cases
        """
        number_of_cases = 0
        expected_list = [case_type, date_created, case_status]
        cases_rows = self.sl.get_element_count('//div[@class="card"]')
        for i in range(int(cases_rows)):
            i += 1
            case_details_list = self.get_case_row_details("edit", i)
            if len(case_details_list) == 4:
                del case_details_list[-1]
            if case_details_list == expected_list:
                number_of_cases += 1
        return number_of_cases
