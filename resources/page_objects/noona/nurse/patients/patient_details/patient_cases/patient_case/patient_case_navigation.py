"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure
from noona.nurse.patients.patient_details.patient_cases.patient_cases_navigation import (
    PatientCasesNavigation,
)
from robot.libraries.BuiltIn import BuiltIn
from selenium.webdriver.common.action_chains import ActionChains


class PatientCaseNavigation(PatientCasesNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(PatientCaseNavigation, self).__init__()

    @keyword
    @save_driver
    @run_on_failure
    def navigate_to_new_case_page(self, patient_id):
        self.page_object_navigation(patient_id)
        self.sl.click_element('//button[contains(text(),"New case")]')
        self.sl.wait_until_element_is_visible('//h1[contains(text(), "New Case")]')

    @keyword
    @save_driver
    @run_on_failure
    def navigate_to_edit_case_page(
        self, patient_id, case_type, date_created, case_status
    ):
        """Navigate to the patient cases tab of the selected patient and assume that the first
        item in the list is a newly created case.

        Get the created date from the details for later comparison.

        :param patient_id: patient's ssn
        :param case_type: type of case to be edited
        :param case_status: status of the case to edit
        :param date_created: date and time the case is created
        :return: case created date
        """
        case_index = self.navigate_to_patient_case_details(
            patient_id, case_type, date_created, case_status
        )
        details_list = self.get_existing_case_notes_details(case_index)
        created_date_xpath = (
            "//div[@class='patient-requests-box']/"
            "descendant::div[@class='case-information'][{0}]/"
            "descendant::div[@class='subtitle'][1]".format(case_index)
        )
        created_date = self.sl.get_text(created_date_xpath)
        edit_case_xpath = (
            "//div[@class='patient-requests-box']/"
            "descendant::div[@class='request-actions'][{0}]/"
            "descendant::button[contains(text(), 'Edit Case')]".format(case_index)
        )
        target = self.driver.find_element_by_xpath(edit_case_xpath)
        ActionChains(self.driver).move_to_element(target).perform()
        self.sl.wait_until_element_is_visible(edit_case_xpath)
        self.poll_keyword(5, 1, "SeleniumLibrary.Click Element", edit_case_xpath)
        self.sl.wait_until_element_is_visible("//h1[contains(text(), 'Process Case')]")
        return case_index, created_date, details_list

    def navigate_to_edit_case_page(
        self, patient_id, case_type, date_created, case_status
    ):
        """Navigate to the patient cases tab of the selected patient and assumes that the first
        item in the list is a newly created case.

        Get the created date from the details for later comparison.

        :param patient_id: patient's ssn
        :param case_type: type of case to be edited
        :param case_status: status of the case to edit
        :param date_created: date and time the case is created
        :return: case created date
        """
        case_index = self.navigate_to_patient_case_details(
            patient_id, case_type, date_created, case_status
        )
        details_list = self.get_existing_case_notes_details(case_index)
        created_date_xpath = (
            '//div[@class="patient-requests-box"]/'
            'descendant::div[@class="case-information"][{0}]/'
            'descendant::div[@class="subtitle"][1]'.format(case_index)
        )
        created_date = self.sl.get_text(created_date_xpath)
        edit_case_xpath = (
            '//div[@class="patient-requests-box"]/'
            'descendant::div[@class="request-actions"][{0}]/'
            'descendant::button[contains(text(), "Edit Case")]'.format(case_index)
        )
        target = self.driver.find_element_by_xpath(edit_case_xpath)
        ActionChains(self.driver).move_to_element(target).perform()
        self.sl.wait_until_element_is_visible(edit_case_xpath)
        self.poll_keyword(5, 1, "SeleniumLibrary.Click Element", edit_case_xpath)
        self.sl.wait_until_element_is_visible('//h1[contains(text(), "Process Case")]')
        return case_index, created_date, details_list

    def navigate_to_patient_case_details(
        self, patient_id, case_type, date_created, case_status
    ):
        """Navigate to the patient case details by clicking the patient case row based on the
        case type and case created date supplied by the user.
        If there's more than one case with the same case type and date, the first entry is
        selected.

        :param patient_id: patient's ssn
        :param case_type: type of case
        :param date_created: date and time the case is created
        :param case_status: case status
        """
        self.page_object_navigation(patient_id)
        cases_xpath = (
            '//div[@class="patient-requests-box"]/descendant::{0}[@class="{1}"][{2}]'
        )
        cases_rows = self.sl.get_element_count('//div[@class="card"]')
        for index in range(int(cases_rows)):
            index += 1
            case_text = self.sl.get_text(cases_xpath.format("div", "case-title", index))
            date_text = self.sl.get_text(cases_xpath.format("span", "date", index))
            status_text = self.sl.get_text(cases_xpath.format("span", "label", index))
            if (
                case_text == case_type
                and date_text == date_created
                and status_text == case_status
            ):
                self.sl.click_element(
                    cases_xpath.format("div", "case-details open-case", index)
                )
                return index

    def get_existing_case_notes_details(self, case_index):
        """Get and return the details of the existing case for later comparison

        Expected details:
        - case created date and time (required)
        - case notes details including Identity Verification, Patient Voiced Understanding
        and Notes (depends if the existing case had these details)

        :return: dictionary of details
        """
        case_header_xpath = (
            '//div[@class="patient-requests-box"]/'
            'descendant::div[@class="case-information"][{0}]/descendant::h5[1]'.format(
                case_index
            )
        )
        self.sl.wait_until_element_is_visible(case_header_xpath)
        details_dict = {}
        identity = BuiltIn().run_keyword_and_return_status(
            "SeleniumLibrary.Element Should Be Visible",
            '//div[@class="patient-requests-box"]/'
            'descendant::div[@class="case-information"][{0}]/'
            'descendant::div[contains(text(), "Identity Verification")]'.format(
                case_index
            ),
        )
        # TODO: should be changed to ID
        notes = BuiltIn().run_keyword_and_return_status(
            "SeleniumLibrary.Element Should Be Visible",
            '//div[@class="patient-requests-box"]/'
            'descendant::div[@class="case-information"][{0}]/'
            'descendant::div[contains(text(), "Identity Verification")]'.format(
                case_index
            ),
        )
        # TODO: should be changed to ID
        if identity is True:
            details_dict["HIPAA Verified"] = "Checked"
        if notes is True:
            notes_elements = self.sl.get_webelements(
                '//div[contains(text(), "Case Notes")]/following-sibling::div'
            )
            notes_text = [
                self.sl.get_text(element)
                for element in notes_elements
                if self.sl.get_text(element)
            ]
            if len(notes_text) == 2:
                details_dict["Patient Voiced Understanding"] = "Checked"
                details_dict["Notes"] = notes_text[1]
            elif len(notes_text) == 1:
                details_dict["Notes"] = notes_text[0]
        return details_dict
