"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.libraries.BuiltIn import logger
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure
from noona.nurse.patients.patient_details.patient_details_navigation import (
    PatientDetailsNavigation,
)


class PatientEducationNavigation(PatientDetailsNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(PatientEducationNavigation, self).__init__()

    @keyword("Open Patient Education Tab")
    @save_driver
    @run_on_failure
    def open_patient_education_tab(self):
        """Open Patient Education tab"""
        logger.info("Opening Patient Education tab")
        self.poll_keyword(5, 1, "Navigation.Click Tab By Id", "tab-education")
        self.sl.wait_until_element_is_visible("id=button-create-binder")
