"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
import time
from robot.libraries.BuiltIn import logger
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure
from noona.navigation.navigation import Navigation
from noona.nurse.patients.patient_details.questionnaires.questionnaires_navigation import (
    QuestionnairesNavigation,
)


class QuestionnaireInquiryNavigation(QuestionnairesNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(QuestionnaireInquiryNavigation, self).__init__()
        self.navigation = Navigation()
        self.nav = QuestionnairesNavigation()

    @keyword("Open Questionnaire")
    @save_driver
    @run_on_failure
    def page_object_navigation(self, patient_id, questionnaire_title):
        """Navigate to questionnaire inquiry page.

        :param patient_id: patient's ssn
        :param questionnaire_title: title of the questionnaire
        """
        logger.info("Opening questionnaire inquiry")
        self.nav.page_object_navigation(patient_id)
        questionnaire_title = questionnaire_title.replace("+", "/")
        self.open_questionnaires_tab()
        # TODO: the following line of code depends on the number of columns.
        # It will not work if the columns are more than 5.
        # We will have to update once id is already available.
        # Todo: remove following commented lines (4)
        # questionnaire_xpath = (
        #     '(//*[text()="{0}"])'
        #     "[last()]".format(questionnaire_title)
        # )
        questionnaire_xpath = (
            '//*[@id="patient-messaging-content"]//noona-treatment-module//tbody//tr[last()]//td[5]//button[2]'
        )
        self.sl.wait_until_page_contains_element(questionnaire_xpath)
        self.poll_keyword(5, 1, "SeleniumLibrary.Click Element", questionnaire_xpath)
        self.sl.wait_until_page_contains_element(
            '//*[@id="mat-dialog-0"]'
        )
        time.sleep(1)
        self.poll_keyword(
            5,
            1,
            "SeleniumLibrary.Click Element",
            '//*[@data-testid="complete-questionnaire"]'
        )
