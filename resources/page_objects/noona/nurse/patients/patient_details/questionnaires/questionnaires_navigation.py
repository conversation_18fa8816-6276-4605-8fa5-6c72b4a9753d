"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.libraries.BuiltIn import logger
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure
from noona.nurse.patients.patient_details.patient_details_navigation import (
    PatientDetailsNavigation,
)


class QuestionnairesNavigation(PatientDetailsNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(QuestionnairesNavigation, self).__init__()

    @keyword("Open Patient Page - Questionnaires Tab")
    @save_driver
    @run_on_failure
    def page_object_navigation(self, patient_id):
        """Navigate to patient page

        :param patient_id: ID/SSN of the patient
        """
        if "handle-patient" in self.sl.get_location():
            self.poll_keyword(5, 1, "Click Button", "id:back-to-button")
        self.open_patient_page(patient_id)
        self.open_questionnaires_tab()

    @keyword
    @save_driver
    @run_on_failure
    def open_questionnaires_tab(self):
        """Open questionnaires tab"""
        logger.info("Opening Questionnaires tab")
        self.poll_keyword(5, 1, "Navigation.Click Tab By Id", "tab-modules")
        self.sl.wait_until_page_contains_element('//*[@data-testid="add-treatment-module"]')
