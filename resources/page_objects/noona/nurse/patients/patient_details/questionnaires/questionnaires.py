"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from datetime import datetime
from time import sleep
from selenium.webdriver.common.action_chains import ActionChains
from robot.api.deco import keyword
from robot.libraries.BuiltIn import BuiltIn
from robot.api import logger
from find_element import FindElement
from base import run_on_failure, save_driver
from noona.nurse.patients.patient_details.questionnaires.questionnaires_navigation import (
    QuestionnairesNavigation,
)
from html_components.radio_button.radio_button import RadioButton
from html_components.checkbox.checkbox import Checkbox
from noona.nurse.patients.patient_details.questionnaires.questionnaire_inquiry.questionnaire_inquiry import (
    QuestionnaireInquiry,
)


class Questionnaires(QuestionnairesNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(Questionnaires, self).__init__()
        self.radio_button = RadioButton()
        self.questionnaire_inquiry = QuestionnaireInquiry()
        self.builtin = BuiltIn()
        self.find_element = FindElement()
        self.checkbox = Checkbox()

    @keyword
    @save_driver
    @run_on_failure
    def complete_questionnaire(
        self, patient_id, questionnaire_title, inquiry_dict=None, **more_args
    ):
        """Complete a questionnaire.

        :param questionnaire_title:
        :param inquiry_dict:
        :param patient_id:
        """
        self.page_object_navigation(patient_id)
        # return self.questionnaire_inquiry.get_questionnaire(questionnaire_title)
        # TODO: complete the keyword

    @keyword
    @save_driver
    @run_on_failure
    def add_schedule(
        self,
        patient_id,
        schedule_name=None,
        module_name=None,
        treatment_date=None,
        date_sent=None,
        navigate=True,
    ):
        """Add a schedule to the patient.

        If the module name is not specified, then the
        main treatment module will be chosen.
        If treatment date is not specified, today date
        will be chosen.

        :param navigate:
        :param patient_id:
        :param schedule_name: name of the schedule
        :param module_name: name of the module
        :param treatment_date: treatment/review date
        :param date_sent: date sent

        Examples:
        | Add Schedule | 12345678 | HADS |
        | Add Schedule | 12345678 | HADS | Palliative treatments | 10.04.2019 |
        | Add Schedule | 12345678 | HADS | Palliative treatments | 10.04.2019 | 08.04.2019 |
        """
        if navigate:
            self.page_object_navigation(patient_id)
        if not treatment_date:
            treatment_date = datetime.today().strftime("%d.%m.%Y")
        self.open_questionnaires_tab()
        if module_name:
            # TODO: To be updated when element id is provided
            edit_xpath = '//div[text()="{0}"]/following::div[text()="Edit"]'.format(
                module_name
            )
        else:
            # TODO: To be updated when element id is provided
            edit_xpath = "//div[@class='row module-header']/child::div[2]"
        self.poll_keyword(10, 1, " SeleniumLibrary.Click Element", edit_xpath)
        self.sl.wait_until_element_is_visible("id:save-treatment-module")
        target = self.driver.find_element_by_id("add-inquiry")
        ActionChains(self.driver).move_to_element(target).perform()
        self.sl.click_button("id:add-inquiry")
        self.sl.select_from_list_by_label(
            "(//select)[last()]", schedule_name.replace("+", "/")
        )
        self.sl.input_text(
            '(//input[contains(@id, "inquiry-treatment-date")])[last()]',
            datetime.strptime(treatment_date, "%d.%m.%Y").strftime("%d%m%Y"),
        )
        if date_sent:
            self.sl.input_text(
                '(//input[contains(@id, "inquiry-sending-date")])[last()]',
                datetime.strptime(date_sent, "%d.%m.%Y").strftime("%d%m%Y"),
            )
        self.sl.click_button("id:save-treatment-module")
        # TODO: This does not check the message itself but only if it shows up after creation.
        # We need to create a separate test for all toast message including translation
        self.sl.wait_until_page_contains_element("//div[@class='toast-message']", 30)

    @keyword
    @save_driver
    @run_on_failure
    def wait_for_schedule_to_be_sent(self):
        """Refresh a page until the schedule is sent.

        Note: This recursive keyword assumes that a schedule
        has been added recently and its status is still
        'scheduled'. It will refreshes the page every second
        till the status 'scheduled' is not on the page anymore.

        Example:
        | Wait For Schedule To Be Sent |
        """
        self.sl.wait_until_page_contains_element("data-testid:open-treatment-module")
        sleep(3)
        element = self.sl.get_webelements("//div[@class='inquiry scheduled']")
        if not element:
            return
        else:
            self.driver.refresh()
            self.wait_for_schedule_to_be_sent()

    @keyword
    @save_driver
    @run_on_failure
    def add_treatment_module(self, patient_id, treatment_dict=None, **more_values):
        """Add a new treatment module.

        Arguments can be passed as in a dictionary or separate
        keys and values in form of 'key=value'.
        Keys are the headers of the elements and they are
        case-insensitive.
        The list below contains all permitted keys:
        - use template
        - use schedule template
        - treatment module
        - status
        - set as main treatment module
        - responsible care team
        - responsible care person
        - line of treatment

       If 'use template' is set to True then templates would
       be used, and if it's set to False or not defined then
       templates would not be used.
       If 'status' key is not set, default would be 'Start now'.

        :param patient_id: patient ssn
        :param treatment_dict: dictionary containing treatment module data
        :param more_values: more values in form of key=value

        Examples:
        | &{dict}= | Create Dictionary | treatment module=Follow up of bowel cancer | Responsible care team=Test |
        | Add Treatment Module | 12345678 | ${dict} |
        | Add Treatment Module | 12345678 | ${dict} | set as main treatment module=${True} |
        | Add Treatment Module | 12345678 | ${dict} | use template=${True} | use schedule template=test |
        """
        treatment_dict = self.join_arguments_into_dictionary(
            treatment_dict, **more_values
        )
        treatment_dict = dict((key.lower(), val) for key, val in treatment_dict.items())
        self.page_object_navigation(patient_id)
        self.poll_keyword(
            5, 1, "SeleniumLibrary.Click Button", "data-testid:open-treatment-module"
        )
        self.sl.wait_until_element_is_visible("id:dialog-title")
        if "use template" not in treatment_dict.keys():
            treatment_dict["use template"] = False
        if "status" not in treatment_dict.keys():
            treatment_dict["status"] = "Start now"
        dict_items = treatment_dict.items()
        for key, val in dict_items:
            if key in "use template":
                if val is False:
                    self.sl.click_element('//span[text()="Add without a template"]')
                    self.sl.wait_until_element_is_visible('//button[text()="Save"]')
                    break
                else:
                    if "use schedule template" not in treatment_dict.keys():
                        raise ValueError("schedule template argument was not provided!")
                    self.radio_button.select_radio_button(treatment_dict["status"])
                    self.sl.select_from_list_by_label(
                        "id:template-selector", treatment_dict["use schedule template"]
                    )
                    self.sl.click_button("id:save-templating-button")
                    self.sl.wait_until_element_is_visible("id:save-treatment-module")
        is_main_module = False
        for key, val in dict_items:
            if key in "treatment module":
                logger.trace("selecting treatment module....")
                self.sl.select_from_list_by_label("id:module-template-selector", val)
            elif key in "status":
                logger.trace("selecting status....")
                self.radio_button.select_radio_button(val.capitalize())
                if val.lower() in "scheduled start":
                    self.sl.input_text(
                        "id:scheduled-date",
                        datetime.strptime(
                            treatment_dict["scheduled date"], "%d.%m.%Y"
                        ).strftime("%d%m%Y"),
                    )
            elif key in "set as main treatment module":
                if val is True:
                    logger.trace("setting as main module....")
                    self.checkbox.select_checkbox(key.capitalize())
                    is_main_module = True
            elif key in "responsible care team":
                logger.trace("selecting responsible care team....")
                self.sl.select_from_list_by_label("name:responsible-unit", val)
            elif key in "responsible care person":
                logger.trace("selecting responsible care person")
                self.sl.select_from_list_by_label("name:responsible-nurse", val)
            elif key in "line of treatment":
                logger.trace("selecting line of treatment....")
                self.sl.select_from_list_by_label(
                    '//select[contains(@class, "line-of-treatment")]', val
                )
        self.sl.click_button("id:save-treatment-module")
        if is_main_module and treatment_dict["status"] == "Start now":
            self.sl.wait_until_element_is_visible("id:ok-confirm")
            self.sl.click_button("ok-confirm")
        self.sl.wait_until_page_contains("Treatment module added")

    @keyword
    @save_driver
    @run_on_failure
    def close_treatment_module(self, patient_id, treatment_module):
        """Close a treatment module.

        :param patient_id: patient ssn
        :param treatment_module: name of the treatment module

        Example:
        | Close Treatment Module | 12345678 | Follow up of bowel cancer |
        """
        self.sl.wait_until_page_does_not_contain("Treatment module added")
        self.page_object_navigation(patient_id)
        self.poll_keyword(
            5,
            1,
            "SeleniumLibrary.Click Element",
            '//div[text()="{0}"]'.format(treatment_module),
        )
        self.sl.wait_until_element_is_visible("id:close-treatment-module")
        self.sl.click_button("id:close-treatment-module")
        self.sl.wait_until_element_is_visible("id:ok-confirm")
        self.sl.click_button("id:ok-confirm")
        self.sl.wait_until_page_contains("Treatment module saved")

    @keyword
    @save_driver
    @run_on_failure
    def remove_treatment_module(self, patient_id, treatment_module):
        """Remove a treatment module.

        :param patient_id: patient ssn
        :param treatment_module: name of the treatment module

        Example:
        | Remove Treatment Module | 12345678 | Follow up of bowel cancer |
        """
        self.sl.wait_until_page_does_not_contain("Treatment module added")
        self.page_object_navigation(patient_id)
        self.poll_keyword(
            5,
            1,
            "SeleniumLibrary.Click Element",
            '//div[text()="{0}"]'.format(treatment_module),
        )
        self.sl.wait_until_element_is_visible("id:remove-treatment-module")
        self.sl.click_button("id:remove-treatment-module")
        self.sl.wait_until_element_is_visible("id:ok-confirm")
        self.sl.click_button("id:ok-confirm")
        self.sl.wait_until_page_contains("Treatment module removed")
