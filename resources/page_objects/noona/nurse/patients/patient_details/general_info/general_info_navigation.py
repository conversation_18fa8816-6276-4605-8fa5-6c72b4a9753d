"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from noona.nurse.patients.patient_details.patient_details_navigation import (
    PatientDetailsNavigation,
)


class GeneralInfoNavigation(PatientDetailsNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(GeneralInfoNavigation, self).__init__()

    def open_general_info_tab(self):
        """Patient is already selected. User open general information tab"""
        self.poll_keyword(5, 1, "Navigation.Click Tab By Id", "tab-general-information")
        self.sl.wait_until_element_is_visible("id:ssn")
