"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
import time
from noona.nurse.patients.patient_details.general_info.general_info_navigation import (
    GeneralInfoNavigation,
)


class PersonalDataUpdate(GeneralInfoNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(PersonalDataUpdate, self).__init__()

    def change_patient_password(self, password):
        """Patient is already selected. Select the general info tab."""
        self.open_general_info_tab()
        self.poll_keyword(5, 1, " SeleniumLibrary.Click Element", "change-password")
        self.sl.wait_until_element_is_visible("newPassword")
        self.sl.wait_until_element_is_enabled("newPassword")
        time.sleep(1)
        self.sl.input_password("newPassword", password)
        # TODO: selenium speed is too fast the whole password cannot be entered without sleep
        time.sleep(1)
        self.sl.input_password("newPasswordAgain", password)
        time.sleep(1)
        self.sl.click_element("save")
        self.sl.wait_until_page_contains('Password changed')
        self.sl.wait_until_page_does_not_contain("Password changed",7)
     #   self.sl.click_element('//div[@class="toast-message"]')
