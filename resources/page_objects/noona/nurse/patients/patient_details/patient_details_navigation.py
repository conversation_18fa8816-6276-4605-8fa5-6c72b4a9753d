"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure
from noona.nurse.patients.patients import Patients


class PatientDetailsNavigation(Base):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(PatientDetailsNavigation, self).__init__()
        self.patients = Patients()

    @keyword("Open Patient Page")
    @save_driver
    @run_on_failure
    def open_patient_page(self, patient_id):
        """Navigate to patient page

        :param patient_id: ID/SSN of the patient
        """
        self.patients.find_patient_by_id(patient_id)
