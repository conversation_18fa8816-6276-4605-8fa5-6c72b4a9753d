"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from robot.libraries.BuiltIn import logger
from robot.api.deco import keyword
from base import Base, save_driver, run_on_failure
from noona.navigation.navigation import Navigation


class PatientsNavigation(Base):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(PatientsNavigation, self).__init__()
        self.navigation = Navigation()

    @keyword(name="Open Patients")
    @save_driver
    @run_on_failure
    def page_object_navigation(self):
        """Open Patients page in Nurse"""
        logger.info("Opening Patients page")
        self.poll_keyword(
            15, 1, "SeleniumLibrary.Click Element", "id:managepatients-link"
        )
        self.sl.wait_until_page_contains_element("ssn")

    @keyword
    @save_driver
    @run_on_failure
    def open_create_patient_tab(self):
        """Open create patient tab"""
        logger.info("Opening Create Patient Tab")
        self.navigation.click_tab_by_id("create-patient")
        self.sl.wait_until_page_contains_element(
            "//form[@name='newPatientForm']/child::h4[1]"
        )

    @keyword
    @save_driver
    @run_on_failure
    def open_connect_patients_tab(self):
        """Open connects patients tab"""
        logger.info("Opening Connect Patients Tab")
        self.navigation.click_tab_by_id("patient-clinic-connection")
        self.sl.wait_until_page_contains_element("open-invite-patient")

    @keyword
    @save_driver
    @run_on_failure
    def open_list_of_patients_tab(self):
        """Open list of patients tab"""
        logger.info("Opening List of Patients Tab")
        self.navigation.click_tab_by_id("patient-list")
        self.sl.wait_until_page_contains("Responsible")
