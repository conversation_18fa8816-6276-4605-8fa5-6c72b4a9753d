"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
import time
from noona.patient.patient_account_navigation import PatientAccountNavigation


class ClinicMenu(PatientAccountNavigation):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(ClinicMenu, self).__init__()

    def get_latest_message(self):
        """Get the latest message title from the patient's inbox

        :return: message title
        """
        self.open_clinic_menu()
        self.sl.wait_until_element_is_visible("//ul[@class='messages ng-star-inserted']")
        unread_messages_xpath = "//ul[@class='messages ng-star-inserted']/descendant::div[@class='unread-marker-bottom ng-star-inserted']"
        unread_count = self.sl.get_element_count(unread_messages_xpath)
        for x in range(5):
            # TODO: wait for 5 seconds to receive the message
            time.sleep(1)
            if int(unread_count) == 1:
                new_message = self.sl.get_text(
                    "//ul[@class='messages ng-star-inserted']/descendant::div[@class='message-subject'][1]"
                )
                return new_message

    def select_latest_message(self):
        """Select latest message in patient's inbox"""
        if "clinic" not in self.sl.get_location():
            self.open_clinic_menu()
            self.sl.wait_until_element_is_visible(
                "//ul[@class='messages ng-star-inserted']"
            )
        self.sl.click_element(
            "//ul[@class='messages ng-star-inserted']/descendant::div[@class='message-subject'][1]"
        )

    def answer_questionnaire(self):
        """Click answer questionnaire button"""
        answer_button_xpath = "//ng-component//ds-button[2]"
        self.sl.wait_until_element_is_visible(answer_button_xpath)
        self.sl.wait_until_element_is_enabled(answer_button_xpath)
        self.sl.click_element(answer_button_xpath)
        # TODO: Answer questionnaire by filling in questions

    def is_latest_message_unread(self):
        self.open_clinic_menu()
        self.sl.wait_until_element_is_visible("//ul[@class='messages ng-star-inserted']")
        unread_messages_xpath = self.sl.get_webelement(
            "//ul[@class='messages ng-star-inserted']/descendant::div[@class='unread-marker-bottom ng-star-inserted'][1]"
        )
        if unread_messages_xpath:
            return True
        return False
