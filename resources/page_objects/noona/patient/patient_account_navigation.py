"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
from base import Base, save_driver, run_on_failure


class PatientAccountNavigation(Base):
    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(PatientAccountNavigation, self).__init__()

    def open_clinic_menu(self):
        """Open clinic menu to view messages from the clinic"""
        self.poll_keyword(
            5, 1, "SeleniumLibrary.Click Element", "//button[@aria-label='Clinic page']"
        )
        self.sl.wait_until_element_is_visible("//nh-header[@class='clinic']")
