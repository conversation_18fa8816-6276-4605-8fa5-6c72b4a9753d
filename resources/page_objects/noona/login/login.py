"""
Copyright (c) 2019 Varian Medical Systems. All Rights Reserved.
COMPANY CONFIDENTIAL
"""
import time
from robot.api.deco import keyword
from robot.libraries.BuiltIn import BuiltIn, RobotNotRunningError
from base import Base, run_on_failure, save_driver
from html_components.radio_button.radio_button import RadioButton


class Login(Base):
    """Login library

    Login class contains keywords for logging in Noona app
    (Management, Nurse and Patient)
    """

    ROBOT_LIBRARY_SCOPE = "GLOBAL"

    def __init__(self):
        super(Login, self).__init__()
        self.builtin = BuiltIn()
        self.radio_button = RadioButton()

    @keyword
    @save_driver
    @run_on_failure
    def login_to_management(self, username, password, clinic_name, keep_logged_in="Yes"):
        """login to management

        :param username: username
        :param password: password
        :param clinic_name: clinic name
        :param keep_logged_in: keep logged in

        Example:
        | Login To Management | <EMAIL> | pass123 | clinic A |
        | Login To Management | <EMAIL> | pass123 | clinic A | No |
        """
        self.login_to_noona(
            self.management_url, username, password, clinic_name, keep_logged_in
        )
        self.sl.wait_until_page_contains_element('//a[text()="{0}"]'.format(username))

    @keyword
    @save_driver
    @run_on_failure
    def login_to_nurse(self, username, password, clinic_name=None, keep_logged_in="Yes"):
        """login to nurse

        :param username: username
        :param password: password
        :param clinic_name: clinic name
        :param keep_logged_in: keep logged in

        Examples:
        | Login To Nurse | <EMAIL> | pass123 | clinic A |
        | Login To Nurse | <EMAIL> | pass123 | clinic A | No |
        """
        self.login_to_noona(
            self.nurse_url, username, password, clinic_name, keep_logged_in
        )
        self.sl.wait_until_page_contains_element('//a[text()="{0}"]'.format(username))

    @keyword
    @save_driver
    @run_on_failure
    def login_to_patient(self, username, password, keep_logged_in="Yes"):
        """login to patient

        :param username: username
        :param password: password
        :param keep_logged_in: keep logged in

        Examples:
        | Login To Patient | <EMAIL> | pass123 |
        | Login To Patient | <EMAIL> | pass123 |
        """
        self.login_to_noona(
            self.patient_url, username, password, keep_logged_in=keep_logged_in
        )
        self.sl.wait_until_page_contains_element("//*[@id='patient']")

    @keyword
    @save_driver
    @run_on_failure
    def logout_from_noona(self):
        """Log out from noona and validate if the user is back at the login page after logout

        Examples:
        | Logout From Noona |
        """
        spinner_xpath = '//*[@class="spinner visible"]'
        self.sl.wait_until_element_is_visible(spinner_xpath)
        self.sl.wait_until_page_does_not_contain_element(spinner_xpath)
        self.sl.wait_until_element_is_enabled("logout-link")
        self.sl.click_element("logout-link")
        self.sl.wait_until_page_contains_element("email-and-password-next-button")
        self.sl.wait_until_page_contains("Welcome to Noona")

    def login_to_noona(
        self, url, username, password, clinic_name=None, keep_logged_in="Yes"
    ):
        browser = self.builtin.get_variable_value("${BROWSER}")
        remote_url = self.builtin.get_variable_value("${REMOTE_URL}")
        if remote_url is None:
            remote_url = False
        self.sl.open_browser(url, browser=browser, remote_url=remote_url)
        if browser.startswith("headless"):
            # Use ipad 3rd/4th generation resolution
            # TODO: questionnaires tab has a different UI on tablets.
            # complete/for patient/declined buttons are merged into options button
            self.sl.set_window_size(2048, 1536)
        else:
            self.sl.maximize_browser_window()
        language_code = BuiltIn().get_variable_value("${LANGUAGE}")
        # Disabling language selection for nurse, logic has changed
#        if "nurse" in url:
#            self.select_language_as_nurse(language_code)
        if "patient" in url:
            self.select_language_as_patient(language_code)
        self.sl.wait_until_element_is_enabled("email")
        self.poll_keyword(5, 1, "SeleniumLibrary.Input Text", "email", username)
        self.poll_keyword(5, 1, "SeleniumLibrary.Input Text", "pwd", password)
        self.sl.wait_until_element_is_visible("email-and-password-next-button")
        time.sleep(1)
        self.sl.click_element('//*[@id="email-and-password-next-button"]')
        if clinic_name:
            self.choose_clinic(clinic_name)
        # TODO: check if the keep-logged-in page would be shown or no
        if keep_logged_in != "None":
            self.sl.wait_until_page_contains_element("//*[@class='login-form']")
            self.keep_logged_in(keep_logged_in)
            self.poll_keyword(
                5,
                1,
                "SeleniumLibrary.Click Element",
                '//*[@id="remember-me-next-button"]',
            )

    def select_language_as_nurse(self, language):
        self.sl.wait_until_element_is_enabled('//*[@id="locale"]')
        self.poll_keyword(5, 1, "SeleniumLibrary.Click Element", '//*[@id="locale"]')
        self.sl.wait_until_page_contains_element('//*[@value="{0}"]'.format(language))
        self.sl.wait_until_element_is_visible(
            '//*[@class="ng-dropdown-panel-items scroll-host"]'
        )
        time.sleep(1)
        self.poll_keyword(
            5, 1, "SeleniumLibrary.Click Element", '//*[@value="{0}"]'.format(language)
        )
        self.sl.wait_until_element_is_enabled("email")

    def select_language_as_patient(self, language):
        self.sl.wait_until_element_is_visible(
            '//*[@id="locale"]//preceding-sibling::label'
        )
        self.sl.wait_until_element_is_visible(
            '//span[@class="ng-value-label ng-star-inserted"]'
        )
        self.sl.wait_until_element_is_enabled("locale")
        self.sl.click_element("locale")
        for i in range(5):
            langcode = self.builtin.run_keyword_and_return_status(
                "SeleniumLibrary.GetWebElement", '//*[@value="{0}"]'.format(language)
            )
            if langcode:
                break
            else:
                self.sl.click_element("locale")
                self.sl.click_element("locale")
        self.sl.click_element('//*[@value="{0}"]'.format(language))
        self.sl.wait_until_element_is_enabled("email")

    def choose_clinic(self, clinic_name):
        self.sl.wait_until_element_is_enabled('//*[@id="clinic-next-button"]')
        self.sl.select_from_list_by_label(
            "//div[@id='clinic']/descendant::select", clinic_name
        )
        self.poll_keyword(
            5, 1, "SeleniumLibrary.Click Element", '//*[@id="clinic-next-button"]'
        )

    def keep_logged_in(self, logged_in):
        self.sl.wait_until_page_contains_element("remember-me-yes")
        if logged_in == "Yes":
            option_id = "//input[@id='remember-me-yes']/following-sibling::label"
        elif logged_in == "No":
            option_id = "//input[@id='remember-me-no']/following-sibling::label"
        self.poll_keyword(20, 1, "SeleniumLibrary.Click Element", option_id)

    @property
    def management_url(self):
        try:
            return self.builtin.get_variable_value("${MANAGEMENT_LOGIN_URL}")
        except RobotNotRunningError:
            return "variable not found!"

    @property
    def nurse_url(self):
        try:
            return self.builtin.get_variable_value("${NURSE_LOGIN_URL}")
        except RobotNotRunningError:
            return "variable not found!"

    @property
    def patient_url(self):
        try:
            return self.builtin.get_variable_value("${PATIENT_LOGIN_URL}")
        except RobotNotRunningError:
            return "variable not found!"
