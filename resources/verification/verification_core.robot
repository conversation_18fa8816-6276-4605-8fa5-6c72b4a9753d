*** Settings ***
Library     DateTime
Library     Dialogs
Library     SeleniumLibrary


*** Variables ***
${VERIFICATION_AUTO_TAG}        VERIFICATION_AUTOMATIC
${VERIFICATION_MANUAL_TAG}      VERIFICATION_MANUAL
${PASS_LOG_LEVEL}               INFO
${LOG_PASS_TO_CONSOLE}          False
${TAKE_SCREENSHOT}              True


*** Keywords ***
Verify Step
    [Documentation]    Provides verification keyword core functionality.
    ...
    ...    Main keyword accessed by proper verification keywords. Handles evidence
    ...    gathering and, where necessary, manual operations, as well as logging
    ...    verification steps separately.
    ...
    ...    If a keyword is always to be executed manually, you can use Verify Manual
    ...    Step in a similar manner.
    ...
    ...    message: The property under test, such as "Login error text"
    ...    expected: What the result should be, such as "Invalid username or password"
    ...    result: What the result actually is, such as "Invadil user or password"
    [Arguments]    ${message}    ${expected}    ${result}
    ${is_manual}=    Is Test Manual
    Run Keyword And Return If    $is_manual==True    Verify Manual Step    ${message}    ${expected}
    Set Tags    ${VERIFICATION_AUTO_TAG}
    Verify And Log    ${message}    ${expected}    ${result}

Verify Manual Step
    [Documentation]    The keyword for executing user-driven verification steps.
    ...
    ...    When run, this keyword will ask the user for what the observed result is.
    ...    They are then given the option to click PASS or FAIL against a prompted
    ...    expected result. On PASS, the verification step succeeds, on FAIL, it
    ...    fails. When FAIL is chosen, the user is also prompted for further input
    ...    on the failure.
    ...
    ...    message: The property under test, such as "Login error text"
    ...    expected: What the result should be, such as "Invalid username or password"
    [Arguments]    ${message}    ${expected}
    Set Tags    ${VERIFICATION_MANUAL_TAG}
    ${q_status}    ${user_input}=    Run Keyword And Ignore Error
    ...    Get Value From User
    ...    What is the observed result of '${message}'?
    ...    default_value=
    Run Keyword And Return If    $q_status=='FAIL'    Fail    Verification step for '${message}' was cancelled.
    ${v_status}    ${retmsg}=    Run Keyword And Ignore Error
    ...    Execute Manual Step
    ...    The scenario '${message}' should be '${expected}' and was '${user_input}'.
    ...    default_error=Please input further information, if any
    Set Test Message    Details for '${message}': "${retmsg}"\n    append=True
    Verify And Log    ${message}    ${expected}    ${user_input}    status=${v_status}

Verify And Log
    [Documentation]    Handles the core expectation-versus-result comparison and results logging.
    ...
    ...    message: The property under test, such as "Login error text"
    ...    expected: What the result should be, such as "Invalid username or password"
    ...    result: What the result actually is, such as "Invadil user or password"
    [Arguments]    ${message}    ${expected}    ${result}    ${status}=None
    ${success}=    Evaluate    ($expected == $result or $status == 'PASS') and not ($status == 'FAIL')
    IF    $TAKE_SCREENSHOT==True    Capture Verification Screenshot
    ${log_msg}=    Get Logging Message    ${message}    ${expected}    ${result}
    Set Test Message    ${log_msg}    append=True
    Run Keyword And Return If    $success==False    Fail    ${log_msg}
    Log    ${log_msg}    level=${PASS_LOG_LEVEL}    console=${LOG_PASS_TO_CONSOLE}

Is Test Manual
    [Documentation]    Determines whether a verification step is executed manually or not.
    ...
    ...    This keyword should always return True or False.
    RETURN    False

Capture Verification Screenshot
    [Documentation]    Capture a screenshot for objective evidence during verification steps.
    Capture Screenshot

Get Logging Message
    [Documentation]    Returns the report message for the verification assertion.
    ...
    ...    message: The property under test, such as "Login error text"
    ...    expected: What the result should be, such as "Invalid username or password"
    ...    result: What the result actually is, such as "Invadil user or password"
    [Arguments]    ${message}    ${expected}    ${result}
    RETURN    '${message}' should be: '${expected}'\n'${message}' was: '${result}'\n

Document Environment
    [Documentation]    Builds a report of all necessary environment data.
    ...
    ...    The data should be stored in a central location, such as suite metadata.
    ${time}=    Get Current Date    time_zone=UTC    result_format=%d-%b-%Y %H:%M:%S
    Set Suite Metadata    Time (UTC)    ${time}
