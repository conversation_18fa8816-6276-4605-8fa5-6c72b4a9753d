*** Settings ***
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource
Resource    ${EXECDIR}${/}resources/clinic_settings/clinic_information.resource

*** Variables ***
${label-changes-saved}                  Updated
${label-team-removed}                   Care team removed
${label-team-cannot-be-removed}         Care team cannot be removed
${user_list_tab}                        //*[@data-testid="noona-clinic-users-list"]
${care_teams_tab}                       users-care-teams
${add_care_team_button}                 //*[@data-testid="add-care-team-template"]
${dialog_title}                         //*[@id='care-teams-modal']//h4
${care_team_input}                      //*[@data-testid="name"]
${save_care_team_button}                //*[@data-testid="save-care-team"]
${remove_care_team_button}              //*[@data-testid="remove-careteam"]
${confirm_button}                       ok-confirm
${notification_email_box}               //*[@data-testid="notificationEmail"]
${notofication_email_checkbox_text}
...                                     //*[@data-testid="work-queue-notification"]//*[contains(text(), 'Send an email notification for new cases assigned to this team')]
${notofication_email_checkbox}          (//*[@data-testid="work-queue-notification"]//label)[1]
${case_assigned_text}                   Case assigned successfully
${cancel_care_team}                     //*[@data-testid="cancel-care-team"]
${opened_case}                          //*[@data-testid='case-callbackRequested index-0']

*** Keywords ***
Go To Clinic Users List
    Try To Click Element    ${manage_nurses_link}
    Try To Click Element    ${user_list_tab}

