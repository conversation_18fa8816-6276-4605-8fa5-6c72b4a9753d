*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}general_information.resource


*** Variables ***
${sms_invitation_care_team_filter}                      //*[@class='searchable-filter']/label[text()=" Responsible care team "]
${sms_invitation_show_in_the_list_filter}               //*[@class='searchable-filter']/label[text()=" Show in the list "]
${sms_invitation_care_team_filter_dropdown}             ${sms_invitation_care_team_filter}/..//span[@class="ng-arrow-wrapper"]
${sms_invitation_show_in_the_list_filter_dropdown}      ${sms_invitation_show_in_the_list_filter}/..//span[@class="ng-arrow-wrapper"]
${sms_invitation_patient_list}                          //*[@id='invitation-table']//tbody/tr


*** Keywords ***
Select SMS Invitation Care Team
    [Arguments]    ${care_team}
    Wait Until Element Is Visible    ${sms_invitation_care_team_filter}
    Wait Until Element Is Enabled    ${sms_invitation_care_team_filter}
    Try To Click Element    ${sms_invitation_care_team_filter_dropdown}
    Try To Click Element    ${sms_invitation_care_team_filter}/..//*[contains(text(),"Remove all selections")][last()]
    Try To Click Element    ${sms_invitation_care_team_filter}/..//*[contains(text(),"${care_team}")][last()]
    Try To Click Element    ${sms_invitation_care_team_filter_dropdown}

Select SMS Invitation Status
    [Arguments]    ${status}
    Wait Until Element Is Visible    ${sms_invitation_show_in_the_list_filter}
    Wait Until Element Is Enabled    ${sms_invitation_show_in_the_list_filter}
    Try To Click Element    ${sms_invitation_show_in_the_list_filter_dropdown}
    Try To Click Element
    ...    ${sms_invitation_show_in_the_list_filter}/..//*[contains(text(),"Remove all selections")][last()]
    Try To Click Element    ${sms_invitation_show_in_the_list_filter}/..//*[contains(text(),"${status}")][last()]
    Try To Click Element    ${sms_invitation_show_in_the_list_filter_dropdown}

Invitation Status Is Correct
    [Arguments]    ${expected_status}
    Wait Until Element Is Visible    (${sms_invitation_patient_list})[last()]
    Wait Until Element Is Visible    (${sms_invitation_patient_list})[1]/td[last()-1][text()=" ${expected_status} "]
    ${count}    Get Element Count    ${sms_invitation_patient_list}
    FOR    ${INDEX}    IN RANGE    1    ${count}
        ${status}    Get Text    (${sms_invitation_patient_list})[${INDEX}]/td[last()-1]
        Should Be Equal    ${status}    ${expected_status}
    END
