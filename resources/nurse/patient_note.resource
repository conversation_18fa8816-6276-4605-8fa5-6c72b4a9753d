*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}case_management.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}work_queue.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource


*** Variables ***
${add_note_button}                          //*[@data-testid="add-note-btn"]
${topic_input_field}                        //*[@data-testid="title"]
${note_text_area}                           //*[@data-testid="content"]
${save_note_button}                         //*[@data-testid="add-note__save--button"]
${internal_message_content}                 //div[contains(@class, 'internal-message')]
${note_reminder_checkbox}                   //*[@data-testid="reminder"]
${note_reminder_date_input}                 //*[@data-testid="reminderDate"]
${work_queue_link}                          //*[@id="work-queue-link"]
${tomorrow_button}                          //*[@data-testid='btn-tomorrow']
${work_queue_items}                         //div[contains(@id, 'workqueue-item-name')]/../../../../mat-card[contains(@class, 'patient-note')]/..
${questionnaire_type_dropdown}              (//button[contains(@class, 'select dropdown')])[2]
${select_all_questionnaires}                //label[contains(text(), 'Select all')]/../..
${automation_note_template_title_2}         Automation note template for add note
${automation_note_template_content_2}       Automation template for add note. Do not delete.
${note_template_dropdown}                   //*[@id="noteTemplate"]//input
${note_template_input}                      //*[@id="noteTemplate"]//input
${note_template_list_options}               //li[contains(@id, 'option')]
${first_template_option}                    //*[@data-testid="option-0"]
${page_messaging_content}                   //div[@id='patient-messaging-content']


*** Keywords ***
Select Patient
    [Arguments]    ${ssn}
    Search Patient By Identity Code    ${ssn}

Add Note
    [Arguments]    ${reminder}==${EMPTY}
    Wait Until Element Is Visible    ${add_note_button}    10s
    Wait Until Element Is Enabled    ${add_note_button}    2s
    Wait Until Element Is Enabled    ${page_messaging_content}
    Sleep    3s
    Try To Click Element    ${add_note_button}
    Wait Until Element Is Visible    ${topic_input_field}
    ${random_string_topic}    Generate Random String    30
    ${random_string_note_text}    Generate Random String    200
    Set Test Variable    ${random_string_topic}
    Set Test Variable    ${random_string_note_text}
    Try To Input Text    ${topic_input_field}    ${random_string_topic}
    Try To Input Text    ${note_text_area}    ${random_string_note_text}
    IF    '${reminder}'=='Reminder'    Add Reminder
    Try To Click Element    ${save_note_button}
    Wait Until Page Contains    Note added

Verify Patient Note
    [Arguments]    ${reminder}==${EMPTY}
    Wait Until Page Contains Element
    ...    //*[contains(@class,"case-title") and contains(text(), '${random_string_topic}')]
    Try To Click Element    //*[contains(@class,"case-title") and contains(text(), '${random_string_topic}')]
    Wait Until Page Contains Element    ${internal_message_content}
    Try For Element Should Contain    ${internal_message_content}    ${random_string_note_text}
    Try For Element Should Contain    ${internal_message_content}    Internal message
    Try For Element Should Contain
    ...    (//noona-patient-case-list-item//*[contains(@class, "mat-expanded")])[1]//div[contains(@class, 'case-info-header__container')]/h5
    ...    Note
    IF    '${reminder}'=='Reminder'
        Try For Element Should Contain
        ...    (//noona-patient-case-list-item//*[contains(@class, "mat-expanded")])[1]//div[contains(@class, 'date')]
        ...    ${current_date_verify}
    END
    IF    '${reminder}'=='Reminder'
        Try For Element Should Contain
        ...    (//noona-patient-case-list-item//*[contains(@class, "mat-expanded")])[1]//div[contains(@class, 'case-status')]
        ...    Closed
    END

Add Reminder
    Try To Click Element    ${note_reminder_checkbox}
    ${current_date}    Get Current Date
    ${current_date_verify}    Convert Date    ${current_date}    result_format=%d.%m.%Y
    Set Test Variable    ${current_date_verify}
    ${tomorrow_date}    Add Time To Date    ${current_date}    1 days
    ${tomorrow_date_input_format}    Convert Date    ${tomorrow_date}    result_format=%d%m%Y
    ${tomorrow_date_verify}    Convert Date    ${tomorrow_date}    result_format=%d.%m.%Y
    Set Test Variable    ${tomorrow_date_verify}
    Try To Input Text    ${note_reminder_date_input}    ${tomorrow_date_input_format}

Check Note From Patient Reports
    Click Element    ${back_to_button}
    Wait Until Page Contains Element    ${work_queue_link}
    Try To Click Element    ${work_queue_link}
    Try To Click Element    ${reports_tab}
    Try To Click Element    ${tomorrow_button}
    Select Questionnaire Type In Work Queue    Select all
    Remove All Care Team Filter
    Select My Patients In Work Queue
    Try To Click Element    ${tomorrow_button}
    Wait Until Page Contains Element    ${work_queue_items}    20s
    ${work_queue_elements}    Get WebElements    ${work_queue_items}
    Try To Click Element    ${work_queue_elements}[0]
    Wait Until Page Contains Element    //div[contains(text(),'${random_string_topic}')]
    Wait Until Page Contains Element    ${internal_message_content}
    Try For Element Should Contain    ${internal_message_content}    ${random_string_note_text}

Add Note With Template
    [Arguments]    ${reminder}==${EMPTY}
    Try To Click Element    ${add_note_button}
    Wait Until Page Contains Element    ${topic_input_field}
    Try To Click Element    ${note_template_dropdown}
    Try To Input Text    ${note_template_input}    ${automation_note_template_title_2}
    Try To Click Element    ${first_template_option}
    Try To Click Element    ${save_note_button}
    Wait Until Page Contains    Note added
    Try To Click Banner Message

Verify Patient Note With Template
    Wait Until Page Contains Element
    ...    //div[contains(@class, 'case-title') and contains(text(), '${automation_note_template_title_2}')]
    Try To Click Element
    ...    //div[contains(@class, 'case-title') and contains(text(), '${automation_note_template_title_2}')]
    Wait Until Page Contains Element    ${internal_message_content}
    Try For Element Should Contain    ${internal_message_content}    ${automation_note_template_title_2}
    Try For Element Should Contain    ${internal_message_content}    ${automation_note_template_content_2}
