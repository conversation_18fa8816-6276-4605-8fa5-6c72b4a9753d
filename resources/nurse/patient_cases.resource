*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}patient_cases.resource
Resource    ${EXECDIR}${/}resources${/}patient${/}clinic.resource


*** Variables ***
${contact_type_input}                   //*[@data-testid="type"]//input
${contact_patient_message_field}        //*[@data-testid="messageForPatient-textarea"]
${contact_patient_send_button}          send-contact
${patient_cases}                        //noona-patient-case-list-item
${message_template_dropdown}            //*[@data-testid="messageTemplate"]/descendant::input
${contact_patient_language_dropdown}    //*[@data-testid='language']
${case_latest_message_or_note}          //div[contains(@class, 'case-messages case-notes')]/div[last()]
${first_opened_case}                    //div[@class='case-details open-case open']
${top_contact_patient_button}           //*[@data-testid="contact-patient-btn"]
${topic_field}                          //*[@data-testid="contact-topic-input"]
${new_case_button}                      //*[@data-testid="new-case-btn"]
${process_case_button}                  //*[@data-testid="process-case-button"]
${edit_case_button}                     //button//span[contains(text(),"Edit Case")]
${case_details_title}                   //div[contains(text(),"{}")]
${qol_questionnaire_summary_title}      //div[starts-with(@class, 'symptom-content-title')]
${archived_to_emr_title}                //*[contains(text(),"Archived to EMR")]
${archived_to_emr_content}              //*[contains(text(),"Archived to EMR")]/../../following-sibling::div/p
${change_log_button}                    //*[contains(@class, 'mat-expanded')]//div[starts-with(@class, 'case-changelog')]/div[1]
${latest_log_row}                       //div[starts-with(@class, 'log-contents')]/div[last()]
${download_pdf_button}                  //*[contains(@class, 'mat-expanded')]//button[@data-testid='pdf-button']
${patient_phone_number_link}            //a[contains(@href, 'tel:')]
${validate_automated_answer_button}     //button[starts-with(@class, 'validate-button btn btn-primary')]
${validation_correct}                   //input[@id='automated-answer-status-correct-']/following-sibling::label
${validation_incorrect}                 //input[@id='automated-answer-status-incorrect-']/following-sibling::label
${validation_note}                      validation-note
${validation_save_button}               save-validation
${distress_score}                       //div[text()=" Score "]/following-sibling::div/span
${case_summary_case_priority}           //div[text()="Case Priority"]/following-sibling::div
${patient_cases_tab}                    //*[@id="tab-patient-messaging"]
${clipboard_icon}                       //*[@data-testid="copy-button"]
${patient_cases_list}                   //*[@data-testid="patient-case-list"]
${add_note_btn}                         //*[@data-testid="add-note-btn"]
${first_case}                           //*[@data-testid='case-closed index-0']
${expanded_open_case}
...                                     (//*[contains(@id, 'mat-expansion-panel-header')]/../*[contains(@class, 'mat-expanded')])[1]
${expanded_case_date_elements}          ${expanded_open_case}//div[contains(@class, "case-date")]
${expanded_case_element}                //div[text()=" {} "]/../../../..
${aria_expanded_attr}                   aria-expanded
${message_unread_text_element}          //div[@class='patient-unread-message']//span[contains(text(),"Message unread by patient")]
${message_unread_envelop_icon}          //div[@class='patient-unread-message']//mat-icon[@data-mat-icon-name='icon-mail']
${message_read_text_element}            //div[@class='patient-read-message']//span[contains(text(),"Message read by patient")]
${message_read_envelop_icon}            //div[@class='patient-read-message']//mat-icon[@data-mat-icon-name='icon-mail-opened']
${attachment_title}                     //*[contains(@class, "attachment-list-header-title")]
${attachment_item_in_case}              //message-attachment
${contact_type_dropdown}                //*[@id='type']/div
${patient_education_contact_option}     //*[@title='Patient Education']
${question_contact_option}              //*[@title='Question']
${consult_care_team_button}             //*[@data-testid='consult-careteam-button']
${clipboard_content}                    //*[@data-testid="text"]
${close_clipboard_button}               //*[@data-testid="cancel"]
${first_case_card}                      //*[@data-testid="patient-case-list"]//noona-patient-case-list-item[1]


*** Keywords ***
Select Message Template To Contact Patient
    [Arguments]    ${template}
    Verify Is Language Selected Is Correct    English
    Wait Until Element Is Enabled    ${message_template_dropdown}
    Try To Input Text    ${message_template_dropdown}    ${template}
    Try To Click Element    //*[@title='${template}']
    Sleep    1
    # TODO:    If language version is missing, template is disabled.

Verify Is Language Selected Is Correct
    [Arguments]    ${language}
    Wait Until Element Is Enabled    ${contact_patient_language_dropdown}
    ${value}    Get Text    ${contact_patient_language_dropdown}
    Should Contain    ${value}    ${language}

Select First Open Case From List
    Sleep    1
    Wait Until Page Contains Element    ${patient_cases}
    ${count}    Get Element Count    ${patient_cases}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${status}    Get Text    (${patient_cases}//*[@class='case-status'])[${INDEX}]
        IF    "${status}"!="Closed"
            Try To Click Element    (${patient_cases}//*[@class='case-status']//*)[${INDEX}]
            BREAK
        END
    END

Select Case From List
    [Documentation]    selects the first case from patient cases tab
    [Arguments]    ${case_type}    ${status}
    Sleep    1
    ${count}    Get Element Count    ${patient_cases}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${actual_status}    Get Text    (${patient_cases}//*[@class='case-status'])[${INDEX}]
        ${actual_case}    Get Text    (${patient_cases}//*[@class='case-title'])[${INDEX}]
        IF    '${actual_case}'=='${case_type}' and '${actual_status}'=='${status}'
            Try To Click Element    (${patient_cases}//*[@class='case-status'])[${INDEX}]
            BREAK
        END
    END

Select A Case From List By Name
    [Arguments]    ${case_name}
    ${count}    Get Element Count    //div[text()=' ${case_name} ']
    ${count_int}    Convert To Integer    ${count}
    IF    ${count_int} == 1
        Log    There is one case of type ${case_name} in the patient case view.
        Try To Click Element    //div[text()=' ${case_name} ']
    ELSE IF    ${count_int} >= 1
        Log    There are more than 1 case of type ${case_name} in the patient case view.
        Try To Click Element    //div[text()=' ${case_name} ']
    ELSE
        Log    There is none case of type ${case_name}
    END

Latest Message/Note Is Added In The Case
    [Arguments]    ${message}
    Sleep    1
    ${status}    Run Keyword And Return Status    Element Should Be Visible    ${add_note_btn}
    IF    ${status}==${False}    Try To Click Element    ${patient_cases_tab}
    Select First Open Case From List
    Sleep    1
    ${text}    Get Text    ${case_latest_message_or_note}
    Should Contain    ${text}    ${message}

Patient Cases Tab Is Selected And Open
    Wait Until Element Is Visible    ${patient_cases_tab}
    ${attr}    Get Element Attribute    ${patient_cases_tab}    class
    Should Contain    ${attr}    selected

Verify Contact Patient Button Visibility
    [Arguments]    ${visibility}
    Wait Until Element Is Visible    ${add_note_btn}
    IF    '${visibility}'=='yes'
        Wait Until Element Is Visible    ${top_contact_patient_button}
    ELSE
        Element Should Not Be Visible    ${top_contact_patient_button}
    END

Send Open Questions To Patient
    [Documentation]    Top contact patient button and not case-specific
    [Arguments]    ${topic}    ${contact_type}    ${message}    ${message_template}=none
    Wait Until Keyword Succeeds    20s    1s    Try To Contact Patient
    Try To Input Text    ${topic_field}    ${topic}
    Try To Input Text    ${contact_type_input}    ${contact_type}
    Sleep    1
    Press Keys    ${contact_type_input}    ENTER
    Wait Until Page Contains Element    ${contact_patient_message_field}
    IF    '${message_template}'!='none'
        Select Message Template To Contact Patient    ${message_template}
    END
    IF    '${message}'!='none'
        Input Text    ${contact_patient_message_field}    ${message}
    END
    Try To Click Element    ${contact_patient_send_button}
    Wait Until Page Contains Element    patient-messaging-content

Try To Contact Patient
    Wait Until Page Contains Element    ${top_contact_patient_button}
    Try To Click Element    ${top_contact_patient_button}
    Wait Until Page Contains Element    ${contact_type_input}
    Element Should Be Visible    ${topic_field}

Check Contact Type Visibility
    [Arguments]    ${contact_type}    ${visibility}
    Try To Contact Patient
    Try To Click Element    ${contact_type_dropdown}
    IF    '${visibility}'=='yes'
        Wait Until Element Is Visible    //*[@title='${contact_type}']
    ELSE
        Sleep    1s
        Element Should Not Be Visible    //*[@title='${contact_type}']
    END

Click New Case Button
    Wait Until Element Is Enabled    ${new_case_button}
    Try To Click Element    ${new_case_button}

Click Process Case Button
    Wait Until Element Is Visible    ${process_case_button}
    Scroll Element Into View    ${process_case_button}
    Try To Click Element    ${process_case_button}

Click Edit Case Button
    Wait Until Element Is Visible    ${edit_case_button}
    Scroll Element Into View    ${edit_case_button}
    Try To Click Element    ${edit_case_button}

Case Outcome Is Displayed In Case Details
    [Arguments]    ${outcome}
    ${title}    Format String    ${case_details_title}    Case outcome
    Wait Until Element Is Visible    ${title}
    ${text}    Get Text    ${title}/following-sibling::div[starts-with(@class, 'subtitle')]
    Should Be Equal    ${outcome}    ${text}

QOL Summary Is Displayed In Patient Case
    Wait Until Element Is Visible    ${qol_questionnaire_summary_title}
    Page Should Contain Element    ${qol_questionnaire_summary_title}/../..

Archived To EMR Internal Message Is Displayed
    Wait Until Page Does Not Contain    Case Created Successfully
    ${archive_emr_displayed}    Run Keyword And Return Status    Wait Until Element Is Visible    ${archived_to_emr_title}    timeout=3s
    IF    ${archive_emr_displayed}==${FALSE}
        Sleep    5s    #agreed with devs to wait and reload because archiving takes time sometimes
        Reload Page
        ${archive_emr_displayed}    Run Keyword And Return Status    Wait Until Element Is Visible    ${archived_to_emr_title}    timeout=3s
        IF    ${archive_emr_displayed}==${FALSE}
            Sleep    60s
            Reload Page
            Wait Until Element Is Visible    ${archived_to_emr_title}
            Scroll Element Into View    ${archived_to_emr_title}
            Wait Until Element Is Visible    ${archived_to_emr_title}
        END
    END
    Wait Until Element Is Visible    ${archived_to_emr_content}
    ${text}    Get Text    ${archived_to_emr_content}
    Should Be Equal    ${text}    Content archived to EMR

Information Is Displayed In Latest Change Log
    [Arguments]    ${info}
    Try To Click Element    ${change_log_button}
    Wait Until Element Is Visible    ${latest_log_row}
    Sleep    1
    Element Should Contain    ${latest_log_row}    ${info}

PDF Icon Is Enabled
    Wait Until Element Is Enabled    ${download_pdf_button}
    Try To Click Element    ${download_pdf_button}
    # TODO: Verify if PDF is downloaded

Patient's Phone Number Link Is Clickable
    [Arguments]    ${phone_number}=+3580000
    Wait Until Page Contains Element    ${patient_phone_number_link}
    Page Should Contain Link    ${phone_number}

Validate Automated Answer
    [Arguments]    ${validation}    ${notes}    ${rule}=default
    IF    '${rule}'!='default'
        Try To Click Element    //h5[text()="${rule}"]/../following-sibling::div[2]/div/button
    ELSE
        Try To Click Element    ${validate_automated_answer_button}
    END
    IF    '${validation}'=='correct'
        Try To Click Element    ${validation_correct}
    ELSE IF    '${validation}'=='incorrect'
        Try To Click Element    ${validation_incorrect}
    END
    Input Text    ${validation_note}    ${notes}
    Try To Click Element    ${validation_save_button}

Tnonc Distress Score Is Displayed Correctly
    [Arguments]    ${score}
    Wait Until Element Is Visible    ${distress_score}
    ${actual_score}    Get Text    ${distress_score}
    Should Be Equal    ${score}    ${actual_score}

Case Priority Is Displayed Correctly
    [Arguments]    ${priority}
    Wait Until Element Is Visible    ${case_summary_case_priority}
    ${actual_prio}    Get Text    ${case_summary_case_priority}
    Should Be Equal    ${priority}    ${actual_prio}

Open Patient Cases Tab
    Wait Until Element Is Visible    ${patient_cases_tab}
    Try To Click Element    ${patient_cases_tab}

Select First Case from Patient Cases Tab
    Try To Click Element    ${first_case}

Case Type Header Is Displayed Correctly
    [Documentation]    Checks patient case header after opening case under Patient cases tab
    [Arguments]    ${case_type_name}
    ${header_element}    Set Variable    //h5[text()="${case_type_name}"]
    Wait Until Element Is Visible    ${header_element}
    ${text}    Get Text    ${header_element}
    Should Be Equal    ${text}    ${case_type_name}

Get Case Date
    Wait Until Page Contains Element    ${expanded_case_date_elements}
    ${case_date}    Get Text    ${expanded_case_date_elements}
    Set Test Variable    ${case_date}

Case Is Expanded
    [Arguments]    ${case_type}
    ${expanded_case_element}    Format String    ${expanded_case_element}    ${case_type}
    Wait Until Element Is Visible    ${expanded_case_element}
    ${attr}    Get Element Attribute    ${expanded_case_element}    ${aria_expanded_attr}
    Should Be Equal    ${attr}    true

Verify Clinic Message With Attachments
    Wait Until Element Is Visible
    ...    //div/p[contains(@class, "message-paragraph") and contains(text(),"${auto_test_question_content}")]
    Try To Click Element    ${attachment_title}
    ${attachment_count_per_case}    Get Element Count    ${attachment_item_in_case}
    Element Text Should Be    ${attachment_title}    Attachments (${attachment_count_per_case})
    Verify Added Attachments

Verify Added Attachments
    Page Should Contain    ${doc_file_name}
    Page Should Contain    ${docx_file_name}
    Page Should Contain    ${xls_file_name}
    Page Should Contain    ${xlsx_file_name}
    Page Should Contain    ${heic_file_name}
    Page Should Contain    ${jpg_file_name}
    Page Should Contain    ${jpeg_file_name}
    Page Should Contain    ${png_file_name}
    Page Should Contain    ${pdf_file_name}

Compare Clipboard Content
    [Arguments]    ${txt_file_name}    ${should_contain}=Yes
    ${content}    Get File    ${EXECDIR}${/}data${/}text_files${/}${txt_file_name}.txt
    Wait Until Page Contains Element    ${clipboard_content}
    ${page_content}    Get Value    ${clipboard_content}
    @{content_lines}    Split To Lines    ${content}
    FOR    ${line}    IN    @{content_lines}
        IF    '${should_contain}'=='Yes'
            Should Contain    ${page_content}    ${line}
        ELSE
            Should Not Contain    ${page_content}    ${line}
        END
    END