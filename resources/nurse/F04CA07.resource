*** Settings ***
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Library     DateTime


*** Variables ***
${date_picker_start}        //input[contains(@class, "date")][1]
${manage_nurses_link}       //*[@id="manage-nurses-link"]
${logs_tab}                 //*[@data-testid="users-logs"]
${search_button}            //button[contains(text(), "Search")]
${log_entry}                //*[@data-testid="logs"]
${user}                     <EMAIL>
${action}                   Edited user
${target}                   <EMAIL>
${result_format}            %d.%m.%Y
${date_range_label}         //div[@class='date-range-component']/label
${logs_cases_start_date}    //*[@data-testid='startDate']
${logs_cases_end_date}      //*[@data-testid='endDate']


*** Keywords ***
Navigate To Logs
    Wait Until Page Contains Element    ${manage_nurses_link}
    Try To Click Element    ${manage_nurses_link}
    Wait Until Page Contains Element    ${logs_tab}
    Try To Click Element    ${logs_tab}
    Wait Until Page Contains    Timestamp
    Page Should Contain    User
    Page Should Contain    Action
    Page Should Contain    Target

Search Activity From Last Year
    Wait Until Page Contains Element    ${date_picker_start}
    ${current_date}    Get Current Date
    ${current_date_2}    Convert Date    ${current_date}    result_format=${result_format}
    ${past_day}    Subtract Time From Date    ${current_date}    365 days
    ${past_day}    Convert Date    ${past_day}    result_format=${result_format}
    Delete Log Start End Date    start
    Try To Input Text    ${logs_cases_start_date}    ${past_day}
    Delete Log Start End Date    end
    Try To Input Text    ${logs_cases_end_date}    ${current_date_2}

Delete Log Start End Date
    [Arguments]    ${date}
    Sleep    1
    Click Element    ${date_range_label}
    Sleep    1
    IF    '${date}'=='start'
        Press Keys    ${date_range_label}    TAB+BACKSPACE
    ELSE IF    '${date}'=='end'
        Press Keys    ${date_range_label}    TAB+TAB+BACKSPACE
    END
    Sleep    1

Activity Found
    Wait Until Page Contains Element    ${log_entry}
    Page Should Contain    ${user}
    Page Should Contain    ${action}
