*** Settings ***
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Library     String


*** Variables ***
${user_nbr}                         5
${firstname_original}               Regular
${lastname_original}                Tester
${email_original}                   <EMAIL>
${firstname_lock}                   Lock
${lastname_user}                    User
${email_lock_user}                  <EMAIL>
${firstname_original_2}             Regular Too
${lastname_original_2}              Tester
${email_original_2}                 <EMAIL>
${role_original}                    Clinic user
${team_original}                    Care Team 1
${locked_original}                  No
${phone_original}                   +35800000
${firstname_changed}                Newname
${lastname_changed}                 Married
${email_changed}                    <EMAIL>
${role_changed}                     Clinic manager
${team_changed}                     Care Team 2
${phone_changed}                    +358654321
${locked_changed}                   Yes
${firstname_box}                    //*[@data-testid="firstName"]
${lastname_box}                     //*[@data-testid="lastName"]
${email_box}                        //*[@data-testid="emailAddress"]
${phone_nbr_box}                    //*[@data-testid="phoneNumber"]
${user_roles_group}                 //*[@data-testid="role"]
${admin}                            //*[@data-testid="role_0"]//label
${user}                             //*[@data-testid="role_2"]//label
${manager}                          //*[@data-testid="role_1"]//label
${care_team1}                       //*[contains(@data-testid,"option")]//p[contains(.,"Care Team 1")]
${care_team2}                       //*[contains(@data-testid,"option")]//p[contains(.,"Care Team 2")]
${data_exporter}                    //*[@data-testid="dataExporter"]
${save_btn}                         //*[@data-testid="save-user"]
${cancel_btn}                       //*[@data-testid="cancel-user-edit"]
${lock_btn}                         //*[@data-testid="lock-user"]
${login_error}                      //*[@id="login-form-error"]/p
${care_team_name_1}                 Care Team 1
${care_team_name_2}                 Care Team 2
${incorrect_email_note}             Incorrect email or password.
${reset_password_button}            //*[@data-testid="reset-password"]
${add_new_user_button}              //*[@data-testid="clinic-users-new"]
${link_existing_user_button}        //*[@data-testid="clinic-users-link"]
${link_user_email_field}            //*[@data-testid="emailAddress"]
${link_button}                      //*[@data-testid="save-user"]
${user_table}                       //tr[contains(@id,'user')]/td
${manage_users}                     //*[@id="manage-nurses-link"]
${change_user_password_button}      //*[@data-testid="change-password"]
${user_password_input_field}        //*[@data-testid="password"]
${user_confirm_password_field}      //*[@data-testid="passwordConfirm"]
${save_new_user_password_button}    save-user
${clinic_user_email_in_list}        //tr[contains(@id,'user')]/td[contains(text(),'{}')]/..
${clinic_user_list_names}           (//*[@data-testid='users']//tr)/td


*** Keywords ***
Add New Clinic User
    Wait Until Element Is Visible
    ...    ${clinic_user_list_last_name_header}
    Try To Click Element    ${add_new_user_button}
    ${user_first_name}    First Name
    Set Test Variable    ${user_first_name}
    ${user_last_name}    Last Name
    Set Test Variable    ${user_last_name}
    ${todays_date}    Get Current Date    result_format=%d.%m.%Y
    Set Test Variable    ${user_email}    ${user_first_name}.${user_last_name}-${todays_date}@noona.com
    ${user_email}    Convert To Lowercase    ${user_email}
    Set Test Variable    ${user_email}
    Set Test Variable    ${phone_number}    +358000000000
    Try To Input Text    ${firstname_box}    ${user_first_name}
    Input Text    ${lastname_box}    ${user_last_name}
    Input Text    ${email_box}    ${user_email}
    Input Text    ${phone_nbr_box}    ${phone_number}
    Try To Click Element    ${user}
    Scroll Element Into View    ${save_btn}
    Toggle Care Team Selection    ${care_team1}

Link User To This Clinic
    Try To Click Element    ${link_existing_user_button}
    Try To Input Text    ${link_user_email_field}    ${user_email}
    Try To Click Element    ${link_button}
    Wait Until Page Contains    User linked to this clinic
    Reload Page
    Wait Until Page Contains Element    ${user_table}

User Tries To Login
    [Arguments]    ${user_email}=${email_original}
    Login To Noona    nurse    ${user_email}
    Wait Until Page Contains    ${incorrect_email_note}

User Logs In With New Info
    [Arguments]    ${user_email}=${email_original}
    Login As Nurse    email=${user_email}

Cancel User Information
    Try To Click Element    ${cancel_btn}

Change Role To
    [Arguments]    ${role}
    Page Should Contain Element    ${admin}
    Page Should Contain Element    ${user}
    Page Should Contain Element    ${manager}
    IF    '${role}'=='admin'    Try To Click Element    ${admin}
    IF    '${role}'=='manager'    Try To Click Element    ${manager}
    IF    '${role}'=='user'    Try To Click Element    ${user}
    IF    '${role}'=='${admin}'
        Page Should Not Contain    Care Team 1
        Page Should Not Contain Element    ${care_team1}
    END

Change User Information
    # Convert email address to uppercase, it should be automatically be converted to lower case when entered.
    ${email}    Convert To Uppercase    ${email_changed}
    Edit User Information    ${firstname_changed}    ${lastname_changed}    ${email}    ${phone_changed}
    Change Role To    admin
    Change Role To    manager
    Toggle Care Team Selection    ${care_team1}
    Toggle Care Team Selection    ${care_team2}
    Save User Information

Edit User Information
    [Arguments]    ${fname}=${EMPTY}    ${lname}=${EMPTY}    ${email}=${EMPTY}    ${phone}=${EMPTY}
    Wait Until Element Is Visible    ${phone_nbr_box}
    IF    '${fname}'!='${EMPTY}'
        Try To Input Text    ${firstname_box}    ${fname}
    END
    IF    '${lname}'!='${EMPTY}'
        Try To Input Text    ${lastname_box}    ${lname}
    END
    IF    '${email}'!='${EMPTY}'
        Try To Input Text    ${email_box}    ${email}
    END
    IF    '${phone}'!='${EMPTY}'
        Try To Input Text    ${phone_nbr_box}    ${phone}
    END

Login To Noona As Nurse
    [Arguments]    ${username}    ${password}    ${clinic}=${None}
    Login.Login To Nurse    ${username}    ${password}    ${clinic}

Navigate To Clinic Users
    Wait Until Element Is Visible    ${manage_users}
    ${status}    Run Keyword And Return Status    Page Should Contain Element    ${add_new_user_button}
    IF    ${status} != True
        Try To Click Element    ${manage_users}
        Wait Until Page Contains    ${add_new_user_button}
    END

Reset Clinic User Information
    Login As Nurse    user_type=${USER_TYPE}[noona_admin]
    Navigate To Clinic Users
    Select User    ${email_changed}
    Edit User Information    ${firstname_original}    ${lastname_original}    ${email_original}    ${phone_original}
    Change Role To    user
    Toggle Care Team Selection    ${care_team2}
    Toggle Care Team Selection    ${care_team1}
    Save User Information
    Verify User From List
    ...    ${email_original}
    ...    ${firstname_original}
    ...    ${lastname_original}
    ...    ${email_original}
    ...    ${role_original}
    ...    ${team_original}
    ...    ${locked_original}

Save User Information
    Try To Click Element    ${save_btn}

Select User
    [Arguments]    ${user_email}
    Wait Until Element Is Visible    //td[contains(text(),'${user_email}')]    10s
    Try To Click Element    //td[contains(text(),'${user_email}')]

Set Status Back To Unlocked
    Login As Nurse    user_type=${USER_TYPE}[noona_admin]
    Navigate To Clinic Users
    Select User    ${email_lock_user}
    Toggle Lock    Unlock
    Verify User From List    ${email_lock_user}    locked=${locked_original}

Set Status Back To Locked
    Login As Nurse    user_type=${USER_TYPE}[noona_admin]
    Navigate To Clinic Users
    Select User    ${email_original_2}
    Toggle Lock    Lock
    Verify User From List    ${email_original_2}    locked=${locked_changed}

Toggle Care Team Selection
    [Documentation]    Toggles care team selection. Selecting and de-selecting a care team happens the same way.
    [Arguments]    ${team}
    Try To Click Element    ${team}

Toggle Data Export Requester
    [Arguments]    ${action}
    Wait Until Element Is Visible    ${data_exporter}
    ${attr}    Get Element Attribute    ${data_exporter}    class
    IF    'checkbox-checked' in '${attr}' and '${action}'=='disable'
        Try To Click Element    ${data_exporter}
    ELSE IF    'checkbox-checked' not in '${attr}' and '${action}'=='enable'
        Try To Click Element    ${data_exporter}
    ELSE IF    'checkbox-checked' in '${attr}' and '${action}'=='enable'
        Try To Click Element    ${data_exporter}
        Sleep    1s
        Try To Click Element    ${data_exporter}
    END

Toggle Lock
    [Arguments]    ${lock_or_unlock}
    Wait Until Page Contains Element    ${lock_btn}
    ${lock_button_text}    Get Text    ${lock_btn}
    IF    '${lock_or_unlock}'=='Lock'
        IF    '${lock_button_text}'==' UNLOCK '
            Try To Click Element    ${lock_btn}
            Wait Until Page Contains    User unlocked
            Try To Click Banner Message
            Select User    ${email_lock_user}
        END
        Sleep    1s
        Try To Click Element    ${lock_btn}
        Wait Until Page Contains    User locked
        Try To Click Banner Message
    ELSE IF    '${lock_or_unlock}'=='Unlock'
        IF    '${lock_button_text}'==' LOCK '
            Try To Click Element    ${lock_btn}
            Wait Until Page Contains    User locked
            Try To Click Banner Message
            Select User    ${email_lock_user}
        END
        Sleep    1s
        Try To Click Element    ${lock_btn}
        Wait Until Page Contains    User unlocked
        Try To Click Banner Message
    END

Verify Role
    [Arguments]    ${item}    ${role}
    Wait Until Element Is Visible    //*[@id="user-${item}"]/td[4]/span/..
    Element Should Contain    //*[@id="user-${item}"]/td[4]/span/..    ${role}

Verify User From List
    [Arguments]
    ...    ${user_email}
    ...    ${fname}=${EMPTY}
    ...    ${lname}=${EMPTY}
    ...    ${email}=${EMPTY}
    ...    ${role}=${EMPTY}
    ...    ${team}=${EMPTY}
    ...    ${locked}=${EMPTY}
    Sleep    1s
    ${email}    Convert To Lowercase    ${email}
    Wait Until Element Is Visible    //tr[contains(@id,'user')]/td[contains(text(),'${user_email}')]    10
    IF    '${fname}'!='${EMPTY}'
        Element Should Contain    //tr[contains(@id,'user')]/td[contains(text(),'${user_email}')]/..    ${fname}
    END
    IF    '${lname}'!='${EMPTY}'
        Element Should Contain    //tr[contains(@id,'user')]/td[contains(text(),'${user_email}')]/..    ${lname}
    END
    IF    '${email}'!='${EMPTY}'
        Element Should Contain    //tr[contains(@id,'user')]/td[contains(text(),'${user_email}')]/..    ${email}
    END
    IF    '${role}'!='${EMPTY}'
        Element Should Contain    //tr[contains(@id,'user')]/td[contains(text(),'${user_email}')]/..    ${role}
    END
    IF    '${team}'!='${EMPTY}'
        Element Should Contain    //tr[contains(@id,'user')]/td[contains(text(),'${user_email}')]/..    ${team}
    END
    IF    '${locked}'!='${EMPTY}'
        Element Should Contain    //tr[contains(@id,'user')]/td[contains(text(),'${user_email}')]/..    ${locked}
    END

Verify User Information
    [Arguments]
    ...    ${item}
    ...    ${fname}=${EMPTY}
    ...    ${lname}=${EMPTY}
    ...    ${email}=${EMPTY}
    ...    ${phone}=${EMPTY}
    ...    ${role}=${EMPTY}
    ...    ${team}=${EMPTY}
    ...    ${locked}=${EMPTY}
    Wait Until Page Contains Element    ${firstname_box}
    Wait Until Element Is Enabled    ${firstname_box}
    Sleep    1s
    IF    '${fname}'!='${EMPTY}'
        Textfield Value Should Be    ${firstname_box}    ${fname}
    END
    IF    '${lname}'!='${EMPTY}'
        Textfield Value Should Be    ${lastname_box}    ${lname}
    END
    IF    '${email}'!='${EMPTY}'
        Textfield Value Should Be    ${email_box}    ${email}
    END
    IF    '${phone}'!='${EMPTY}'
        Textfield Value Should Be    ${phone_nbr_box}    ${phone}
    END

Send Random Password
    Try To Click Element    ${reset_password_button}
    Wait Until Page Contains    New password sent to user

Change User's Password
    Try To Click Element    ${change_user_password_button}
    Try To Input Text    ${user_password_input_field}    ${DEFAULT_PASSWORD}
    Try To Input Text    ${user_confirm_password_field}    ${DEFAULT_PASSWORD}
    Try To Click Element    ${save_new_user_password_button}
    Wait Until Page Contains    Password changed

Select Clinic User
    [Arguments]    ${email}
    ${clinic_user_element}    Format String    ${clinic_user_email_in_list}    ${email}
    Try To Click Element    ${clinic_user_element}

Select Clinic User By Start Name
    [Arguments]    ${name_start}
    Wait Until Element Is Visible    (${clinic_user_list_names})[2]
    ${count}  Get Element Count    ${clinic_user_list_names}
    FOR    ${INDEX}    IN RANGE    1    ${count}
        Wait Until Element Is Visible    (${clinic_user_list_names})[${INDEX}]
        ${text}    Get Text    (${clinic_user_list_names})[${INDEX}]
        ${status}    Run Keyword And Return Status    Should Start With    ${text}    ${name_start}
        IF    ${status}
            Try To Click Element    (${clinic_user_list_names})[${INDEX}]
            Exit For Loop
        END
    END

Update Clinic User Email
    [Arguments]    ${user_email}
    Wait Until Element Is Visible    ${email_box}
    Try To Input Text    ${email_box}    ${user_email}

Data Is Visible In User List
    [Arguments]    ${data}
    Wait Until Element Is Visible    ${clinic_user_list_names}\[text()="${data}"]

User Roles Group Should Be Disabled
    Wait Until Element Is Visible    ${user_roles_group}
    ${attr}    Get Element Attribute    ${user_roles_group}    disabled
    Should Be Equal    ${attr}    true