*** Settings ***
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource


*** Variables ***
${patient_cards}                        //div[@class='patient-card-content']
${refresh_work_queue_button}            //div[@class="refresh"]
${care_team_filter}                     //*[@id='workQueue__careTeam--filter']/div
${care_team_filter_legacy}              //*[@id='workQueue__careTeam-legacy--filter']/div
${case_type_filter}                     //*[@id='workQueue__caseType--filter']//input
${care_team_filter_list}                //*[@id='workQueue__careTeam--filter']
${case_type_filter_list}                //*[@id='workQueue__caseType--filter']
${care_team_filter_option}              ${care_team_filter_list}//*[contains(text(),"{}")]
${case_type_filter_option}              ${case_type_filter_list}//*[contains(text(),"{}")]
${select_all_filter_option_button}      //button[@data-testid="btn-selectAll"]
${open_cases_tab}                       new-messages
${patient_reports_tab}                  side-effect-reports
${automated_answers_tab}                automated-messages-link
${unhandled_tab}                        //*[@data-testid='btn-unreviewed']
${non_completed_tab}                    //*[@data-testid='btn-unanswered']
${today_tab}                            //*[@data-testid='btn-today']
${card_view_button}                     //div[@class='list-button'][1]
${list_view_button}                     //div[@class='list-button'][2]
${patient_card_status}                  (//div[starts-with(@class, 'handled-container')]/div/div)[last()]
${patient_card_name}                    //div[@class='name-container']
${care_team_filter_container}           //*[@role='listbox']    # //div[@class="btn-group open"]/descendant::ul
${patient_card_container_view}          //div[starts-with(@class, 'patient-cards-container')]
${case_statuses_text}                   //td[contains(@class, 'column-priority')]
${patient_list_view}                    //noona-table[@class='noona-work-queue']
${open_cases_tab_column}                //span[contains(text(),"{}")]
${open_cases_tab}                       //div[@id='new-messages']
${patient_group_filter}                 //*[@id="workQueue__patientGroup--filter"]//input
${patient_group_filter_arrow}           (//*[@id="workQueue__patientGroup--filter"]//span)[1]
${patient_group_filter_option}          //*[@id="workQueue__patientGroup--filter"]//*[contains(text(),"{}")]
${filter_option_remove_all}             //*[@data-testid='btn-removeAll']
# //label[text()="Questionnaire type"]/following-sibling::nh-grouped-dropdown-filter/div
${questionnaire_type_filter}
...                                     //*[@id='workQueue__questionnaireType--filter']
# ${questionnaire_type_filter}/descendant::li
${questionnaire_type_option}
...                                     ${questionnaire_type_filter}/descendant::span
${nurse_initials_on_patient_card}       //div[text()=" {} "]/following-sibling::span[1]
# //div[contains(@class, "search-patient-table")]
${patient_table}
...                                     //*[@class='noona-work-queue']/div
${closed_cases_start_date}              //*[@data-testid='startDate']
${closed_cases_end_date}                //*[@data-testid='endDate']
${closed_cases_start_date_value_js}     return document.getElementsByClassName('mat-date-range-input-container')[0].getElementsByTagName('div')[0].getElementsByTagName('span')[0].textContent;
${closed_cases_end_date_value_js}       return document.getElementsByClassName('mat-date-range-input-container')[0].getElementsByTagName('div')[1].getElementsByTagName('span')[0].textContent;
${closed_cases_date_range_label}        //div[@class='date-range-component']/label
${work_queue_grid}                      //workqueue-cards-grid
${workqueue_items}                      //div[contains(@id, "workqueue-item")]
${patient_cards_container}              //div[contains(@class, "patient-cards-container")]
${handled_messages}                     //*[@id="workqueue-0"]
${export_pdf_button}                    //*[@data-testid= "pdf-button"]
${closed_cases_tab}                     sent-messages-link
${search_care_team_dropdown_button}     //*[@data-testid="workQueue__careTeam--filter"]
${workqueue_refresh_toaster}            Work queue updated
${no_patient_cards}                     //*[@class='empty-state']
${refresh_workqueue_button}             //*[@class='refresh']
${mrn_column}                           //*[@id="nurse-work-queue"]//noona-table//span[text()="MRN"]
${first_case_initials}                  //*[@id="workqueue-0"]//span/div
${my_patients_checkbox}                 //*[@data-testid='work-queue-mypatients']
${care_team_dropdown_arrow}             //*[@id='workQueue__careTeam--filter']//span[@class='ng-arrow-wrapper']
${selected_care_team}                   //*[@id='workQueue__careTeam--filter']//span
${care_team_input_field}                //label[contains(text(),"Care teams")]/..//input
${list_view_case_type}                  (//p[contains(text(),"{}")]/../../following-sibling::td[contains(@class, 'column-caseTypeEnum')]//p)[last()]
${list_view_questionnaire_type}         (//p[contains(text(),"{}")]/../../following-sibling::td[contains(@class, 'column-questionnaireTypeEnum')]//p)[last()]
&{case_type_filters}
...    results_or_labs=Results or labs
...    question=Question
...    instructions=Instructions
...    invitation_clinical_tests=Invitation for clinical tests
...    invitation_physical_exam=Invitation for a physical examination
...    patient_education=Patient Education
...    symptom_from_questionnaire=Symptom from questionnaire
...    symptom_report=Symptom Report
&{questionnaire_type_filters}           symptom_questionnaire=Symptom questionnaire    symptom_report=Symptom Report
${tomorrow_tab_data_testid}             //*[@data-testid='btn-tomorrow']
${unhandled_filter}                     //*[@data-testid='btn-unreviewed']/button/span
${primary_provider_dropdown}            (//*[@data-testid='workQueue__primaryProvider--filter']//span[contains(@class, 'arrow')])[1]
${primary_provider_option}              //*[@data-testid='workQueue__primaryProvider--filter']//div[contains(text(),"{}")]
${primary_provider_column}              //*[@id="nurse-work-queue"]//noona-table//span[text()="Primary provider"]
${case_types_dropdown}                  //*[@id='workQueue__caseType--filter']//span[@class='ng-arrow-wrapper']
${wq_grouped_by_date}                   //h2[contains(@class, 'selected-date')]
${other_date_filter_button}             //*[@data-testid='btn-otherDate']/button
${other_date_filter_next_button}        //button[contains(@class, 'calendar-next-button')]
${id_ssn_column_header}                 //span[text()='ID / SSN']
${ins_column_header}                    //span[text()='INS']
${nurse_work_queue}                     //*[@id="nurse-work-queue"]
${closed_cases_care_teams}              //*[@id='workQueue__careTeam-legacy--filter']//*[@aria-label='Options list']//*[contains(@class, 'filter__optionLabel--item')]
${care_team_column_values}
...                                         //*[@class="noona-work-queue"]//table/tbody[1]//td[8]
${care_person_column_values}
...                                         //*[@class="noona-work-queue"]//table/tbody[1]//td[9]

*** Keywords ***
Go To Patient Reports
    Wait Until Noona Loader Is Not Visible
    Wait Until Element Is Visible    ${patient_reports_tab}
    Try To Click Element    ${patient_reports_tab}

Go To Open Cases Tab
    Wait Until Element Is Visible    ${open_cases_tab}
    Try To Click Element    ${open_cases_tab}

Go To Closed Cases Tab
    Wait Until Element Is Enabled    ${closed_cases_tab}
    Try To Click Element    ${closed_cases_tab}

Select Non Completed Tab
    Wait Until Element Is Visible    ${non_completed_tab}
    Try To Click Element    ${non_completed_tab}

Select Today Tab
    Wait Until Element Is Visible    ${today_tab}
    Try To Click Element    ${today_tab}

Click Card View Button
    Wait Until Element Is Visible    ${card_view_button}
    Try To Click Element    ${card_view_button}

Click List View Button
    Wait Until Element Is Visible    ${list_view_button}
    Try To Click Element    ${list_view_button}

Return To Work Queue
    [Documentation]    Use when clinic user is on other page and needs to return to workqueue
    Try To Click Element    ${back_to_button}

Select Patient Card
    [Documentation]    Selects the first instance of patient card
    [Arguments]    ${patient_name}
    Wait Until Element Is Visible    (${patient_cards})[last()]
    Wait Until Keyword Succeeds    10s    1s    Scroll Element Into View    (${patient_cards})[last()]
    Sleep    1
    @{cards}    Get Webelements    ${patient_cards}
    ${count}    Get Element Count    ${patient_cards}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        Wait Until Element Is Visible    (${patient_cards}/div/div)[${INDEX}]
        ${text}    Get Text    (${patient_cards}/div/div)[${INDEX}]
        IF    '${text}'=='${patient_name}'
            Sleep    1
            Try To Click Element    (${patient_cards}/div/div)[${INDEX}]
            BREAK
        END
    END

Patient Card Is Not Displayed In Current Work Queue
    [Arguments]    ${patient_name}
    ${status}    Run Keyword And Return Status    Wait Until Page Contains    No patients in the queue.    timeout=5s
    IF    ${status}==${False}
        Wait Until Element Is Visible    (${patient_cards})[last()]
        Wait Until Keyword Succeeds    20s    1s    Scroll Element Into View    (${patient_cards})[last()]
    END
    Wait Until Page Does Not Contain    ${patient_name}

Patient Card Is Displayed In Current Work Queue
    [Arguments]    ${patient_name}
    Wait Until Element Is Visible    (${patient_cards})[last()]
    Wait Until Keyword Succeeds    20s    1s    Scroll Element Into View    (${patient_cards})[last()]
    Wait Until Page Contains    ${patient_name}

Refresh Work Queue
    Wait Until Element Is Enabled    ${refresh_work_queue_button}
    Try To Click Element    ${refresh_work_queue_button}
    Wait Until Page Contains    ${workqueue_refresh_toaster}
    Wait Until Element Is Visible    (${patient_cards})[last()]

Remove All Care Team Filter
    Sleep    1s
    Wait Until Page Does Not Contain Element    ${loader}    20s
    Try To Click Element    ${care_team_filter}
    Sleep    1s
    Try To Click Element    (${care_team_filter_list}//*[contains(text(),"Remove all selections")])[last()]

Select Care Team Filter In Work Queue
    [Arguments]    ${care_team}
    Sleep    1s
    Wait Until Page Does Not Contain Element    ${loader}    20s
    Try To Click Element    ${care_team_filter}
    Sleep    1s
    Try To Input Text    ${care_team_input_field}    ${care_team}
    Try To Click Element    (${care_team_filter_list}//*[contains(text(),"${care_team}")])[last()]
    Wait Until Element Is Not Visible    ${care_team_filter_option}
    Try To Click Element    ${care_team_dropdown_arrow}

Select Care Team Filter In Closed Cases
    [Documentation]    Closed cases tab uses a different care team filter for now, might be updated in the future
    [Arguments]    ${care_team}
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    ${care_team_filter_legacy}
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    (${care_team_filter_legacy}//*[contains(text(),"${care_team}")])[last()]
    Wait Until Element Is Not Visible    ${care_team_filter_option}

Select My Patients In Work Queue
    Wait Until Element Is Visible    ${my_patients_checkbox}
    Sleep    1s
    ${attr}    Get Element Attribute    ${my_patients_checkbox}    class
    IF    'checkbox-checked' not in '${attr}'
        Try To Click Element    ${my_patients_checkbox}
    END

Unselect My Patients In Filter In Work Queue
    Wait Until Element Is Visible    ${my_patients_checkbox}
    Sleep    1s
    ${attr}    Get Element Attribute    ${my_patients_checkbox}    class
    IF    'mat-checkbox-checked' in '${attr}'
        Try To Click Element    ${my_patients_checkbox}
    END

Select Primary Provider
    [Arguments]    ${provider}    # TODO: update to select multiple
    Wait Until Element Is Visible    ${primary_provider_dropdown}
    Try To Click Element    ${primary_provider_dropdown}
    Try To Click Element    ${filter_option_remove_all}
    ${primary_provider_option}    Format String    ${primary_provider_option}    ${provider}
    Try To Click Element    ${primary_provider_option}
    Try To Click Element    ${primary_provider_dropdown}

Remove Selected Primary Providers
    Wait Until Element Is Visible    ${primary_provider_dropdown}
    Try To Click Element    ${primary_provider_dropdown}
    Try To Click Element    ${filter_option_remove_all}
    Try To Click Element    ${primary_provider_dropdown}

Care Team Is Not Included In The Filter
    [Arguments]    ${care_team}
    Sleep    1s
    Try To Click Element    ${care_team_filter}
    Element Should Not Contain    ${care_team_filter_container}    ${care_team}

Select Case Type Filter In Work Queue
    [Arguments]    ${case_type}
    Try To Click Element    ${case_type_filter}    wait_in_seconds=7s
    Try To Click Element    ${filter_option_remove_all}
    Try To Click Element    ${open_cases_tab}
    Wait Until Element Is Enabled    ${case_type_filter}
    Try To Click Element    ${case_type_filter}
    Sleep    1
    Try To Click Element    ${case_type_filter_list}//*[text()=" ${case_type} "]
    Try To Click Element    ${open_cases_tab}

Remove Case Type Filters In Work Queue
    Wait Until Element Is Visible    ${case_types_dropdown}
    Try To Click Element    ${case_types_dropdown}
    Try To Click Element    ${filter_option_remove_all}
    Try To Click Element    ${case_types_dropdown}

Select Questionnaire Type In Work Queue
    [Arguments]    ${type}
    Try To Click Element    ${questionnaire_type_filter}
    Try To Click Element    ${filter_option_remove_all}
    Try To Click Element    //*[contains(text(), '${type}')]
    Try To Click Element    ${patient_reports_tab}
    Wait Until Element Is Visible    ${questionnaire_type_filter}

Patient Card's Color Is Correct
    [Documentation]    low = light purple, medium = purple, high = red, critical = dark red; checks the 1st card in view
    [Arguments]    ${priority}    ${questionnaire}=no    ${index}=last
    IF    '${index}'=='last'
        ${rgb}    Execute Javascript    return document.defaultView.getComputedStyle(
        ...    document.getElementsByClassName('figure-container')[document.getElementsByClassName('figure-container').length - 1],null)['background-color']
    ELSE
        ${rgb}    Execute Javascript    return document.defaultView.getComputedStyle(
        ...    document.getElementsByClassName('figure-container')[${index}],null)['background-color']
    END
    ${actual_color}    Set Variable If    '${priority}'=='medium' and '${questionnaire}'=='no'    rgb(126, 86, 166)
    ...    '${priority}'=='critical'    rgb(208, 2, 27)
    ...    '${priority}'=='high'    rgb(255, 133, 133)
    ...    '${priority}'=='low'    rgb(184, 158, 209)
    ...    '${priority}'=='medium' and '${questionnaire}'=='yes'    rgb(131, 42, 184)
    ...    '${priority}'=='no_symptoms'    rgb(54, 179, 234)
    ...    '${priority}'=='unanswered'    rgb(135, 139, 138)
    ${status}    Run Keyword And Return Status    Should Be Equal    ${rgb}    ${actual_color}
    IF    ${status}==${FALSE}
        Capture Screenshot
        Fail    Card color should be ${priority} but it is not.
    END

Patient's Card Status Is Correct
    [Arguments]    ${status}
    Wait For Element To Be Present    ${patient_card_status}
    ${text}    Get Text    ${patient_card_status}
    Should Be Equal    ${status}    ${text}

Nurse Initials Is Displayed In Patient Card
    [Arguments]    ${patient_name}    ${initials}
    Wait Until Element Is Visible    (${patient_cards})[last()]
    Scroll Element Into View    (${patient_cards})[last()]
    Sleep    1
    ${count}    Get Element Count    ${patient_card_name}/div[contains(text(),"${patient_name}")]
    ${text}    Get Text    //div[contains(text(),"${patient_name}")]/../following-sibling::span[1]
    Should Be Equal    ${initials}    ${text}

Patient Cards Are Displayed In Work Queue
    Wait Until Page Contains Element    ${patient_card_container_view}

Patients Are Displayed In List View
    Wait Until Page Contains Element    ${patient_list_view}

Case Statuses Order In List View Is Correct
    [Arguments]    @{expected_status_list}
    Sleep    1
    Wait Until Element Is Visible    ${case_statuses_text}\[1]
    @{status_list}    Create List
    ${count}    Get Element Count    ${case_statuses_text}
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${text}    Get Text    (${case_statuses_text})\[${INDEX}]
        Append To List    ${status_list}    ${text}
    END
    Lists Should Be Equal    ${expected_status_list}    ${status_list}

Sort Open Case Tab Column
    [Arguments]    ${column}
    ${open_cases_tab_column}    Format String    ${open_cases_tab_column}    ${column}
    Click Element    ${open_cases_tab_column}

Open Cases Tab Column Is Sorted Correctly
    [Arguments]    ${column}    @{expected_list}
    ${open_cases_tab_column}    Format String    ${open_cases_tab_column}    ${column}
    Try To Click Element    ${open_cases_tab_column}
    Sleep    1
    Try To Click Element    ${open_cases_tab_column}
    Sleep    1
    Get Open Cases Tab Column List    ${column}
    Lists Should Be Equal    ${actual_column_values_list}    ${expected_list}
    Try To Click Element    ${open_cases_tab_column}

Get Open Cases Tab Column List
    [Arguments]    ${column}
    Sleep    1
    @{actual_column_values_list}    Create List
    ${col_index}    Set Variable If    '${column}'=='Priority'    1
    ...    '${column}'=='ID / SSN'    3
    ...    '${column}'=='Case type'    5
    ...    '${column}'=='Case opened'    6
    ...    '${column}'=='Status'    7
    ...    '${column}'=='Assignee'    10
    Set Test Variable    ${col_index}
    Wait Until Element Is Enabled    //tbody/tr[1]/td[${col_index}]
    ${count}    Get Element Count    //tbody/tr/td[${col_index}]
    FOR    ${INDEX}    IN RANGE    1    ${count}+1
        ${text}    Get Text    (//tbody/tr/td[${col_index}])[${INDEX}]
        Append To List    ${actual_column_values_list}    ${text}
    END
    Set Test Variable    @{actual_column_values_list}

Open Cases Count In Tab Is Correct
    [Documentation]    count should be number of filtered cases/total number of cases
    [Arguments]    ${filtered_cases}    ${total_cases}
    ${text}    Get Text    ${open_cases_tab}
    Should Be Equal    ${text}    OPEN CASES (${filtered_cases}${SPACE}/${SPACE}${total_cases})

Click Assign To Me From List View
    [Arguments]    ${ssn}
    Get Open Cases Tab Column List    ID / SSN
    ${ssn_index}    Get Index From List    ${actual_column_values_list}    ${ssn}
    ${ssn_index}    Evaluate    ${ssn_index}+1
    Try To Click Element    //tr[${ssn_index}]/td[10]/div

Work Queue Nurse Initials Background Color Is Correct
    [Arguments]    ${ssn}    ${expected_rgba}
    ${nurse_initials}    Format String    ${nurse_initials_on_patient_card}    ${ssn}
    ${element}    Get WebElement    ${nurse_initials}
    ${rbga_color}    Call Method    ${element}    value_of_css_property    background-color
    Should Be Equal    ${rbga_color}    ${rbga_color}

Select Patient Group Filter In Work Queue
    [Arguments]    ${group_name}
    Remove Patient Group Filters In Patient Reports Work Queue
    Try To Click Element    ${open_cases_tab}
    Try To Click Element    ${patient_group_filter}
    Sleep    1
    ${patient_group_filter_option}    Format String    ${patient_group_filter_option}    ${group_name}
    Try To Click Element    ${patient_group_filter_option}
    Try To Click Element    ${open_cases_tab}

Remove Patient Group Filters In Work Queue
    Try To Click Element    ${patient_group_filter}
    Try To Click Element    ${filter_option_remove_all}
    Try To Click Element    ${open_cases_tab}
    Wait Until Element Is Enabled    ${patient_group_filter}

Remove Patient Group Filters In Patient Reports Work Queue
    Try To Click Element    ${patient_group_filter}
    Try To Click Element    ${filter_option_remove_all}
    Try To Click Element    ${patient_reports_tab}
    Wait Until Element Is Enabled    ${patient_group_filter}

Remove Patient Group Filters In Current Work Queue
    Try To Click Element    ${patient_group_filter}
    Try To Click Element    ${filter_option_remove_all}
    Try To Click Element    ${patient_group_filter_arrow}

Remove Questionnaire Filter In Work Queue
    Try To Click Element    ${questionnaire_type_filter}
    Try To Click Element    ${filter_option_remove_all}
    Try To Click Element    ${patient_reports_tab}
    Wait Until Element Is Enabled    ${questionnaire_type_filter}

Change And Verify Selected Care Team Member
    Wait Until Page Contains Element    ${care_team_filter}
    Try To Click Element    ${care_team_filter}
    Remove All Care Team Filter
    Select Care Team Filter In Work Queue    Clinic Manager
    Sleep    1
    Try To Click Element    ${patient_reports_tab}
    Wait Until Page Contains    Unhandled
    Try To Click Element    ${open_cases_tab}
    Wait Until Element Is Visible    ${selected_care_team}
    Element Text Should Be    ${selected_care_team}    Clinic Manager

Change And Verify Work Queue View
    Change To List View And Verify
    Change To Patient Card View And Verify

Change To List View And Verify
    Wait Until Page Contains Element    ${list_view_button}
    Try To Click Element    ${list_view_button}
    Wait Until Page Contains Element    ${patient_table}
    Page Should Not Contain Element    ${patient_cards}
    Try To Click Element    ${patient_reports_tab}
    Try To Click Element    ${open_cases_tab}
    Wait Until Page Contains Element    ${patient_table}
    Page Should Not Contain Element    ${patient_cards}

Change To Patient Card View And Verify
    Wait Until Page Contains Element    ${card_view_button}
    Try To Click Element    ${card_view_button}
    Wait Until Page Contains Element    ${patient_cards}
    Page Should Not Contain Element    ${patient_table}
    Try To Click Element    ${patient_reports_tab}
    Try To Click Element    ${open_cases_tab}
    Wait Until Page Contains Element    ${patient_cards}
    Page Should Not Contain Element    ${patient_table}

Show Closed Cases From The Last
    [Arguments]    ${days}
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    ${current_date_2}    Get Current Date
    ${past_day}    Subtract Time From Date    ${current_date_2}    ${days}
    ${past_day}    Convert Date    ${past_day}    result_format=%d.%m.%Y
    ${past_year}    Get Substring    ${past_day}    -4
    Select Care Team Filter In Closed Cases    My teams' patients
    Delete Closed Cases Start End Date    start
    Input Text    ${closed_cases_start_date}    ${past_day}
    Delete Closed Cases Start End Date    end
    Input Text    ${closed_cases_end_date}    ${current_date}
    Wait Until Noona Loader Is Not Visible
    Wait Until Page Contains Element    ${workqueue_items}

Show Closed Cases Today
    ${current_date}    Get Current Date    result_format=%d.%m.%Y
    Delete Closed Cases Start End Date    start
    Input Text    ${closed_cases_start_date}    ${current_date}
    Delete Closed Cases Start End Date    end
    Input Text    ${closed_cases_end_date}    ${current_date}
    Wait Until Noona Loader Is Not Visible
    Wait Until Page Contains Element    ${workqueue_items}

Delete Closed Cases Start End Date
    [Arguments]    ${date}
    Wait Until Noona Loader Is Not Visible
    Try To Click Element    ${closed_cases_date_range_label}    wait_in_seconds=30x
    Wait Until Noona Loader Is Not Visible
    IF    '${date}'=='start'
        Press Keys    ${closed_cases_date_range_label}    TAB+BACKSPACE
    ELSE IF    '${date}'=='end'
        Press Keys    ${closed_cases_date_range_label}    TAB+TAB+BACKSPACE
    END
    Wait Until Noona Loader Is Not Visible

Input Invalid Dates And Search
    Delete Closed Cases Start End Date    start
    Input Text    ${closed_cases_start_date}    ÄELWYYWELYÄew
    Delete Closed Cases Start End Date    end
    Input Text    ${closed_cases_end_date}    *35+weyoej
    Wait Until Page Contains Element    ${patient_cards_container}

Change End Date And Show Cases
    ${current_date}    Get Current Date
    ${current_date-1_day}    Subtract Time From Date    ${current_date}    1 days
    ${current_date-1_day}    Convert Date    ${current_date-1_day}    result_format=%d.%m.%Y
    ${current_date_2}    Get Current Date
    ${past_day}    Subtract Time From Date    ${current_date_2}    365 days
    ${past_day}    Convert Date    ${past_day}    result_format=%d.%m.%Y
    Delete Closed Cases Start End Date    start
    Input Text    ${closed_cases_start_date}    ${past_day}
    Delete Closed Cases Start End Date    end
    Input Text    ${closed_cases_end_date}    ${current_date-1_day}
    Wait Until Page Contains Element    ${workqueue_items}    20s

View First Item On List
    Wait Until Noona Loader Is Not Visible
    Wait Until Element Is Visible    (${handled_messages}/div)[1]    #to make sure wq is fully loaded before next steps
    Wait Until Element Is Enabled    (${handled_messages}/div)[1]
    Capture Screenshot    #to see which case is being clicked, this step can be removed soon when investigation is done
    Try To Click Element    (${handled_messages}/div)[1]    wait_in_seconds=30x
    Wait Until Element Is Visible    ${export_pdf_button}

Change Care Team To
    [Arguments]    ${care_team}
    Wait Until Page Contains Element    ${search_care_team_dropdown_button}
    Try To Click Element    ${search_care_team_dropdown_button}
    Execute Javascript
    ...    document.querySelector('[data-testid=workQueue__careTeam--filter]').value=document.querySelector('[data-testid=option-7]').click()
    Wait Until Page Contains    ${care_team}

Verify That Workqueue Does Not Have Any Card
    Wait Until Page Contains Element    ${no_patient_cards}

Verify If Patient With Symptom Is In Nurse's Work Queue
    [Arguments]    ${patient_name}    ${care_team}    ${displayed}=yes
    Login As Nurse
    Unselect My Patients In Filter In Work Queue
    Remove All Care Team Filter
    Remove Selected Primary Providers
    Select Care Team Filter In Work Queue    ${care_team}
    Select Case Type Filter In Work Queue    Symptom from questionnaire
    Remove Patient Group Filters In Work Queue
    IF    '${displayed}'=='yes'
        Patient Card Is Displayed In Current Work Queue    ${patient_name}
    ELSE IF    '${displayed}'=='no'
        Patient Card Is Not Displayed In Current Work Queue    ${patient_name}
    END

Case Type Is Displayed in List View
    [Documentation]    Gets the last instance that matches with ssn/mrn
    [Arguments]    ${ssn}    ${case_type}
    ${case_type_element}    Format String    ${list_view_case_type}    ${ssn}
    Wait Until Element Is Visible    ${case_type_element}
    Sleep    1s
    ${text}    Get Text    ${case_type_element}
    Should Be Equal    ${text}    ${case_type}

Questionnaire Type Is Displayed In List View
    [Documentation]    Gets the last instance that matches with ssn/mrn
    [Arguments]    ${ssn}    ${questionnaire_type}
    ${questionnaire_type_element}    Format String    ${list_view_questionnaire_type}    ${ssn}
    Wait Until Element Is Visible    ${questionnaire_type_element}
    Sleep    1    #to avoid stale element, wait kw alone is not enough
    ${text}    Get Text    ${questionnaire_type_element}
    Should Be Equal    ${text}    ${questionnaire_type}

Select Patient Report Unhandled Filter
    Wait Until Element Is Visible    ${unhandled_filter}
    Try To Click Element    ${unhandled_filter}

Unhandled Tab Is Selected By Default
    Wait Until Element Is Visible    ${unhandled_filter}
    ${unhandled_attr}    Get Element Attribute    ${unhandled_tab}    class
    ${non_completed_attr}    Get Element Attribute    ${non_completed_tab}    class
    ${today_attr}    Get Element Attribute    ${today_tab}    class
    Should Contain    ${unhandled_attr}    button-toggle-checked
    Should Not Contain    ${non_completed_attr}    button-toggle-checked
    Should Not Contain    ${today_attr}    button-toggle-checked

Work Queue Is Grouped By Date
    [Arguments]    ${no_date_groups}
    Wait Until Location Contains    /work-queue/side-effect-reports
    Wait Until Element Is Visible    (${wq_grouped_by_date})[1]
    ${loader_status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${noona-loader}    timeout=3s
    IF    ${loader_status}    Wait Until Element Is Not Visible    ${noona-loader}
    Sleep    2s
    ${count}    Get Element Count    ${wq_grouped_by_date}
    ${count}    Convert To String    ${count}
    ${status}    Run Keyword And Return Status    Should Be Equal    ${count}    ${no_date_groups}
    IF    ${status}!=${TRUE}
        Fail    Number of date groups is not correct
    END

Latest Date Is Displayed In Patient Reports
    [Documentation]    For example, 25.07.2024
    [Arguments]    ${date}
    Wait Until Location Contains    /work-queue/side-effect-reports
    Group Date Is Correct    ${date}

Group Date Is Correct
    [Arguments]    ${date}
    Wait Until Element Is Visible    (${wq_grouped_by_date})[1]
    Wait Until Element Is Visible    //h2/span[contains(text(),"${date}")]

Patient Reports Can Be Filtered By Date
    [Arguments]    ${date}
    IF    '${date}'=='Today'
        Try To Click Element    ${today_tab}
    ELSE IF    '${date}'=='Tomorrow'
        Try To Click Element    ${tomorrow_tab_data_testid}
    ELSE    #other date
        Select Date From Other Date Filter    ${date}
    END

Select Date From Other Date Filter
    [Arguments]    ${date}
    Try To Click Element   ${other_date_filter_button}
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    //*[contains(@class, 'calendar-body-cell')]//*[@aria-label='${date}']
    WHILE    ${status}!=${TRUE}    limit=2    #atm, limits only to next 2 months or next 2 pages, update if needed
        Try To Click Element    ${other_date_filter_next_button}
        ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    //*[contains(@class, 'calendar-body-cell')]//*[@aria-label='${date}']
    END
    Try To Click Element    //*[contains(@class, 'calendar-body-cell')]//*[@aria-label='${date}']
    ${date_title_is_visible}    Run Keyword And Return Status    Wait Until Element Is Visible    //h2/span[contains(text(),"${date}")]
    IF    ${date_title_is_visible} == True
        Page Should Contain Element    ${patient_cards}
    ELSE
        Page Should Not Contain Element    ${patient_cards}
    END

Care Team Is Selected
    [Arguments]    ${care_team}
    Wait Until Element Is Visible    //*[contains(@class, 'searchable-filter')]//span[@title='${care_team}']

${tab_element} Tab Is Selected
    [Documentation]    This keyword checkes if a workqueue tab/ sub-tab is selected. Expand the logic when needed.
    IF    '${tab_element}' == 'Non Completed'
        Set Test Variable    ${locator}    ${non_completed_tab}
    ELSE IF    '${tab_element}'== 'Today'
        Set Test Variable    ${locator}    ${today_tab}
    ELSE IF    '${tab_element}' == 'Tomorrow'
        Set Test Variable    ${locator}    ${tomorrow_tab_data_testid}
    ELSE IF     '${tab_element}' == 'Other Date'
        Set Test Variable    ${locator}    ${other_date_filter_button}
    END
    ${element_attribute}    Get Element Attribute    ${locator}        class
    IF    'mat-button-toggle-checked' in '${element_attribute}'
        RETURN    ${tab_element} is selected.
    ELSE
        RETURN    ${tab_element} is NOT selected.
    END

