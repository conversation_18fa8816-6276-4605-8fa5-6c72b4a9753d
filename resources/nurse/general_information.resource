*** Settings ***
Resource    ${EXECDIR}${/}resources${/}patient${/}login.resource


*** Variables ***
${patients-search}                              //*[@id="input-search-ssn-search"]
${patient-start-header}                         //*[@id="sst-start-header"]
${other_clinics_header}                         //h4[contains(text(),"Other Clinics")]
${other_clinic_patient_status}                  //span[contains(text(),"Patient status")]/span
${lock_button}                                  lock
${unlock_button}                                unlock
${send_new_login_link}                          send-login-link
${send_invitation_icd_dropdown}                 //*[@data-testid='currentDiagnosis']//input/../..
${send_user_account_icd_first_option}           //*[@data-testid='option-0']
${send_user_account_first_treatment_module}     //*[@title='Acute Leukemia and MDS']
${send_user_care_team_dropdown}                 //*[@data-testid='responsible-unit']
${care_team_option}                             //*[@title='Appointment Care Team']
${patient_updated_text}                         Patient updated
${spmc_patient_text}                            Email already used by another clinic. Verify that this is the same patient. Accounts will be linked (1):
${username_sent_status}                         Username sent
${patient_created_by_ehr_status}                Patient created by the EHR
${patient_locked_banner_text}                   Patient locked
${general_info_tab}                             tab-general-information
${account_updated_banner_message}               Account updated
${my_health_status_id}                          //*[@id='my-health-status']
${family_resource_guide_id}                     //*[@id='family-resource-guide']
${completed_education_modules_area}             //label[text()="Completed education modules"]/..//div[@class='checkbox-container']
${careteam_input}                               //*[@id="responsible-unit"]//input


*** Keywords ***
Click "Lock" Or "Unlock" Button To Allow / Disallow Patient Login
    [Arguments]    ${action}
    Try To Click Element    ${general_info_tab}
    ${patient_is_unlocked}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${lock_button}
    ...    timeout=5s
    ${patient_is_locked}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${unlock_button}
    ...    timeout=5s
    IF    '${action}'=='lock' and ${patient_is_unlocked}==${TRUE}
        Try To Click Element    ${lock_button}
        Wait Until Page Contains    Patient locked
        Try To Click Banner Message
    ELSE IF    '${action}'=='unlock' and ${patient_is_locked}==${TRUE}
        Try To Click Element    ${unlock_button}
        Wait Until Page Contains    Patient unlocked
        Try To Click Banner Message
    END

Verify Patient Login Locked
    [Arguments]    ${email}
    Login To Noona    patient    ${email}
    Wait Until Page Contains    ${incorrect_error_message_oidc}
    Page Should Contain Element    ${oidc_sms_login_button}

Verify Patient Login Unlocked
    [Arguments]    ${email}
    Login As Patient    ${email}
    Wait Until Element Is Visible    ${patient-start-header}

Other Clinic Details Are Visible In General Info Tab
    [Arguments]    ${clinic_name}    ${patient_status}    ${patient_name}    ${dob}    ${phone_number}
    Wait Until Element Is Visible    ${other_clinics_header}
    Element Should Contain    ${other_clinics_header}/..    ${clinic_name}
    Element Should Contain    ${other_clinics_header}/..    ${patient_name}
    Element Should Contain    ${other_clinics_header}/..    ${patient_status}
    Element Should Contain    ${other_clinics_header}/..    ${patient_name}
    Element Should Contain    ${other_clinics_header}/..    ${patient_name}
    Element Should Contain    ${other_clinics_header}/..    ${patient_name}

Other Clinic Patient Status Is Correct
    [Documentation]    Verifies status of the other clinic in general info tab. Only verifies 1 other clinic. To update for multiple other clinics.
    [Arguments]    ${status}
    Wait Until Element Is Visible    ${other_clinic_patient_status}
    Sleep    1
    ${text}    Get Text    ${other_clinic_patient_status}
    Should Be Equal    ${text}    ${status}

Patient Status Is Correct
    [Arguments]    ${status}
    Wait Until Element Is Visible    //*[@id='status-entry-content' and contains(text(),"${status}")]

Fill In ICD, Module And Care Team With Default Values
    Wait Until Page Contains Element    ${send_invitation_icd_dropdown}
    Set Focus To Element    ${send_invitation_icd_dropdown}
    Try To Click Element    ${send_invitation_icd_dropdown}
    Scroll Element Into View    ${send_user_account_icd_first_option}
    Try To Click Element    ${send_user_account_icd_first_option}
    Scroll Element Into View    ${treatment_module_dropdown}
    Try To Click Element    ${treatment_module_dropdown}
    Try To Click Element    ${send_user_account_first_treatment_module}
    ${status}    Run Keyword And Return Status    Page Should Contain Element    ${send_user_care_team_dropdown}
    IF    ${status}
        Try To Click Element    ${send_user_care_team_dropdown}
        Try To Input Text    ${careteam_input}    ${care_team}
        Press Keys    ${careteam_input}    RETURN
    ELSE
        Try To Click Element    ${care_team_link}
        Try To Click Element    ${send_user_care_team_dropdown}
        Try To Input Text    ${careteam_input}    ${care_team}
        Press Keys    ${careteam_input}    RETURN
    END