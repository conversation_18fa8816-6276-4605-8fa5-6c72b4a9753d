*** Settings ***
Resource    ${EXECDIR}${/}resources${/}try_keywords.resource
Resource    ${EXECDIR}${/}resources${/}mailosaur.resource


*** Variables ***
${nurse_profile_dropdown}               //*[@data-testid='profile-dropdown']
${nurse_user_profile}                   //*[@data-testid='user-profile-link']
${logout_from_clinic}                   //*[@data-testid='logout-link']
${nurse_fname_field}                    //input[@name='firstName']
${nurse_lname_field}                    //input[@name='lastName']
${nurse_email_field}                    //input[@name='email']
${update_profile_save_button}           //button[@type='submit']
${update_profile_password_field}        password
${password_next_button}                 (//button[@type='submit'])[2]
${nurse_phone_field}                    //input[@name='phoneNumber']
${login_label}                          //label[contains(text(),'Login *')]
${ask_for_password_only}                //label[contains(text(),'Ask for password only')]
${verification_code_header}             //h4[contains(text(),'Verification code')]
${send_verification_code_button}        //a[contains(text(),'Send a new verification code')]
${new_verification_code_sent_info}      //div[contains(text(),'A new verification code has been sent')]
${2FA_via_sms}                          //label[contains(text(),'Ask for password and verification code sent via SM')]
${2FA_code_input_field}                 //*[@class='verification-code']/div[1]
${nurse_profile_identity_code_field}    //label[@for='identityCode']/..//div
${clinic_last_login_time}               //p[@class='profile-info-text']


*** Keywords ***
Go To Nurse Profile
    Wait Until Element Is Visible    ${nurse_profile_dropdown}
    Try To Click Element    ${nurse_profile_dropdown}
    Wait Until Element Is Visible     ${nurse_user_profile}
    Try To Click Element     ${nurse_user_profile}
    Wait Until Page Contains Element    ${update_profile_save_button}

Update Clinic User's Email
    [Arguments]    ${email}
    Wait Until Element Is Visible    ${nurse_email_field}
    Wait Until Keyword Succeeds    5x    1s    Clear Element Text    ${nurse_email_field}
    Sleep    1
    Try To Input Text    ${nurse_email_field}    ${email}

Save Nurse Profile
    Wait Until Element Is Enabled    ${update_profile_save_button}
    Click Element    ${update_profile_save_button}

Clinic User Clicks Log Out
    Wait Until Element Is Visible    ${nurse_profile_dropdown}
    Try To Click Element    ${nurse_profile_dropdown}
    Wait Until Page Contains Element      ${logout_from_clinic}
    Try To Click Element     ${logout_from_clinic}     wait_in_seconds=7x

Input Password To Update Profile
    [Arguments]    ${password}=${DEFAULT_PASSWORD}
    Wait Until Element Is Enabled    ${update_profile_password_field}
    Try To Input Text    ${update_profile_password_field}    ${password}
    Try To Click Element    ${password_next_button}
    Wait Until Page Contains    Profile settings saved
    Wait Until Page Does Not Contain    Profile settings saved

Update Nurse Phone Number
    [Arguments]    ${number}
    Wait Until Element Is Visible    ${nurse_phone_field}
    Clear Element Text    ${nurse_phone_field}
    Sleep    1
    Try To Input Text    ${nurse_phone_field}    ${number}

Get Nurse Email Value
    ${text}    Get Value    ${nurse_email_field}
    RETURN    ${text}

Verify If Contact Values Are Correct
    [Arguments]    ${fname}    ${lname}    ${email}    ${phone}
    Sleep    1
    ${new_fname}    Get Value    ${nurse_fname_field}
    Should Be Equal    ${new_fname}    ${fname}
    ${new_lname}    Get Value    ${nurse_lname_field}
    Should Be Equal    ${new_lname}    ${lname}
    ${new_email}    Get Value    ${nurse_email_field}
    Should Be Equal    ${new_email}    ${email}
    ${new_phone}    Get Value    ${nurse_phone_field}
    Should Be Equal    ${new_phone}    ${phone}

Select Pasword Only Or With Verification Code Sent Via SMS
    [Arguments]    ${login}=password only
    Sleep    1
    IF    '${login}'=='password only'
        Try To Click Element    ${ask_for_password_only}
    ELSE
        Try To Click Element    ${2FA_via_sms}
    END

Input Password To Modal
    [Arguments]    ${password}=${DEFAULT_PASSWORD}
    Wait Until Element Is Enabled    ${update_profile_password_field}
    Try To Input Text    ${update_profile_password_field}    ${password}
    Try To Click Element    ${password_next_button}

Send New Verification Code
    Wait Until Element Is Visible    ${verification_code_header}
    Try To Click Element    ${send_verification_code_button}
    Wait Until Element Is Visible    ${new_verification_code_sent_info}

Disable 2FA In Nurse Profile
    Delete All SMS From Mailosaur
    Wait Until Keyword Succeeds    2x    5s    Go To Nurse Profile
    Select Pasword Only Or With Verification Code Sent Via SMS
    Save Nurse Profile
    Input Password To Modal
    Input Verification Code

Input Verification Code
    ${sms_message}    User Received SMS
    ${get_sms_code}    Get Substring    ${sms_message}    0    6
    Set Global Variable    ${get_sms_code}
    Try To Click Element    ${2FA_code_input_field}
    Press Keys    ${2FA_code_input_field}    ${get_sms_code}

Profile Field Is Disabled
    [Arguments]    ${textfield}
    Wait Until Element Is Visible    ${nurse_phone_field}
    Element Should Be Disabled    ${textfield}

Profile Field Is Enabled
    [Arguments]    ${textfield}
    Wait Until Element Is Visible    ${nurse_phone_field}
    Element Should Be Enabled    ${textfield}

Update Clinic User First Name
    [Arguments]    ${fname}
    Wait Until Element Is Visible    ${nurse_fname_field}
    Clear Element Text    ${nurse_fname_field}
    Sleep    1
    Try To Input Text    ${nurse_fname_field}    ${fname}

Update Clinic User Last Name
    [Arguments]    ${lname}
    Wait Until Element Is Visible    ${nurse_lname_field}
    Clear Element Text    ${nurse_lname_field}
    Sleep    1
    Try To Input Text    ${nurse_lname_field}    ${lname}

Identity Code Field Is Visible
    Wait Until Element Is Visible    ${nurse_profile_identity_code_field}

Last Login Date And Time Is Displayed
    [Arguments]    ${datetime}
    Sleep    1
    Wait Until Element Is Visible    ${clinic_last_login_time}
    Scroll Element Into View    ${clinic_last_login_time}
    ${text}    Get Text    ${clinic_last_login_time}
    ${remote_url_exists}    Run Keyword And Return Status    Variable Should Exist    ${REMOTE_URL}
    IF    '${remote_url_exists}'=='False'    #gitlab run's timezone is different from local timezone
        Set Test Variable    ${time_zone}    Europe/Helsinki
    ELSE
        Set Test Variable    ${time_zone}    Africa/Abidjan
    END
    ${expected_date_time}    Set Variable    Last Login: ${datetime} (Time Zone: ${time_zone})
    ${status}    Run Keyword And Return Status    Should Be Equal    ${text}    ${expected_date_time}
    IF    ${status}==${FALSE}
        Capture Screenshot
        Fail    The actual Last Login Date Time (${text}) does not match expected (${expected_date_time}) !
    END