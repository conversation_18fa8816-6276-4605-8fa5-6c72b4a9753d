*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource


*** Variables ***
${clinic_menu}                              clinic-link
${triage_management_link}                   clinic-triage-management-link
${loader}                                   //*[contains(@class,"noona-loader")]
${back_to_button}                           //*[@data-testid="back-to-button"]
${templates_menu}                           manage-templates-link
${discard_changes_button}                   ok-confirm
${help_link}                                help-link
${help_about_link}                          about-link
${manage_questionnaire_templates_link}      questionnaire-templates
${clinic_user_list_last_name_header}        //th[text()="Last name"]
${patient_status_analytics}                 patient-status-analytics
${provider_analytics_menu}                  provider-analytics-link
${reporting_menu_link}                      reporting-link
${manage_templates}                         manage-templates-link
${operational_analytics_menu}               operational-analytics-link
${nurse_name_in_header}                     //div[@class='profile-text user-name ng-star-inserted' and text()="{}"]
${first_user_in_list}                       //*[@id='user-0']
${access_restricted_label}                  //span[text()="Access restricted"]
${access_restricted_go_to_homepage}         //div[contains(@class, 'noona-access-restricted-dialog-actions')]/button
${clinic_users_header_text}                 //*[@id='clinic']//h2[text()="Clinic users"]


*** Keywords ***
Login As Nurse Via API
    [Arguments]    ${email}    ${clinic_id}    ${level}=test
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    IF    '${level}'=='suite'
        Set Suite Variable    &{header}
    ELSE
        Set Test Variable    &{header}
    END
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}loginNurse.json
    ${body_mod}    Replace String    ${body}    email    ${email}
    ${body_mod}    Replace String    ${body_mod}    clinic_id    ${clinic_id}
    IF    '${level}'=='suite'
        Set Suite Variable    ${body_mod}
    ELSE
        Set Test Variable    ${body_mod}
    END
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    ${response_json}    Evaluate    json.loads("""${response.content}""")    json
    ${result_json}    Get From Dictionary    ${response_json}    result
    ${login_token}    Get From Dictionary    ${result_json}    loginToken
    ${nurse_user_id}    Get From Dictionary    ${result_json}    userId
    IF    '${level}'=='suite'
        Set Suite Variable    ${login_token}
        Set Suite Variable    ${nurse_user_id}
    ELSE
        Set Test Variable    ${login_token}
        Set Test Variable    ${nurse_user_id}
    END
    Delete All Sessions

Remove Patient Via API
    [Documentation]    Precondition: Login As Nurse Via API is executed first and patient details are set
    ...    Note: Gender is male by default as it is what was set in invitePatientViaSMSInvite.json
    [Arguments]    ${patient_id}    ${patient_user_id}    ${gender}=male    ${level}=test
    &{header}    Create Dictionary    Authorization=Bearer ${login_token}    Content-Type=application/json-rpc
    IF    '${level}'=='suite'
        Set Suite Variable    &{header}
    ELSE
        Set Test Variable    &{header}
    END
    ${today}    Get Current Date    exclude_millis=true
    ${today}    Convert Date    ${today}    epoch
    ${today}    Convert To Integer    ${today}
    ${today}    Convert To String    ${today}
    ${body}    Get File    ${EXECDIR}${/}resources${/}test_data${/}removePatient.json
    ${body_mod}    Replace String    ${body}    patient_id    ${patient_id}
    ${body_mod}    Replace String    ${body_mod}    patient_user_id    ${patient_user_id}
    ${body_mod}    Replace String    ${body_mod}    ssn    ${patient_ssn}
    ${body_mod}    Replace String    ${body_mod}    first_name    ${first_name}
    ${body_mod}    Replace String    ${body_mod}    last_name    ${family_name}
    ${body_mod}    Replace String    ${body_mod}    patient_email    ${patient_email}
    ${body_mod}    Replace String    ${body_mod}    phone_number    ${phone_number}
    ${body_mod}    Replace String    ${body_mod}    mrn    ${patient_mrn}
    ${body_mod}    Replace String    ${body_mod}    status_changed_date    ${today}
    ${body_mod}    Replace String    ${body_mod}    gender_option    ${gender}
    ${body_mod}    Replace String    ${body_mod}    birth_date    ${dob}
    IF    '${level}'=='suite'
        Set Suite Variable    ${body_mod}
    ELSE
        Set Test Variable    ${body_mod}
    END
    ${body_json}    Evaluate    json.loads("""${body_mod}""")    json
    Create Session    noona    ${API_NURSE_URL}    verify=True
    ${response}    POST On Session    noona    /api/clinic    headers=${header}    data=${body_mod}
    Should Contain    '${response.status_code}'    '200'
    Delete All Sessions

Navigate To Schedule Templates
    Wait Until Element Is Visible    ${clinic_menu}
    Wait Until Element Is Enabled    ${clinic_menu}
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${templates_menu}
    Wait Until Noona Loader Is Not Visible
    Execute JavaScript    document.getElementById('manage-templates-link').click()

Check If Clinic Analytics Is Visible
    Try To Click Element   ${clinic_menu}
    Wait Until Element Is Visible    ${patient_status_analytics}    timeout=3s

Navigate to Message templates
    Wait Until Element Is Visible    ${clinic_menu}
    Wait Until Element Is Enabled    ${clinic_menu}
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${manage_templates}

Navigate To Analytics Page - Reporting
    Try To Click Element    ${provider_analytics_menu}
    Try To Click Element    ${reporting_menu_link}
    Wait Until Noona Loader Is Not Visible