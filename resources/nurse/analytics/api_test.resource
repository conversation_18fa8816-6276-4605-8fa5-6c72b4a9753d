*** Settings ***
Library                 ExcelLibrary
Library                 OperatingSystem

*** Variables ***
# ${JSON_body}            {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
${year}                 2021
${year_msg}             2019
${environment}          staging
${client_secret}        642GgkF=2d9CN2qXQKZJ/ku=4bt=+3%8
# ${client_secret}        7X2,Q3kd7Mb99*HWTzG:94Ms@6-anQJQ

# careteams from default hospital 1
${careteam_1}           bfed2175-5fe9-4990-a3e0-d0a7c9452113
${careteam_2}           a8901469-dc31-4025-a14a-090ff924cf31

# careteams from default hospital 2
${careteam_1_msg}       1df65796-78dd-4b93-9810-8abd155c754c
${careteam_2_msg}       2e244d94-35f5-400c-8d6b-8ade7d1eca63


${clinic}               d2b97a10-e262-11e3-8b68-0800200c9a66
${clinic_msg}           4f47c972-52f1-4df8-a6da-0903690e059a

${clinic_fake}          d2b97a10-e262-11e3-8b68
${path}                 ${EXECDIR}${/}test${/}nurse${/}analytics${/}API_testing${/}data_and_results.xlsx
${token_str_expired}    "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ5ZDFBSFl6cS1ZTEE2TTd1N2dMalZPWlQ0czNlZ21mSjE4VUlmWm9DaHdvIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bLT9dETyek7FRi07A4Djym5ON4UfKxkfJ1qRgknh8TRPAkR1tt73Z3z88qb8592oTsL0lIhoBh0UOECTSiAS3eQk8Ft7bptK-2BNcWU8UWvkIPAjdbh-P8mFtR0LarQ7Nfxzpns88qPsrCYdumfGoEq4ogLtz8NIvXluY4jd6g-qKGj9o5680KuuwYSoN63nEsPcLPsEkAmonaZLxMCNKhLHv_HCpddJHx0lHgNbOba8msTUUmx5n8C-e5cs6LbszRZmS4E-ZaSW3M3fQiusk_mfOunzsX-dcB1m7qJclVTwNvwkZYo9RUzfP9h52eszfaoiAcljJ2nJ5UDGYqWI5g"

*** Keywords ***
Get Token
    # Run    echo export PYTHONWARNINGS="ignore:Unverified HTTPS request"

    Create Session
    ...    token_session
    ...    https://${environment}.traefik.noonatest.com/idp/auth/realms/noona/protocol/openid-connect

    &{body}=    Create Dictionary
    ...    client_id=provider-analytics-client
    ...    client_secret=${client_secret}
    ...    grant_type=client_credentials

    ${token_response}=    POST On Session    token_session    /token    data=${body}

    Status Should be    200    ${token_response}

    ${token_str}=    Get Value From JSON    ${token_response.json()}    $..access_token
    ${token_str}=    Convert JSON To String    ${token_str}

    ${token_str}=    Remove String    ${token_str}    "
    ${token_str}=    Remove String    ${token_str}    [
    ${token_str}=    Remove String    ${token_str}    ]

    ${token_str}=    Set Variable    Bearer ${token_str}
    Set Global Variable    ${token_str}
    Sleep    1

Create a Session
    [Arguments]    ${month}    ${year}    ${endpoint_group}    ${endpoint}      ${clinic}

    Set Global Variable         ${year}
    Set Global Variable         ${month}

    Create Session
    ...    session
    ...    https://${environment}.pa.noonadsnonprod.com/analytics/noona-provider-analytics/api/${endpoint_group}/${clinic}/

    Post request    ${month}    ${year}     ${endpoint}     ${clinic}

Post request
    [Arguments]     ${month}    ${year}     ${endpoint}     ${clinic}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${time_start}=      Get Current Date
    ${response}=        POST On Session    session    ${endpoint}    headers=${header}    data=${JSON_body}
    ${time_finish}=     Get Current Date

    ${actual_time}=     Subtract Date From Date     ${time_finish}    ${time_start}
    Set Global Variable    ${actual_time}
    Log To Console      Response time: ${actual_time}

    Set Global Variable    ${response}

Compare data: Opened Messages
    FOR     ${INDEX}    IN RANGE    1    13
        ${row}=    Evaluate    ${INDEX}+16
        ${row_1}=    Evaluate    ${INDEX}+31
        ${month}=    Set Variable    ${INDEX}
        ${unread_msg_count}=  Set variable
        ${read_msg_count}=  Set variable

        ${JSON_body}=    Set Variable   {"year":${year_msg},"month":${month},"careTeamIds":[],"contactTopicTypes":[]}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year_msg}    clinic-messages    opened-messages   ${clinic_msg}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${JSON_month}=    Set Variable

        @{counts}=    Get Value From Json    ${response.json()}    $..unreadMessages
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            Set Global Variable    ${INDEX_length}

            ${JSON_month}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..month

            IF  [${month}] == ${JSON_month}
                Log To Console    Messages unread for month ${month} ${response.json()[${INDEX_length}]["unreadMessages"]}
                ${unread_msg_count}=    Set Variable    ${response.json()[${INDEX_length}]["unreadMessages"]}

                Log To Console    Messages read for month ${month} ${response.json()[${INDEX_length}]["readMessages"]}
                ${read_msg_count}=    Set Variable    ${response.json()[${INDEX_length}]["readMessages"]}
            END

        END

        Write Excel Cell    ${row}    79    ${unread_msg_count}    test_summary
        Write Excel Cell    ${row_1}    79    ${read_msg_count}    test_summary
        Save Excel Document    ${path}
    END

Compare data: Sent Messages Per Careteam
    FOR     ${INDEX}    IN RANGE    1    13
        ${row}=    Evaluate    ${INDEX}+16
        ${row_1}=    Evaluate    ${INDEX}+31
        ${month}=    Set Variable    ${INDEX}
        ${careteam1_count}=  Set variable
        ${careteam2_count}=  Set variable

        ${JSON_body}=    Set Variable   {"year":${year_msg},"month":${month},"careTeamIds":[],"contactTopicTypes":[]}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year_msg}    clinic-messages    messages-per-careteam     ${clinic_msg}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${JSON_month}=    Set Variable

        @{counts}=    Get Value From Json    ${response.json()}    $..sentMessagesCount
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            Set Global Variable    ${INDEX_length}

            ${careteam_from_json}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..careTeamId

            IF  ['${careteam_1_msg}'] == ${careteam_from_json}
                Log To Console    Messages sent for careteam 1 is ${response.json()[${INDEX_length}]["sentMessagesCount"]}
                ${careteam1_count}=    Set Variable    ${response.json()[${INDEX_length}]["sentMessagesCount"]}
            END

            IF  ['${careteam_2_msg}'] == ${careteam_from_json}
                Log To Console    Messages sent for careteam 2 is ${response.json()[${INDEX_length}]["sentMessagesCount"]}
                ${careteam2_count}=    Set Variable    ${response.json()[${INDEX_length}]["sentMessagesCount"]}
            END
        END

        Log To Console      ${careteam1_count}
        Log To Console      ${careteam2_count}

        Write Excel Cell    ${row}    84    ${careteam1_count}    test_summary
        Write Excel Cell    ${row_1}    84    ${careteam2_count}    test_summary
        Save Excel Document    ${path}
    END

Compare data: Time To Open
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${count_to_write}=    Set Variable
        ${row}=    Evaluate    ${INDEX}+16

        ${JSON_body}=    Set Variable   {"year":${year_msg},"month":${month},"careTeamIds":[]}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year_msg}    clinic-messages    time-to-open     ${clinic_msg}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${JSON_month}=    Set Variable

        @{counts}=    Get Value From Json    ${response.json()}    $..medianOpenTimeMs
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            IF  ${month} == ${response.json()[${INDEX_length}]["month"]}
                Log To Console    Messages sent for month: ${month} is ${response.json()[${INDEX_length}]["medianOpenTimeMs"]}
                ${count_to_write}=    Set Variable    ${response.json()[${INDEX_length}]["medianOpenTimeMs"]}
            END
        END

        Write Excel Cell    ${row}    89    ${count_to_write}    test_summary
        Save Excel Document    ${path}
    END


Compare data: Sent Messages
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${count_to_write}=    Set Variable
        ${row}=    Evaluate    ${INDEX}+16

        ${JSON_body}=    Set Variable   {"year":${year_msg},"month":${month},"careTeamIds":[],"contactTopicTypes":[]}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year_msg}    clinic-messages    sent-messages     ${clinic_msg}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${JSON_month}=    Set Variable

        @{counts}=    Get Value From Json    ${response.json()}    $..sentMessagesCount
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            IF  ${month} == ${response.json()[${INDEX_length}]["month"]}
                Log To Console    Messages sent for month: ${month} is ${response.json()[${INDEX_length}]["sentMessagesCount"]}
                ${count_to_write}=    Set Variable    ${response.json()[${INDEX_length}]["sentMessagesCount"]}
            END
        END

        Write Excel Cell    ${row}    74    ${count_to_write}    test_summary
        Save Excel Document    ${path}
    END

Compare data: Drop off Rate
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${answer_status}=    Set Variable
        ${answered_count}=    Set Variable
        ${unanswered_count}=    Set Variable
        ${row}=    Evaluate    ${INDEX}+16
        ${row_1}=    Evaluate    ${INDEX}+31

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    engagement    drop-off-rate      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            IF  ${month} == ${response.json()[${INDEX_length}]["month"]}

                ${answer_status}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..answered
                Log To Console  ${answer_status}
                IF  ${answer_status} == [True]
                    ${answered_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console  answered: ${answered_count}
                ELSE
                    ${unanswered_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console  not answered: ${unanswered_count}
                END
                Write Excel Cell    ${row}    69    ${answered_count}    test_summary
                Write Excel Cell    ${row_1}    69    ${unanswered_count}    test_summary
            END
        END

        Save Excel Document    ${path}

    END

Compare data: Delay Reasons
    FOR    ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${delayReason}=    Set Variable
        ${delay_count}=    Set Variable

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    patient-cases    delay-reasons      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${JSON_month}=    Set Variable
        ${row}=     Evaluate    ${INDEX}+16
        ${row_1}=   Evaluate    ${INDEX}+31
        ${row_2}=   Evaluate    ${INDEX}+46
        ${row_3}=   Evaluate    ${INDEX}+61

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR    ${INDEX_length}    IN RANGE    ${length}
            IF  ${month} == ${response.json()[${INDEX_length}]["month"]}
                ${delayReason}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..delayReason

                IF  ${delayReason} == ['technologyIssue']
                    ${delay_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    For ${delayReason} there is: ${delay_count}
                    Write Excel Cell    ${row}    49    ${delay_count}    test_summary
                END

                IF  ${delayReason} == ['highCallVolume']
                    ${delay_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    For ${delayReason} there is: ${delay_count}
                    Write Excel Cell    ${row_1}    49    ${delay_count}    test_summary
                END

                IF  ${delayReason} == ['lowerPriority']
                    ${delay_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    For ${delayReason} there is: ${delay_count}
                    Write Excel Cell    ${row_2}    49    ${delay_count}    test_summary
                END

                IF    ${delayReason} == ['afterHours']
                    ${delay_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    For ${delayReason} there is: ${delay_count}
                    Write Excel Cell    ${row_3}    49    ${delay_count}    test_summary
                END

                IF  ${delayReason} == ['cannotReachOrWaitingPatientCallback']
                    ${delay_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    For ${delayReason} there is: ${delay_count}
                    Write Excel Cell    ${row}    54    ${delay_count}    test_summary
                END
            END
        END

        Save Excel Document    ${path}
    END

Compare data: Answered Cases Per Careteam
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${team1_delayed_true_amount}=    Set Variable
        ${team2_delayed_true_amount}=    Set Variable
        ${team1_delayed_false_amount}=   Set Variable
        ${team2_delayed_false_amount}=   Set Variable
        ${row}=     Evaluate    ${INDEX}+16
        ${row_1}=   Evaluate    ${INDEX}+31
        ${row_2}=   Evaluate    ${INDEX}+46
        ${row_3}=   Evaluate    ${INDEX}+61

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    patient-cases    answered-cases-per-care-team      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            ${careteam_from_json}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..careTeamId

            IF    ['${care_team_1}'] == ${careteam_from_json}
                IF  not ${response.json()[${INDEX_length}]["wasDelayed"]}
                    ${team1_delayed_false_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                IF  ${response.json()[${INDEX_length}]["wasDelayed"]}
                    ${team1_delayed_true_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END
            END

            IF    ['${care_team_2}'] == ${careteam_from_json}
                Log To Console    Team 2 details

                IF  not ${response.json()[${INDEX_length}]["wasDelayed"]}
                    ${team2_delayed_false_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                IF  ${response.json()[${INDEX_length}]["wasDelayed"]}
                    ${team2_delayed_true_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END
            END

            Write Excel Cell    ${row}    34    ${team1_delayed_false_amount}    test_summary
            Write Excel Cell    ${row_1}    34    ${team1_delayed_true_amount}    test_summary
            Write Excel Cell    ${row_2}    34    ${team2_delayed_false_amount}    test_summary
            Write Excel Cell    ${row_3}    34    ${team2_delayed_true_amount}    test_summary
        END

        Save Excel Document    ${path}
    END

Compare data: Response Rate Per Careteam
    FOR    ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${team1_answered_true_amount}=    Set Variable
        ${team2_answered_true_amount}=    Set Variable
        ${team1_answered_false_amount}=    Set Variable
        ${team2_answered_false_amount}=    Set Variable
        ${row}=    Evaluate    ${INDEX}+16
        ${row_1}=    Evaluate    ${INDEX}+31
        ${row_2}=    Evaluate    ${INDEX}+46
        ${row_3}=    Evaluate    ${INDEX}+61

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    engagement    response-rate-per-care-team      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR    ${INDEX_length}    IN RANGE    ${length}
            ${careteam_from_json}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..careTeamId

            IF    ['${care_team_1}'] == ${careteam_from_json}
                Log To Console    Team 1 details
                IF  ${response.json()[${INDEX_length}]["answered"]}
                    ${team1_answered_true_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    Answered amount: ${team1_answered_true_amount}
                END

                IF  not ${response.json()[${INDEX_length}]["answered"]}
                    ${team1_answered_false_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    NOT Answered amount: ${team1_answered_false_amount}
                END
            END

            IF    ['${care_team_2}'] == ${careteam_from_json}
                Log To Console    Team 2 details
                IF    ${response.json()[${INDEX_length}]["answered"]}
                    ${team2_answered_true_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    Answered amount: ${team2_answered_true_amount}
                END

                IF  not ${response.json()[${INDEX_length}]["answered"]}
                    ${team2_answered_false_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                    Log To Console    NOT Answered amount: ${team2_answered_false_amount}
                END
            END

            Write Excel Cell    ${row}    24    ${team1_answered_true_amount}    test_summary
            Write Excel Cell    ${row_1}    24    ${team1_answered_false_amount}    test_summary
            Write Excel Cell    ${row_2}    24    ${team2_answered_true_amount}    test_summary
            Write Excel Cell    ${row_3}    24    ${team2_answered_false_amount}    test_summary
        END

        Save Excel Document    ${path}
    END

Compare data: Delayed Cases
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${row}=    Evaluate    ${INDEX}+16
        ${case_count}=    Set Variable

        ${JSON_body}=    Set Variable   {"page":0,"limit":20,"filters":{"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    patient-cases    delayed-cases      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${case_count}=    Set Variable    ${response.json()["total"]}

        IF  ${case_count} > 0
            Write Excel Cell    ${row}    44    ${case_count}    test_summary
        END

        Save Excel Document    ${path}
    END

Compare data: Case Volumes
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${contact_type}=    Set Variable
        ${message_count}=    Set Variable
        ${call_count}=    Set Variable
        ${row}=    Evaluate    ${INDEX}+16
        ${row_1}=    Evaluate    ${INDEX}+31

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    patient-cases    case-volumes      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            IF  ${month} == ${response.json()[${INDEX_length}]["month"]}
                ${contact_type_text}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..contactType

                IF  ${contact_type_text} == ['phone']
                    ${call_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                IF  ${contact_type_text} == ['message']
                    ${message_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                Write Excel Cell    ${row}    39    ${call_count}    test_summary
                Write Excel Cell    ${row_1}    39    ${message_count}    test_summary
            END
        END

        Save Excel Document    ${path}
    END

Compare data: Response Rate
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${Questionary_true_amount}=    Set Variable
        ${Questionary_false_amount}=    Set Variable
        ${Symptom_true_amount}=    Set Variable
        ${Symptom_false_amount}=    Set Variable
        ${row}=     Evaluate    ${INDEX}+16
        ${row_1}=   Evaluate    ${INDEX}+31
        ${row_2}=   Evaluate    ${INDEX}+46
        ${row_3}=   Evaluate    ${INDEX}+61

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    engagement    response-rate      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            IF   ${month} == ${response.json()[${INDEX_length}]["month"]}
                ${category_text}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..category

                IF   ${category_text} == ['Questionary'] and ${response.json()[${INDEX_length}]["answered"]}
                    ${Questionary_true_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                IF   ${category_text} == ['Questionary'] and not ${response.json()[${INDEX_length}]["answered"]}
                    ${Questionary_false_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                IF   ${category_text} == ['Symptom'] and ${response.json()[${INDEX_length}]["answered"]}
                    ${Symptom_true_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                IF   ${category_text} == ['Symptom'] and not ${response.json()[${INDEX_length}]["answered"]}
                    ${Symptom_false_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                Write Excel Cell    ${row}    19    ${Questionary_true_amount}    test_summary
                Write Excel Cell    ${row_1}    19    ${Questionary_false_amount}    test_summary
                Write Excel Cell    ${row_2}    19    ${Symptom_true_amount}    test_summary
                Write Excel Cell    ${row_3}    19    ${Symptom_false_amount}    test_summary
            END
        END

        Save Excel Document    ${path}
    END

Compare data: Touched Patients
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${month_to_write}=    Set Variable
        ${ActivePortalPatient}=    Set Variable
        ${ActivePortalPatient_amount}=    Set Variable
        ${TriagePatient_amount}=    Set Variable
        ${ePRO_amount}=    Set Variable

        ${row}=    Evaluate    ${INDEX}+16
        ${row_1}=    Evaluate    ${INDEX}+31
        ${row_2}=    Evaluate    ${INDEX}+46


        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    enrolment    patients-touched      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${JSON_month}=    Set Variable

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            IF  ${month} == ${response.json()[${INDEX_length}]["month"]}

                Set Global Variable    ${ActivePortalPatient}
                ${ActivePortalPatient_text}=    Get Value From Json
                ...    ${response.json()[${INDEX_length}]}
                ...    $..populationCategory

                IF  ${ActivePortalPatient_text} == ['ActivePortalPatient']
                    ${ActivePortalPatient_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                IF  ${ActivePortalPatient_text} == ['TriagePatient']
                    ${TriagePatient_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                IF  ${ActivePortalPatient_text} == ['ePRO']
                    ${ePRO_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
                END

                Write Excel Cell    ${row}      14    ${ActivePortalPatient_amount}    test_summary
                Write Excel Cell    ${row_1}    14    ${TriagePatient_amount}    test_summary
                Write Excel Cell    ${row_2}    14    ${ePRO_amount}    test_summary
            END
        END

        Save Excel Document    ${path}
    END

Compare data: New Portal Patients
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${count_to_write}=    Set Variable
        ${row}=    Evaluate    ${INDEX}+16

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    enrolment    new-portal-patients      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${JSON_month}=    Set Variable

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            IF  ${month} == ${response.json()[${INDEX_length}]["month"]}
                Log To Console    Patients for month: ${month} is ${response.json()[${INDEX_length}]["count"]}
                ${count_to_write}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
            END
        END

        Write Excel Cell    ${row}    4    ${count_to_write}    test_summary
        Save Excel Document    ${path}
    END

Compare data: Cases answered in time
    FOR     ${INDEX}    IN RANGE    1    13
        ${month}=    Set Variable    ${INDEX}
        ${delayed_true_amount}=    Set Variable
        ${delayed_false_amount}=    Set Variable
        ${row}=    Evaluate    ${INDEX}+16
        ${row_1}=    Evaluate    ${INDEX}+31

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    patient-cases    answered-cases      ${clinic}  #${JSON_body}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        ${JSON_month}=    Set Variable

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            IF  ${month} == ${response.json()[${INDEX_length}]["month"]} and ${response.json()[${INDEX_length}]["wasDelayed"]}
                Log To Console
                ...    Delayed answered cases for month: ${month} is ${response.json()[${INDEX_length}]["count"]}
                ${delayed_true_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
            END

            IF  ${month} == ${response.json()[${INDEX_length}]["month"]} and not ${response.json()[${INDEX_length}]["wasDelayed"]}
                Log To Console
                ...    Not delayed answered cases for month: ${month} is ${response.json()[${INDEX_length}]["count"]}
                ${delayed_false_amount}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
            END
        END

        Write Excel Cell    ${row}      29    ${delayed_false_amount}    test_summary
        Write Excel Cell    ${row_1}    29    ${delayed_true_amount}    test_summary

        Save Excel Document    ${path}
    END

Compare data: New Portal Patients per care team
    FOR     ${INDEX}    IN RANGE    1    13
        ${row}=    Evaluate    ${INDEX}+16
        ${row_1}=    Evaluate    ${INDEX}+31
        ${month}=    Set Variable    ${INDEX}
        ${careteam1_count}=  Set variable
        ${careteam2_count}=  Set variable

        ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}
        Set Global Variable     ${JSON_body}

        Create a Session    ${month}    ${year}    enrolment    new-portal-patients-per-care-team      ${clinic}

        Log To Console    Month: ${month}
        Log To Console    ${response.json()}

        @{counts}=    Get Value From Json    ${response.json()}    $..count
        ${length}=    Get length    ${counts}

        FOR     ${INDEX_length}    IN RANGE    ${length}
            Set Global Variable    ${INDEX_length}

            ${careteam_from_json}=    Get Value From Json    ${response.json()[${INDEX_length}]}    $..careTeamId

            IF  ['${care_team_1}'] == ${careteam_from_json}
                ${careteam1_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
            END

            IF  ['${care_team_2}'] == ${careteam_from_json}
                ${careteam2_count}=    Set Variable    ${response.json()[${INDEX_length}]["count"]}
            END
        END

        Log To Console      ${careteam1_count}
        Log To Console      ${careteam2_count}

        Write Excel Cell    ${row}    9    ${careteam1_count}    test_summary
        Write Excel Cell    ${row_1}    9    ${careteam2_count}    test_summary
        Save Excel Document    ${path}
    END

Check Response status code
    [Arguments]    ${row}    ${column}
    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console    ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Check Response content type
    [Arguments]    ${row}    ${column}
    ${accept_type}=    Set Variable    application/json
    ${headers}=    Create Dictionary    Accept    ${accept_type}
    ${content_type_response}=    Get From Dictionary    ${response.headers}    Content-Type

    Should Be Equal    ${accept_type}    ${content_type_response}

    Write Excel Cell    ${row}    ${column}    ${content_type_response}    test_summary
    Save Excel Document    ${path}

Check Response time
    [Arguments]    ${row}    ${column}
    Write Excel Cell    ${row}    ${column}    ${actual_time}    test_summary
    Save Excel Document    ${path}

Create a Session for fail test
    [Arguments]    ${endpoint_group}    ${endpoint}     ${clinic}       ${row}      ${column}

    Set Global Variable     ${endpoint_group}
    Set Global Variable     ${endpoint}
    Set Global Variable     ${column}
    Set Global Variable     ${row}

    Create Session
    ...    session_fail
    ...    https://${environment}.pa.noonadsnonprod.com/analytics/noona-provider-analytics/api/${endpoint_group}/${clinic}/


Create a Session With Expired Token
    ${month}=    Set Variable    5   # does not matter what month and year used in this case
    ${year}=    Set Variable    2021
    ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str_expired}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}    expected_status=403
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Create a Session With Expired Token for clinic-messages
    ${month}=    Set Variable    5
    ${year}=    Set Variable    2019
    ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"contactTopicTypes":[]}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str_expired}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}    expected_status=403
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Create a Session With Incorrect URL
    ${row}=     Evaluate    ${row}+1
    ${month}=    Set Variable    5
    ${year}=    Set Variable    2021
    ${endpoint}=    Set Variable    a

    ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}     expected_status=404
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Create a Session With Incorrect URL clinic-messages
    ${row}=     Evaluate    ${row}+1
    ${month}=    Set Variable    5
    ${year}=    Set Variable    2019
    ${endpoint}=    Set Variable    a

    ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"contactTopicTypes":[]}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}     expected_status=404
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Create a Session With non-existent month
    ${row}=     Evaluate    ${row}+2
    ${month}=    Set Variable    15
    ${year}=    Set Variable    2021

    ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}     expected_status=200
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Create a Session With non-existent month clinic-messages
    ${row}=     Evaluate    ${row}+2
    ${month}=    Set Variable    15
    ${year}=    Set Variable    2019

    ${JSON_body}=    Set Variable   {"year":${year},"month":${month},"careTeamIds":[],"contactTopicTypes":[]}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}     expected_status=200
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Create a Session With empty request body
    ${row}=     Evaluate    ${row}+3
    ${month}=    Set Variable    5
    ${year}=    Set Variable    2021

    ${JSON_body}=    Set Variable   {}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}     expected_status=400
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Create a Session With empty request body clinic-messages
    ${row}=     Evaluate    ${row}+3
    ${month}=    Set Variable    5
    ${year}=    Set Variable    2019

    ${JSON_body}=    Set Variable   {}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}     expected_status=400
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Create a Session With non-existent month (delayed cases)
    ${row}=     Evaluate    ${row}+2
    ${month}=    Set Variable    15
    ${year}=    Set Variable    2021

    ${JSON_body}=    Set Variable   {"page":0,"limit":20,"filters":{"year":${year},"month":${month},"careTeamIds":[],"caseType":null,"questionnaire":null}}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=    POST On Session    session_fail    ${endpoint}    headers=${header}    data=${JSON_body}     expected_status=200
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Get Filter Options
    [Arguments]    ${clinic}
    Create Session
    ...    session
    ...    https://${environment}.pa.noonadsnonprod.com/analytics/noona-provider-analytics/api/filters/${clinic}/

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${time_start}=      Get Current Date
    ${response}=        GET On Session     session     options      headers=${header}       expected_status=200
    ${time_finish}=     Get Current Date
    ${actual_time}=     Subtract Date From Date     ${time_finish}    ${time_start}
    Set Global Variable    ${actual_time}

    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Log To Console      ${response.json()}

    Write Excel Cell    13    64   ${response.json()["startYear"]}    test_summary

    FOR     ${INDEX_caseTypes}    IN RANGE    50
            ${row}=     Evaluate    ${INDEX_caseTypes}+16

            Run Keyword and Ignore Error    Log To Console      ${response.json()["caseTypes"][${INDEX_caseTypes}]}
            Run Keyword and Ignore Error    Write Excel Cell    ${row}    62   ${response.json()["caseTypes"][${INDEX_caseTypes}]}    test_summary
    END

    FOR     ${INDEX_questionnaires}     IN RANGE    50
            ${row}=     Evaluate    ${INDEX_questionnaires}+32

            Run Keyword and Ignore Error    Log To Console      ${response.json()["questionnaires"][${INDEX_questionnaires}]}
            Run Keyword and Ignore Error    Write Excel Cell    ${row}    62   ${response.json()["questionnaires"][${INDEX_questionnaires}]["category"]}    test_summary
            Run Keyword and Ignore Error    Write Excel Cell    ${row}    64   ${response.json()["questionnaires"][${INDEX_questionnaires}]["type"]}    test_summary

    END
    Save Excel Document    ${path}

Filter Options With Expired Token
    &{header}=    Create Dictionary
    ...    Authorization=${token_str_expired}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=        GET On Session    session_fail     options      headers=${header}       expected_status=403
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Filter Options With Incorrect URL
    ${row}=     Evaluate    ${row}+1
    ${endpoint}=    Set Variable    a

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=        GET On Session    session_fail     ${endpoint}      headers=${header}       expected_status=404
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}


Get Last Update time
    Create Session
    ...    session
    ...    https://${environment}.pa.noonadsnonprod.com/analytics/noona-provider-analytics/api/

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${time_start}=      Get Current Date
    ${response}=        GET On Session    session     last-update/${clinic}      headers=${header}       expected_status=200
    ${time_finish}=     Get Current Date
    ${actual_time}=     Subtract Date From Date     ${time_finish}    ${time_start}
    Set Global Variable    ${actual_time}

    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Log To Console      ${response.json()}

    Write Excel Cell    14    59   ${response.json()}    test_summary

    Save Excel Document    ${path}

Last Update Time With Expired Token
    &{header}=    Create Dictionary
    ...    Authorization=${token_str_expired}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=        GET On Session    session_fail     /      headers=${header}       expected_status=403
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Last Update Time With Incorrect URL
    ${row}=     Evaluate    ${row}+1
    ${endpoint}=    Set Variable    a

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
    ...    x-varian-customer=${clinic}
    ...    Content-Type=application/json

    ${response}=        GET On Session    session_fail     ${endpoint}      headers=${header}       expected_status=404
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}

Last Update Time With Missing Header
    ${row}=     Evaluate    ${row}+2

    Create Session
    ...    session_fail
    ...    https://${environment}.pa.noonadsnonprod.com/analytics/noona-provider-analytics/api/last-update/${clinic}

    &{header}=    Create Dictionary
    ...    Authorization=${token_str}
#    ...    x-varian-customer=
    ...    Content-Type=application/json

    ${response}=        GET On Session    session_fail     /      headers=${header}       expected_status=500
    Set Global Variable    ${response}

    ${status_code}=    Set Variable    ${response.status_code}
    Log To Console       ${status_code}

    Write Excel Cell    ${row}    ${column}    ${status_code}    test_summary
    Save Excel Document    ${path}