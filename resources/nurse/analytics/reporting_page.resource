*** Variables ***
${analytics_button}                                     //*[@id='provider-analytics-link']
${reporting_button}                                     //*[@id='reporting-link']
${reporting_page}                                       //*[contains(text(), 'Reports')]
${reporting_burger_menu}                                //*[contains(@class,'noona-reporting-menu')]
${reporting_menu}                                       //div[contains(@role ,'menu')]
${report_name_from_list_symptom}                        //*[contains(text(), 'Symptom')]
${report_name_from_list_enrollment}                     //*[contains(text(), 'Enrollment')]
${report_name_from_list_casemanagement}                 //*[contains(text(), 'Case Management')]
${report_name_from_list_clinicinitiatedmessages}        //button//*[contains(text(), 'Clinic Initiated Messages')]
${enrollment_tooltip_txt}                               Enrollment report - Provides a comprehensive list of data about the enrollment history as each activation status is achieved by the patient.
${symptom_tooltip_txt}                                  Symptom report - Provides a comprehensive list of data about all symptoms reported in Noona and their date, priority and response date.
# COMMON FILTERS
${date_filter}                                          //button[contains (@class, 'button--0')]//span[contains (@class, 'button__text')]
${gender_filter}                                        //button[contains(@title, 'Gender')]
${default_value_of_gender_filter}                       Gender
${patient_activation_status_filter}                     //button[contains(@title, 'Patient Activation Status')]/span
${default_value_of_patient_activation_status_filter}    Patient Activation Status
${care_team_filter}                                     //button[contains(@title, 'Care Team')]/span
${default_value_of_care_team_filter}                    Care Team
${current_status_filter}                                //button[contains(@title, 'Current Status')]/span
${default_value_of_current_status_filter}               Current Status
${age_filter}                                           //button/span[contains(text(), 'Age')]
${age_range_title}                                      //span[contains(text(), 'Age range') and contains(@class,'header__left')]
${age_range_min}                                        //span[contains(text(), 'Age range')]//following-sibling::div[contains(@class,'header__right')]//input[contains(@min,'0')][1]
${age_range_max}                                        //span[contains(text(), 'Age range')]//following-sibling::div[contains(@class,'header__right')]//input[contains(@max,'125')][2]
${age_range_min_default_value}                          //input[@min='0' and contains(@class, 'slider__right-input')]
${age_range_max_default_value}                          //input[@max='125' and contains(@class, 'slider__input')]
# ENROLLMENT REPORTS FILTERS
${patient_name_search_box}                              //input[contains(@type, 'text') and @placeholder='Patient Name']
${patient_name_search_list_box}                         //*[contains(@role,"listbox")]//span[contains(text(),"Type at least two characters")]
# SYMPTOM REPORTS FILTERS
${treament_module_type_filter}                          //button[contains(@title, 'Treatment Module Type')]/span
${default_value_of_treament_module_type_filter}         Treatment Module Type
${patient_group_filter}                                 //button[contains(@title, 'Patient Group')]/span
${default_value_of_patient_group_filter}                Patient Group
${symptom_type_filter}                                  //button[contains(@title, 'Symptom type')]/span
${default_value_of_symptom_type_filter}                 Symptom type
${patient_diagnosis_filter}                             //button[contains(@title, 'Patient Diagnosis')]/span
${default_value_of_patient_diagnosis_filter}            Patient Diagnosis
${priority_filter}                                      //button[contains(@title, 'Priority')]/span
${default_value_of_priority_filter}                     Priority
${care_team_member_filter}                              //button[contains(@title, 'Care team member')]/span
${default_value_of_care_team_member_filter}             Care team member
${reporter_role_filter}                                 //button[contains(@title, 'Reporter role')]/span
${default_value_of_reporter_role_filter}                Reporter role
${reporting_filter_selection_container}                 //*[contains(@class, "noona-multiple-selection-list__items")]
${reporting_filter_selection_item}                      ${reporting_filter_selection_container}//mat-list-option
${reset_button}                                         //button/span[contains(text(), 'Reset')]
${apply_button}                                         //button/span[contains(text(), 'Apply')]
${noona_reporting_multiselection_search_input}          //*[contains(@class, "noona-multiple-selection-list__search")]
${disclaimer_dialog}                                    //*[contains(@class,"noona-dialog-panel")]
${acknowledge_button}                                   //*[contains(@class,"noona-dialog-panel")]//*[contains(text(),"Acknowledge")]
${noona_disclaimer_dialog}                              //*[contains(@class, "noona-disclaimer-dialog-content")]
${noona_disclaimer_dialog_content_texts}                This platform allows you to print and export information which may include patient and/or personal information. It is your responsibility to secure patient and personal information after it is printed or exported from the system. You hereby affirm that you will take the appropriate action to secure printed documents and exported information.
${noona_disclaimer_acknowledge_button}                  //button//span[text()="Acknowledge "]
${noona_reporting_menu}                                 //*[contains(@class, "noona-menu-panel--reporting")]
${noona_reporting_menu_title}                           ${noona_reporting_menu}//span[text()="Reports"]
${noona_reporting_menu_item}                            //*[contains(@class, "noona-menu-panel--reporting")]//button
${noona_reporting_menu_item_title}                      //*[contains(@class, "noona-menu-panel--reporting")]//button//span[text()="{}"]
${noona_reporting_result_container}                     //*[contains(@class, 'noona-table-container--reporting')]
${noona_reporting_result_container_header}              //noona-reporting-result-set/*[1]
${noona_reporting_result_table}                         //noona-reporting-result-set/*[2]
${noona_reporting_result_table_header}                  //tr[contains(@class, "mdc-data-table__header-row")]
${noona_reporting_table_column_label}                   //*[@class="noona-table-multi-sorting__label"]
${noona_reporting_table_column_label_text_template}    //*[@class="noona-table-multi-sorting__label" and text()="{}"]

*** Keywords ***
Clinic User Is Redirected To Reporting Screen
    Wait Until Element Is Visible    ${reporting_page}
    Wait Until Element Is Visible    ${noona_reporting_menu}

Acknowledge Reporting Page Disclaimer If It Is Visible
    ${disclaimer_dialog_is_visible}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${noona_disclaimer_dialog}
    IF    '${disclaimer_dialog_is_visible}' == 'True'
        Page Should Contain    ${noona_disclaimer_dialog_content_texts}
        Try To Click Element    ${noona_disclaimer_acknowledge_button}
    END
    Wait Until Element Is Not Visible    ${noona_disclaimer_dialog}

Clinic User Can See A List Of Reports
    ${report_items}    Get Element Count    ${noona_reporting_menu_item}
    Log    The clinic has ${report_items} report items on reports list.
    @{reports_list}    Create List
    FOR    ${item}    IN RANGE    1    ${report_items} + 1
        ${report}    Get Text    //*[contains(@class, "noona-menu-panel--reporting")]//button[${item}]
        Append To List    ${reports_list}    ${report}
    END
    Log    ${reports_list}

Clinic User Selects A Report They Want To View
    [Arguments]    ${reporting_name}
    IF    '${reporting_name}' == 'Enrollment'
        Try To Click Element    ${report_name_from_list_enrollment}
    ELSE IF    '${reporting_name}' == 'Symptom'
        Try To Click Element    ${report_name_from_list_symptom}
    ELSE IF    '${reporting_name}' == 'Clinic Initiated Messages'
        Try To Click Element    ${report_name_from_list_clinicinitiatedmessages}
    ELSE IF    '${reporting_name}' == 'Case Management'
        Try To Click Element    ${report_name_from_list_casemanagement}
    END
    Wait Until Element Is Not Visible    ${reporting_menu}
    Wait Until Noona Loader Is Not Visible

Get All Options Under A Reporting Filter
    [Arguments]    ${filter_title}
    Try To Click Element    //button[contains(@title, '${filter_title}')]
    Wait Until Page Contains Element    ${reporting_filter_selection_container}
    ${options_count}    Get Element Count    ${reporting_filter_selection_item}
    @{list_of_options}    Create List
    FOR    ${index}    IN RANGE    2    ${options_count} + 1
        ${option_title}    Get Element Attribute    //mat-list-option[${index}]    title
        Append To List    ${list_of_options}    ${option_title}
    END
    Log    ${list_of_options}
    Set Test Variable    ${list_of_options}

Select A Reporting Filter Option
    [Documentation]    Common keyword used for other reporting filter which has multiselection box.
    [Arguments]    ${reporting_filter_title}    ${option}
    Set Test Variable    ${reporting_filter_title}
    Try To Click Element    //button[contains(@title, '${reporting_filter_title}')]
    Wait Until Element Is Visible    ${reporting_filter_selection_container}
    ${is_option_selected}    Get Element Attribute    //mat-list-option[@title="${option}"]    aria-selected
    IF    '${is_option_selected}' == 'false'
        Log    Option is not selected.
        Wait Until Element Is Visible    //*[contains(@title, "${option}")]//span[contains(@class, "checkbox")]
        Try To Click Element    //*[contains(@title, "${option}")]//span[contains(@class, "checkbox")]
        Sleep    1
    END
    ${is_option_selected}    Get Element Attribute    //mat-list-option[@title="${option}"]    aria-selected
    Should Be Equal    ${is_option_selected}    true
    Set Test Variable    ${filter_title_after_selection}    ${option}

Unselect A Reporting Filter Option
    [Documentation]    Common keyword used for other reporting filters. The keyword is usable right after the previous keyword "Select A Reporting Filter Option"
    [Arguments]    ${option}
    # First click on the filter title will close the dropdown. Hence, the keyword uses 2 clicks on the same element
    IF    '${option}' == 'Select all'
        Repeat Keyword
        ...    2 times
        ...    Try To Click Element
        ...    //button[contains(@title, '${reporting_filter_title}: All')]
    ELSE
        Repeat Keyword
        ...    2 times
        ...    Try To Click Element
        ...    //button[contains(@title, '${filter_title_after_selection}')]
    END
    Wait Until Page Contains Element    ${reporting_filter_selection_container}
    ${is_option_selected}    Get Element Attribute    //mat-list-option[@title="${option}"]    aria-selected
    IF    '${is_option_selected}' == 'true'
        Log    Option is selected.
        Wait Until Element Is Visible    //*[contains(@title, "${option}")]//span[contains(@class, "checkbox")]
        Click Element    //*[contains(@title, "${option}")]//span[contains(@class, "checkbox")]
    END
    ${is_option_selected}    Get Element Attribute    //mat-list-option[@title="${option}"]    aria-selected
    Should Be Equal    ${is_option_selected}    false
    Try To Click Element    //button[contains(@title, '${reporting_filter_title}')]

Search For A Option From A Reporting Filter
    [Documentation]    Common keyword used for other reporting filter which has an input search field.
    [Arguments]    ${reporting_filter_title}    ${search_string}
    Repeat Keyword    2 times    Try To Click Element    //button[contains(@title, '${reporting_filter_title}')]
    Wait Until Element Is Visible    ${noona_reporting_multiselection_search_input}
    Input Text    ${noona_reporting_multiselection_search_input}    ${search_string}
    ${selection_container_is_visible}    Run Keyword And Return Status
    ...    Wait Until Element Is Visible
    ...    ${reporting_filter_selection_container}
    IF    '${selection_container_is_visible}' == 'True'
        ${found_item_count}    Get Element Count    ${reporting_filter_selection_item}
        IF    '${found_item_count}' == '1'
            Log    The treatment module type is found.
        END
    ELSE
        Log    The treatment module type is not found.
    END

Get All ${report_name} Reporting Result Columns Labels
    Wait Until Page Contains Element    ${noona_reporting_table_column_label}
    ${number_of_result_columns}    Get Element Count    ${noona_reporting_table_column_label}
    ${number_of_result_columns_str}    Convert To String    ${number_of_result_columns}
    RETURN    ${number_of_result_columns_str}
