*** Settings ***
Resource    ${EXECDIR}${/}resources${/}nurse${/}login.resource
Resource    ${EXECDIR}${/}resources${/}management${/}login.resource
Resource    ${EXECDIR}${/}resources${/}management${/}admin_common.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}clinic_common.resource


*** Variables ***
${alphabets}                        abcdefghijklmnopqrstuvwxyzåäöABCEDEFGHIJKLMNOPQRSTUVWXYZÅÄÖ
${numbers}                          1234567890
${special_chars}                    éÉüÜœŒßøØæÆçã!#€%/()?@$∞[]≈≤≥,.-;:_‚…–/*-+{}
${information_text}                 ${alphabets}\n\n ${numbers}\n\n ${special_chars}
${displayed_text}                   ${alphabets}\n${numbers}\n${special_chars}
${language_tab}                     //*[@id="tab-en_GB-nurse-support-information"]
${save_button}                      //*[@id="save"]
${support_link}                     //*[@id="support-link"]
${support_link_admin}               //*[@id="nurse-support-information-link"]
${text_area_en}                     //*[@id="nurse-support-information-en_GB"]
${text_en}                          Nurse Support EN
${bulleted_list}                    - *First bullet*\n\n- Second bullet\n\n- *Third bullet*
${bulleted_first_item_italic}       //*[@class="showdown-content"]/ul/li[1]/p/em
${bulleted_second_item_italic}      //*[@class="showdown-content"]/ul/li[2]/p/em
${bulleted_third_item_italic}       //*[@class="showdown-content"]/ul/li[3]/p/em
${bulleted_second_item}             //*[@class="showdown-content"]/ul/li[2]/p
${preview_button}                   //button[contains(., 'Preview')]
${edit_button}                      //button[contains(., 'Edit')]
${showdown_content}                 class:showdown-content
${settings_dropdown}                //li[@class='dropdown']/a
${main_view}                        //*[@id="main"]
${last_clinic_in_list}              (//tr[@id='clinic-'])[last()]


*** Keywords ***
Change Support Page Content
    [Arguments]    ${text_area}=${text_area_en}    ${input_text}=${text_en}
    Login As Admin
    Wait Until Noona Loader Is Not Visible
    Wait Until Element Is Visible    ${settings_dropdown}
    Wait Until Element Is Visible    ${last_clinic_in_list}
    Execute Javascript    document.getElementById('settings-dropdown').click();
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${support_link_admin}    timeout=3
    WHILE    ${status}==${FALSE}
        Execute Javascript    document.getElementById('settings-dropdown').click();
        ${status}    Run Keyword And Return Status    Element Should Be Visible    ${support_link_admin}
    END
    Try To Click Element    ${support_link_admin}
    Try To Click Element    ${text_area}
    Try To Input Text    ${text_area}    ${input_text}
    Try To Click Element    ${save_button}
    Wait Until Page Contains    Changes saved
    Wait Until Page Does Not Contain    Changes saved    7s
    Element Should Be Disabled    ${save_button}

Check Bulleted List
    [Arguments]    ${user}
    IF    '${user}'=='nurse'
        Login As Nurse
    ELSE IF    '${user}'=='manager'
        Login As Nurse    email=${automated_tests_clinic}[default_manager]
    ELSE
        Fail
    END
    Wait Until Element Is Enabled    ${help_link}
    Try To Click Element    ${help_link}
    Wait Until Page Contains Element    ${support_link}
    Try To Click Element    ${support_link}
    Wait Until Page Contains Element    ${showdown_content}
    Page Should Contain Element    ${bulleted_first_item_italic}
    Page Should Not Contain Element    ${bulleted_second_item_italic}
    Page Should Contain Element    ${bulleted_second_item}
    Page Should Contain Element    ${bulleted_third_item_italic}

Check Support Information As Manager
    [Arguments]    ${expected_text}
    Login As Nurse    email=${automated_tests_clinic}[default_manager]
    Wait Until Page Contains Element    ${help_link}
    Try To Click Element    ${help_link}
    Wait Until Page Contains Element    ${support_link}
    Try To Click Element    ${support_link}
    Wait Until Page Contains Element    ${showdown_content}
    Element Text Should Be    ${showdown_content}    ${expected_text}

Check Support Information As Nurse
    [Arguments]    ${expected_text}
    Login As Nurse
    Wait Until Page Contains Element    ${help_link}
    Try To Click Element    ${help_link}
    Wait Until Page Contains Element    ${support_link}
    Try To Click Element    ${support_link}
    IF    """${expected_text}"""!="""${EMPTY}"""
        Wait Until Page Contains Element    ${showdown_content}
    END
    IF    """${expected_text}"""!="""${EMPTY}"""
        Element Text Should Be    ${showdown_content}    ${expected_text}
    END
    IF    """${expected_text}"""=="""${EMPTY}"""
        Element Text Should Be    ${main_view}    ${expected_text}
    END

Fill Support Page Content Over Limit
    [Arguments]    ${input_text}    ${text_area}=${text_area_en}
    Login As Admin
    Wait Until Page Contains Element    ${support_link_admin}
    Try To Click Element    ${settings_dropdown}
    Try To Click Element    ${support_link_admin}
    Try To Click Element    ${text_area}
    Try To Input Text    ${text_area}    ${input_text}

Preview Bulleted Support Information
    Press Keys    None    PAGE_UP
    Scroll Element Into View    ${preview_button}
    Try To Click Element    ${preview_button}
    Wait Until Page Contains Element    ${edit_button}
    Wait Until Page Contains Element    ${showdown_content}
    Page Should Contain Element    ${bulleted_first_item_italic}
    Page Should Not Contain Element    ${bulleted_second_item_italic}
    Page Should Contain Element    ${bulleted_second_item}
    Page Should Contain Element    ${bulleted_third_item_italic}

Preview Support Information
    [Arguments]    ${expected_text}
    Press Keys    None    PAGE_UP
    Scroll Element Into View    ${preview_button}
    Try To Click Element    ${preview_button}
    Wait Until Page Contains Element    ${edit_button}
    Wait Until Page Contains Element    ${showdown_content}
    Element Text Should Be    ${showdown_content}    ${expected_text}

Reset Support Page And Close Browsers
    Change Support Page Content
    Close All Browsers
