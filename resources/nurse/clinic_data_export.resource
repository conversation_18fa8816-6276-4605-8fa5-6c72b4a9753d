*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources/nurse/clinic_common.resource


*** Variables ***
${clinic_data_export_link}                      clinic-data-export-link
${request_description_textarea}                 //label[text()='Request description']/../textarea
${data_export_request_send_button}              //button[@class='requestButton btn btn-primary btn-centered']
${data_export_request_being_processed_text}     Your data export request is being processed. Please come back later to download the export file. If necessary, you can contact your Varian representative.
${data_export_download_link}                    //a[@id='action-edit-patient']
${clinic_data_export_page_info_text}            This functionality will export all data from the clinic for a certain group of patients. Please select at least one search option in all the fields and describe the purpose of the data export in the text field.
${saving_requests_banner_message}               Saving of the request was successful


*** Keywords ***
Navigate To Clinic Data Export
    Wait Until Element Is Enabled    ${clinic_menu}
    Try To Click Element    ${clinic_menu}
    Try To Click Element    ${clinic_data_export_link}

Send Data Export Request
    [Arguments]    ${export_type}    ${care_team}    ${module}    ${icd}    ${data_type}    ${request_text}
    Select Data Export Filter    Export Type    ${export_type}
    Select Data Export Filter    Care teams    ${care_team}
    Select Data Export Filter    Treatment modules    ${module}
    Select Data Export Filter    ICD-10 code    ${icd}
    Select Data Export Filter    Data Types    ${data_type}
    Input Text    ${request_description_textarea}    ${request_text}
    Try To Click Element    ${data_export_request_send_button}

Select Data Export Filter
    [Arguments]    ${filter}    ${option}
    Try To Click Element    //label[text()=" ${filter} "]/..//span[@class='ng-arrow-wrapper']
    Try To Click Element    (//div[@title='${option}'])[last()]
    IF    '${filter}'!='Export Type'
        Try To Click Element    //label[text()=" ${filter} "]/..//span[@class='ng-arrow-wrapper']
    END
