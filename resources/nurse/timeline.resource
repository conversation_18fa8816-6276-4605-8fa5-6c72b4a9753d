*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}common.resource


*** Variables ***
${timeline_tab}                                             tab-nurse-diary
${current_month}                                            //div[@class='month-selector-wrapper']
${summary_section_first_symptom_expansion_panel}            (//noona-symptom-group-expansion-panel)[1]
${reported_symptom_div}                                     //div[@class='symptom-group-panel-header']
${summary_section_first_symptom_expansion_panel_name}       (//span[starts-with(@class, 'expansion-panel-header-col')])[1]
${summary_section_first_symptom_entry_name}                 (//span[starts-with(@class, 'timeline-content-header-col')])[1]
${summary_section_first_symptom_entry_summary}              (//div[@class='symptom-summary-content'])[1]
${summmary_section_first_symptom_entry_photo}               (${summary_section_first_symptom_entry_summary})//*[contains(@class,'dz-image')]//img
${chart_last_data_point}                                    //*[starts-with(@class, 'highcharts-series highcharts-series-0 highcharts-xrange-series')]/*[starts-with(@class, 'highcharts-point')][last()]
${tooltip}                                                  //div[starts-with(@class, 'highcharts-label highcharts-tooltip')]
${tooltip_reported_on}                                      (${tooltip})//div[@class='symptom-graph-tooltip-details']/p[@class='symptom-graph-tooltip-reported']
${timeline_previous_month}                                  prev-month
${timeline_next_month}                                      next-month
${timeline_reported_symptoms}                               //div[@class='timeline-section'][1]
${summary_section_symptom_intensity}                        //mat-expansion-panel-header[.//span[starts-with(@class, 'expansion-panel-header-col') and text()='{}']]//span[@class='symptom-severity-text']
${summary_section_symptom_group_header}                     //mat-expansion-panel-header[.//span[contains(text(),"{}")]]
${summary_section_first_symptom_expansion_panel_date}       (//mat-expansion-panel[.//span[contains(text(),'{}')]]//*[starts-with(@class, 'timeline-content-header-col')])[3]
${chart_yaxis_labels}                                       //*[@class='highcharts-axis-labels highcharts-yaxis-labels']/*[position() mod 4 = 3]
${loading_spinner}                                          //div[starts-with(@class, 'noona-loader')]
${photo_thumbnail_src_tst}                                  blob:https://test.clinic.noonatest.com/
${photo_thumbnail_src_stg}                                  blob:https://staging.clinic.noonatest.com/
${no_reported_symptoms_text}                                No reported symptoms this month
${timeline_summmary_section_qol_card}                       //*[contains(@class, "timeline-expansion")]//noona-timeline-qol-card
${timeline_summmary_section_next_of_kin_info_text}          //*[contains(@class, "next-of-kin-info")]//*[text()="Information entered by a caregiver"]

*** Keywords ***
Navigate To Timeline Tab
    Wait Until Element Is Visible    ${timeline_tab}
    Try To Click Element    ${timeline_tab}

Correct Timeline Month Is Displayed
    [Arguments]    ${month_and_year}
    Wait Until Element Contains    ${current_month}    ${month_and_year}

Symptom Is Visible Under Summary Tab
    [Arguments]    ${symptom}
    Wait Until Element Is Visible    ${summary_section_first_symptom_expansion_panel}
    Scroll Element Into View    ${summary_section_first_symptom_expansion_panel}
    Element Should Contain    ${summary_section_first_symptom_expansion_panel_name}    ${symptom}

Symptom Entry Is Listed Below Symptom Type
    [Arguments]    ${symptom}
    Wait Until Element Is Not Visible    ${loading_spinner}
    Click Element    ${summary_section_first_symptom_expansion_panel}
    Wait Until Element Contains    ${summary_section_first_symptom_entry_name}    ${symptom}

Symptom Details Is Displayed Under Symptom Entry
    [Arguments]    ${photo_uploaded}=no
    Wait Until Element Is Visible   ${summary_section_first_symptom_entry_summary}
    Scroll Element Into View    ${summary_section_first_symptom_entry_summary}
    Capture Screenshot
    IF    '${photo_uploaded}'=='yes'    Verify If Photo Uploaded Is Displayed

Verify If Photo Uploaded Is Displayed
    Wait Until Element Is Visible    ${summmary_section_first_symptom_entry_photo}
    ${attr}    Get Element Attribute    ${summmary_section_first_symptom_entry_photo}    src
    IF    '${ENVIRONMENT}'=='test'
        Should Contain    ${attr}    ${photo_thumbnail_src_tst}
    ELSE IF    '${ENVIRONMENT}'=='staging'
        Should Contain    ${attr}    ${photo_thumbnail_src_stg}
    END

Symptom Tooltip Is Displayed
    [Arguments]    ${symptom}
    Hover Chart Data Point
    Wait Until Element Is Visible    ${tooltip}
    Element Should Contain    ${tooltip}    ${symptom}

Hover Chart Data Point
    [Documentation]    'Get Current Month And Year' keyword should be run before this
    Scroll Element Into View    ${chart_last_data_point}
    Mouse Over    ${chart_last_data_point}

Individual Information Is Listed For Multiple Entries
    Wait Until Element Is Visible    ${tooltip_reported_on}

Go To Timeline Previous Month
    Scroll Element Into View    ${timeline_previous_month}
    Try To Click Element    ${timeline_previous_month}

Timeline Entry Container Is Visible
    Wait Until Page Contains Element    ${timeline_entries_container}

Symptom Intensity Value Is Displayed
    [Arguments]    ${symptom}    ${expected_intensity}
    ${actual_symptom}    Format String    ${summary_section_symptom_intensity}    ${symptom}
    Wait Until Element Is Visible    ${actual_symptom}
    ${actual_intensity}    Get Text    ${actual_symptom}
    Should Be Equal    ${expected_intensity}    ${actual_intensity}

Verify Symptom Date
    [Arguments]    ${symptom}
    ${symptom_group}    Format String    ${summary_section_symptom_group_header}    ${symptom}
    Try To Click Element    ${symptom_group}
    ${today}    Get Current Date    result_format=%d.%m.%Y
    ${symptom_date}    Format String    ${summary_section_first_symptom_expansion_panel_date}    ${symptom}
    Wait Until Element Is Visible    ${symptom_date}
    ${symptom_date}    Get Text    ${symptom_date}
    Should Contain    ${symptom_date}    ${today}

Multiple Symptom Entries Are Displayed Under Each Other
    [Arguments]    ${symptom_1}    ${symptom_2}
    ${symptoms}    Get Element Count    ${chart_yaxis_labels}
    ${symptoms}    Convert To String    ${symptoms}
    Should Be Equal    2    ${symptoms}
    ${actual_symptom_1}    Get Text    ${chart_yaxis_labels}\[1]
    ${actual_symptom_2}    Get Text    ${chart_yaxis_labels}\[2]
    IF    "${actual_symptom_1}"=="${symptom_1}"
        Should Be Equal    ${actual_symptom_2}    ${symptom_2}
    ELSE IF    "${actual_symptom_1}"=="${symptom_2}"
        Should Be Equal    ${actual_symptom_2}    ${symptom_1}
    ELSE
        Fail    Symptoms do not match!
    END
    ${today}    Get Current Date    result_format=%Y-%m-%d
    Element Should Be Visible    //*[contains(concat(' ',normalize-space(@class),' '),' fatigue_${today} ')]
    Element Should Be Visible    //*[contains(concat(' ',normalize-space(@class),' '),' otherSymptom_${today} ')]

Select First Symptom Details From Summary Section
    Try To Click Element    ${summary_section_first_symptom_expansion_panel}

Expand A Symptom Or Questionnaire From Timeline Summary Section By Name
    [Arguments]    ${symptom_or_questionnaire_name}
    Wait Until Page Contains Element    //span[contains(@class, "expansion-panel") and text()="${symptom_or_questionnaire_name}"]
    Scroll Element Into View    //span[contains(@class, "expansion-panel") and text()="${symptom_or_questionnaire_name}"]
    ${item_count}    Get Element Count    //*//span[contains(@class, "expansion-panel") and text()="${symptom_or_questionnaire_name}"]
    Try To Click Element    //span[contains(@class, "expansion-panel") and text()="${symptom_or_questionnaire_name}"]