*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource
Resource    ${EXECDIR}${/}resources${/}nurse${/}general_information.resource


*** Variables ***
${list_of_patient_rows}                     (//tbody)[last()]/tr
${patient_list_care_team_dropdown}          //*[@id='patientList__careTeam--filter']//span[@class='ng-arrow-wrapper']
${patient_list_filter_option}               //div[@title='{}']
${selected_care_teams}                      (//*[@id='patientList__careTeam--filter']//div[@class='ng-value ng-star-inserted'])    # selected from the dropdown input
${remove_all_selections_option}             //button[@data-testid='btn-removeAll']
${care_team_filter_text_input}              //*[@id='patientList__careTeam--filter']//input
${view_other_team_also_button}              view-other-team-also-button
${selected_status}                          (//*[@id='patientList__status--filter']//div[@class='ng-value ng-star-inserted'])    # selected from the dropdown input
${patient_list_status_dropdown}             //*[@id='patientList__status--filter']//span[@class='ng-arrow-wrapper']
${status_filter_text_input}                 //*[@id='patientList__status--filter']//input
${select_all_filters_option}                //button[@data-testid='btn-selectAll']
${username_locked_status}                   Username locked
${no_scheduled_questionnaires_status}       No scheduled questionnaires
${proxy_patient_status}                     Proxy patient
${username_sent_status}                     Username sent
${login_reminder_sent_status}               Login reminder sent
${locked_by_user_status}                    Locked by user
${email_delivery_problems_status}           Email delivery problems
${username_active_status}                   Username active
${welcome_to_noona_text}                    Welcome to Noona
${view_patients_outside_noona_text}         View patients also outside of my teams?
${selected_filter_locator}                  //div[contains(text(),"{}")]/../..
${selected_filter_attr}                     ng-option-selected
${first_patient}                            patient-0


*** Keywords ***
Filter Patients By Care Team
    [Arguments]    ${option}
    Try To Click Element    ${patient_list_care_team_dropdown}    wait_in_seconds=7x
    Try To Click Element    ${remove_all_selections_option}
    Input Text    ${care_team_filter_text_input}    ${option}
    Click Option From Patient List Filter    ${option}
    Try To Click Element    ${patient_list_care_team_dropdown}
    Sleep    1

Select All Care Teams In Patient List
    Try To Click Element    ${patient_list_care_team_dropdown}    wait_in_seconds=7x
    Try To Click Element    ${remove_all_selections_option}
    Try To Click Element    ${select_all_filters_option}
    Try To Click Element    ${patient_list_care_team_dropdown}    wait_in_seconds=7x

Filter Patients By Status
    [Arguments]    ${option}
    Try To Click Element    ${status_filter_text_input}
    Try To Click Element    ${remove_all_selections_option}
    Input Text    ${status_filter_text_input}    ${option}
    Click Option From Patient List Filter    ${option}
    Try To Click Element    ${patient_list_status_dropdown}
    Sleep    1

Exclude Status From Filter
    [Arguments]    ${option}
    Try To Click Element    ${status_filter_text_input}
    Try To Click Element    ${remove_all_selections_option}
    Try To Click Element    ${select_all_filters_option}
    Input Text    ${status_filter_text_input}    ${option}
    Click Option From Patient List Filter    ${option}
    Try To Click Element    ${patient_list_status_dropdown}

Click Option From Patient List Filter
    [Arguments]    ${option}
    ${element}    Format String    ${patient_list_filter_option}    ${option}
    Try To Click Element    ${element}

Get Selected Care Teams
    Sleep    1
    @{selected_care_team_list}    Create list
    @{elements}    Get WebElements    ${selected_care_teams}
    FOR    ${element}    IN    @{elements}
        ${text}    Get Text    ${element}
        Append To List    ${selected_care_team_list}    ${text}
    END
    Set Test Variable    ${selected_care_team_list}

Get Selected Status
    Sleep    1
    @{selected_status_list}    Create list
    @{elements}    Get WebElements    ${selected_status}
    FOR    ${element}    IN    @{elements}
        ${text}    Get Text    ${element}
        Append To List    ${selected_status_list}    ${text}
    END
    Set Test Variable    ${selected_status_list}

List Of Patients Filter Is Selected
    [Documentation]    Verifies that the care team is selected in the care team or filter dropdown
    [Arguments]    ${option}
    ${element}    Format String    ${selected_filter_locator}    ${option}
    ${attr}    Get Element Attribute    ${element}    class
    Should Contain    ${attr}    ${selected_filter_attr}

List Of Patients Filter Is Unselected
    [Arguments]    ${option}
    ${element}    Format String    ${selected_filter_locator}    ${option}
    ${attr}    Get Element Attribute    ${element}    class
    Should Not Contain    ${attr}    ${selected_filter_attr}

Get SSN List
    @{ssn_list}    Create List
    @{ssns}    Get WebElements    (${list_of_patient_rows})/td[1]
    Capture Screenshot    #to see which care team's patients are collected
    FOR    ${ssn}    IN    @{ssns}
        ${text}    Get Text    ${ssn}
        Append To List    ${ssn_list}    ${text}
    END
    Set Test Variable    ${ssn_list}

Lock Patients To Exclude From Announcement
    [Documentation]    Makes sure that only one patient will get sms notification
    [Arguments]    ${nurse}    ${care_team}
    Login As Nurse    ${nurse}
    Navigate To Patient Page
    Go To List Of Patients Tab
    Filter Patients By Care Team    ${care_team}
    Exclude Status From Filter    ${locked_by_user_status}
    ${status1}    Run Keyword And Return Status    Wait Until Element Is Visible    ${first_patient}    timeout=3s
    ${status2}    Run Keyword And Return Status    Wait Until Page Contains    No patients found    timeout=3s
    WHILE    ${status1} and ${status2}==${FALSE}
        Try To Click Element    ${first_patient}
        Wait Until Element Is Visible    ${lock_button}
        Try To Click Element    ${lock_button}
        Wait Until Page Contains    ${patient_locked_banner_text}
        Try To Click Banner Message
        Return To Patients
        ${status1}    Run Keyword And Return Status    Wait Until Element Is Visible    ${first_patient}    timeout=3s
    END
    Close Browser

Click First Patient From Patient List
    Wait Until Element Is Visible    ${first_patient}
    Try To Click Element    ${first_patient}

Go To List Of Patient And Select First Patient
    Go To Patients > List Of Patients
    Select All Care Teams In Patient List
    ${status}    Run Keyword And Return Status    Wait Until Element Is Visible    ${view_other_team_also_button}    timeout=3s
    IF    ${status}    Try To Click Element    ${view_other_team_also_button}
    Click First Patient From Patient List