*** Settings ***
Resource    ${EXECDIR}${/}resources${/}libraries.resource


*** Variables ***
${ios_keyboard_done}                        //XCUIElementTypeButton[@name='Done']
${banner_message_element}                   //div[contains(@class, 'toast-message')]
${google_translation_popup_close_button}    //*[@resource-id ="com.android.chrome:id/infobar_close_button"]


*** Keywords ***
Set Up Mobile Environment
    [Documentation]    Used for testing with virtual device
    Open Application    ${REMOTE_URL}
    ...    platformName=${PLATFORM_NAME}
    ...    platformVersion=${PLATFORM_VERSION}
    ...    deviceName=${DEVICE_NAME}
    ...    automationName=UiAutomator2
    ...    newCommandTimeout=2500
    ...    noReset=true
    ...    autoWebview=true
    ...    setWebContentsDebuggingEnabled=true
    ...    appActivity=${Activity_NAME}
    ...    appPackage=${PACKAGE_NAME}

Setup Native App In Browserstack
    [Documentation]    Used for testing with Browserstack
    [Arguments]    ${passcode}=${ENABLE_PASSCODE}
    Open Application    ${REMOTE_URL}
    ...    browserstack.user=${SERVICE_ACCOUNT_USERNAME}
    ...    browserstack.key=${SERVICE_ACCOUNT_PASSWORD}
    ...    app=bs://${APP}
    ...    newCommandTimeout=2500
    ...    project=test project
    ...    build=Test Mobile
    ...    name=${TEST_NAME}
    ...    platformName=${PLATFORM_NAME}
    ...    platformVersion=${PLATFORM_VERSION}
    ...    deviceName=${DEVICE_NAME}
    ...    autoWebview=true
    ...    browserstack.enablePasscode=${passcode}
    ...    strict_ssl=False
    ...    browserstack.timezone=Helsinki
    @{native_app_run_specs}    Create List    ${SERVICE_ACCOUNT_USERNAME}    ${SERVICE_ACCOUNT_PASSWORD}
    ...    ${APP}    ${TEST_NAME}    ${PLATFORM_NAME}    ${PLATFORM_VERSION}    ${DEVICE_NAME}
    Log List    ${native_app_run_specs}

Try To Click Native App Element
    [Arguments]    ${element}
    Wait Until Element Is Visible    ${element}    timeout=7s
    Wait Until Keyword Succeeds    5s    0.2s    Click Element    ${element}

Try To Input Native App Text
    [Arguments]    ${element}    ${text}
    Wait Until Element Is Visible    ${element}    timeout=7s
    Wait Until Keyword Succeeds    5s    0.2s    Input Text    ${element}    ${text}

Hide Mobile Keyboard
    IF    '${PLATFORM_NAME}'=='android'
        Hide Keyboard
    ELSE
        Try To Click Native App Element    ${ios_keyboard_done}    # only works if there's "Done" on the keyboard
    END

Patient Is In The Right Page
    [Arguments]    ${page_header}
    Sleep    1    # to avoid stale element
    Wait Until Element Is Visible    //*[@id='${diary_page_title}']
    Generic: Element Should Contain    //*[@id='${diary_page_title}']    ${page_header}

Dismiss Translation Popup
    [Documentation]    On Android, use this keyword to dismiss translation pop-up
    Wait Until Element Is Visible    ${google_translation_popup_close_button}
    Click Element    ${google_translation_popup_close_button}
