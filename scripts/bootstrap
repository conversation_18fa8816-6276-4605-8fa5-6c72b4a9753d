#!/bin/bash

create_virtualenv() {
  echo -e "\033[1;37m## Configuring virtualenv \033[1;34m$VENV_NAME\033[1;37m...\033[0m"

  python3 -m venv "$VENV_HOME/$VENV_NAME"
  source "$VENV_HOME/$VENV_NAME/bin/activate"
  python3 -m pip install --upgrade pip wheel
  pip install -r requirements.txt

  echo "virtualenv configured."
}

add_homebrew_to_path() {
  SHELL_PROFILE=""

  # Determine the shell profile based on the current shell
  case "$SHELL" in
    */zsh)
      SHELL_PROFILE="$HOME/.zshrc"
      ;;
    */bash)
      SHELL_PROFILE="$HOME/.bash_profile"
      ;;
    */fish)
      SHELL_PROFILE="$HOME/.config/fish/config.fish"
      ;;
    */tcsh|*/csh)
      SHELL_PROFILE="$HOME/.tcshrc"
      ;;
    *)
      echo "Unsupported shell: $SHELL"
      return 1
      ;;
  esac

  # Add Homebrew to the PATH in the determined shell profile
  if [ -n "$SHELL_PROFILE" ]; then
    echo "Adding Homebrew to PATH in $SHELL_PROFILE"
    if [[ $(uname -m) = "arm64" ]]; then
      echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> "$SHELL_PROFILE"
      eval "$(/opt/homebrew/bin/brew shellenv)"
    else
      echo 'eval "$(/usr/local/bin/brew shellenv)"' >> "$SHELL_PROFILE"
      eval "$(/usr/local/bin/brew shellenv)"
    fi
  else
    echo "Failed to determine shell profile."
    return 1
  fi
}

install_homebrew() {
  echo -e "\033[1;37m## Installing Homebrew...\033[0m"

  if ! command -v brew > /dev/null; then
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install.sh)"
    add_homebrew_to_path
    echo "Homebrew installed."
  else
    echo "Homebrew already installed."
    add_homebrew_to_path
  fi
}

install_homebrew_bundle() {
  echo -e "\033[1;37m## Installing Homebrew bundle...\033[0m"

  # Run brew bundle and capture output and errors
  if brew bundle > /dev/null 2>&1; then
    echo "Homebrew bundle installed successfully."
  else
    echo "Failed to install Homebrew bundle. See details below:"
    brew bundle
  fi
}

echo -e "\033[1;37m# Setting up environment\033[0m"

mkdir -p "$VENV_HOME"

install_homebrew

install_homebrew_bundle

brew install python3

create_virtualenv

# Convenience to install chromedriver and getting it added to the local PATH environment variable
if ! which chromedriver > /dev/null; then
  echo "chromedriver not found, installing..."
  brew install chromedriver
else
  echo "chromedriver is already installed"
fi

# Upgrade chromedriver to the latest version
brew upgrade chromedriver

# Mark the installed chromedriver as a trusted application
CHROMEDRIVER_PATH=$(brew info chromedriver | grep "/chromedriver" | grep -v ".rb$" | grep -v "^http" | cut -d' ' -f1 | paste -d'/' - -)
if xattr $CHROMEDRIVER_PATH | grep "com.apple.quarantine" > /dev/null; then
  echo "Removing apple quarantine attribute from brew installed chromedriver binary path ${CHROMEDRIVER_PATH}"
  xattr -d com.apple.quarantine $CHROMEDRIVER_PATH
else
  echo "No quarantine attribute found on ${CHROMEDRIVER_PATH}"
fi