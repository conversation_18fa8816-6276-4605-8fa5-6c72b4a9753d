*** Settings ***
Documentation     RPA for Jama.
...               Get test case counts from verification folders
Library           OperatingSystem
Library           String
Library           RequestsLibrary
Library           JSONLibrary
Library           Collections

*** Variables ***
${client_id}    *
${client_secret}    *
${manual_folder_id}     910145
${automated_folder_id}  910146

*** Tasks ***
Test Automation Coverage
    Get Manual Case Count
    Get Automated Case Count
    Test Automation Coverage
    [Teardown]  Delete All Sessions

*** Keywords ***
Get authToken by Password Authentication
    Create Session  hook    https://${client_id}:${client_secret}@varian-medical.jamacloud.com/rest  verify=${True}
    ${data}      Create Dictionary     grant_type=client_credentials
    ${headers}      Create Dictionary     Content-Type=application/x-www-form-urlencoded
    ${resp}        POST On Session    hook    /oauth/token    data=${data}     headers=${headers}
    Should Be Equal As Strings  ${resp.status_code}     200
    ${accessToken}    evaluate    $resp.json().get("access_token")
    Set Test Variable   ${accessToken}

Create Session And Headers
    Create Session  hook    https://varian-medical.jamacloud.com/rest
    ${headers}      Create Dictionary     Content-Type=application/json     Authorization=Bearer ${accessToken}
    Set Test Variable   ${headers}

Get Manual Case Count
    Get authToken by Password Authentication
    Create Session And Headers
    ${resp}        GET On Session    hook    url=/v1/items/${manual_folder_id}/children   headers=${headers}  data=${EMPTY}
    ${json_content}     Convert String to JSON  ${resp.content}
    ${total_manual_cases_count}    Get Value From Json     ${json_content}     $..totalResults
    ${total_manual_cases_count}   Convert To String    ${total_manual_cases_count}
    ${total_manual_cases_count}   Remove String  ${total_manual_cases_count}  [   ]
    ${total_manual_cases_count}   Convert To Integer  ${total_manual_cases_count}
    Set Test Variable   ${total_manual_cases_count}
    Log To Console  ${total_manual_cases_count} manual cases

Get Automated Case Count
    Get authToken by Password Authentication
    Create Session And Headers
    ${resp}        GET On Session    hook    url=/v1/items/${automated_folder_id}/children   headers=${headers}  data=${EMPTY}
    ${json_content}     Convert String to JSON  ${resp.content}
    ${total_automated_cases_count}   Get Value From Json     ${json_content}     $..totalResults
    ${total_automated_cases_count}   Convert To String    ${total_automated_cases_count}
    ${total_automated_cases_count}   Remove String  ${total_automated_cases_count}  [   ]
    ${total_automated_cases_count}   Convert To Integer  ${total_automated_cases_count}
    Set Test Variable   ${total_automated_cases_count}
    Log To Console  ${total_automated_cases_count} automated cases

Test Automation Coverage
    ${test_automation_coverage}     Evaluate    (${total_automated_cases_count}/(${total_automated_cases_count}+${total_manual_cases_count}))*100
    Log To Console  Test automation coverage ${test_automation_coverage}%