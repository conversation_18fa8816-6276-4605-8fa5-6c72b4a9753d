*** Settings ***
Documentation     RPA for Jama.
...               - Preconditions: Get client id and secret from your Jama profile. Go to "My Details" and "Set API Credentials".
...               - Search API id for the cycle under test. This you can find by using filtering.
...               - Update attachment api id
...               REMEMBER TO UPDATE THE CYCLE ID VARIABLE!
...               - To run all tasks: robot  jama/jama_remove.robot
...               - To run individual task: robot  --include "tag name" jama/jama_remove.robot
Library           OperatingSystem
Library           String
Library           RequestsLibrary
Library           JSONLibrary
Library           Collections

*** Variables ***
${client_id}    *
${client_secret}    *
${cycle_id}    354539
${project_id}   22
${attachment_id}    365341

*** Tasks ***
Remove Attachment From Test Runs
    [Tags]  remove_attachment
    Get Test Run IDs From The Test Cycle
    Remove Attachment From Test Runs
    [Teardown]  Delete All Sessions

*** Keywords ***
Get authToken by Password Authentication
    Create Session  hook    https://${client_id}:${client_secret}@varian-medical.jamacloud.com/rest  verify=${True}
    ${data}      Create Dictionary     grant_type=client_credentials
    ${headers}      Create Dictionary     Content-Type=application/x-www-form-urlencoded
    ${resp}        POST On Session    hook    /oauth/token    data=${data}     headers=${headers}
    Should Be Equal As Strings  ${resp.status_code}     200
    ${accessToken}    evaluate    $resp.json().get("access_token")
    Set Test Variable   ${accessToken}

Create Session And Headers
    Create Session  hook    https://varian-medical.jamacloud.com/rest
    ${headers}      Create Dictionary     Content-Type=application/json     Authorization=Bearer ${accessToken}
    Set Test Variable   ${headers}

Get Test Run IDs From The Test Cycle
    Get authToken by Password Authentication
    Create Session And Headers
    ${resp}        GET On Session    hook    url=/v1/testruns?testCycle=${cycle_id}   headers=${headers}  data=${EMPTY}
    Should Be Equal As Strings  ${resp.status_code}     200
    ${body}    Evaluate    json.loads('''${resp.content}''')    json
    ${test_run_ids}    Get Value From Json     ${body}     $..id
    Convert To List     ${test_run_ids}
    Set Test Variable   ${test_run_ids}

Remove Attachment From Test Runs
    FOR    ${test_run_id}    IN     @{test_run_ids}
        ${json_data}    Load JSON From File     ${EXECDIR}${/}jama${/}associate_attachment_payload.json
        ${updated_file_data}    Update Value To Json    ${json_data}    $..attachment    ${attachment_id}
        ${headers}      Create Dictionary     Content-Type=application/json     Authorization=Bearer ${accessToken}
        ${resp}        Delete Request    hook    /v1/testruns/${test_run_id}/attachments/${attachment_id}    data=${updated_file_data}     headers=${headers}
        Run Keyword And Ignore Error    Should Be Equal As Strings  ${resp.status_code}     204
    END