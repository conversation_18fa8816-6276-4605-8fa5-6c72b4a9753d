#!/bin/sh

# This is common robot runner script used by both CI and local runner scripts

source ./common-utils.sh

timestamp="$(date +%Y%m%d-%H%M%S)"
SLACK_WEBHOOK="*******************************************************************************"
RDR_SLACK_WEBHOOK="*******************************************************************************"
ENVIRONMENT_ARG="test"
DEBUG_VARIABLES="--variable BROWSER:chrome --variable WINDOW_WIDTH:1200 --variable WINDOW_HEIGHT:1600"
#Variables below are used only when running locally, uncomment if needed
# These variables are used to run PROD smoke tests. Uncomment when debugged locally
NURSE_USERNAME="<EMAIL>"
NURSE_PASSWORD="Password-1"
PATIENT_USERNAME="<EMAIL>"
PATIENT_PASSWORD="Password-2"

if [[ "$RUN_IN_GITLAB" == "true" ]]; then
  export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_dev
  export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_dev
fi

# function to run robot test cases
run_test_cases() {
  pabot --pabotlib \
  --processes "$CONCURRENCY" \
  --outputdir "${outputdir}" \
  --report final_report \
  --argumentfile arguments.txt \
  --variable TEST_TIMEOUT:30m \
  "$@" \
  --include "$INCLUDE_TAGS" \
  --exclude "$EXCLUDE_TAGS" \
  --variablefile data/vars.yaml \
  --variablefile data/patients/web-app.yaml \
  --variablefile data/vars-$ENVIRONMENT_ARG.yaml \
  --variablefile data/clinics/clinics.yaml \
  --variablefile data/symptoms/symptom_types.yaml \
  --variablefile data/modules/modules.yaml \
  --variablefile data/questionnaires/questionnaire_types/questionnaire_types.yaml \
  --variable NURSE_USERNAME:"${NURSE_USERNAME}" \
  --variable NURSE_PASSWORD:"${NURSE_PASSWORD}" \
  --variable PATIENT_USERNAME:"${PATIENT_USERNAME}" \
  --variable PATIENT_PASSWORD:"${PATIENT_PASSWORD}" \
  $(if [ "$DEBUG" ]; then echo "$DEBUG_VARIABLES"; else echo ""; fi) \
  --xunit xunit \
  test

  retval=$?
  return $retval
}

# function to run cases in test level split
run_test_level_split() {
  pabot --pabotlib \
  --processes "$CONCURRENCY" \
  --testlevelsplit \
  --outputdir "${outputdir}" \
  --report final_report \
  --argumentfile arguments.txt \
  --variable TEST_TIMEOUT:30m \
  "$@" \
  --include "$INCLUDE_TAGS" \
  --exclude "$EXCLUDE_TAGS" \
  --variablefile data/vars.yaml \
  --variablefile data/patients/web-app.yaml \
  --variablefile data/vars-$ENVIRONMENT_ARG.yaml \
  --variablefile data/clinics/clinics.yaml \
  --variablefile data/symptoms/symptom_types.yaml \
  --variablefile data/modules/modules.yaml \
  --variablefile data/questionnaires/questionnaire_types/questionnaire_types.yaml \
  --variable NURSE_USERNAME:"${NURSE_USERNAME}" \
  --variable NURSE_PASSWORD:"${NURSE_PASSWORD}" \
  --variable PATIENT_USERNAME:"${PATIENT_USERNAME}" \
  --variable PATIENT_PASSWORD:"${PATIENT_PASSWORD}" \
  $(if [ "$DEBUG" ]; then echo "$DEBUG_VARIABLES"; else echo ""; fi) \
  --xunit xunit \
  test

  retval=$?
  return $retval
}

# function to run failed robot test cases again
rerun_failed_cases() {
  pabot --pabotlib \
  --processes "$CONCURRENCY" \
  --rerunfailed "${outputdir}"/output.xml \
  --outputdir "${outputdir}" \
  --output output_rerun.xml \
  --argumentfile arguments.txt \
  --variable TEST_TIMEOUT:30m \
  --timestamp \
  --variablefile data/vars.yaml \
  --variablefile data/patients/web-app.yaml \
  --variablefile data/vars-$ENVIRONMENT_ARG.yaml \
  --variablefile data/clinics/clinics.yaml \
  --variablefile data/symptoms/symptom_types.yaml \
  --variablefile data/modules/modules.yaml \
  --variablefile data/questionnaires/questionnaire_types/questionnaire_types.yaml \
  --variable NURSE_USERNAME:"${NURSE_USERNAME}" \
  --variable NURSE_PASSWORD:"${NURSE_PASSWORD}" \
  --variable PATIENT_USERNAME:"${PATIENT_USERNAME}" \
  --variable PATIENT_PASSWORD:"${PATIENT_PASSWORD}" \
  test

  retval=$?
  return $retval
}

# function to combine two robot run results
combine_robot_runs() {
  rebot \
  --flattenkeywords for \
  --flattenkeywords while \
  --outputdir "${outputdir}" \
  --report final_report \
  --output output.xml \
  --xunit xunit \
  --merge "${outputdir}"/output.xml "${outputdir}"/output_rerun.xml
  echo "Reports merged."
}

combine_all_results() {
  echo "This is the env $ENVIRONMENT_ARG"
  S3_BUCKET="s3://noona-test-reports"

  if [[ "$NIGHTLY_RUN" == "true" ]] ; then
    S3_FILE_PREFIX="web/output/$ENVIRONMENT_ARG/"
  elif [[ "$DAILY_RUN" == "true" ]] ; then
    S3_FILE_PREFIX="daily_run/web/output/$ENVIRONMENT_ARG/"
  else
    S3_FILE_PREFIX="others/web/output/$ENVIRONMENT_ARG/"
  fi

  if [[ "$NIGHTLY_RUN" == "true" ]]; then
    FILES=$(aws s3 ls "$S3_BUCKET/$S3_FILE_PREFIX" --recursive | awk '{print $4}' | grep -E "$ENVIRONMENT_ARG/[0-9]{8}-[0-9]{6}/output\.xml" | tail -n 3)

    FILE1=$(echo "$FILES" | head -n 1)
    FILE2=$(echo "$FILES" | head -n 2 | tail -n 1)
    FILE3=$(echo "$FILES" | tail -n 1)

    if [ -z "$FILE1" ] || [ -z "$FILE2" ] || [ -z "$FILE3" ]; then
      echo "Not enough XML files found in S3 for merging."
      exit 1
    fi

    echo "Found Robot Framework XML result files: $FILE1, $FILE2, and $FILE3"

    aws s3 cp "$S3_BUCKET/$FILE1" ./output1.xml
    aws s3 cp "$S3_BUCKET/$FILE2" ./output2.xml
    aws s3 cp "$S3_BUCKET/$FILE3" ./output3.xml

    rebot --merge \
    --flattenkeywords for \
    --flattenkeywords while \
    --outputdir ./ \
    --output output.xml \
    --log log.html \
    --report final_report.html \
    --xunit xunit.xml \
    output1.xml output2.xml output3.xml

  elif [[ "$DAILY_RUN" == "true" ]]; then
    FILES=$(aws s3 ls "$S3_BUCKET/$S3_FILE_PREFIX" --recursive | awk '{print $4}' | grep -E "$ENVIRONMENT_ARG/[0-9]{8}-[0-9]{6}/output\.xml" | tail -n 2)
    FILE1=$(echo "$FILES" | head -n 1)
    FILE2=$(echo "$FILES" | tail -n 1)

    if [ -z "$FILE1" ] || [ -z "$FILE2" ]; then
      echo "Not enough XML files found in S3 for merging."
      exit 1
    fi
    echo "Found Robot Framework XML result files: $FILE1 and $FILE2"
    aws s3 cp "$S3_BUCKET/$FILE1" ./output1.xml
    aws s3 cp "$S3_BUCKET/$FILE2" ./output2.xml

    rebot --merge \
      --flattenkeywords for \
      --flattenkeywords while \
      --outputdir ./ \
      --output output.xml \
      --log log.html \
      --report final_report.html \
      --xunit xunit.xml \
      output1.xml output2.xml
  fi

  echo "Robot Framework results merged into 'output.xml'."

  if [[ "$NIGHTLY_RUN" == "true" ]] ; then
    aws s3 cp ./output.xml "$S3_BUCKET/web/$outputdir/output.xml"
    aws s3 cp ./final_report.html "$S3_BUCKET/web/$outputdir/final_report.html"
    aws s3 cp ./log.html "$S3_BUCKET/web/$outputdir/log.html"
  elif [[ "$DAILY_RUN" == "true" ]] ; then
    aws s3 cp ./output.xml "$S3_BUCKET/daily_run/web/$outputdir/output.xml"
    aws s3 cp ./final_report.html "$S3_BUCKET/daily_run/web/$outputdir/final_report.html"
    aws s3 cp ./log.html "$S3_BUCKET/daily_run/web/$outputdir/log.html"
  else
    aws s3 cp ./output.xml "$S3_BUCKET/others/web/$outputdir/output.xml"
    aws s3 cp ./final_report.html "$S3_BUCKET/others/web/$outputdir/final_report.html"
    aws s3 cp ./log.html "$S3_BUCKET/others/web/$outputdir/log.html"
  fi
  echo "Merged output uploaded to S3."
}
# function to send a success message to slack channel
post_passed_result_to_slack(){
  curl -X POST -H 'Content-type: application/json' --data '{"text":":large_green_circle: '"$TEXT_HEADER"'
        Click the link below to see the report:
        https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$JOB_ID"'/artifacts/output/final_report.html
        To download, go to the link below and click Download:
        https://gitlab.com/varian-noona/testing/noona-system-test/-/jobs/'"$JOB_ID"'
        "}' $SLACK_WEBHOOK
}

post_about_success_to_slack() {
  if [[ "$COMBINE_WEB_RESULTS" != "true" ]]; then
    if [[ "$RUN_IN_GITLAB" == "true" ]] ; then
      if [[ "$INCLUDE_TAGS" == "time-constraint" ]]; then
          TEXT_HEADER="Time constraint cases passed in $ENVIRONMENT_ARG environment!"
          post_passed_result_to_slack
      elif [[ "$INCLUDE_TAGS" == "clinic-webORmanagement" ]]; then
          TEXT_HEADER="All Clinic and Admin cases passed in $ENVIRONMENT_ARG environment!"
          post_passed_result_to_slack
      elif [[ "$INCLUDE_TAGS" == "patient-web" ]]; then
        TEXT_HEADER="All Patient cases passed in $ENVIRONMENT_ARG environment!"
        post_passed_result_to_slack
      elif [[ "$INCLUDE_TAGS" == "sms" ]]; then
        TEXT_HEADER="All SMS cases passed in $ENVIRONMENT_ARG environment!"
        post_passed_result_to_slack
      elif [[ "$INCLUDE_TAGS" != "demo_smoke_test" ]] \
      && [[ "$INCLUDE_TAGS" != "login_tests_master" ]]; then
        TEXT_HEADER="Robot cases passed in $ENVIRONMENT_ARG"
        post_passed_result_to_slack
      fi
    fi
  fi
}

post_warning_result_to_slack(){
  curl -X POST -H 'Content-type: application/json' --data '{"text":":warning: '"$TEXT_HEADER"'
      Click the link below to see the report:
      https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$JOB_ID"'/artifacts/output/final_report.html
      To download, go to the link below and click Download:
      https://gitlab.com/varian-noona/testing/noona-system-test/-/jobs/'"$JOB_ID"'
      "}' $SLACK_WEBHOOK
}

# function to send a warning message to various slack channel in case of failed test run
post_about_error_to_slack() {
  if [[ "$COMBINE_WEB_RESULTS" != "true" ]]; then
    if [[ "$RUN_IN_GITLAB" == "true" ]]; then
      if [[ "$INCLUDE_TAGS" == "login_tests_master" ]] ; then
        echo "Smoke tests failed"
        curl -X POST -H 'Content-type: application/json' --data '{"text":":alert: Login tests have failed! Please check
        https://test.clinic.noonatest.com/nurse/#/login
        https://test.patient.noonatest.com/patient/#/login
        Click the link below to see the report:
        https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$JOB_ID"'/artifacts/output/final_report.html
        "}' $SLACK_WEBHOOK
      elif [[ "$INCLUDE_TAGS" == "demo_smoke_test" ]] ; then
        curl -X POST -H 'Content-type: application/json' --data '{"text":":alert: Robot smoke tests failed in Demo environment!
        Click the link below to see the report:
        https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$JOB_ID"'/artifacts/output/final_report.html
        "}' $RDR_SLACK_WEBHOOK
      elif [[ "$INCLUDE_TAGS" == "prod_smoke_tests" ]] ; then
        if [[ "$ENVIRONMENT" == "prod-ca" ]] ; then
          ENVIRONMENT_LABEL="Prod Canada"
        elif [[ "$ENVIRONMENT" == "prod-us" ]] ; then
          ENVIRONMENT_LABEL="Prod US"
        elif [[ "$ENVIRONMENT" == "prod-eu" ]] ; then
          ENVIRONMENT_LABEL="Prod EU"
        fi
      curl -X POST -H 'Content-type: application/json' --data '{"text":":alert: Robot smoke tests failed in '"$ENVIRONMENT_LABEL"' environment!
        Click the link below to see the report:
        https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$JOB_ID"'/artifacts/output/final_report.html
        "}' $RDR_SLACK_WEBHOOK
      elif [[ "$INCLUDE_TAGS" == "time-constraint" ]] ; then
        TEXT_HEADER="Time constraint tests failed in $ENVIRONMENT_ARG environment!"
        post_warning_result_to_slack
      elif [[ "$INCLUDE_TAGS" == "clinic-webORmanagement" ]]; then
        TEXT_HEADER="Some Clinic and Admin cases failed in $ENVIRONMENT_ARG environment!"
        post_warning_result_to_slack
      elif [[ "$INCLUDE_TAGS" == "patient-web" ]]; then
        TEXT_HEADER="Some Patient cases failed in $ENVIRONMENT_ARG environment!"
        post_warning_result_to_slack
      elif [[ "$INCLUDE_TAGS" == "sms" ]]; then
        TEXT_HEADER="Some SMS cases failed in $ENVIRONMENT_ARG environment!"
        post_warning_result_to_slack
      elif [[ "$INCLUDE_TAGS" != "prod_smoke_tests" ]] && [[ "$INCLUDE_TAGS" != "demo_smoke_test" ]] \
       && [[ "$INCLUDE_TAGS" != "time-constraint" ]] ; then
          TEXT_HEADER="Some robot cases failed in $ENVIRONMENT_ARG!"
          post_warning_result_to_slack
      fi
    fi
  fi
}

if [[ "$CONCURRENCY" == "" ]]; then
  if [[ "$JOB_NAME" == "run-sms-cases" ]]; then
    CONCURRENCY=1
  else
    CONCURRENCY=8
  fi
  echo "Using default pabot concurrency of $CONCURRENCY threads"
fi

if [[ "$INCLUDE_TAGS" == "" ]]; then
  echo "Including every test suite in the test run."
else
  echo "Including test suites that match the tag \"$INCLUDE_TAGS\" in the test run."
fi

if [[ "$EXCLUDE_TAGS" == "" ]]; then
  echo "No test suites are being excluded."
else
  echo "Excluding test suites that match the tag \"$EXCLUDE_TAGS\" from the test run."
fi

[[ "$ENVIRONMENT" != "" ]] && ENVIRONMENT_ARG="$ENVIRONMENT"
echo "Using variables from the data/vars-$ENVIRONMENT_ARG.yaml file"

[[ "$outputdir" == "" ]] && outputdir="output/$ENVIRONMENT_ARG/$timestamp"
echo "outputdir $outputdir"

echo_relevant_envvars

if [[ "$INCLUDE_TAGS" == *"modules-"* ]] || [[ "$INCLUDE_TAGS" == "compare-questionnaires" ]] || [[ "$INCLUDE_TAGS" == "complete-questionnaires" ]]; then
  run_test_level_split  "$@"
  retval=$?
else
  run_test_cases "$@"
  retval=$?
fi

if [ "$retval" -ne 0 ] && [ "$RUN_IN_GITLAB" = "true" ] && [ "$ENVIRONMENT_ARG" != "local-patient" ];
then
  if ! rerun_failed_cases;
  then
    post_about_error_to_slack
  else
    post_about_success_to_slack
  fi
  combine_robot_runs
else
  if [ "$ENVIRONMENT_ARG" != "local-patient" ]
  then
    post_about_success_to_slack
  fi
fi

# Upload results to Amazon S3 if the tests were run in pipeline
#[[ "$RUN_IN_GITLAB" == "true" ]] && (aws s3 cp $outputdir s3://noona-test-reports/$outputdir --recursive 2>&1 > /dev/null || echo Failed to upload reports to S3)
if [[ "$RUN_IN_GITLAB" == "true" ]]; then
  if [[ "$NIGHTLY_RUN" == "true" ]]; then
    aws s3 cp $outputdir s3://noona-test-reports/web/$outputdir --recursive 2>&1 > /dev/null || echo Failed to upload reports to S3
  elif [[ "$DAILY_RUN" == "true" ]]; then
    aws s3 cp $outputdir s3://noona-test-reports/daily_run/web/$outputdir --recursive 2>&1 > /dev/null || echo Failed to upload reports to S3
  else
    aws s3 cp $outputdir s3://noona-test-reports/others/web/$outputdir --recursive 2>&1 > /dev/null || echo Failed to upload reports to S3
  fi
fi

if [[ "$RUN_IN_GITLAB" == "true" ]]; then
  if [[ "$COMBINE_WEB_RESULTS" == "true" ]] && [[ "$JOB_NAME" == "run-in-gitlab-2" ]] ; then
    echo "Combining results..."
    combine_all_results
    TEXT_HEADER="Please check the web automation result in $ENVIRONMENT_ARG environment below!"
    curl -X POST -H 'Content-type: application/json' --data '{"text":":robot_face: '"$TEXT_HEADER"'
        Click the link below to see the report:
        https://varian-noona.gitlab.io/-/testing/noona-system-test/-/jobs/'"$JOB_ID"'/artifacts/output/final_report.html
        To download, go to the link below and click Download:
        https://gitlab.com/varian-noona/testing/noona-system-test/-/jobs/'"$JOB_ID"'
        "}' $SLACK_WEBHOOK
  else
    echo "Conditions not met for combining results."
  fi
fi

# for convenience a second log as gitlab cuts lenghty logs in preview
echo_relevant_envvars

echo Finished.
