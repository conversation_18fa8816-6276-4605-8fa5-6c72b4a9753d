<!-- PROJECT LOGO NOONA-->
<br />
<div align="center">
  <a href="https://gitlab.com/varian-noona/testing/noona-system-test">
    <img src="https://www.noonaclinic.com/patient-assets/icons/noona_logo_text_color.svg" alt="Logo" width="80" height="80">
  </a>

  <h3 style="text-align: center;">noona-system-test</h3>

  <p style="text-align: center;">
    System test repository for Noona
    <br />
  </p>
</div>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

## About The Project

This repository contains system tests for Noona, a patient engagement platform designed to improve communication between patients and healthcare providers. 
The tests ensure the reliability and performance of Noona's features, providing confidence in its stability, detecting regressions early, and enabling faster development cycles through continuous validation.
The test suite includes Robot Framework-based automation for both web and mobile platforms, covering iOS and Android devices through BrowserStack, ensuring consistent and cross-platform validation of Noona's key functionalities.

## Setup

### Prerequisites
1. Enable 2-Factor Authentication in [GitLab](https://docs.gitlab.com/ee/user/profile/account/two_factor_authentication.html#enabling-2fa)
2. Generate a [SSH keypair for GitLab](https://docs.gitlab.com/ee/ssh/#generate-an-ssh-key-pair)
3. Clone this repository:
   ```sh
   <NAME_EMAIL>:varian-noona/testing/noona-system-test.git
   ```

### Robot Framework Installation on macOS

#### 1. Run the following command in the repository root:
   ```sh
   make configure
   ```

This make target:
- Installs Homebrew
- Installs the latest Python version
- Configures a virtual environment (venv) for Python and Robot Framework
- Configures a WebDriver for Chrome

#### 2. Configure the IDE interpreter's settings:

1. **Open PyCharm**.
2. **Go to Settings**
3. **Navigate to Project: [noona-system-test] > Python Interpreter**.
4. **Click on the gear icon** next to the current interpreter.
5. **Select Add Interpreter**.
6. **Choose Add Local Interpreter**.
7. **Select the existing interpreter**.
8. PyCharm will automatically select the recently installed version of Python.

---

#### :memo: Important Notes
- Make sure to install all dependencies.
- Follow the setup instructions carefully
- Restart IDE (e.g. PyCharm) to ensure it picks up the updated PATH settings.
- If your local Chromedriver is not compatible with your Chrome browser version, update your Chromedriver with the steps below:

#### How to update webdriver via script for Chrome

1. Give permission to run the script `chmod +x ./scripts/update_chromedriver.sh`
2. Then Run `./scripts/update_chromedriver.sh`

#### How to install webdriver manually for Chrome

1. Go to this website to download [ChromeDriver for testing](https://googlechromelabs.github.io/chrome-for-testing/)
2. Scroll down to the Stable table and download the chromedriver binary file which matches your OS (e.g. macOS)
3. Locate to the folder where this binary file was downloaded
4. Move this binary file to these following locations
`/usr/local/bin` and `/opt/homebrew/bin` (given that Homebrew has been installed)

---

### Native App Automation Setup

#### Install Appium on macOS

1. Go to https://appium.io/docs/en/latest/quickstart/install/
2. Follow installation steps as per operating system
3. Install Appium and open it. It populates the default host (0.0.0.0) and port (4723) option which you can change. It also mentions the version of Appium being used.

#### Install Robot Framework AppiumLibrary
1. Refer to https://pypi.org/project/robotframework-appiumlibrary/

> :bulb: **Tip:** In interpreter make sure to have Appium-Python-Client with version 2.8.1 if you are getting errors while running TA locally.

For API 28 and below:
```sh
dumpsys window windows | grep -E 'mCurrentFocus'
```

For API 29 and up:
```sh
dumpsys window displays | grep -E 'mCurrentFocus'
```

To see the information about app package info and app activity info:
```sh
1|generic_x86_arm:/ $ dumpsys window windows | grep -E 'mCurrentFocus'                                         
  mCurrentFocus=Window{fc42053 u0 com.noona.application.staging/com.noona.application.staging.MainActivity}
```
For Noona staging application the following information are needed for the script to open the app:
```
appPackage = com.noona.application.staging
appActivity = com.noona.application.staging.MainActivity
```
---

## Development
It is recommended to use tool validated by Varian which is PyCharm Community Edition 2022.3, with the IntelliBot @SeleniumLibrary Patched plugin.

> :memo: **Note:** IDE/Text editors must be approved by Varian IT (under Tool Validation Process).

### How to run tests for Web Automation
The runner-common.sh script expects to have the Robot framework in the path so first run the command below to configure your local environment:
```sh
source ~/.virtualenvs/noona-system-test/bin/activate 
```
*    add **Tags** to robot test suites. They can be added as **[Tags]** to the test case or specified in **Force Tags** in the **Settings** of the test suite. **runner-common.sh** runs tests based on the tags given as parameters when running the script.
*    **runner-common.sh** reads tag and environment information from the environment variables **INCLUDE_TAGS**, **EXCLUDE_TAGS** and **ENVIRONMENT**. On top of that you can include any additional parameters you'd like to use as additional arguments for the execution command of the **runner-common.sh** file.
*    For example, if you want to run all tests tagged with **smoke** while excluding tests tagged with **wip**, using the **dev1** environment, you can set those values to the before mentioned environment variables and then simply run **./runner-common.sh**. Alternatively you may set the tag values as additional arguments for the actual script.
*    Example scripts which work identically (using tag environment variables and tag additional parameters at the same time is not recommended): 
```sh
export ENVIRONMENT=test
export INCLUDE_TAGS=smoke
export EXCLUDE_TAGS=wip 
./runner-common.sh
```
```sh
export ENVIRONMENT=test
./runner-common.sh --include smoke --exclude wip
```
Oneliner:
```sh
export ENVIRONMENT=dev1 && ./runner-common.sh --include smoke --exclude wip
```
*    **runner-common.sh** will automatically include the variable file **data/vars-ENVIRONMENT.yaml** file into the test runs based on which environment was set into the **ENVIRONMENT** variable (defaults to **test**). For additional variable files you can add them as arguments for the **runner-common.sh** script.
*    This example script would add **data/vars-test.yaml** as additional variable file to be used in the test run:
```
export ENVIRONMENT=test
./runner-common.sh --include smoke --exclude wip --variablefile data/vars-test.yaml
```
*    open a terminal in noona-system-test root directory and run **runner-common.sh**. You can also run a specific test suite. eg: `./runner-common.sh --include login_all`
*    results of the test are stored in the directory **output** with timestamped folders. Remember to cleanup older results as needed.
*   ***To run tests locally***, before execution go to Project repository -> **data folder** -> **vars-test.yaml file**

Comment the line:

`REMOTE_URL: http://selenium-hub:4444/wd/hub`

### Running tests with local frontends proxied to test backend and data

As a developer changing frontend code, you can get additional test exposure before merging to master by running a subset of robot tests locally against your changes while developing.

Principle: have Noona's patient frontend run locally. Set proxy to map all API calls to test environment where test data sets are prepared.  

Known issues currently:
* Tests involving following login links will not work

#### Preparations:

**Patient frontend**
* Prepare and launch patient frontend running as described in https://gitlab.com/varian-noona/development/core/noona/-/blob/master/noona-web/src/main/resources/patient/README.md#local-development
* At patient frontend, execute to start: 
```npm
npm run web:test
```
git Local tests are configured to execute in a larger browser window to help to understand failures faster. 

#### Steps:

1. To run tests at **noona-system-test** execute `./run-local-patient.sh --include [TAG]`
   * 1.1 Replace `[TAG]` with e.g `patient-web`
2. Open test report at `output/local-patient/latest/report.html`
   * 2.1 Tip: after rerun, just reload this in browser
3. To debug robot test with single browser run  `./debug-local-patient.sh --include [TAG]`
   * 3.1 Open test report at `output/local-patient/latest-debug/report.html`

#### Tips:

> Having both patient & clinic frontends running in localdev is not strictly necessary. You can
then set for example clinic urls in `vars-local.yaml` to point directly to test environment.

> You can always run test(s) against branching-off point (master) and see comparison what failing tests your code change caused.

---

## Native App Automation (iOS & Android) with Browserstack

Native app automation covers mostly patient app related test cases. At the moment, tests are running on Browserstack with an integration with Gitlab for scheduled or nightly run.
Find the basic instructions to setup, run and develop native app automation scripts with Browserstack on the link below:

[Native App Automation](https://vocscs.atlassian.net/wiki/x/eAK_KQc)


#### Points For Developing Native App Test Scripts

* Use **resources/native_app** to **store/find** native app resources
* Use **resources/common_mobile.resource** to **store/find** common keywords for native app automation
* Use **test/native_app** to **store/find** native app test cases
* Native app local test results is found at **output/native**
* Adding **Tags** is the different with web automation e.g. **JAMA-ID-app** or **NMS7-VER-123-app** and add another tag **native-app**
* Configuration for mobile environment was set in the file **common_mobile.resource**

### Inspecting Elements Via Browserstack (iOS only)

:warning: For Android - device PIN code cannot be configured hence this can not applicable. 
- Refer to **Inspecting Elements Via Chrome’s Inspect Devices (Android only)**

:warning: For iOS, Privacy Screen can be disabled in `Noona Log in > About box > PS`

1. In Browserstack, go to the App Live feature
2. Upload the latest **iOS .ipa** file under test if not yet uploaded
3. Select a platform **iOS REAL DEVICES**
4. Select a device
5. Noona app will start on the selected device
6. In the **Dev Tools** in the right panel of the website, select **Inspect(Beta)**
7. Initiate **UI inspector mode**.
8. Select an element to inspect by hovering and clicking on the device 
9. Locators will display on the right panel of the website
10. Get the type and other related values
11. IOS element locator will usually look like this :
```
//XCUIElementTypeButton[contains(@name, 'LOG IN')] 
```

### Inspecting Elements Via Chrome’s Inspect Devices (Android Device only)

### Prerequisites:
1. Enable Developer mode on Android device
2. Connect Android device to laptop via usb cable
3. Allow permissions on USB debugging

### Steps:
1. In Chrome, go to `chrome://inspect/#devices`
2. Click **Inspect**
3. Webview elements should be displayed on separate window
4. Use the inspect icon (same with web) to select/inspect UI elements
5. Locators have the same format with web

### Tagging conventions

We use tags to categorize test cases as well as following statistics like what test cases are (currently) manual.

**Each test case must be tagged with the use case id the test case relates to.** Use prefix `usecase-` and small letters in the tag name. E.g. `usecase-f01n16`
Additionally, use the JAMA ids equivalent to each test case. Use prefix `NMS7-VER-` and numeric id in the tag name. E.g. `NMS7-VER-254`

You can set tags to individual test case with `[Tags]` setting. You can also set tags to many test cases with `Force Tags` and `Default Tags` in the suite settings. Read more about available options in [Robot Framework User Guide](http://robotframework.org/robotframework/latest/RobotFrameworkUserGuide.html#tagging-test-cases).

Following tags are optional to use when deemed suitable:
* If the test case is not yet automated, use tag `manual`
* To denote that the test case is a smoke test, use tag `smoke`
* To denote that the test case relates to an accessibility standard, use prefix `accessibility` and small letters in the tag name. E.g. `accessibility-wcag-a`
* To denote that the test case relates to some ongoing ticket in JIRA (e.g. bug report), use the JIRA ticket id directly as a tag. E.g. `NOONA-8242` or `TESTING-124`
* To denote that the test case relates to native app, use prefix `NMS7-VER-` and prefix `-app` E.g. `NMS7-VER-40-app`
* If the test case is related to native app but not yet done, use tag `native-app-todo`
* If the test case is related to Pendo guides, use tag `pendo`
* If the test case is not in use (as of now or not at all but we want to keep it documented), use tag `disabled`
* If the test case already created should be run locally (due to time constraint), use tag `local`
* If the test case to be created should be run locally (due to time constraint), use tag `local-wip`
* If the test case cannot be automated as of the moment and can be tested only manually (native app-related), use tag `non-automated`

### How to run PROD smoke tests locally
* Navigate to appropriate `vars-prod-x.yaml` and uncomment `REMOTE_URL` variable. Since, we are running tests locally so we don't 
need to enable remote server. 
* Navigate to `runner-common.sh` file and uncomment 4 variables and provide appropriate values. These variables can be used only for PROD smoke tests. 
>* NURSE_USERNAME
>* NURSE_PASSWORD
>* PATIENT_USERNAME
>* PATIENT_PASSWORD 

* Run this command to run `prod_smoke_tests`:

e.g: Run prod smoke tests in EU
```sh
export ENVIRONMENT=prod-eu ./runner-common.sh --include prod_smoke_tests
```

### Utilities and helper scripts
* We have created several helper scripts to help in various topics regarding automation:
1. utilities/fetch_lastest_csv.py - Use to convert the original csv file to file that is readable by our Robot Framework script. It removes unnecessary characters and just leaves the exact content.
2. utilities/module_parser.py - Parses the modules based on the how the symptoms are defined in noona repo. This combines all the symptoms and the questions under to 1 file used by specific module test. 
3. utilities/robotdiff.py - This script compares two or more Robot Framework output files and creates a report where possible differences between test case statuses in each file are highlighted.
4. utilities/md_to_txt_converter.py - This file will help to convert .md file to txt. Mostly useful when there is content provided in md file and for script we will need txt format. How to use it, is mentioned in file. 

### Contributing
Please read our [Contribution guidelines for this project](docs/CONTRIBUTING.md).

### Useful links

* [Test Automation](https://vocscs.atlassian.net/wiki/x/E4AlMgc)
* [Accessing Native Apps Builds (Test & Staging) And Uploading To BrowserStack](https://vocscs.atlassian.net/wiki/x/CgElLwc)
* [Writing New Automated Test Cases](https://vocscs.atlassian.net/wiki/x/EQA1NAc)
* [Robot Framework Language Server settings for Pycharm](https://vocscs.atlassian.net/wiki/x/EYGRNgc)
* [How to access test automation reports in AWS](https://vocscs.atlassian.net/wiki/x/K4DnaQc)
* [Adding New Automated Case For A New Module](https://vocscs.atlassian.net/wiki/x/PYFzdwc)
* [Testing appointments with automated script](https://vocscs.atlassian.net/wiki/x/GwAcNgc)

### License
This project and all the source code contained is proprietary software. It is owned by Varian Medical Systems and is confidential. Redistribution of the source code is strictly forbidden.
```
Copyright (c) 2019-2025 Varian, A Siemens Healthineers Company, All Rights Reserved.
COMPANY CONFIDENTIAL
```