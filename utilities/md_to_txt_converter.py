"""Description: This file will help to convert .md file to txt
Precondition:
Download noona-system-test repository to local
run command: pip3 install markdown beautifulsoup4
Replace file path of md and txt file
Run with command: python3 utilities/md_to_txt_converter.py
"""


import markdown
from bs4 import BeautifulSoup

# Replace file path with the .md you want to convert
with open('/noona-system-test/data/privacy_content/emea_PrivacyPolicy_pl_PL.md', 'r') as f:
    md_content = f.read()
html = markdown.markdown(md_content)
text = BeautifulSoup(html, 'html.parser').get_text()

# Replace file path with the .txt you want to save to
with open('/noona-system-test/data/privacy_content/Europe_Noona_PrivacyStatement_PL.txt', 'w') as f:
    f.write(text)
