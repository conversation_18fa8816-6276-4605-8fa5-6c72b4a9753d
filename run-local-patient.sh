#!/bin/bash

# Shorthand to run local proxy testing faster
# Main differences being:
# * no rerun of failed tests
# * override some params to adapt tests to local-proxy
# * no timestamped reports => rerun and refresh report in browser to see latest run results

ENVIRONMENT="local-patient"
DEFAULT_EXCLUDES='manualORquestionnaires-testORdemo_login_testsORdemo_smoke_testORprod_smoke_testsORemeaORnative-app'

# Use Python version and libraries from a dedicated virtualenv
source $HOME/.virtualenvs/noona-system-test/bin/activate

[[ "$outputdir" == "" ]] && outputdir=output/$ENVIRONMENT/latest

if [[ "$INCLUDE_TAGS" == "" ]]; then
  echo $@ |grep -- '--include' > /dev/null
  if [ $? == 1 ]
  then
    echo "Nothing included from INCLUDE_TAG environment variable or --include command line option."
    echo "Using default option of running all test suites with the tag patient-web"
    INCLUDE_TAGS="patient-web"
  else
    INCLUDE_TAGS=$(TMP=${@##*--include}; echo $TMP | cut -d" " -f 1)
  fi
fi

if [[ "$EXCLUDE_TAGS" == "" ]]; then
  echo $@ |grep -- '--exclude' > /dev/null
  if [ $? == 1 ]
  then
    echo "Nothing excluded from EXCLUDE_TAGS environment variable or --exclude command line option."
    echo "Using default option of excluding the follwowing test suites: $DEFAULT_EXCLUDES"
    EXCLUDE_TAGS=$DEFAULT_EXCLUDES
  else
    EXCLUDE_TAGS=$(TMP=${@##*--exclude}; echo $TMP | cut -d" " -f 1)
  fi
fi

[[ "$CONCURRENCY" == "" ]] && CONCURRENCY=4

export ENVIRONMENT
export INCLUDE_TAGS
export EXCLUDE_TAGS
export CONCURRENCY
export outputdir

echo "Clearing previous test results if any at ${outputdir}"
rm -rf "${outputdir}"
./runner-common.sh
