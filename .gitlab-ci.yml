stages:
  - deploy
  - run
  - block-cicd

variables:
  NOONA_IMAGE_REGISTRY: "${CI_REGISTRY}/varian-noona/development/core/docker-image"
  EXCLUDE_TAGS_APP: "manualORquestionnaires-testORdemo_login_testsORdemo_smoke_testORprod_smoke_testsORemeaORnms9-ver-420"
  EXCLUDE_TAGS_GITLAB: "manualORquestionnaires-testORdemo_login_testsORdemo_smoke_testORprod_smoke_testsORemeaORnative-appORlong-tcORsmsORnms9-ver-420"
  INCLUDE_TAGS_1: "clinic-webORmanagement"
  INCLUDE_TAGS_2: "patient-web"
  APP_IOS: "4ecfcb8b4d630e0fd7a8568d0893a25354161be4"
  DEVICE_NAME_IOS: "iPhone 14"
  PLATFORM_VERSION_IOS: "18"
  APP_ANDROID: "d2e680e970d1a3fee62304d6848b730fa62377dd"
  DEVICE_NAME_ANDROID: "Samsung Galaxy S24"
  PLATFORM_VERSION_ANDROID: "14.0"
  AWS_PUBLIC_ECR: "public.ecr.aws/docker/library"
  VAULT_SKIP_VERIFY: "true"
  DOCKER_IN_DOCKER_IMAGE: $AWS_PUBLIC_ECR/docker:24.0.7-dind-alpine3.18
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_DRIVER: overlay2
  DOCKER_BUILDKIT: 1
  NURSE_USERNAME: "none"
  NURSE_PASSWORD: "none"
  PATIENT_USERNAME: "none"
  PATIENT_PASSWORD: "none"


image: $NOONA_IMAGE_REGISTRY/python:3.11.3-slim

# FIXME fine-tune warnings/errors to ignore

# Default rules for build jobs. If environment variable RUN has been set to true
# then only run the test run job(s). Otherwise build(any) + deploy(master build)
.default_rules:
  rules:
    - if: '$RUN_IN_GITLAB == "true" || $RUN_IN_AWS == "true" || $RUN_IN_NATIVE == "true"'
      when: never
    - when: always

.docker-in-docker:
  image: $DOCKER_IN_DOCKER_IMAGE
  services:
    - name: $DOCKER_IN_DOCKER_IMAGE
      # See https://gitlab.com/gitlab-org/gitlab-runner/-/issues/6295#note_446441089 for alias
      alias: docker

deploy:
  image: $NOONA_IMAGE_REGISTRY/docker:noona-aws-25.0.1-dind
  services:
    - name: $NOONA_IMAGE_REGISTRY/docker:noona-aws-25.0.1-dind
      # See https://gitlab.com/gitlab-org/gitlab-runner/-/issues/6295#note_446441089 for alias
      alias: docker
  rules:
    - if: '$NO_DEPLOY == "true"'
      when: manual
      allow_failure: true
    - when: always
  cache:
    paths:
      - .cache/pip
      - .venv-deploy
  needs: []
  timeout: 10m
  tags:
    - infrastructure-runner
  stage: deploy
  script:
    - time ./.gitlab-ci/${CI_JOB_STAGE}/${CI_JOB_NAME}.sh

update-task-definition:
  extends: .docker-in-docker
  rules:
    - if: '$RUN_IN_AWS == "true" || $RUN_IN_GITLAB == "true" || $RUN_IN_NATIVE == "true"'
      when: never
    - if: '$CI_COMMIT_REF_NAME == "master"'
      changes:
        - .gitlab-ci/deploy/aws-task-definition.json
      when: always
  stage: deploy
  script:
    - time ./.gitlab-ci/${CI_JOB_STAGE}/${CI_JOB_NAME}.sh
  tags:
    - infrastructure-runner

run-in-gitlab:
  extends: .docker-in-docker
  stage: run
  timeout: 3h
  tags:
    - noona-system-test
  allow_failure: true
  needs:
    - job: deploy
  rules:
    - if: '$RUN_IN_GITLAB == "true"'
      when: always
    - when: manual
  variables:
    INCLUDE_TAGS: $INCLUDE_TAGS_1
    # exclude all tests that have the "manual" tag
    EXCLUDE_TAGS: $EXCLUDE_TAGS_GITLAB
    # Use variables from data/vars-test.yaml
    ENVIRONMENT: "test"
    NIGHTLY_RUN: "false"
    DAILY_RUN: "false"
  script:
    - time ./.gitlab-ci/${CI_JOB_STAGE}/${CI_JOB_NAME}.sh
  artifacts:
    paths:
      - ./output/*
    expire_in: 1 month
    when: always

run-sms-cases:
  extends: .docker-in-docker
  stage: run
  timeout: 2h
  tags:
    - noona-system-test
  allow_failure: true
  needs:
    - job: deploy
  rules:
    - if: '$RUN_SMS == "true"'
      when: always
    - when: manual
  variables:
    INCLUDE_TAGS: "sms"
    # exclude all tests that have the "manual" tag
    EXCLUDE_TAGS: "manualORlong-tcORnative-app"
    ENVIRONMENT: "test"
    NIGHTLY_RUN: "false"
    DAILY_RUN: "false"
  script:
    - sleep 15
    - time ./.gitlab-ci/${CI_JOB_STAGE}/run-in-gitlab.sh
  artifacts:
    paths:
      - ./output/*
    expire_in: 1 month
    when: always

run-in-gitlab-2:
  extends: .docker-in-docker
  stage: run
  timeout: 3h
  tags:
    - noona-system-test
  allow_failure: true
  needs:
    - job: deploy
  rules:
    - if: '$RUN_IN_GITLAB_2 == "true"'
      when: always
    - when: manual
  variables:
    INCLUDE_TAGS: $INCLUDE_TAGS_2
    # exclude all tests that have the "manual" tag
    EXCLUDE_TAGS: $EXCLUDE_TAGS_GITLAB
    # Use variables from data/vars-test.yaml
    ENVIRONMENT: "test"
    NIGHTLY_RUN: "false"
    DAILY_RUN: "false"
  script:
    - sleep 10
    - time ./.gitlab-ci/${CI_JOB_STAGE}/run-in-gitlab.sh
  artifacts:
    paths:
      - ./output/*
    expire_in: 1 month
    when: always

run-android-native:
  image: $DOCKER_IN_DOCKER_IMAGE
  stage: run
  timeout: 4h
  allow_failure: true
  rules:
    - if: '$RUN_IN_NATIVE == "true" || $RUN_ANDROID == "true"'
      when: always
  needs:
    - job: deploy
  variables:
    # include all tests that have mentioned tags.
    INCLUDE_TAGS: "native-appORnative-web"
    # exclude all tests that have the "manual" tag
    EXCLUDE_TAGS: $EXCLUDE_TAGS_APP
    # Use variables from data/vars-test.yaml
    ENVIRONMENT: "native-test"
    PLATFORM_NAME: "android"
    APP: $APP_ANDROID
    DEVICE_NAME: $DEVICE_NAME_ANDROID
    PLATFORM_VERSION: $PLATFORM_VERSION_ANDROID
    ENABLE_PASSCODE: "true"
    NIGHTLY_RUN: "false"
  script:
    - time ./.gitlab-ci/${CI_JOB_STAGE}/run-in-native.sh
  artifacts:
    paths:
      - ./output/*/*
    expire_in: 1 month
    when: always
  tags:
    - noona-system-test

.cicd:
  stage: block-cicd
  image: $DOCKER_IN_DOCKER_IMAGE
  allow_failure: true
  rules:
    - if: '$BLOCK_CICD == "true"'
      when: always
    - when: never
  needs: [ ]
  script:
    - apk add bash vault expect
    - time ./.gitlab-ci/${CI_JOB_STAGE}/${CI_JOB_NAME}.sh
  tags:
    - infrastructure-runner

allow-cicd:
  extends: .cicd
  needs:
    - job: run-in-gitlab

block-cicd:
  extends: .cicd

run-ios-native:
  image: $DOCKER_IN_DOCKER_IMAGE
  stage: run
  timeout: 3h
  allow_failure: true
  rules:
    - if: '$RUN_IN_NATIVE == "true"'
      when: always
      needs:
        - job: run-android-native
    - if: '$RUN_IOS == "true"'
      when: always
  variables:
    # include all tests that have mentioned tags.
    INCLUDE_TAGS: "native-appORnative-web"
    # exclude all tests that have the "manual" tag
    EXCLUDE_TAGS: $EXCLUDE_TAGS_APP
    # Use variables from data/vars-test.yaml
    ENVIRONMENT: "native-test"
    PLATFORM_NAME: "ios"
    APP: $APP_IOS
    DEVICE_NAME: $DEVICE_NAME_IOS
    PLATFORM_VERSION: $PLATFORM_VERSION_IOS
    ENABLE_PASSCODE: "true"
    NIGHTLY_RUN: "false"
  script:
    - time ./.gitlab-ci/${CI_JOB_STAGE}/run-in-native.sh
  tags:
    - noona-system-test
  artifacts:
    paths:
      - ./output/*/*
    expire_in: 1 month
    when: always

jama-reports:
  extends: .docker-in-docker
  stage: run
  timeout: 2h
  allow_failure: true
  rules:
    - if: '$JAMA_REPORTS == "true"'
      when: always
    - when: manual
  variables:
    # include all tests that have one or more tags.
    INCLUDE_TAGS: "*"
    # exclude all tests that have the "manual" tag
    EXCLUDE_TAGS: "manual"
    # Use variables from data/vars-test.yaml
    ENVIRONMENT: "native"
    NIGHTLY_RUN: "false"
  script:
    - time ./.gitlab-ci/${CI_JOB_STAGE}/${CI_JOB_NAME}.sh
  artifacts:
    paths:
      - ./output/*
    expire_in: 1 month
    when: always
  tags:
    - gitlab-aws-autoscaler
