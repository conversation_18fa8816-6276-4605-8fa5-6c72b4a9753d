FROM python:3.8.19-alpine3.20

ENV BUILD_DEPS="\
    build-base \
    libffi-dev \
    postgresql-dev \
    yaml-dev \
"

ENV RUNTIME_DEPS="\
    jq \
    libffi \
    libstdc++ \
    postgresql-client \
    tk \
    wget \
    curl \
    yaml \
"

COPY . /opt/run/

RUN set -e ;\
    apk update ;\
    apk add --no-cache --virtual .build-deps ${BUILD_DEPS} ;\
    apk add --no-cache ${RUNTIME_DEPS} ;\
    pip install --upgrade pip ;\
    pip install --no-cache-dir -r /opt/run/requirements.txt ;\
    pip install awscli ;\
    # List packages and python modules installed
    apk info -vv | sort ;\
    pip freeze ;\
    # Cleanup
    apk del --no-cache --purge .build-deps ;\
    rm -rf /var/cache/apk/*

WORKDIR /opt/run/

CMD /opt/run/run.sh
