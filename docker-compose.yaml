---
version: '3'

services:
  noona-test:
    container_name: noona-test
    image: ${DC_NOONA_TEST_IMAGE}
    environment:
      - INCLUDE_TAGS
      - JOB_ID=${CI_JOB_ID}
      - RUN_IN_GITLAB
      - RUN_IN_GITLAB_2
      - EXCLUDE_TAGS
      - ENVIRONMENT
      - AWS_ACCESS_KEY_ID_dev
      - AWS_SECRET_ACCESS_KEY_dev
      - NIGHTLY_RUN
      - DAILY_RUN
      - NURSE_USERNAME
      - NURSE_PASSWORD
      - PATIENT_USERNAME
      - PATIENT_PASSWORD
      - RUN_SMS
      - COMBINE_WEB_RESULTS
      - JOB_NAME=${CI_JOB_NAME}
    volumes:
      - ${RUN_PATH}/output:/opt/run/output
    networks:
      - noona-network
    command: |
      sh -c "
        set -e;
        until wget -q -O - http://selenium-hub:4444/wd/hub/status | jq -r '.value.ready' | grep -q "true"; do
          >&2 echo 'Selenium Hub is unavailable - sleeping';
          sleep 1;
        done;
        /opt/run/run.sh
      "
    depends_on:
      - selenium-hub

  selenium-hub:
    container_name: selenium-hub
    image: ${DC_SELENIUM_HUB}
    networks:
      - noona-network
    ports:
      - "4442:4442"
      - "4443:4443"
      - "4444:4444"
    environment:
      - HUB_HOST=selenium-hub
      - HUB_PORT=4444

  chrome:
    container_name: chrome
    image: ${DC_SELENIUM_CHROME}
    networks:
      - noona-network
    volumes:
      - '/dev/shm:/dev/shm'
    shm_size: '2gb'
    ports:
      - "5555:5555"
    environment:
      - START_XVFB=false
      - SE_NODE_MAX_SESSIONS=16
      - SE_NODE_SESSION_TIMEOUT=310
      - SE_SESSION_REQUEST_TIMEOUT=500
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_EVENT_BUS_HOST=selenium-hub
      - SE_EVENT_BUS_PUBLISH_PORT=4442
      - SE_EVENT_BUS_SUBSCRIBE_PORT=4443
      - SE_NODE_GRID_URL=http://selenium-hub:4444
      - JAVA_OPTS=-Dwebdriver.chrome.whitelistedIps=

    depends_on:
      - selenium-hub

networks:
  noona-network:
    name: noona-network
