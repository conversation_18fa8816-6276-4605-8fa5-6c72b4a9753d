#!/bin/bash
# make-and-bundle-noona-test
#
# For MacOS's bash.  To be run in a Noona local Git directory.
#
# Teemu Leisti 2017-12-11
# Forked and reconfigured by <PERSON>t APR-2020

set -eu # Exit on using an uninitialized variable, and on a command returning an error.
head_commit=`git rev-parse HEAD`

echo "${head_commit}"

mvn clean install -Dmaven.test.skip=true

java -cp noona-composite-web/target/noona-composite-web.jar adelmann.util.codegenerator.TypescriptModelGenerator
java -cp noona-composite-web/target/noona-composite-web.jar adelmann.util.codegenerator.TypescriptNoonaEndpointGenerator

pushd noona-web/src/main/resources/webapp/
yarn install
yarn build
popd

mvn clean install -Dmaven.test.skip=true

time_now=$(date +"%Y-%m-%d-at-%H-%M")

bundle_name="noona-local-docker-$time_now"

if [[ -d /tmp/$bundle_name ]] ; then
    echo "[$head_commit] Removing directory /tmp/$bundle_name."
    rm -rf /tmp/$bundle_name
elif [[ -e /tmp/$bundle_name ]] ; then
    echo "[$head_commit] Removing file /tmp/$bundle_name."
    rm -f /tmp/$bundle_name
fi

dest_dir="/tmp/${bundle_name}"
mkdir -p "${dest_dir}"

release_desc="${head_commit} ${bundle_name}"



#######################
#   noona-processor   #
#######################

echo "[$head_commit] Copying and creating files for noona-processor ..."

mkdir -p "${dest_dir}/noona-processor/lib"

cp noona-processor/target/lib/*                        "${dest_dir}/noona-processor/lib/"
cp noona-processor/target/noona-processor.jar          "${dest_dir}/noona-processor/"

echo "${release_desc}" > "${dest_dir}/noona-processor/release.txt"

cat > "${dest_dir}/noona-processor/log4j.xml" <<EOF
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">
  <appender name="file" class="org.apache.log4j.DailyRollingFileAppender">
    <param name="file" value="/tmp/noona-processor.log" />
    <param name="DatePattern" value="'.'yyyy-MM-dd" />
    <layout class="org.apache.log4j.PatternLayout">
      <param name="ConversionPattern" value="%d{ISO8601} (%F:%L) %-5p: %m%n"/>
    </layout>
  </appender>
  <logger name="adelmann" additivity="false">
    <level value="info"/>
    <appender-ref ref="file" />
  </logger>
  <root>
    <priority value="info" />
    <appender-ref ref="file" />
  </root>
</log4j:configuration>
EOF

cat > "${dest_dir}/noona-processor/noona-ext.properties" <<EOF
aws-sns-cloud-watch-log-group-name=mock
business-intelligence-reporter-enabled=false
device-variant=*
disable-night-message-sending=false
firebase-server-key.encrypted=a0YhkisN5R5O1tmIFG/bMw==
hsm-key-id=mock
javax.persistence.jdbc.password.encrypted=Oq2OygUXBf8cMpYOhTW9rA==
key-encryption-secret-key=51LszUG8IgzWi7sRWCedoWANaRUPqgvYD0OGDjVoVo4Hpid/B1lRPzbRRTLmQqVu
management-url=http://127.0.0.1:8080/management/
nurse-url=http://127.0.0.1:8080/nurse/
patient-url=http://127.0.0.1:8080/patient/
production-mode=false
smtp-password.encrypted=J0riP+0eUj8jPNIIw9IZQw==
symptom-inquiry-sending-delay-after-modify=0
system-secret-key=dSk80Nj17zXEyzOKYJEWZRNcGWW5xC3PzEZGsHoAy1s=
worker-delay-aeq=15000
worker-delay-questionnaires=15000
EOF

cat > "${dest_dir}/noona-processor/Dockerfile" <<EOF
FROM adoptopenjdk/openjdk8:x86_64-alpine-jdk8u212-b04
COPY noona-processor.jar noona-ext.properties /opt/run/
COPY lib/* /opt/run/lib/
COPY log4j.xml /opt/run/log4j.xml
EXPOSE 8081
WORKDIR /opt/run/
CMD java -jar /opt/run/noona-processor.jar password
EOF



###########################
#   noona-composite-web   #
###########################

echo "[$head_commit] Copying and creating files for noona-composite-web ..."

mkdir -p "${dest_dir}/noona-composite-web/lib"

cp noona-composite-web/target/lib/*                     "${dest_dir}/noona-composite-web/lib/"
cp noona-composite-web/target/noona-composite-web.jar   "${dest_dir}/noona-composite-web/noona-composite-web.jar"

echo "${release_desc}" > "${dest_dir}/noona-composite-web/release.txt"

cat > "${dest_dir}/noona-composite-web/log4j.xml" <<EOF
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">
  <appender name="file" class="org.apache.log4j.DailyRollingFileAppender">
    <param name="file" value="/tmp/noona-web.log" />
    <param name="DatePattern" value="'.'yyyy-MM-dd" />
    <layout class="org.apache.log4j.PatternLayout">
      <param name="ConversionPattern" value="%d{ISO8601} (%F:%L) %-5p: %m%n"/>
    </layout>
  </appender>
  <logger name="adelmann" additivity="false">
    <level value="info"/>
    <appender-ref ref="file" />
  </logger>
  <root>
    <priority value="info" />
    <appender-ref ref="file" />
  </root>
</log4j:configuration>
EOF

cat > "${dest_dir}/noona-composite-web/noona-ext.properties" <<EOF
aws-sns-cloud-watch-log-group-name=mock
base-url=https://1ecb137c.ngrok.io
browser-time-shift-enabled=false
business-intelligence-load-interval-millis=5000
business-intelligence-reporter-enabled=false
device-variant=*
disable-night-message-sending=false
firebase-server-key.encrypted=a0YhkisN5R5O1tmIFG/bMw==
from-email=<EMAIL>
hsm-key-id=mock
http-port=8080
https-port=0
javax.persistence.jdbc.driver=org.postgresql.Driver
javax.persistence.jdbc.password.encrypted=Oq2OygUXBf8cMpYOhTW9rA==
javax.persistence.jdbc.url=**************************************
javax.persistence.jdbc.user=noona
key-encryption-secret-key=51LszUG8IgzWi7sRWCedoWANaRUPqgvYD0OGDjVoVo4Hpid/B1lRPzbRRTLmQqVu
liquibase-change-log=database/noona/db.changelog-master.xml
management-url=http://127.0.0.1:8080/management/
nurse-url=http://127.0.0.1:8080/nurse/
patient-url=http://127.0.0.1:8080/patient/
production-mode=false
reporting-mode-enabled=false
site-type=noona
smtp-password.encrypted=J0riP+0eUj8jPNIIw9IZQw==
symptom-inquiry-sending-delay-after-modify=0
system-secret-key=dSk80Nj17zXEyzOKYJEWZRNcGWW5xC3PzEZGsHoAy1s=
update-patients=false
wellness-import-enabled=false
worker-delay-aeq=15000
worker-delay-questionnaires=15000
EOF

cat > "${dest_dir}/noona-composite-web/Dockerfile" <<EOF
FROM adoptopenjdk/openjdk8:x86_64-alpine-jdk8u212-b04
COPY noona-composite-web.jar noona-ext.properties /opt/run/
COPY lib/* /opt/run/lib/
COPY log4j.xml /opt/run/log4j.xml
EXPOSE 8080
WORKDIR /opt/run/
CMD java -jar /opt/run/noona-composite-web.jar password
EOF



####################
#   noona-search   #
####################

echo "[$head_commit] Copying and creating files for noona-search ..."

mkdir -p "${dest_dir}/noona-search/lib"

cp noona-search/target/lib/*              "${dest_dir}/noona-search/lib/"
cp noona-search/target/noona-search.jar   "${dest_dir}/noona-search/"

echo "${release_desc}" > "${dest_dir}/noona-search/release.txt"

cat > "${dest_dir}/noona-search/log4j.xml" <<EOF
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">
  <appender name="file" class="org.apache.log4j.DailyRollingFileAppender">
    <param name="file" value="/tmp/noona-search.log" />
    <param name="DatePattern" value="'.'yyyy-MM-dd" />
    <layout class="org.apache.log4j.PatternLayout">
      <param name="ConversionPattern" value="%d{ISO8601} (%F:%L) %-5p: %m%n"/>
    </layout>
  </appender>
  <logger name="adelmann" additivity="false">
    <level value="info"/>
    <appender-ref ref="file" />
  </logger>
  <root>
    <priority value="info" />
    <appender-ref ref="file" />
  </root>
</log4j:configuration>
EOF

cat > "${dest_dir}/noona-search/noona-ext.properties" <<EOF
hsm-key-id=mock
javax.persistence.jdbc.driver=org.postgresql.Driver
javax.persistence.jdbc.password=password
javax.persistence.jdbc.password.encrypted=Oq2OygUXBf8cMpYOhTW9rA==
javax.persistence.jdbc.url=**************************************
javax.persistence.jdbc.user=noonasearch
key-encryption-secret-key=51LszUG8IgzWi7sRWCedoWANaRUPqgvYD0OGDjVoVo4Hpid/B1lRPzbRRTLmQqVu
production-mode=false
system-secret-key=dSk80Nj17zXEyzOKYJEWZRNcGWW5xC3PzEZGsHoAy1s=
EOF

cat > "${dest_dir}/noona-search/Dockerfile" <<EOF
FROM adoptopenjdk/openjdk8:x86_64-alpine-jdk8u212-b04
COPY noona-search.jar noona-ext.properties /opt/run/
COPY lib/* /opt/run/lib/
COPY log4j.xml /opt/run/log4j.xml
EXPOSE 8082
WORKDIR /opt/run/
CMD java -jar /opt/run/noona-search.jar password
EOF



################
#   postgres   #
################

echo "[$head_commit] Copying and creating files for PostgreSQL ..."

mkdir -p "${dest_dir}/noona-postgres"

scriptdir="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd )"

cp "${scriptdir}/noona.sql"  "${dest_dir}/noona-postgres/noona.sql"
cp "${scriptdir}/noonaref.sql"  "${dest_dir}/noona-postgres/noonaref.sql"

cat > "${dest_dir}/noona-postgres/Dockerfile" <<EOF
FROM ubuntu:16.04
RUN apt-key adv --keyserver hkp://p80.pool.sks-keyservers.net:80 --recv-keys B97B0AFCAA1A47F044F244A07FCC7D46ACCC4CF8
RUN echo 'deb http://apt.postgresql.org/pub/repos/apt/ xenial-pgdg main' >> /etc/apt/sources.list.d/pgdg.list
RUN apt-get update && apt-get install -y python-software-properties software-properties-common postgresql-10 postgresql-client-10 postgresql-contrib-10
COPY noona.sql /opt/run/
COPY noonaref.sql /opt/run/
USER postgres
RUN echo "host all  all    0.0.0.0/0  md5" >> /etc/postgresql/10/main/pg_hba.conf
RUN echo "listen_addresses='*'" >> /etc/postgresql/10/main/postgresql.conf
EXPOSE 5444
VOLUME  ["/etc/postgresql", "/var/log/postgresql", "/var/lib/postgresql"]
WORKDIR /opt/run/
CMD /etc/init.d/postgresql start && psql < noona.sql > /dev/null && psql < noonaref.sql > /dev/null && bash
EOF