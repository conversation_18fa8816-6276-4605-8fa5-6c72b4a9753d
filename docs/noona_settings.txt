Configuration:
    To use these settings, create the file noona-ext.properties in the same folder as the JAR you are running.
    In this file, configure any values you wish by entering "key=value", one per line.
        (for example, "firebase-server-key=mock" without the quotation marks)
    Some settings can, and some must be set using the encrypted accessor.
        (for example, "firebase-server-key.encrypted=a0YhkisN5R5O1tmIFG/bMw==")

    In the section below, keys have been grouped loosely by functionality.
    Most importantly, the "Mockable" section refers to the ones that mock out services.
    This means that a real service is not expected/used.
    There is also a sizable list of unknown keys that have no real explanation so far.
    There is also a final section of SAM variables. These are the environment variables that are set to enable SAM.
        In Java code, this is usually referred to as dockerMode.
    Which keys to use is dependent on the component being launched.
    Consult existing or alternative properties files for ideas on what to use where.

Properties inheritance:
    According to developers, the intended idea is for "noona.properties" to be the default.
    "noona-ext.properties" would then be an override file that takes precedence.
    However, it seems that "noona" is not read to begin with and "noona-ext" is the de facto configuration.



Mockable:
    analytics.database.url: Do not set, or set to ''
    aws-sns-cloud-watch-log-group-name: Set to 'mock'
    firebase-server-key: Set to 'mock'
    hsm-key-id: Set to 'mock'
    production-mode: Set to 'false'



Analytics:
    analytics-test: Boolean, set whether analytics testing is enabled (production mode must be off)
    analytics.google.clinic.id: GA measurement ID for clinic UI
    analytics.google.patient.id: GA measurement ID for patient UI
    analytics-database configuration:
        analytics.database.password: eg. 'password'
        analytics.database.schema: eg. 'usageanalytics'
        analytics.database.type: eg. 'PostgreSQL'
        analytics.database.url: eg. '******************************************' (a non-blank url causes isGeneralDataSyncEnabled to return true)
        analytics.database.user: eg. 'analytics'
    firebase-server-key: Read into FIREBASE_SERVER_KEY. Can be set to 'mock' to enable Firebase mock mode. Can be set as firebase-server-key-encrypted.

API:
    api-throttle-limit-*: Read in the format api-throttle-limit-X-Y, where X is an ApiUsageType name field and Y is 'address', 'hour' or 'day'; eg. 'api-throttle-limit-signup-day'. Address limits are day-based. Set to an integer.

Application:
    browser-time-shift-enabled: Boolean, used for the JadeServlet template base model.
    device-variant: 'CE', 'CI', 'DEMO'
    disable-night-message-sending: Boolean, if true messages cannot be sent outside of 9-21. Forced true if in production mode.
    environment.key: Used to set ENVIRONMENT_KEY. Used for sharing RDS backups?
    import-enabled: Boolean, if false, disables daily data import in w2e 
    production-mode: Boolean, sets whether we're in production mode
    site-type: eg. 'noona', sets the component type for the security configuration and email audit log
    symptom-inquiry-sending-delay-after-modify: Integer, minutes (default 15). How long to wait after save to send inquiries
    update-patients: Boolean, whether to run the updates in UpdatePatientsWorker (eg. work out missing birth date from Finnish SSN)
    wellness-import-enabled: Boolean, sets whether the wellness importer runs
    worker-delay-*: Integer representing milliseconds. Read in the format worker-delay-X, where X is the worker's name in a property file.

Authentication/authorization:
    client-id: w2e configuration
    client-secret: w2e configuration
    system-secret-key: The secret key used by DataSyncService

Cloud configuration:
    aws-access-key-id: Used to set up the AWS credentials provider. Can be set with aws-access-key-id.encrypted
    aws-backup-access-key: Used to set AWS_BACKUP_ACCESS_KEY. Used for sharing backups
    aws-backup-account-access-key: Used to set up AWS credentials for sharing backups
    aws-backup-account-id: The ID of the AWS backup sharing account
    aws-backup-account-secret-key: Used to set up AWS credentials for sharing backups
    aws-backup-secret-key: Used to set AWS_BACK_SECRET_KEY. Used for sharing backups
    aws-region: Used to set AWS_REGION. Used for sharing backups
    aws-secret-key: Used to set up the AWS credentials provider. Can be set with aws-secret-key.encrypted
    aws-sms-region: Note that despite the name, this configures the AWS SNS region
    aws-sns-cloud-watch-log-group-name: Sets the SNS log group name. Can be set to 'mock'
    azure-sso-client-id: Sets the Azure SSO client ID
    azure-sso-redirect-url: Sets the Azure SSO redirect URI
    hsm-key-id: Sets the HSM key ID. Can be set to 'mock'
    hsm-key-id-arn: Used to set AWS_DATABASE_KSM_ARN. Used for sharing backups
    s3.audit.log.access.key: Used to set ACCESS_KEY_PROPERTY_KEY to use for creating the audit log S3 bucket
    s3.audit.log.region: Used to set REGION_PROPERTY_KEY to use for creating the audit log S3 bucket
    s3.audit.log.secret.key: Used to set SECRET_KEY_PROPERTY_KEY to use for creating the audit log S3 bucket

Database:
    data-lake-test: Boolean, set whether data lake testing is enabled. Cannot be enabled in production mode.
    driver: eg. org.postgresql.Driver
    javax.persistence.jdbc.password = Used by SAM and DataSyncService/CustomerDataSyncService. Can also be set with javax.persistence.jdbc.password.encrypted
    javax.persistence.jdbc.url Used by SAM and DataSyncService/CustomerDataSyncService. Uses the format '**************************************'
    javax.persistence.jdbc.user Used by SAM and DataSyncService/CustomerDataSyncService. eg. 'noona'
    liquibase-change-log: Used by DatabaseDiffUtil/PersistenceUtil. Relative path to the XML

Email:
    from-email: Where mails are sent from, eg. Noona <<EMAIL>>
    smtp-host: eg. smtp.gmail.com
    smtp-password: Password for the SMTP server. Can also be set with smtp-password.encrypted
    smtp-port: eg. 587
    smtp-user: Username for the SMTP server
    support-email: Emails for contacting support

Integration:
    sentry-backend-dsn: URI. If set to a nonempty string, Sentry is considered configured and will be used (for noona-search, noona-composite-web, noona-processor and noona-web)
    sentry-frontend-clinic-dsn: Used to get Sentry DSN for the clinic UI in Jade templates
    sentry-frontend-csp: If set, sets the report-uri for Content-Security-Policy in noona-web (note that report-uri is deprecated)
    sentry-frontend-patient-dsn: Used to get Sentry DSN for the patient UI in Jade templates

Network:
    HTTP configuration:
        http-port: eg. 8080
        http-proxy: URL
        https-port: Set to 0 to use http-port for HTTPS
        https-proxy: URL
    Used to configure redirects and email notifications:
        management-url: The full URL for /management/
        nurse-url: The full URL for /nurse/
        patient-url: The full URL for /patient/

Noona services:
    noona-search:
        noonasearch.url: Used to connect to noona-search from the noona REST client, eg. http://localhost:9447
    noona-study:
        noonastudy.jwtsecret: Used for signing in JWT
        noonastudy.passwordresettokenttlinhours: Password reset TTL
        noonastudy.tokenexpirationinhours: JWT token TTL
        noonastudy.url: eg. http://localhost:4200
    noona-web:
        noonaweb.admin.url: eg. http://127.0.0.1:8080/nurse
        noonaweb.eproImport.url: Used for configuring ProImport RPC, eg. http://127.0.0.1:8080/api/system/proimport
        noonaweb.url: Used for configuring web/clinic RPC, eg. http://127.0.0.1:8080/api/system/cliniclink

User management:
    default-admin-user-initial-password: Used for default admin credentials on first run
    default-admin-user-name: Used for default admin credentials on first run




Unclear usage:
    password: eg. password
    username: eg. noona
    url: eg. ********************************************




No apparent usage:
    mix-panel-token
    application-name
    application-url
    logging.level.org.springframework.web
    organization-name
    client-certificate-required
    key-encryption-secret-key
    key-store-password
    key-store-path
    server-certificate-entry-alias
    server-certificate-entry-password
    server-certificate-self-sign-host-name
    server-certificate-self-sign-ip-address
    asset-cache-path
    asset-maximum-size
    javax.persistence.jdbc.driver
    outputChangeLogFile
    spring.jpa.database-platform
    spring.jpa.generate-ddl
    spring.jpa.hibernate.ddl-auto
    redis.cache.url
    referenceUrl
    spring.liquibase.change-log
    spring.jpa.open-in-view
    business-intelligence-reporter-enabled
    eclipselink.ddl-generation
    jira-password
    jira-url
    jira-user
    server.port
    sftp-address
    sftp-directory
    sftp-password
    sftp-port
    sftp-user
    redirect-url
    noonasearch.database.password
    noonasearch.database.password.encrypted
    noonasearch.database.user




Interactive configuration:
    To configure using SAM instead, all of the following environment variables must be set to enable dockerMode:
        NOONA_CONFIG_URL
        NOONA_CONFIG_ACCOUNT
        NOONA_CONFIG_PASSWORD
        NOONA_CONFIG_KEYSTORE_BYTES
        NOONA_CONFIG_KEYSTORE_PASSWORD
        NOONA_CONFIG_KEY
        NOONA_SERVER_KEYSTORE_BYTES
        NOONA_SERVER_KEYSTORE_PASSWORD
