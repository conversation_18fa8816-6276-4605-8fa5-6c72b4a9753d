{"containerDefinitions": [{"name": "noona-test", "image": "146377986248.dkr.ecr.eu-central-1.amazonaws.com/noona-test:latest", "cpu": 1, "memory": 7000, "essential": true, "environment": [{"name": "RUN_IN_AWS", "value": "true"}], "command": ["sh", "-c", "sleep 30; /opt/run/run.sh"], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/noona-test-logs", "awslogs-region": "eu-west-1", "awslogs-stream-prefix": "robot"}}, "dependsOn": [{"containerName": "selenium-hub", "condition": "START"}], "extraHosts": [{"hostname": "selenium-hub", "ipAddress": "127.0.0.1"}]}, {"name": "selenium-hub", "hostname": "localhost", "image": "selenium/hub:latest", "cpu": 1, "memory": 8000, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/noona-test-logs", "awslogs-region": "eu-west-1", "awslogs-stream-prefix": "robot"}}, "environment": [{"name": "GRID_MAX_SESSION", "value": "10"}, {"name": "GRID_TIMEOUT", "value": "310"}, {"name": "SE_SESSION_REQUEST_TIMEOUT", "value": "500"}, {"name": "GRID_BROWSER_TIMEOUT", "value": "310"}], "portMappings": [{"containerPort": 4444, "hostPort": 4444, "protocol": "tcp"}, {"containerPort": 4443, "hostPort": 4443, "protocol": "tcp"}, {"containerPort": 4442, "hostPort": 4442, "protocol": "tcp"}]}, {"name": "chrome", "image": "selenium/node-chrome:latest", "cpu": 10, "memory": 12000, "linuxParameters": {"sharedMemorySize": 2000}, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/noona-test-logs", "awslogs-region": "eu-west-1", "awslogs-stream-prefix": "robot"}}, "environment": [{"name": "HUB_HOST", "value": "localhost"}, {"name": "HUB_PORT", "value": "4444"}, {"name": "START_XVFB", "value": "false"}, {"name": "SE_NODE_MAX_SESSIONS", "value": "16"}, {"name": "SE_NODE_SESSION_TIMEOUT", "value": "310"}, {"name": "SE_SESSION_REQUEST_TIMEOUT", "value": "500"}, {"name": "SE_NODE_OVERRIDE_MAX_SESSIONS", "value": "true"}, {"name": "SE_EVENT_BUS_HOST", "value": "localhost"}, {"name": "SE_EVENT_BUS_PUBLISH_PORT", "value": "4442"}, {"name": "SE_EVENT_BUS_SUBSCRIBE_PORT", "value": "4443"}, {"name": "SE_NODE_GRID_URL", "value": "http://localhost:4444"}], "dependsOn": [{"containerName": "selenium-hub", "condition": "START"}]}], "networkMode": "host", "family": "noona-test", "taskRoleArn": "arn:aws:iam::228054927545:role/NoonaTestS3Role"}