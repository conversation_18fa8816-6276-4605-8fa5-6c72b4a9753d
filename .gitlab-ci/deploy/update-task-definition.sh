#!/bin/sh

set -e

export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_dev
export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_dev

apk update -q
apk add --no-cache python3 curl build-base libffi-dev openssl-dev python3-dev py3-pip
pip3 install -U pip
# Use specific pyyaml/urllib3 versions compatible with both awscli and docker-compose
pip install awscli pyyaml==3.13 urllib3==1.25.6
pip freeze && python3 -V

aws --version
aws configure list

aws ecs register-task-definition --region eu-west-1 --cli-input-json file://$CI_PROJECT_DIR/.gitlab-ci/deploy/aws-task-definition.json
