#!/bin/sh
set -e
export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_dev
export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_dev
apk update -q
apk add --no-cache py-pip openssl-dev python3-dev build-base libffi-dev curl zip
pip install --upgrade pip
pip install awscli
aws --version
pip install -r requirements-jama.txt
RUN_PATH=$(pwd)
SLACK_WEBHOOK="*******************************************************************************"
echo $RUN_PATH
#mkdir reports
##Download report from S3 bucket
aws s3 --region eu-west-1 cp s3://noona-test-reports/${FILE_UPLOAD_DIRECTORY}/ ./reports/ --recursive
zip -r ./reports/${ZIP_NAME}.zip ./reports/

robot \
 --variable ZIP_NAME:"${ZIP_NAME}" \
 --variable CLIENT_ID:"${CLIENT_ID}" \
 --variable CLIENT_SECRET:"${CLIENT_SECRET}" \
 --variable CYCLE_ID:"${CYCLE_ID}" \
 --variable PROJECT_ID:"${PROJECT_ID}" \
 --variable TEST_CASE_API_ID:"${TEST_CASE_API_ID}" \
 --variable TEST_CASE_ID:"${TEST_CASE_ID}" \
 --outputdir "output" jama/jama.robot
retval=$?

if [ "$retval" -ne 0 ]; then
  curl -X POST -H 'Content-type: application/json' --data '{"text":":Alert: Uploading report to Jama failed"}' $SLACK_WEBHOOK
else
  curl -X POST -H 'Content-type: application/json' --data '{"text":":page_with_curl: Uploading report to Jama is done"}' $SLACK_WEBHOOK
fi