#!/bin/sh
set -e
export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_dev
export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_dev
apk update -q
apk add --no-cache py-pip openssl-dev python3-dev build-base libffi-dev curl
#pip install -U --force-reinstall pip virtualenv
#virtualenv .venv && source .venv/bin/activate
pip install --upgrade pip
pip install awscli
aws --version
pip install -r requirements-native.txt
RUN_PATH=$(pwd)
echo $RUN_PATH
echo "Job id $CI_JOB_ID"
./runner-native.sh