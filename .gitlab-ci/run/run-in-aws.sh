#!/bin/sh

set -e

export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_dev
export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_dev

apk update -q
apk add --no-cache python3 jq curl build-base libffi-dev openssl-dev python3-dev py3-pip
pip3 install -U pip
# Use specific pyyaml/urllib3 versions compatible with both awsc<PERSON> and docker-compose
pip install awscli pyyaml==3.13 urllib3==1.25.6
pip freeze && python3 -V
curl -o /usr/local/bin/ecs-cli https://amazon-ecs-cli.s3.amazonaws.com/ecs-cli-linux-amd64-latest
chmod +x /usr/local/bin/ecs-cli

aws --version
ecs-cli --version

# NOTE: This script has been tested locally, but has never been run within the Gitlab runner.
# The main difference are the AWS credentials. The role needed for this script to succeed must include the following permissions:
# ecs:RunTask, ecs:DescribeTasks and s3:GetObject 
# The credentials of that account must be stored within the AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.
# Only then may this script succeed.

# Make sure at least the ENVIRONMENT variable has been given
[[ "$ENVIRONMENT" == "" ]] && echo "You must set the ENVIRONMENT environment variable. 'dev1' and 'test' are currently supported." && exit 1

echo Starting AWS task...

RESPONSE=$(aws ecs run-task --region eu-west-1 --cluster noona-test-cluster --task-definition noona-test --count 1 --overrides '{ "containerOverrides": [ { "name": "noona-test", "environment": [ { "name": "ENVIRONMENT", "value": "'"$ENVIRONMENT"'" }, { "name": "INCLUDE_TAGS", "value": "'"$INCLUDE_TAGS"'" }, { "name": "EXCLUDE_TAGS", "value": "'"$EXCLUDE_TAGS"'" },{ "name": "JOB_ID", "value": "'"$CI_JOB_ID"'" } ] } ] }')

echo "Response from the cluster: $RESPONSE"

TASK_ID=$(echo $RESPONSE | jq --raw-output '.tasks[0].taskArn | split("/")[2]')

echo "Got task ID: $TASK_ID"

echo "Waiting for task to be finished..."
STATUS=none

while [[ "$STATUS" != "STOPPED" ]]
do
    echo Current status: $STATUS. Waiting until task status becomes STOPPED...
    RESPONSE=$(aws ecs describe-tasks --region eu-west-1 --cluster noona-test-cluster --task $TASK_ID)
    STATUS=$(echo $RESPONSE | jq --raw-output '.tasks[0].lastStatus')
    sleep 10
done

STOPPED_REASON=$(echo $RESPONSE | jq --raw-output '.tasks[0].stoppedReason')
echo "Task has finished running. Reason was: $STOPPED_REASON"
echo "Printing task log."

ecs-cli logs --task-id $TASK_ID --cluster noona-test-cluster --region eu-west-1 > aws_log
cat aws_log

echo "Downloading test reports from S3..."
OUTPUT_DIR=$(cat aws_log | grep "outputdir output" | awk '{print $2}' | head -1)
S3_DIR=s3://noona-test-reports/$OUTPUT_DIR

echo "Reports stored in S3 at $S3_DIR"
mkdir output
aws s3 --region eu-west-1 cp s3://noona-test-reports/$OUTPUT_DIR ./output/ --recursive
FAILED=$(cat aws_log | grep "Smoke tests failed" | tail -1)
if [[ "$FAILED" == "Smoke tests failed" ]]; then
  echo "$FAILED exit 1"
  exit 1
fi