Field type;Enumeration key;Translation key;Type;Id;Max length;Min length;Validation pattern;Required;Show conditions;static condition;fi_FI;en_GB;sv_FI;da_DK;no_NO;de_DE;fr_FR;es_ES;it_IT;pt_PT;AR;HE;NL;TR;FI_DIARY;EN_DIARY;SV_DIARY;DA_DIARY;NO_DIARY;DE_DIARY;FR_DIARY;ES_DIARY;IT_DIARY;PT_DIARY;AR_DIARY;HE_DIARY;NL_DIARY;TR_DIARY;;
Form begin;;;;;;;;;;;;Tennessee Oncology Medication Adherence Assessment;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Wizard section;;;;Questionnaire;;;;;;;;Tennessee Oncology Medication Adherence Assessment;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Wizard column;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
checkbox;Completed actions Park;;;Completed actions;;;;0;;;;Select completed actions:;;;;;;;;;;;;;;Select completed actions:;;;;;;;;;;;;;;
;Oncology problems reviewed;;;;;;;;;;;Oncology problems reviewed;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Current regimen reviewed;;;;;;;;;;;Current regimen reviewed;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Allergies reviewed;;;;;;;;;;;Allergies reviewed;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Medications reviewed;;;;;;;;;;;Medications reviewed;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;Adhere tech;;;;0;;;;Adhere Tech Enrollment:;;;;;;;;;;;;;;Adhere Tech Enrollment:;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment adhere tech;;;Add comment adhere tech;2000;1;text;0;;;;Add comment (Adhere Tech Enrollment);;;;;;;;;;;;;;Add comment (Adhere Tech Enrollment);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
checkbox;Person contacted Park;;;Person contacted;;;;0;;;;Person contacted:;;;;;;;;;;;;;;Person contacted:;;;;;;;;;;;;;;
;Patient;;;;;;;;;;;Patient;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Caregiver;;;;;;;;;;;Caregiver;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Other field;;;;;;;;;;;Other;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;person contacted text box;;;person contacted text box;2000;1;text;0;;;;Person contacted;;;;;;;;;;;;;;Person contacted;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
checkbox;Patient feel overall Park;;;Patient feel overall;;;;0;;;;How has the patient been feeling overall since we last spoke?;;;;;;;;;;;;;;How has the patient been feeling overall since we last spoke?;;;;;;;;;;;;;;
;Better;;;;;;;;;;;Better;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Coping;;;;;;;;;;;Coping;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Declining;;;;;;;;;;;Declining;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Unable to perform daily activities;;;;;;;;;;;Unable to perform daily activities;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment patient feel overall;;;Add comment patient feel overall;2000;1;text;0;;;;Add comment (How has the patient been feeling overall since we last spoke?);;;;;;;;;;;;;;Add comment (How has the patient been feeling overall since we last spoke?);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;New or worsening symptoms;;;;0;;;;Any new or worsening symptoms?;;;;;;;;;;;;;;Any new or worsening symptoms?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;New or worsening comment;;;New or worsening comment;2000;1;text;0;;;;Add comment (Any new or worsening symptoms);;;;;;;;;;;;;;Add comment (Any new or worsening symptoms);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
checkbox;Changes in medication past month Park;;;Changes in medication;;;;0;;;;What changes in medication has the patient had in the past month?;;;;;;;;;;;;;;What changes in medication has the patient had in the past month?;;;;;;;;;;;;;;
;Changes;;;;;;;;;;;Changes;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;No changes;;;;;;;;;;;No changes;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment changes in medication;;;Add comment changes in medication;2000;1;text;0;;;;Add comment (What changes in medication has the patient had in the past month?);;;;;;;;;;;;;;Add comment (What changes in medication has the patient had in the past month?);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;Current medication intake Park;;;Current medication intake;;;;0;;;;Tell me how the patient is currently taking their medication (including auxiliary medication):;;;;;;;;;;;;;;Tell me how the patient is currently taking their medication (including auxiliary medication):;;;;;;;;;;;;;;
;Able to verbalize;;;;;;;;;;;Able to verbalize correct dosing instructions;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Able to verbalize instruction;;;;;;;;;;;Able to verbalize correct dosing instructions with assistance;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Unable to verbalize instruction;;;;;;;;;;;Unable to verbalize correct dosing instructions;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Md changed dosing;;;;;;;;;;;MD has changed dosing instructions;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment how the patient;;;Add comment how the patient;2000;1;text;0;;;;Add comment (Tell me how the patient is currently taking their medication (including auxiliary medication));;;;;;;;;;;;;;Add comment (Tell me how the patient is currently taking their medication (including auxiliary medication));;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;Taking correct dose;;;;0;;;;Taking correct dose?;;;;;;;;;;;;;;Taking correct dose?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment take correct dose;;;Add comment take correct dose;2000;1;text;0;;;;Add comment (taking correct dose);;;;;;;;;;;;;;Add comment (taking correct dose);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;Taking on correct schedule;;;;0;;;;Taking on correct schedule?;;;;;;;;;;;;;;Taking on correct schedule?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment taking on correct schedule;;;Add comment taking on correct schedule;2000;1;text;0;;;;Add comment (taking on correct schedule);;;;;;;;;;;;;;Add comment (taking on correct schedule);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;Over the past weeks not medication;;;;0;;;;Over the past 2-3 weeks, were there any days when the patient did not take their medication?;;;;;;;;;;;;;;Over the past 2-3 weeks, were there any days when the patient did not take their medication?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment over the past weeks not medication;;;Add comment over the past weeks not medication;2000;1;text;0;;;;Add comment (over the past 2-3 weeks, were there any days when the patient did not take their medication);;;;;;;;;;;;;;Add comment (over the past 2-3 weeks, were there any days when the patient did not take their medication);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;Adherence with 80 scheduled doses;;;;0;;;;Adherence with 80% scheduled doses?;;;;;;;;;;;;;;Adherence with 80% scheduled doses?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
checkbox;If no select reason Park;;;If no select reason;;;;0;Adherence with 80 scheduled doses contains any false;;;If no, select reason:;;;;;;;;;;;;;;If no, select reason:;;;;;;;;;;;;;;
;Tolerability issues;;;;;;;;;;;Tolerability issues;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Forgetfulness;;;;;;;;;;;Forgetfulness;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Financial concerns;;;;;;;;;;;Financial concerns;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Other;;;;;;;;;;;Other;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment adherence with 80 scheduled doses;;;Add comment adherence with 80 scheduled doses;2000;1;text;0;;;;Add comment (Adherence with 80% scheduled doses);;;;;;;;;;;;;;Add comment (Adherence with 80% scheduled doses);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
checkbox;In home inventory Park;;;In home inventory;;;;0;;;;In home inventory:;;;;;;;;;;;;;;In home inventory:;;;;;;;;;;;;;;
;Days of medication;;;;;;;;;;;Days of medication;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Date of inventory;;;;;;;;;;;Date of inventory;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Has the patient taken todays dose;;;;;;;;;;;Has the patient taken today’s dose?;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Text area;Days of medication;;;Days of medication;100;1;Text;0;In home inventory contains any days of medication;;;Days of medication:;;;;;;;;;;;;;;Days of medication:;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Text area;Date of inventory;;;Date of inventory;10;1;Text;0;In home inventory contains any date of inventory;;;Date of inventory:;;;;;;;;;;;;;;Date of inventory:;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;Patient taken dose;;;;0;In home inventory contains any Has the patient taken todays dose;;;Has the patient taken today’s dose?;;;;;;;;;;;;;;Has the patient taken today’s dose?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Text area;Date of next prescription start;;;Date of prescription;2000;1;Text;0;;;;When will the patient start their next prescription?;;;;;;;;;;;;;;When will the patient start their next prescription?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment home inventory;;;Add comment home inventory;2000;1;Text;0;;;;Add comment (In home inventory);;;;;;;;;;;;;;Add comment (In home inventory);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;Continuing treatment;;;;0;;;;Continuing treatment?;;;;;;;;;;;;;;Continuing treatment?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
checkbox;Continue treatment select reason Park;;;Continue treatment;;;;0;Continuing treatment contains any false;;;If no, select reason:;;;;;;;;;;;;;;If no, select reason:;;;;;;;;;;;;;;
;Progression;;;;;;;;;;;Progression;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Treatment complete;;;;;;;;;;;Treatment complete;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Intolerable side effects;;;;;;;;;;;Intolerable side effects;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Change in treatment;;;;;;;;;;;Change in treatment;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Deceased;;;;;;;;;;;Deceased;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Financial;;;;;;;;;;;Financial;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Patient Decision;;;;;;;;;;;Patient decision;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
text area;Add comment for not continuing treatment;;;Add comment for not continuing treatment;2000;1;Text;0;;;;Add comment (For not continuing treatment);;;;;;;;;;;;;;Add comment (For not continuing treatment);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Radio button;boolean;;;Patient understands;;;;0;;;;The patient understands plan to continue medication at prescribed dose and schedule?;;;;;;;;;;;;;;The patient understands plan to continue medication at prescribed dose and schedule?;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Text area;Add comment patient understands;;;Add comment patient understands;2000;1;Text;0;;;;Add comment (the patient understands plan to continue medication at prescribed dose and schedule);;;;;;;;;;;;;;Add comment (the patient understands plan to continue medication at prescribed dose and schedule);;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Text area;Comments;;;Comments;2000;1;Text;0;;;;Comments:;;;;;;;;;;;;;;Comments:;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
Form end;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;