const warnings = [];
const originalWarn = console.warn;

console.warn = function(...args) {
  const message = args.map(a => (typeof a === 'string' ? a : a.outerHTML || JSON.stringify(a))).join(' ');
  warnings.push({
    message,
    html: args.find(a => a instanceof Element)?.outerHTML || null,
    timestamp: new Date().toISOString()
  });
  originalWarn.apply(console, args);
};

(function() {
  const targetId = prompt('Enter the ID of the container to scan (leave blank to scan the entire page):');
  const container = targetId ? document.querySelector(`#${targetId}`) : document;

  if (targetId && !container) {
    console.warn(`⚠️ No element found with ID "${targetId}".`);
    return;
  }

  function isAriaHidden(el) {
    while (el) {
      if (el.getAttribute && el.getAttribute('aria-hidden') === 'true') {
        return true;
      }
      el = el.parentElement;
    }
    return false;
  }

  const elements = container.querySelectorAll(`
    button,
    a,
    input,
    textarea,
    select,
    img,
    svg,
    label,
    fieldset,
    legend,
    details,
    summary,
    iframe,
    object,
    embed,
    figure,
    figcaption,
    progress,
    meter,
    table,
    caption,
    th,
    form,
    video,
    audio,
    area,
    [role],
    [tabindex],
    [aria-label],
    [aria-labelledby]
  `);

  const roleCount = {};

  elements.forEach(el => {
    if (isAriaHidden(el)) return;
    if (el.getAttribute('tabindex') === '-1') return;
    if ((el.tagName.toLowerCase() === 'svg' || el.tagName.toLowerCase() === 'img') &&
        /^(presentation|none)$/i.test(el.getAttribute('role'))) return;

    const role = el.getAttribute('role');
    const ariaLabel = el.getAttribute('aria-label');
    const ariaLabelledById = el.getAttribute('aria-labelledby');
    const ariaLabelledByEl = ariaLabelledById ? document.getElementById(ariaLabelledById) : null;
    const ariaLabelledBy = ariaLabelledByEl?.textContent?.trim() || null;

    const ariaDescribedById = el.getAttribute('aria-describedby');
    const ariaDescribedByEl = ariaDescribedById ? document.getElementById(ariaDescribedById) : null;

    const ariaControlsId = el.getAttribute('aria-controls');
    const ariaControlsEl = ariaControlsId ? document.getElementById(ariaControlsId) : null;

    const ariaOwnsId = el.getAttribute('aria-owns');
    const ariaOwnsEl = ariaOwnsId ? document.getElementById(ariaOwnsId) : null;

    const altText = el.hasAttribute('alt') ? 'Setting text so empty string does not trigger anything' : null;
    const titleText = el.getAttribute('title');
    const summaryEl = el.querySelector('summary');
    const summaryText = summaryEl?.textContent?.trim() || null;
    const captionEl = el.querySelector('caption');
    const captionText = captionEl?.textContent?.trim() || null;
    const legendEl = el.querySelector('legend');
    const legendText = legendEl?.textContent?.trim() || null;
    const visibleText = el.innerText?.trim() || null;
    const inputValue = el.value || null;

    const elementId = el.getAttribute('id');
    const labelEl = elementId ? document.querySelector(`label[for="${elementId}"]`) : null;
    const labelText = labelEl?.textContent?.trim() || null;

    const accessibleName = ariaLabel || ariaLabelledBy || labelText || altText || titleText ||
                           summaryText || captionText || legendText || visibleText || inputValue;

    if (role && /^navigation|main|region|search|complementary|contentinfo|banner|form$/.test(role)) {
      roleCount[role] = (roleCount[role] || 0) + 1;
    }

    if (ariaLabelledById && !ariaLabelledByEl) console.warn(`⚠️ Broken aria-labelledby reference '${ariaLabelledById}' on`, el);
    if (ariaDescribedById && !ariaDescribedByEl) console.warn(`⚠️ Broken aria-describedby reference '${ariaDescribedById}' on`, el);
    if (ariaControlsId && !ariaControlsEl) console.warn(`⚠️ Broken aria-controls reference '${ariaControlsId}' on`, el);
    if (ariaOwnsId && !ariaOwnsEl) console.warn(`⚠️ Broken aria-owns reference '${ariaOwnsId}' on`, el);

    const tag = el.tagName.toLowerCase();
    if (tag === 'img' && !altText) console.warn('⚠️ <img> missing alt attribute:', el);
    if (tag === 'iframe' && !titleText) console.warn('⚠️ <iframe> missing title attribute:', el);
    if (tag === 'area' && !altText) console.warn('⚠️ <area> missing alt attribute in image map:', el);

    if (!accessibleName) console.warn('⚠️ Missing Accessible Name:', el);
  });

  Object.entries(roleCount).forEach(([role, count]) => {
    if (count > 1) {
      console.warn(`⚠️ Multiple '${role}' landmarks found${targetId ? ' in #' + targetId : ''}. Add unique labels using aria-label or aria-labelledby.`);
    }
  });
})();

// Highlight and annotate problematic elements
warnings.forEach(w => {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(w.html || '', 'text/html');
    const example = doc.body.firstElementChild;
    if (!example || !example.tagName) return;

    // Try to find matching elements using tag, id, and classes
    let selector = example.tagName.toLowerCase();
    if (example.id) selector += `#${example.id}`;
    if (example.className) {
      const classSelector = example.className.trim().split(/\s+/).map(cls => `.${cls}`).join('');
      selector += classSelector;
    }

    const matches = document.querySelectorAll(selector);
    matches.forEach((el, idx) => {
      // Style
      el.style.outline = '3px solid red';
      el.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
      el.setAttribute('data-accessibility-issue', 'true');

      // Tooltip
      el.setAttribute('title', w.message);

      // Add a label if not already
      const label = document.createElement('div');
      label.innerText = `⚠ ${w.message}`;
      label.style.position = 'absolute';
      label.style.background = 'yellow';
      label.style.color = 'black';
      label.style.border = '1px solid red';
      label.style.padding = '2px 4px';
      label.style.fontSize = '12px';
      label.style.zIndex = 9999;
      label.style.pointerEvents = 'none';

      const rect = el.getBoundingClientRect();
      label.style.left = `${rect.left + window.scrollX}px`;
      label.style.top = `${rect.top + window.scrollY - 20}px`;
      document.body.appendChild(label);

      // Scroll first element into view
      if (idx === 0) el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });

  } catch (err) {
    console.warn('⚠️ Failed to highlight issue:', err);
  }
});

return warnings;