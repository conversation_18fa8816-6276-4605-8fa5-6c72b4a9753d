#TA clinic Automated_tests
f13cu01_patient:  #should have a case created to export, answered qol and reported symptom
  ssn: N54624518
  name: <PERSON>
  email: <EMAIL>
f04ca06_patient_extb:
  ssn: 241136-549C
  name: f04ca06-ExtB Patient01
  email: <EMAIL>
f03n04_patient_3:  #inactive account
   ssn: N21445292
   name: <PERSON>
   email: <EMAIL>
f01p01_patient_male:
   ssn: N16002877
   name: <PERSON>
   email: <EMAIL>
f01p01_patient_female:
   ssn: N72440196
   name: <PERSON><PERSON>
   email: <EMAIL>
expired_password_patient_email:
   ssn: N91506185
   name: F07U01 App  Auto Test
   email: <EMAIL>
f07p10_a_patient:
   ssn: N64884722
   name: <PERSON>
   email: <EMAIL>
f07p10_c_patient:
   ssn: N10388176
   name: <PERSON>
   email: <EMAIL>
f01n01_patient_1:
   ssn: N08930307
   name: <PERSON>
   email: <EMAIL>
f01n01_patient_2:
   ssn: N62613710
   name: Dillon Stone
   email: <EMAIL>
f15cm01_patient_1:
   ssn: 100673-273X
   name: F15cm01 Patient1
   email: <EMAIL>
f15cm01_patient_3:
   ssn: 250292-011U
   name: F15cm01 Patient3
   email: <EMAIL>
f15cm01_patient_4:
   ssn: 8yG6cvby
   name: Test ihgsmeab
   email: <EMAIL>
usecase_f01n14_3:
    ssn: 270762-241U
    name: usecase-f01n14-3 patient03x
    email: <EMAIL>
    id_test: 0c4866e2-58d4-467f-8bac-48f88b5dbf3d
    id_staging: 27f6fe2e-b134-465c-a405-55e79b31a7f2
f01n09-d_patient_1:
   ssn: N92460497
   name: F01N09-D Usecase
   email: <EMAIL>
f07u05_patient:
   ssn: F07U05
   name: f07u05 test
   email: <EMAIL>
f01n15_patient_2:
   ssn: N25335939
   name: F01n15 Do not update
   email: <EMAIL>
f01n09-a_patient_1:
   ssn: 170537-245E
   name: f01n09-a patient1
   email: <EMAIL>
f01p07_patient:
   ssn: N73193117
   name: Eugene Johnson
   email: <EMAIL>
f10p01_patient_b:
   ssn: N27227569
   name: F10P01 Test
   email: <EMAIL>
f10p01_patient_c:
   ssn: N18698904
   name: F10P01 Ext C
   email: <EMAIL>
f01n06_patient:
   ssn: N48723326
   name: Roy Mcgee
   email: <EMAIL>
f01n06_patient_extc:
   ssn: N51949683
   name: F01n06 Extension C
   email: <EMAIL>
f14p01_patient1:
   ssn: N06823560
   name: F14p01 Test
   email: <EMAIL>
intake_service_patient_1:  #should have clinical reports
   ssn: N18637707
   name: Intake Service Patient 1
   email: <EMAIL>
   delegate: <EMAIL>
intake_service_patient_2:  #should have clinical reports
   ssn: N63014397
   name: Intake Service Patient 2
   email: <EMAIL>
   id: 45b073f6-9ec2-4ae7-b75d-914ae02d110d
   delegate: <EMAIL>
f13p02_patient_extd:
   ssn: N76875226
   name: f13p02 extd
   email: <EMAIL>
   delegate: <EMAIL>
f03n01_patient:
   ssn: N7454857
   name: Bianca Winner
   email: <EMAIL>
f02n03_patient:
   ssn: N34047245
   name: Jacob Williams
   email: <EMAIL>
f02n04_patient:
   ssn: 040300A9311
   name: Change Password
   email: <EMAIL>
f01p07_patient_mail:
   ssn: N73193118
   name: f01p07 mainx
   email: <EMAIL>

### f01p01c01 photo upload test patients, clinic user: <EMAIL>
f01p01c01_patient:
   ssn: F01P01C01
   name: F01P01C01 TEST
   email: <EMAIL>
f01p01c01_patient_2:
   ssn: F01P01C01-103
   name: F01P01C01-103 TEST
   email: <EMAIL>
f01p01c01_patient_3:
   ssn: F01P01C01-104
   name: F01P01C01-104 TEST
   email: <EMAIL>
f01p01c01_patient_4:
   ssn: F01P01C01-105
   name: F01P01C01-105 TEST
   email: <EMAIL>

f07u06_patient:
   ssn: F07U06
   name: f07u06 test
   email: <EMAIL>
f07u01_patient:
   ssn: N07572510
   name: Tyler Wilkins
   email: <EMAIL>
   delegate: <EMAIL>
f07cu02_patient3:
   ssn: 091269-751H
   name: usecase-f07cu02 patient3
   email: <EMAIL>
f01p01s18_patient:
   ssn: N47669190
   name: Breastcancer Surgery Follow up
   email: <EMAIL>
f07p08_patient:
   ssn: N63143210
   name: F07P08 Patient
   email: <EMAIL>
f01n14_patient_extB:  #should have answered baseline questionnaire
   ssn: 634646
   name: Clipboard Patient
   email: <EMAIL>
f01p13_patient:  #patient should have a baseline questionnaire scheduled in the far future
   ssn: N14602001
   name: f01p13 testx
   email: <EMAIL>
f02p05_patient:
   ssn: N23642533
   name: f02p05 Test
   email: <EMAIL>
   delegate: <EMAIL>
f02p05_patient_extb:
   ssn: N7867676
   name: F02p05 Test Reset
   email: <EMAIL>
   delegate: <EMAIL>
f02n08_patient_main:
   ssn: 010544-012D
   name: f02n08 patient_main
   email: <EMAIL>
f13p01_patient:  #should have added severe pain symptom to diary
   ssn: N87839884
   name: Thomas White
   email: <EMAIL>
f02n07_patient_1:  #f02n07_patient_1 to 6 should be existing for search test cases
   ssn: N80232328
   name: Patientf02n07 kjIwXpG
   email: <EMAIL>
f02n07_patient_2:
   ssn: lkUMqzDM
   name: Patientf02n07 fnumydpj
   email: <EMAIL>
f02n07_patient_3:
   ssn: Patientf02n07-SSN11
   name: Patientf02n07 aocdcwjn
   email: <EMAIL>
f02n07_patient_4:
   ssn: vCeMNcT7
   name: Patientf02n07 ngcrfenn
   email: <EMAIL>
f02n07_patient_5:
   ssn: HZ9elAgE
   name: Patientf02n07 fgugjsqc
   email: <EMAIL>
f02n07_patient_6:
   ssn: Patientf02n07-SSN12
   name: Patientf02n07 rmjucvbu
   email: <EMAIL>
f15cu01_patient:
   ssn: N72447267
   name: F15CU01-1 Usecase
   email: <EMAIL>
f01n17_patient1:  #clinic user should be added as responsible care person
   ssn: 220772-7653X
   name: f01n17 patient1
   email: <EMAIL>
f01n17_patient2:
   ssn: 010101-122MX
   name: f01n17 patient2
   email: <EMAIL>
f15cu02_patient:
   ssn: 643643
   name: usecase f15cu02
   email: <EMAIL>
f15cu05_patient:
   ssn: N64632176
   name: Ronnie Brown
   email: <EMAIL>
f01n13_wqt_patient:  #should have triage cases created
   ssn: N48724694
   name: Daniel Craig
   email: <EMAIL>
f15cu01_ssn1:
   ssn: 140863-472Y
   name: f15cu01 ssn1
   email: <EMAIL>
f15cu01_ssn2:
   ssn: 151096-564P
   name: f15cu01 ssn2
   email: <EMAIL>
f15cu01_ssn3:
   ssn: 090338-386F
   name: f15cu01 ssn3
   email: <EMAIL>
f10cu05_patient:
   ssn: N00925434
   name: F10CU05 Patient
   email: <EMAIL>
f15cu06_patient:
   ssn: N475487
   name: f01p01 nodulesx
   email: <EMAIL>
f05p02_patient:
   ssn: 070686-316R
   name: F05P02 Patient
   email: <EMAIL>
f14p04_extb:
  ssn: 200669-229X
  email: <EMAIL>
f01p08_patient:
  ssn: ***********
  email: <EMAIL>
f03na05_patient:
  ssn: 071040-028T
  email: <EMAIL>
  id_staging: 54f8f6fd-757c-4a67-8237-9878b56f6580
  id_test: 2d61cc11-a33c-4c2b-94a4-206a473a719c
f07p13_patient:
  ssn: 170662-390C
  email: <EMAIL>
f15cm01_patient_2:
  ssn: 061287-424N
  name: F15cm01 Patient2
  email: <EMAIL>
f07p14_patient:
  ssn: N5654545
  email: <EMAIL>
f07p14_patient_exta:
  ssn: N56434343
  email: <EMAIL>
f07u08_labelling:
  name: Product Labelling
  ssn: PL0000001
  email: <EMAIL>
f02n06_patient_1:  #variable not used anywhere but should be added under f02n06 care team
  name: Anthony Mueller
  ssn: N67208543
  email: <EMAIL>  #username active
f02n06_patient_2:  #variable not used anywhere but should be added under f02n06 care team
  name: Curtis Ramos
  ssn: N12136717
  email: <EMAIL>  #username sent
f02n06_patient_3:  #variable not used anywhere but should be added under f02n06 care team
  name: Jayden Smart
  ssn: 060355-565P
  email: <EMAIL>  #locked by user
f02n06_patient_4:  #variable not used anywhere but should be added under f02n06 care team
  name: f02n06 locked
  ssn: N6879890
  email: <EMAIL>  #username locked
f07p01_patient:
  ssn: 1lhbzcY3
  email: <EMAIL>
f07p01_spanish:
  ssn: peTU8jac
  email: <EMAIL>
f01n13_patient_4:
  name: F01n13-4 Main
  ssn: N64165542
  email: <EMAIL>
f07p16_cookie_setting:
  ssn: Y262626626
  name: f07p16 cookie
  email: <EMAIL>
f07u05_ext_e_patient:
   ssn: ***********
   name: F07U05 Exte
   email: <EMAIL>
   delegate: <EMAIL>
f02p01_last_login_patient:
   ssn: XPK4000
   name: Last-Login Patient
   email: <EMAIL>
   delegate: <EMAIL>

#TA clinic Automated_tests_4, with no multiple clinic patient groups
f15cu06_no_mc_patient:
  name: Paul F15UC06
  ssn: ***********
  email: <EMAIL>
f07p07_patient_a:
  ssn: N11321456
  name: Matthew Wood
  email: <EMAIL>
f07p07_patient_b:
  ssn: N53778221
  name: Joshua Shelton
  email: <EMAIL>

# TA clinic Language Testing1
f07u11_finnish_patient1:
  ssn: 240124-1
  name: Finnish Default Patient1
  email: <EMAIL>
  delegate: <EMAIL>
f07u11_finnish_patient2:
  ssn: 070324-1
  name: Finnish Default Patient2
  email: <EMAIL>
  delegate: <EMAIL>

# TA clinic Language Testing2
f07u11_finnish_spmc1: # this patient has also an account in TA clinic Language Testing1
  ssn: 110424-1
  name: Finnish SPMC1
  email: <EMAIL>

#TA clinic Appointment_Clinic
f13p02_patient_1:  #disabled clinical reports under Appointment Clinic
  ssn: lmtAUYyv
  name: f13p02 test
  email: <EMAIL>
f01n13_patient:  #should have cases and answered questionnaires - for details pls check tc 319 documentation
  ssn: UnIXaUhs
  name: f01n13 patient
  email: <EMAIL>
f12sy07_patient_1:
  mrn: PXmB122oX
  name: F12sy07 Patient
  email: <EMAIL>
  id_staging: 08277f24-f54e-493b-b0c0-5a28056b8860
  id_test: 5ad85bbe-2ec9-44ae-aae3-dbb1131aec27
f12sy07_patient_2:
  mrn: 222333
  ssn: 260500A953J
  name: Raivo Raitis
  email: <EMAIL>
  id_test: a53d535a-5f90-4553-b5fa-62d8c5cbb6e4
  id_staging: c84f3f3c-1b59-4055-9083-cde6a2715c9e
f12sy07_patient_3:
  mrn: 888777
  ssn: 260500A965X
  name: App Ointment
  email: <EMAIL>
  id_test: a1eb8a74-c69e-4230-831b-7daad5edbd99
  id_staging: 1f30750e-e17d-43fa-a162-cc7e72e58089
f12sy07_patient_4:
  mrn: KRaoN8t2
  ssn: 3wEnWCu3
  name: f12sy07 zjhtinrb
  email: <EMAIL>
  id_test: b737258d-b90f-4655-862f-0a5f616b73dd
  id_staging: e23efe62-ed87-4851-aca8-6280ef8a0cda
f12sy07_patient_5:
  mrn: qvz8GmhP
  ssn: scJjKTFW
  name: f12sy07 WYIZBufr
  email: <EMAIL>
  id_test: 6b5a086f-ec54-45a3-a822-c2839f2a0bf4
  id_staging: 39436fc4-9ab2-4e6d-a6bb-2a46b177772b
f12sy05_patient:
  mrn: 555777
  ssn: 260500A977X
  name: Omena Noona
  email: <EMAIL>
f12sy05_patient_2:
  mrn: yUg9fktQ
  ssn: zKM83PYh
  name: Test mAuFgSUB
  email: <EMAIL>
f12sy07_patient_6:
  mrn: 031024-1
  ssn: 031024-1
  email: <EMAIL>
  name:  Appt Changes F12SY07
  id_test: 569d4105-181a-491a-9ce9-c1abfcaa6180
  id_staging: 08eed211-0ade-4cdc-8582-a508cd03e410
f12sy07_patient_6_sms:
  mrn: 031024-5
  ssn: 031024-5
  email: <EMAIL>
  name: Appt Changes SMS F12SY07
  id_test: 8619a606-1c1d-4aae-bb98-6baf9a4a2e2e
  id_staging: 783c1b84-5360-4c1f-b94f-be7e632636a3
f12sy07_patient_6_pn:
  mrn: 031024-6
  ssn: 031024-6
  email: <EMAIL>
  name: Appt Changes PN F12SY07
  id_test: a9f94a92-3006-4f2a-9100-ab8335f0290d
  id_staging: 45528975-7170-412d-a9bc-e92fada8835f
f12sy07_patient_7:
  mrn: 031024-2
  ssn: 031024-2
  email: <EMAIL>
  name: Appt Reminder F12SY07
  id_test: 93a2119a-9a69-469b-8894-d71aa7a182ce
  id_staging: 70f51dac-f6d5-440f-9f48-f4be97ced70b
f12sy07_patient_7_sms:
  mrn: 031024-3
  ssn: 031024-3
  email: <EMAIL>
  name: Appt Reminder SMS F12SY07
  id_test: e1fdcd76-8a3e-43f9-a7b6-95f869eb946c
  id_staging: b20a3131-7310-410d-9d2d-e88f9c7f96db
f12sy07_patient_7_pn:
  mrn: 031024-4
  ssn: 031024-4
  email: <EMAIL>
  name: Appt Reminder PN F12SY07
  id_test: 24b5264e-e416-4623-a15c-9fa3abf5082d
  id_staging: b0591e22-9511-4547-b7bd-0c1cd3b32684
f12sy07_patient_8:
  mrn: 041024-1
  ssn: 041024-1
  email: <EMAIL>
  name: Appt New F12SY07
  id_test: 245ce81f-5187-4178-8a80-4a703656c01f
  id_staging: 4b205ad6-9d02-4caf-a8ad-6a8a9a55f59a
f12sy07_patient_8_sms:
  mrn: 041024-2
  ssn: 041024-2
  email: <EMAIL>
  name: Appt New SMS F12SY07
  id_test: b90fcc05-d353-4a51-829d-5a94f0bf713e
  id_staging: 9dd0e439-6a63-4586-96e7-0bcaf0c742e1
f12sy07_patient_8_pn:
  mrn: 041024-3
  ssn: 041024-3
  email: <EMAIL>
  name: Appt New PN F12SY07
  id_test: 595bf21c-2b35-4e70-bee0-488b28889a02
  id_staging: e77b8c38-4b26-4665-906c-f7586746e4b8
f13p02_3_patient_1:
  name: f13p02-3 medication
  ssn: M6272888
  email: <EMAIL>

#TA Clinic Users_Test
f05n01_patient:
  ssn: 200183-664J
  name: Salla Noronen
  email: <EMAIL>

#TA clinic SPMC_Consent_A, TA clinic SPMC_Consent_B
f07p07_patient_c:
   ssn: N19144109
   name: Joseph Owens
   email: <EMAIL>
f07p14_unapproved_unapproved:
   ssn: N89809091
   name: f07p14 unapproved unapproved
   email: <EMAIL>
f07p06_exta:
   ssn: N72109143
   name: f07p06 exta
   email: <EMAIL>
f07p06_extb:
  ssn: N62980969
  name: f07p06 extb
  email: <EMAIL>

#TA clinic Patient_Education
f13p02_patient:
   ssn: 004-003-004-02
   name: Mario Gomez
   email: <EMAIL>
   recipient_email: <EMAIL>
f01p03_patient_exta:
   ssn: N4738473
   name: Automated F01P03
   email: <EMAIL>
f14p04_patient_2:
  ssn: PE-001
  name: Paiva Karhunen
  email: <EMAIL>
  delegate: <EMAIL>
f14p04_re_doc:
  ssn: RMV-001
  email: <EMAIL>
f13p02_1_patient:
  ssn: N47311565
  id_test: 0f3c9490-b853-4503-aaf9-65e25993a1a2
  id_staging: 010bc13e-0b7a-418d-8c84-e08af56329ea
  name: F13P02-1 gIyisle
  email: <EMAIL>

#TA clinic SPMC_Clinic_A
f07p14_patient_1_active:
   ssn: N7898980
   name: f07p14 Patient
   email: <EMAIL>
f07p14_patient_delegate:
  email: <EMAIL>

#TA clinic SPMC_Clinic_A, TA clinic Custom_Branding_Enabled
f07uo5_extd_patient:
  ssn: N09989890
  name: F07U05 ExtD
  email: <EMAIL>

#TA clinic SPMC_Clinic_A, TA clinic disabled
f07p08_activated_disabled:
  ssn: N4998090
  name: F07p08 Disabled
  email: <EMAIL>
  id_test: 1639ade8-6e15-4ae1-89af-269f78588b1e
  id_staging: 4012f553-6879-433e-b6e3-b92cc00d9264

#TA clinic SPMC_Clinic_A, TA clinic SPMC_Clinic_B
f07p14_active_active:
   ssn: N8980909
   name: f07p14 active active
   email: <EMAIL>
f07p14_active_locked:
   ssn: N82144368
   name: f07p14 Active Locked
   email: <EMAIL>
f07p14_active_proxy:
   ssn: N09973426
   name: f07p14 active proxy
   email: <EMAIL>
f07p14_active_candidate:
   ssn: 0qZYc2gf
   name: f07p14 active candidate
   email: <EMAIL>
f07p14_active_invited:
   ssn: N37297229
   name: f07p14 active invited
   email: <EMAIL>
f07p14_active_unapproved:  # Second clinic for this patient is a '...Consent B' clinic
   ssn: N89067687
   name: f07p14 active unapproved
   email: <EMAIL>
f07p14_active_declined:
   ssn: Jmn1dxao
   name: f07p14 active declined
   email: <EMAIL>
f02n03_spmc_patient:
   ssn: N89876767
   name: Those Writing
   email: <EMAIL>
f02n03_patient2:
   ssn: N62992528
   name: F02n03 Test
   email: <EMAIL>

#TA clinic SPMC_Clinic_A, Disabled Clinic Test
f07p14_active_disabled:
  ssn: N46905526
  name: f07p14 Active Disabled
  email: <EMAIL>

#TA clinic Automated_tests_3
f01p01s18_extb:
  ssn: R95467813
  name: f01p01s18 extb
  email: <EMAIL>

#TA clinic Automated_tests_4
f01p01s18_extd:
  ssn: T0303754V
  name: f01p01s18 extd
  email: <EMAIL>

#TA clinic Penan_klinikka
f07u01_patient_sms3:
  ssn: 3243432
  name: Invalid Code
  email: <EMAIL>

#Mailosaur-SMS
f07u01_patient_sms1:
  ssn: 4634634  #170945-127X in test
  name: SMS Patient 2
  email: <EMAIL>  #number should be +3584573966771
f07u01_patient_sms2:
  ssn: 235325
  name: SMS Patient 4
  email: <EMAIL>  #number should be +3584573966771
f07u02_2fa_patient:
  ssn: 020248-132J
  email: <EMAIL>  #number should be +3584573966771
f07p02_2fa_patient:
  mrn: 235236  #*********** in test
  email: <EMAIL>  #number should be +3584573966771
f07u02app_patient_extD:
    ssn: ***********
    email: <EMAIL>  #number should be +3584573966771
    name: f07u02app patient_extd


#TA clinic Test_Clinic_Setting_1
f03n03_patient:
  ssn: N46687900
  name: Test Clinic Info
  email: <EMAIL>
f03na01_patient2:
  ssn: 310764-667N
  name: Test Clinic Settings
  email: <EMAIL>

#TA clinic disabled
f07u01_disabled_clinic:
  email: <EMAIL>
  ssn: N673637
  delegate: <EMAIL>
f07u01_active_disabled_clinic:
  email: <EMAIL>
  ssn: N673627

#TA clinic disabled two
f07u01_disabled_two_clinics:
  mrn: N673631
  name: f07u01 disabled_two_clinics
  email: <EMAIL>

# Time Constraint Testing
smart_symptom_3days:
  name: Smart Symptom Patient3D
  ssn: 150124-1
  email: <EMAIL>
smart_symptom_6days:
  name: Smart Symptom Patient6D
  ssn: 150124-2
  email: <EMAIL>

# New Integration Project Test Clinic
f13p02_3_patient_2:
  name: f13p02-3 TA1
  ssn: H52626662
  email: <EMAIL>

f13p02_3_patient_3:
  name: f13p02-3 TA2
  ssn: H52626636
  email: <EMAIL>

f13p02_3_patient_4:
  name: f13p02-3 TA3
  ssn: H526266541
  email: <EMAIL>

# TA clinic 2fa Enabled
f07u05_patient_ext_c:
  name: F07u05 Extc
  ssn: N78790900
  email: <EMAIL>

# TA All Settings clinic
f03na01_patient:
  name: basic.settings f03na01
  ssn: IpJhHwBd
  email: <EMAIL>

# TA clinic Test_Clinic_Setting_4
f03na01_capture_symptom:
  name: f03na1 capture.symptom
  ssn: YVB0000
  email: <EMAIL>

#TA clinic F13CU03-6
f13cu03-6_patient1:
  ssn: 170681-582F
  name: f13cu03-6 patient01
  email: <EMAIL>
f13cu03-6_patient2:
  ssn: 120748-189V
  name: f13cu03-6 patient02
  email: <EMAIL>
f13cu03-6_patient3:
  ssn: 070889-202E
  name: f13cu03-6 patient03
  email: <EMAIL>

# TA clinic Password Expiry
patient_expired_password_one:
  name: f07p18 patient one
  ssn: *********
  email: <EMAIL>
  id_test: 713c9d84-1692-4c1d-9f2b-2755f94f6a17
  id_staging: 3f951be2-94bf-469e-87c5-c6e2545ee6cb
patient_expired_password_two:
  name: f07p18 patient two
  ssn: *********
  email: <EMAIL>
  id_test: 99356175-f882-4e7b-a689-1be4c1e8b466
  id_staging: 9c473547-e5a9-4df3-abc2-8cb1a22f6e2a

# NoonaFhir Fiji Clinic
# existing in Aria + medication list available
fiji_patient_1:
  name: Fiji4 Testy MiddleTest
  ssn: 071261-830P
  email: <EMAIL>
# existing in Aria + No medication list available
fiji_patient_2:
  name: Fiji9 Testy Middle
  ssn: 210651-290W
  email: <EMAIL>
# not existing in Aria + No medication list available + medication list settings enabled
fiji_patient_3:
  name: Magnus Test Fiji4
  ssn: 260581-083C
  email: <EMAIL>
