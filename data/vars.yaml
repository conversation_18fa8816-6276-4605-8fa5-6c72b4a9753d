# Common variables
BROWSER: headlesschrome
SYS_VAR_PAGE_TIMEOUT: 15
WINDOW_WIDTH: 1200
WINDOW_HEIGHT: 800

# location where patient web app is hosted - including root slash
PATIENT_PATH: /patient/
WORKQUEUE_ENDPOINT: /new-messages
PATIENT_REPORTS_ENDPOINT: /side-effect-reports
PATIENT_CASE_PATH: /nurse#/handle-patient
PASSWORD_CHANGE_ENDPOINT: patient/#/login-by-link/password-change
LAB_RESULTS_PATH: patient/#/library/lab-results
CREATE_PATIENT_ENDPOINT: /create-patient

# test account configs
NOONA_ADMIN: <EMAIL>
NOONA_ADMIN_2FA: <EMAIL>
DEFAULT_PASSWORD: Password-1
CLINIC_PATIENT:
  first_name: Clinic
  last_name: Patient
  ssn: 170662-390C
  email: <EMAIL>
CLINIC_PATIENT_2:
  first_name: Clinic2
  last_name: Patient2
  ssn: 260200A9279
  email: <EMAIL>
LANGUAGE: en_GB
LANGUAGE_LABEL: English
TEST_EMAIL: <EMAIL>
CLINIC_PATIENT_3:
  first_name: Clinic3
  last_name: Patient3
  ssn: ***********
  email: <EMAIL>
TEST_EMAIL3: <EMAIL>
TEST_EMAIL_YAHOO: <EMAIL>
TEST_EMAIL_APP_PASSWORD: pfdsroveqsaxckyy
TEST_EMAIL_YAHOO_2: <EMAIL>
TEST_EMAIL_APP_PASSWORD_2: expeheuliyohbrdl
TEST_EMAIL_YAHOO_3: <EMAIL>
TEST_EMAIL_APP_PASSWORD_3: vrrwpuipniqkmbma
CLINIC_PATIENT_DEMO_1:
  first_name: richard
  last_name: morris
  email: <EMAIL>
CLINIC_PATIENT_DEMO_2:
  first_name: jon
  last_name: rudd
  email: <EMAIL>
DEFAULT_PASSWORD_DEMO: NoonaDemo12!
DEFAULT_CLINIC_DEMO: Smoke Test
CLINIC_ADMIN: <EMAIL>
USER_TYPE:
  noona_admin: noona_admin
  clinic_admin: clinic_admin
  clinic_mngr: clinic_manager
  clinic_user: clinic_user
