Cookie-policy
Uppdaterad den 11 april 2024

Vi använder cookies
Dessa cookies behövs för att vår app ska kunna fungera på ett säkert och korrekt sätt. Nödvändiga cookies gör det möjligt att använda grundläggande funktioner på webbplatsen/i appen. Exempelvis så identifierar de dig när du loggar in i Noona och detekterar upprepade misslyckade inloggningsförsök. Analytiska cookies bidrar till att vi kan förbättra tjänsterna baserat på informationen vi mottar.
Hur använder vi cookies
Precis som de flesta onlinetjänster använder vår webbplats/app nödvändiga och analytiska cookies för flera ändamål. Nödvändiga cookies är avgörande för att webbplatsen/appen ska kunna fungera på rätt sätt samt tillhandahålla dig våra tjänster på ett säkert sätt. Analytiska cookies som används på vår webbplats/app har som huvudsakligt syfte att förstå hur webbplatsen presterar, hur du interagerar med vår webbplats samt övergripande erbjuda dig en förbättrad och optimerad användarupplevelse samt snabba upp dina framtida interaktioner med vår webbplats/app.
Vilka typer av cookies används på vår webbplats/i vår app
Nödvändiga
Nödvändiga cookies krävs för att möjliggöra de grundläggande egenskaperna på webbplatsen/i appen. Exempel är att erbjuda en säker inloggning eller ändra dina samtyckespreferenser.
Cookie Varaktighet Beskrivning
JSESSIONID Session Används för inloggningsändamål
authJSESSIONID Session Används för inloggningsändamål
AUTH_SESSION_ID Session Används i engångslänkar för inloggning
AUTH_SESSION_ID_LEGACY Session Används för inloggningsändamål
KC_RESTART Session Används för inloggningsändamål
KEYCLOAK_IDENTITY Session Används för inloggningsändamål
KEYCLOAK_IDENTITY_LEGACY Session Används för inloggningsändamål
KEYCLOACK_SESSION Session Används för inloggningsändamål
KEYCLOACK_SESSION_LEGACY Session Används för inloggningsändamål
ONETIMELOGINMODEL Session Används i engångslänkar för inloggning
noona-web-patient_sticky Session Används i engångslänkar för inloggning

Analytiska
Analytiska cookies används för att förstå hur besökare interagerar med webbplatsen/appen. Dessa cookies ger oss information om mätningar som till exempel antalet besökare, avvisningsfrekvens, trafikkälla, osv.
Cookie Varaktighet Beskrivning
dtCookie Session Spårar ett besök via flera begäranden
dtDisabled Session Används för att avgöra om Java-skriptet för analys ska avaktiveras
dtLatC Session Mäter serverlatens för prestandaövervakning
dtPc Session Krävs för att identifiera lämpliga effektmått för sändning av analysdata, inkluderar sessions-ID för korrelation
dtSa Session Fungerar som mellanlagring för sidövergripande åtgärder, sparar namn på användaråtgärder (t.ex. ”Klicka på inloggning”) över flera olika sidor
dtValidationCookie Raderas efter några millisekunder Används för att avgöra toppnivådomän
rxVisitor Session Innehåller en anonymiserad besökar-ID för korrelering av sessioner
rxvt Session Specificerar sessionstidsgränsen