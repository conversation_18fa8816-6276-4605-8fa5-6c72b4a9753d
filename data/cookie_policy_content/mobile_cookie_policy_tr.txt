Çerez Politikası
Güncelleme Tarihi: 11 Nisan 2024

Çerez kullanıyoruz
Bu çerezler, uygulamamızın güvenli ve doğru şekilde çalışması için gereklidir. Gerekli çerezler, web sitesinin/uygulamanın temel işlevlerini yerine getirmesine yardımcı olur, <PERSON><PERSON><PERSON><PERSON>, Noona'ya giriş yaptığınızda sizi tanımlar ve tekrarlanan başarısız giriş denemelerini tespit eder. Analitik çerezler, sağlanan bilgilere dayalı olarak hizmetlerin iyileştirilmesine yardımcı olur.
Çerezleri nasıl kullanıyoruz
Birçok çevrim içi hizmetler gibi, web sitemiz/uygulamamız çeşitli amaçlara yönelik gerekli ve analitik çerezler kullanır. Gerekli çerezler, web sitesinin/uygulamanın temel işlevlerini yerine getirmesine yardımcı olur ve hizmetlerimizi güvenilir bir şekilde size ulaştırır. Web sitemizde/uygulamamızda kullanılan analitik çerezler, esas olarak web sitesinin nasıl performans gösterdiğini, web sitemizle nasıl etkileşimde bulunduğunuzu anlamak ve sonuç olarak size daha iyi ve gelişmiş bir kullanıcı deneyimi sağlamak ve web sitemiz/uygulamamızla gelecekteki etkileşimlerinizi hızlandırmaya yardımcı olmaya yöneliktir.
Web sitemizde/uygulamamızda ne tür çerezler kullanılıyor
Gerekli
Bu web sitesinin/uygulamanın güvenli oturum açma sağlama veya izin tercihlerinizi ayarlama gibi temel özelliklerini etkinleştirmek için gerekli çerezler kullanılmaktadır.
Çerez Süresi Açıklama
JSESSIONID Oturum Oturum açma amaçlarına yönelik kullanılır
authJSESSIONID Oturum Oturum açma amaçlarına yönelik kullanılır
AUTH_SESSION_ID Oturum Tek seferlik oturum açma bağlantılarında kullanılır
AUTH_SESSION_ID_LEGACY Oturum Oturum açma amaçlarına yönelik kullanılır
KC_RESTART Oturum Oturum açma amaçlarına yönelik kullanılır
KEYCLOAK_IDENTITY Oturum Oturum açma amaçlarına yönelik kullanılır
KEYCLOAK_IDENTITY_LEGACY Oturum Oturum açma amaçlarına yönelik kullanılır
KEYCLOACK_SESSION Oturum Oturum açma amaçlarına yönelik kullanılır
KEYCLOACK_SESSION_LEGACY Oturum Oturum açma amaçlarına yönelik kullanılır
ONETIMELOGINMODEL Oturum Tek seferlik oturum açma bağlantılarında kullanılır
noona-web-patient_sticky Oturum Tek seferlik oturum açma bağlantılarında kullanılır

Analitik
Analitik çerezler, ziyaretçilerin web sitesi/uygulama ile nasıl etkileşime girdiğini anlamak için kullanılır. Bu çerezler, ziyaretçi sayısı, tek sayfalık ziyaret veya geri dönüş oranı, trafik kaynağı vb. metrikler hakkında bilgi sağlamaya yardımcı olur.
Çerez Süresi Açıklama
dtCookie Oturum Birden fazla istem boyunca bir ziyareti izler
dtDisabled Oturum Analiz JavaScript'inin devre dışı bırakılıp bırakılmayacağını belirler
dtLatC Oturum Performans izleme için sunucu gecikmesini ölçer
dtPc Oturum Analitik veri iletimi için uygun uç noktaları belirlemek için gereklidir; Korelasyon için Oturum Kimliği içerir
dtSa Oturum Sayfa genişletme işlemleri için ara depolama görevi görür ve kullanıcı eylem adlarını (ör. "Oturum Aç'a Tıklayın") farklı sayfalarda kaydeder
dtValidationCookie Birkaç mili saniye sonra silinir Üst düzey etki alanını belirlemek için kullanılır
rxVisitor Oturum Oturumları ilişkilendirmek için anonimleştirilmiş bir ziyaretçi kimliği içerir
rxvt Oturum Oturum zaman aşımını belirtir