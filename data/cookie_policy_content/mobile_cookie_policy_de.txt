Cookie-Richtlinie
Aktualisiert am 11. April 2024

Wir verwenden Cookies
Diese Cookies werden für die sichere und korrekte Funktion unserer App benötigt. Notwendige Cookies ermöglichen grundlegende Funktionen der Website/App, z. B. die Identifizierung Ihrer Person, wenn Sie sich bei Noona anmelden, und die Erkennung wiederholter fehlgeschlagener Anmeldeversuche. Analytische Cookies helfen uns, die Dienste auf der Grundlage der bereitgestellten Informationen zu verbessern.
Wie verwenden wir Cookies
Wie die meisten Online-Dienste verwendet auch unsere Website/App notwendige und analytische Cookies für verschiedene Zwecke. Notwendige Cookies sind unerlässlich, damit die Website/App richtig funktioniert und Ihnen unsere Dienste auf sichere Weise zur Verfügung stellt. Analytische Cookies, die auf unserer Website/App verwendet werden, dienen hauptsächlich dazu, zu verstehen, wie die Website funktioniert und wie Sie mit unserer Website interagieren, und Ihnen insgesamt eine bessere und optimierte Benutzererfahrung zu bieten und Ihre zukünftigen Interaktionen mit unserer Website/App zu beschleunigen.
Welche Arten von Cookies werden auf unseren Websites/ in unserer App verwendet
Notwendige
Notwendige Cookies sind erforderlich, um die grundlegenden Funktionen dieser Website/App zu ermöglichen, wie z. B. die sichere Anmeldung oder die Anpassung Ihrer Einwilligungseinstellungen.
Cookie Dauer Beschreibung
JSESSIONID Sitzung Wird für Anmeldezwecke verwendet
authJSESSIONID Sitzung Wird für Anmeldezwecke verwendet
AUTH_SESSION_ID Sitzung Wird für einmalige Anmeldelinks verwendet
AUTH_SESSION_ID_LEGACY Sitzung Wird für Anmeldezwecke verwendet
KC_RESTART Sitzung Wird für Anmeldezwecke verwendet
KEYCLOAK_IDENTITY Sitzung Wird für Anmeldezwecke verwendet
KEYCLOAK_IDENTITY_LEGACY Sitzung Wird für Anmeldezwecke verwendet
KEYCLOACK_SESSION Sitzung Wird für Anmeldezwecke verwendet
KEYCLOACK_SESSION_LEGACY Sitzung Wird für Anmeldezwecke verwendet
ONETIMELOGINMODEL Sitzung Wird für einmalige Anmeldelinks verwendet
noona-web-patient_sticky Sitzung Wird für einmalige Anmeldelinks verwendet

Analytische
Analytische Cookies werden verwendet, um zu verstehen, wie Besucher mit der Website/App interagieren. Diese Cookies helfen dabei, Informationen über Kennzahlen wie die Anzahl der Besucher, die Bounce-Rate, die Quelle des Datenverkehrs usw. zu erhalten.
Cookie Dauer Deschreibung
dtCookie Sitzung Verfolgt einen Besuch über mehrere Anfragen hinweg
dtDisabled Sitzung Legt fest, ob das Analyse-JavaScript deaktiviert werden soll
dtLatC Sitzung Misst die Server-Latenz für die Leistungsüberwachung
dtPc Sitzung Erforderlich, um die richtigen Endpunkte für die Übertragung von Analysedaten zu identifizieren; enthält die Sitzungs-ID für die Korrelation
dtSa Sitzung Dient als Zwischenspeicher für seitenübergreifende Aktionen und speichert die Namen von Benutzeraktionen (z. B. „Klicken auf Anmelden“) über verschiedene Seiten hinweg
dtValidationCookie Wird nach ein paar Millisekunden gelöscht Wird verwendet, um die oberste Domäne zu bestimmen
rxVisitor Sitzung Enthält eine anonymisierte Besucher-ID, um Sitzungen zu korrelieren
rxvt Sitzung Gibt die Zeitüberschreitung der Sitzung an