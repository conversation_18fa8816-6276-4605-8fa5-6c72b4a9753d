Cookie Policy
Updated on April 11, 2024

We use cookies
These cookies are needed for our app to work in a secure and correct way. Necessary cookies make basic functions of the website/app possible, for example, identify you when you log into Noona, detecting repeated failed login attempts. Analytical cookies help up to improve the services based on the information provided.
How do we use cookies
As most of the online services, our website/app uses necessary and analytical cookies for several purposes. Necessary cookies are essential for the website/app to function the right way and provide you with our services in a secure manner. Analytical cookies used on our website/app are mainly for understanding how the website performs, how you interact with our website , and all in all providing you with a better and improved user experience and help speed up your future interactions with our website/app.
What types of cookies are used on our websites/in our app
Necessary
Necessary cookies are required to enable the basic features of this website/app, such as providing secure log-in or adjusting your consent preferences.
Cookie Duration Description
JSESSIONID Session Used for log-in purposes
authJSESSIONID Session Used for log-in purposes
AUTH_SESSION_ID Session Used in one time login links
AUTH_SESSION_ID_LEGACY Session Used for log-in purposes
KC_RESTART Session Used for log-in purposes
KEYCLOAK_IDENTITY Session Used for log-in purposes
KEYCLOAK_IDENTITY_LEGACY Session Used for log-in purposes
KEYCLOACK_SESSION Session Used for log-in purposes
K<PERSON><PERSON>CLOACK_SESSION_LEGACY Session Used for log-in purposes
ONETIMELOGINMODEL Session Used in one time login links
noona-web-patient_sticky Session Used in one time login links

Analytical
Analytical cookies are used to understand how visitors interact with the website/app. These cookies help provide information on metrics such as the number of visitors, bounce rate, traffic source, etc.
Cookie Duration Description
dtCookie Session Tracks a visit across multiple requests
dtDisabled Session Determines if the analytics JavaScript should be deactivated
dtLatC Session Measures server latency for performance monitoring
dtPc Session Required to identify proper endpoints for analytics data transmission; includes session ID for correlation
dtSa Session Serves as intermediate storage for page-spanning actions, saving user action names (e.g., “Click on Login”) across different pages
dtValidationCookie Deleted after a few milliseconds Used to determine the top-level domain
rxVisitor Session Contains an anonymised visitor ID to correlate sessions
rxvt Session Specifies the session timeout