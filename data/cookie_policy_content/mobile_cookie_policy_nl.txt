Cookiebeleid
Bijgewerkt op 11 april 2024

Wij gebruiken cookies
Deze cookies zijn nodig om onze app op een veilige en correcte manier te laten werken. Noodzakelijke cookies maken basisfuncties van de website/app mogelijk, bijvoorbeeld u identificeren wanneer u inlogt bij Noona en het detecteren van herhaalde mislukte inlogpogingen. Analytische cookies helpen ons om de services te verbeteren op basis van de verstrekte informatie.
Hoe gebruiken we cookies
Zoals de meeste online diensten, gebruikt onze website/app noodzakelijke en analytische cookies voor verschillende doeleinden. Noodzakelijke cookies zijn essentieel om de website/app op de juiste manier te laten functioneren en u op een veilige manier van onze diensten te voorzien. Analytische cookies die op onze website/app worden gebruikt, zijn voornamelijk bedoeld om te begrijpen hoe de website presteert, hoe u met onze website omgaat en in het algemeen om u een betere en verbeterde gebruikerservaring te bieden en uw toekomstige interacties met onze website/app te versnellen.
Welke soorten cookies worden gebruikt op onze websites/in onze app
Nodig
Noodzakelijke cookies zijn nodig om de basisfuncties van deze website/app mogelijk te maken, zoals veilig inloggen of het aanpassen van uw toestemmingsvoorkeuren.
Cookie Duur Beschrijving
JSESSIONID Sessie Wordt gebruikt om in te loggen
authJSESSIONID Sessie Gebruikt in eenmalige aanmeldlinks
AUTH_SESSION_ID Sessie Gebruikt in eenmalige aanmeldlinks
AUTH_SESSION_ID_LEGACY Sessie Gebruikt voor inloggen
KC_RESTART Sessie Wordt gebruikt om in te loggen
KEYCLOAK_IDENTITY Session Wordt gebruikt om in te loggen
KEYCLOAK_IDENTITY_LEGACY Session Wordt gebruikt om in te loggen
KEYCLOACK_SESSION Session Wordt gebruikt om in te loggen.
KEYCLOACK_SESSION_LEGACY Sessie Gebruikt voor inloggen
ONETIMELOGINMODEL Session Gebruikt in eenmalige aanmeldlinks
noona-web-patient_sticky Session Gebruikt in eenmalige aanmeldlinks

Analytisch
Analytische cookies worden gebruikt om te begrijpen hoe bezoekers omgaan met de website/app. Deze cookies helpen informatie te verstrekken over statistieken zoals het aantal bezoekers, bouncepercentage, verkeersbron, enz.
Cookie Duur Beschrijving
dtCookie Sessie Traceert een bezoek over meerdere verzoeken
dtDisabled Sessie Bepaalt of het analytische JavaScript moet worden gedeactiveerd
dtLatC Sessie Meet serverlatentie voor prestatiebewaking
dtPc Sessie Vereist om de juiste eindpunten te identificeren voor het verzenden van analytische gegevens; bevat sessie-ID voor correlatie
dtSa Sessie Dient als tussenopslag voor paginaspreidende acties, waarbij namen van gebruikersacties (bijv. "Klik op Aanmelden") op verschillende pagina's worden opgeslagen
dtValidationCookie Verwijderd na een paar milliseconden Wordt gebruikt om het topleveldomein te bepalen
rxVisitor Sessie Bevat een geanonimiseerde bezoekers-ID om sessies te correleren
rxvt Sessie Specificeert de sessietime-out